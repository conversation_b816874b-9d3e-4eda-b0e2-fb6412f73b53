# Docker 部署

## 构建阶段

FROM node:20-alpine AS builder

## 安装 pnpm

RUN corepack enable && corepack prepare pnpm@9.15.3 --activate

## 设置工作目录

WORKDIR /app

## 复制 package.json 和 pnpm-lock.yaml

COPY package.json pnpm-lock.yaml ./

## 安装依赖

RUN pnpm install --frozen-lockfile

## 复制源代码

COPY . .

## 构建应用

RUN pnpm generate

## 生产阶段

FROM node:20-alpine

## 安装 serve

RUN npm install -g serve

## 设置工作目录

WORKDIR /app

## 从构建阶段复制静态文件

COPY --from=builder /app/.output/public ./public

## 设置环境变量

ENV NODE_ENV=production
ENV PORT=3000

## 暴露端口

EXPOSE 3000

## 启动服务

CMD ["serve", "-s", "public", "-l", "3000"]
