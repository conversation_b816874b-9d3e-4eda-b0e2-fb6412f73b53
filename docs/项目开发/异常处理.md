## 异常页展示

### 404 页面

- 路由：`[...404].vue`
- 触发条件：访问不存在的路由
- 展示内容：
  - "页面未找到"提示
  - 返回上一页按钮
  - 返回首页按钮

## 错误处理流程

### 1. API 错误拦截

在 `src/plugins/api.ts` 中，当 API 返回错误状态时:

1. 触发错误提示
2. 派发 `ApiError` 自定义事件
3. 抛出错误信息

### 2. 错误事件监听

在 `useApiErrorHandler` 中监听 API 错误事件，根据错误状态码进行路由跳转:

- 链接过期/无效 (4001): 跳转到错误页
- 其他错误: 显示错误提示

### 3. 错误页面渲染

在错误页面组件中：

1. 获取 URL 查询参数中的错误码
2. 使用 valibot 进行参数验证
3. 根据错误码展示对应的错误信息

## 错误状态定义

在 `src/enums/common.ts` 中定义了所有可能的 API 错误状态码:

```typescript
export const enum ApiStatus {
  Success = 200,
  /** 未授权 */
  Unauthorized = 401,
  /** 登录令牌过期 */
  TokenExpired = 411,
  /** 登录链接过期或无效 */
  LinkExpiredOrInvalid = 4001,
}
```

主要包括：

- 200: 成功
- 401: 未授权
- 411: 登录令牌过期
- 4001: 链接过期或无效

## 样式处理

错误页面采用统一的样式结构:

- 居中布局
- 最大宽度限制
- 图标 + 标题 + 描述信息
- 操作按钮区域

## 最佳实践

### 1. 错误码统一管理

- 使用枚举定义所有错误码
- 避免使用魔法数字

### 2. 错误提示规范

- 提供清晰的错误原因
- 给出可行的解决建议

### 3. 用户体验

- 提供返回上一页选项
- 提供返回首页选项
- 保持统一的视觉风格

### 4. 安全性

- 验证 URL 参数
- 避免暴露敏感错误信息

## 注意事项

### 1. 添加新的错误类型时：

- 在 `ApiStatus` 枚举中添加错误码
- 在 `ERROR_ROUTE_MAP` 中配置路由映射
- 在错误页面添加对应的展示逻辑

### 2. 错误页面应保持简洁：

- 避免复杂的交互
- 确保错误信息易于理解
- 提供明确的后续操作指引
