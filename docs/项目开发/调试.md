## 移动端调试

### vConsole 调试工具

在移动端环境（包括企业微信内部）调试时，可以使用 vConsole 工具查看控制台日志、网络请求等信息。

#### 开启方法

在访问应用的 URL 后添加 `?debug=true` 参数即可自动开启 vConsole 调试面板。

**示例：**

- 基本 URL：`https://your-app-url.com?debug=true`
- 带其他参数的 URL：`https://your-app-url.com?param1=value1&debug=true`

#### 功能说明

vConsole 提供以下调试功能：

- **Console**：查看 JavaScript 控制台日志
- **Network**：监控网络请求
- **Element**：查看 DOM 结构
- **Storage**：查看本地存储内容
- **System**：查看设备和系统信息

#### 注意事项

- vConsole 仅用于开发和测试环境
- 生产环境中请谨慎使用，避免泄露敏感信息
- 使用完毕后建议移除 URL 中的 debug 参数
