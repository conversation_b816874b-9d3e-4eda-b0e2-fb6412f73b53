# 快速开始

## 环境要求

- Node.js >= 18.0.0
- pnpm 9.15.3

或者使用 Docker：

- Docker >= 20.10.0
- Docker Compose >= 2.0.0

## 开发环境搭建

### 方式一：本地开发

#### 1. 安装依赖

```bash
pnpm install
```

#### 2. 环境配置

1. 复制环境变量配置文件：

```bash
cp .env.example .env.dev
```

2. 根据实际需求修改 `.env.dev` 文件中的配置

### 方式二：Docker 部署

1. 构建镜像：

```bash
# 构建 Docker 镜像
docker build -t waner-cyber .

# 或使用国内镜像加速
docker build -t waner-cyber . --build-arg REGISTRY_MIRROR=https://registry.npmmirror.com
```

2. 运行容器：

```bash
# 启动容器
docker run -d -p 3000:3000 --name waner-cyber waner-cyber

# 查看容器日志
docker logs -f waner-cyber
```

## 启动开发服务器

```bash
# 本地开发
pnpm dev

# 使用 Mock 数据开发
pnpm dev:mock

# 团队协作模式
pnpm dev:teamwork
```

启动成功后，访问 `http://localhost:3000`

## 常用命令

```bash
# 代码检查
pnpm lint

# 构建项目
pnpm build

# 预览构建结果
pnpm preview
```

## 注意事项

1. 首次启动前确保已正确配置环境变量
2. 如遇依赖问题，可尝试：
   ```bash
   rm -rf node_modules pnpm-lock.yaml
   pnpm store prune
   pnpm install
   ```
3. 端口被占用时，可在 `.env.dev` 中修改端口配置
4. Docker 相关：
   - 如需修改端口映射，更改 `docker run` 命令中的端口参数，如 `-p 8080:3000`
   - 容器内默认使用生产环境配置
   - 如需进入容器内部：`docker exec -it waner-cyber sh`
