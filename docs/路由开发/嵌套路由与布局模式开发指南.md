# Nuxt 3 嵌套路由与布局模式开发指南

## 简介

本文档详细介绍了 Nuxt 3 中嵌套路由与布局模式的实现方法，特别适用于需要共享导航和 UI 元素的相关页面集合，如设置页面、管理后台、工作空间等。通过这种模式，可以构建结构清晰、逻辑分明的多页面应用，提高代码的可维护性和用户体验。

## 基本架构

在 Nuxt 3 中，嵌套路由与布局模式主要包含三个核心组件：

1. **布局组件 (Layout)**：定义在 `layouts` 目录下，包含共享的 UI 元素和导航
2. **容器页面 (Container Page)**：一个中间层页面，用于连接布局和子页面
3. **子页面 (Child Pages)**：实际内容页面，渲染在容器页面中

### 文件结构示例

```
src/
├── layouts/
│   └── settings.vue          # 布局组件
├── pages/
│   ├── (admin)/
│   │   ├── settings.vue      # 容器页面
│   │   └── settings/
│   │       ├── profile.vue   # 子页面
│   │       ├── account.vue   # 子页面
│   │       ├── license.vue   # 子页面
│   │       └── preference.vue # 子页面
```

## 工作流程

嵌套路由与布局模式的工作流程如下：

1. 当访问路由如 `/admin/settings/profile` 时：
   - Nuxt 加载 `pages/(admin)/settings.vue` 作为容器
   - 容器页面使用 `<NuxtLayout name="settings">` 加载 `layouts/settings.vue` 布局
   - 在布局的 `<slot />` 位置，渲染容器页面的内容
   - 容器页面的 `<NuxtPage />` 位置，渲染 `pages/(admin)/settings/profile.vue` 的内容

2. 当用户通过导航切换到不同的子页面时：
   - 只有子页面内容发生变化，布局和容器页面保持不变
   - 可以配置过渡效果，使页面切换更加平滑

## 实现步骤

### 1. 创建布局组件

布局组件 (`layouts/settings.vue`) 负责定义页面的总体结构和导航元素。使用 `<slot />` 作为内容占位符：

```vue
<script setup lang="ts">
import { RouteKey } from '~/enums/route'

// 定义当前活动标签
const activeTab = ref<RouteKey>(RouteKey.个人中心)

// 定义标签项结构
interface TabItem {
  label: string
  icon: Component | null
  routeKey: RouteKey
}

// 定义标签列表
const tabs: TabItem[] = [
  {
    label: '个人信息',
    icon: getRouteIcon(RouteKey.个人中心),
    routeKey: RouteKey.个人中心,
  },
  {
    label: '账号安全',
    icon: getRouteIcon(RouteKey.账号安全),
    routeKey: RouteKey.账号安全,
  },
  {
    label: '偏好设置',
    icon: getRouteIcon(RouteKey.偏好设置),
    routeKey: RouteKey.偏好设置,
  },
  {
    label: '许可证',
    icon: getRouteIcon(RouteKey.许可证),
    routeKey: RouteKey.许可证,
  },
]

const route = useRoute()

// 监听路由变化，更新活动标签
watch(() => route.name, (newRouteName) => {
  const matchedTab = tabs.find((tab) => tab.routeKey === newRouteName)
  if (matchedTab) {
    activeTab.value = matchedTab.routeKey
  }
}, { immediate: true })

// 监听标签点击，进行页面导航
watch(activeTab, async (newTab) => {
  if (newTab !== route.name) {
    await navigateTo(getRoutePath(newTab))
  }
})
</script>

<template>
  <div class="flex h-full flex-col overflow-hidden">
    <!-- 标签导航 -->
    <div>
      <Tabs v-model:value="activeTab">
        <TabList>
          <Tab
            v-for="tab of tabs"
            :key="tab.routeKey"
            :value="tab.routeKey"
          >
            <span class="gap-2 flex-center">
              <Component
                :is="tab.icon"
                :size="18"
              />
              {{ tab.label }}
            </span>
          </Tab>
        </TabList>
      </Tabs>
    </div>

    <!-- 内容区域：用于渲染容器页面内容 -->
    <div class="flex-1 overflow-auto p-4">
      <slot />
    </div>
  </div>
</template>
```

### 2. 创建容器页面

容器页面 (`pages/(admin)/settings.vue`) 是连接布局和子页面的中间层，它负责：
1. 选择并应用布局组件
2. 使用 `<NuxtPage />` 渲染子页面

```vue
<script setup lang="ts">
// 定义页面元数据
definePageMeta({
  title: '系统设置',
})

// 引入应用状态存储，用于配置页面过渡效果
const appStore = useAppStore()
</script>

<template>
  <div class="h-full">
    <!-- 使用 settings 布局 -->
    <NuxtLayout name="settings">
      <!-- 渲染子页面，并配置过渡效果 -->
      <NuxtPage
        :transition="{
          name: appStore.themeState.transition,
          mode: 'out-in',
        }"
      />
    </NuxtLayout>
  </div>
</template>
```

### 3. 创建子页面

子页面 (如 `pages/(admin)/settings/profile.vue`) 包含实际的内容和功能：

```vue
<script setup lang="ts">
// 页面元数据，会被父容器页面读取
definePageMeta({
  title: '个人信息',
})

// 页面逻辑...
</script>

<template>
  <!-- 页面内容 -->
  <div>
    <!-- 表单组件、信息显示等 -->
  </div>
</template>
```

## 路由导航与同步

### 路由工具函数

为了方便路由导航和管理，使用以下辅助函数：

```typescript
// 根据路由键获取对应的路由路径
export function getRoutePath(routeKey: RouteKey, params?: Record<string, string | number>) {
  let path: string = routeConfig[routeKey].route

  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      const paramPattern = `[${key}]`
      path = path.replace(paramPattern, String(value))
    })
  }

  return path
}

// 根据路由键获取对应的图标组件
export function getRouteIcon(routeKey: RouteKey, fallback: Component | null = null): Component | null {
  if (Object.hasOwn(routeConfig, routeKey)) {
    const route = routeConfig[routeKey]
    const icon = 'icon' in route ? route.icon : null
    if (icon) {
      return icon
    }
  }
  return fallback
}
```

### 路由与标签同步

布局组件内使用以下机制保持路由与标签的同步：

1. 监听路由变化，更新活动标签：
```javascript
watch(() => route.name, (newRouteName) => {
  const matchedTab = tabs.find((tab) => tab.routeKey === newRouteName)
  if (matchedTab) {
    activeTab.value = matchedTab.routeKey
  }
}, { immediate: true })
```

2. 监听标签变化，更新路由：
```javascript
watch(activeTab, async (newTab) => {
  if (newTab !== route.name) {
    await navigateTo(getRoutePath(newTab))
  }
})
```

## 过渡效果

容器页面配置子页面之间的过渡效果：

```vue
<NuxtPage
  :transition="{
    name: appStore.themeState.transition,
    mode: 'out-in',
  }"
/>
```

CSS 过渡样式示例：

```css
/* 淡入淡出过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
```

## 路由元数据

使用 `definePageMeta` 为页面定义元数据，可用于页面标题、权限控制等：

```javascript
definePageMeta({
  title: '个人信息',
  middleware: ['auth'], // 可选：添加中间件
  layout: false, // 可选：禁用默认布局
})
```

## 最佳实践

1. **路由命名规范**：
   - 使用枚举定义路由键，便于类型检查和维护
   - 保持路由命名一致性，有助于理解和导航

2. **布局组件设计**：
   - 将共享 UI 元素和导航放在布局组件中
   - 使用响应式状态跟踪当前活动标签或页面

3. **容器页面设计**：
   - 保持容器页面简洁，主要负责连接布局和子页面
   - 合理配置过渡效果，提升用户体验

4. **子页面设计**：
   - 专注于特定功能和内容
   - 不要重复布局组件中已有的 UI 元素

5. **路由同步**：
   - 使用 `watch` 监听路由和标签变化，保持两者同步
   - 使用辅助函数简化路由导航和图标获取

## 常见问题与解决方案

### 1. 标签与路由不同步

**问题**：点击标签后，URL 已更新但标签高亮没有变化。

**解决方案**：确保正确设置 `immediate: true` 选项来立即触发 `watch` 回调：

```javascript
watch(() => route.name, (newRouteName) => {
  // 处理标签高亮
}, { immediate: true })
```

### 2. 子页面不显示

**问题**：容器页面加载但子页面内容未显示。

**解决方案**：
- 确认容器页面中包含 `<NuxtPage />` 组件
- 检查子页面路径是否正确
- 验证没有重定向或权限问题阻止子页面加载

### 3. 布局样式问题

**问题**：布局在某些页面下样式异常。

**解决方案**：
- 使用 CSS 类隔离样式，避免冲突
- 考虑使用 CSS 模块或 Scoped CSS
- 确保响应式设计适应不同内容高度

## 示例：扩展应用到其他场景

此模式不仅适用于设置页面，还可以扩展到其他场景：

### 工作空间页面

```
src/
├── layouts/
│   └── space.vue             # 工作空间布局
├── pages/
│   ├── (admin)/
│   │   ├── space.vue         # 工作空间容器
│   │   └── space/
│   │       └── [spaceId]/
│   │           ├── index.vue # 工作空间概览
│   │           ├── workflow/ # 工作流子页面
│   │           └── setting/  # 工作空间设置
```

### 项目管理页面

```
src/
├── layouts/
│   └── project.vue           # 项目布局
├── pages/
│   ├── (admin)/
│   │   ├── project.vue       # 项目容器
│   │   └── project/
│   │       ├── list.vue      # 项目列表
│   │       └── [projectId]/
│   │           ├── detail.vue # 项目详情
│   │           └── feedback.vue # 项目评价
```

## 结论

Nuxt 3 的嵌套路由与布局模式提供了一种优雅的方式来组织多页面应用，特别适合于需要共享导航和 UI 元素的相关页面集合。通过布局组件、容器页面和子页面的组合，可以构建出结构清晰、用户体验一致的应用界面。

合理利用这种模式，可以显著提高代码的可维护性和可复用性，同时为用户提供直观的导航体验。 