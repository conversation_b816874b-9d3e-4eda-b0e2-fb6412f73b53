# 网络安全运营一体化平台

## 系统介绍

[系统介绍](./系统介绍.md)

## 技术栈

- Vue 3
- Nuxt 3
- TypeScript

## 启动项目

确保安装依赖：

```bash
# pnpm
pnpm install
```

## 本地开发

首先，如果没有 `/.env` 文件，请复制 `/.env.example` 文件并重命名为 `/.env`。

启动本地开发服务：http://localhost:3000

```bash
# pnpm
pnpm dev
```

## 生产环境

构建生产环境应用：

```bash
# pnpm
pnpm build
```

预览生产环境构建：

```bash
# pnpm
pnpm preview
```

## 部署

### 静态部署

```bash
pnpm generate
```

更多部署方式请查看 [部署文档](https://nuxt.com/docs/getting-started/deployment) 了解更多信息。

## 开发工具库介绍

- [Pinia](https://pinia.vuejs.org/) 状态管理
- [Valibot](https://valibot.dev/) 数据验证
- [Vue Query](https://github.com/TanStack/query) 数据请求

## 依赖分析

可以使用 [node-modules-inspector](https://github.com/antfu/node-modules-inspector) 来可视化检查项目依赖：

```bash
# 在项目根目录运行
pnpx node-modules-inspector
```

这将启动一个交互式界面，帮助你:

- 分析项目依赖关系
- 查看依赖包大小
- 检查模块类型(ESM/CJS)
- 识别重复依赖
