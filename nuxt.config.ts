import type { NuxtPage } from 'nuxt/schema'
import { definePreset } from '@primevue/themes'
import Aura from '@primevue/themes/aura'
import { fileURLToPath } from 'node:url'
import zhCN from 'primelocale/zh-CN.json'
import { nodePolyfills } from 'vite-plugin-node-polyfills'

import { defaultDarkModeClass, themeNoir } from './src/enums/theme'

const Noir = definePreset(Aura, themeNoir)

const isFuYao = process.env.NUXT_PUBLIC_RELEASE_ENV?.includes('fuyao') || false

export default defineNuxtConfig({

  modules: [
    '@nuxt/eslint', // 使用 ESLint 检查代码
    '@nuxtjs/stylelint-module', // 使用 Stylelint 检查样式
    '@primevue/nuxt-module', // 使用 PrimeVue UI 组件
    '@pinia/nuxt', // 使用 Pinia 状态管理
    'pinia-plugin-persistedstate/nuxt', // 持久化 Pinia 状态
    '@nuxt/icon', // 使用 Iconify 图标
    'nuxt-svgo', // 压缩 SVG 图标
    '@vant/nuxt', // 使用 Vant UI 组件
    ...(isFuYao ? ['~/modules/workflow-engine/module'] : []),
  ],

  ssr: false, // 禁用服务端渲染，使用静态部署（Static Hosting）

  imports: {
    // 指定自动导入的目录，这些目录中的文件会被自动导入
    dirs: ['services', 'types', 'composables/**'],
  },

  devtools: { enabled: true },

  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      htmlAttrs: { lang: 'zh-CN' },
    },
  },

  css: [
    'primeicons/primeicons.css',
    'vue-json-pretty/lib/styles.css',
    '~/assets/styles/main.css',
  ],

  runtimeConfig: {
    // 在 `public` 中定义的配置，服务端和客户端都能访问
    public: {
      releaseEnv: process.env.NUXT_PUBLIC_RELEASE_ENV,
      // 网站名称
      siteName: process.env.NUXT_PUBLIC_SITE_NAME || '平台',
      // 网站 Logo
      siteLogo: process.env.NUXT_PUBLIC_SITE_LOGO || '/images/logo.webp',
      apiHost: process.env.NUXT_PUBLIC_API_HOST || '',
      apiBase: `${process.env.NUXT_PUBLIC_API_HOST || ''}/api`,
      darkModeClass: process.env.NUXT_PUBLIC_DARK_MODE_CLASS || defaultDarkModeClass,
      enableMock: process.env.NUXT_PUBLIC_ENABLE_MOCK === 'true',

      wecomAppId: process.env.NUXT_PUBLIC_WECOM_APP_ID,
      wecomAgentId: process.env.NUXT_PUBLIC_WECOM_AGENT_ID,
      aiBaseUrl: process.env.NUXT_PUBLIC_AI_BASE_URL,
      aiDefaultModel: process.env.NUXT_PUBLIC_AI_DEFAULT_MODEL,
      // HACK: 临时使用，后续需要迁移到服务端，不能直接暴露给客户端
      aiApiKey: process.env.NUXT_PUBLIC_AI_API_KEY,
      n8nHost: process.env.NUXT_PUBLIC_N8N_HOST,
    },
  },

  srcDir: 'src/',

  alias: {
    '#wf': fileURLToPath(new URL('./src/modules/workflow-engine/runtime', import.meta.url)),
  },

  compatibilityDate: '2024-04-03',

  nitro: {
    // devProxy 用于配置开发环境下的代理设置
    devProxy: {
      // 当请求路径以 '/api' 开头时，会触发这个代理规则
      '/api': {
        target: process.env.NUXT_PUBLIC_API_HOST || '',
        changeOrigin: true,
        // prependPath: true 表示保留原始请求路径中的 '/api' 前缀
        // 例如：请求 '/api/users' 会被代理到 'target/api/users'
        prependPath: true,
      },
    },
  },

  vite: {
    plugins: [
      // @ts-ignore - 忽略类型错误
      nodePolyfills({
        include: ['stream', 'util', 'timers', 'os'], // 只包含需要的模块s
        globals: {
          Buffer: true,
          global: true,
          process: true,
        },
        protocolImports: true,
      }),
    ],
  },

  typescript: {
    strict: true,
  },

  postcss: {
    plugins: {
      'tailwindcss/nesting': {},
      tailwindcss: {},
      autoprefixer: {},
    },
  },

  hooks: {
    'pages:extend': (pages) => {
      const setLayoutAndMiddleware = (pages: NuxtPage[]) => {
        for (const page of pages) {
          // 为所有页面添加重定向中间件
          page.meta ||= {}

          if (page.name === 'login' || page.file?.includes('/pages/(admin)')) {
            page.meta.middleware = ['auth', 'redirect']

            if (page.path.startsWith('/space')) {
              page.meta.middleware.push('space')
            }
          }
          else {
            // 为非管理页面也添加重定向中间件
            page.meta.middleware = page.meta.middleware || []

            if (!Array.isArray(page.meta.middleware)) {
              page.meta.middleware = [page.meta.middleware]
            }

            if (!page.meta.middleware.includes('redirect')) {
              page.meta.middleware.push('redirect')
            }
          }

          if (page.file?.includes('/pages/(admin)')) {
            page.meta.pageGroup = 'admin' // 设置 admin 页面组，从而为 admin 页面设置 admin layout
          }

          if (page.children) {
            setLayoutAndMiddleware(page.children)
          }
        }
      }

      setLayoutAndMiddleware(pages)
    },
  },

  eslint: {
    config: {
      stylistic: {
        braceStyle: 'stroustrup',
        quoteProps: 'as-needed',
        arrowParens: true,
      },
    },
  },

  icon: {
    // 配置 iconify 自定义图标库
    customCollections: [
      {
        prefix: 'custom-icon',
        dir: './src/assets/icons/custom',
      },
    ],
  },

  primevue: {
    autoImport: true,
    options: {
      // PrimeVue 本身仅支持英语，这里我们使用 PrimeVue 社区语言来进行国际化配置
      locale: zhCN['zh-CN'],
      theme: {
        preset: Noir,
        options: {
          darkModeSelector: `.${process.env.NUXT_PUBLIC_DARK_MODE_CLASS || defaultDarkModeClass}`,
        },
      },
      ripple: false,
    },
  },

  stylelint: {
    include: ['./src/assets/styles/**/*.css'],
  },

  svgo: {
    autoImportPath: false,
  },

})
