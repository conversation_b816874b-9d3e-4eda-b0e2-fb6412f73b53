# 知识库管理功能需求文档

## 1. 项目概述

### 1.1 功能定位

在现有工作空间（Space）基础上，新增知识库管理模块，为每个工作空间提供独立的知识内容存储和管理能力，支持与工作流系统的集成调用。

### 1.2 核心目标

- 实现空间级别的知识库隔离管理
- 提供简洁高效的知识内容存储和检索
- 为未来工作流节点调用知识内容提供基础支撑

## 2. 功能需求

### 2.1 知识库管理

- **创建知识库**：在工作空间下创建多个知识库，支持命名和描述
- **知识库列表**：展示当前空间下所有知识库，支持搜索和排序
- **知识库设置**：修改知识库名称、描述等基本信息
- **删除知识库**：删除知识库及其包含的所有知识文件

### 2.2 知识文件管理

- **文件上传**：支持上传多种格式文件（PDF、Word、TXT、Markdown等）
- **文件列表**：展示知识库内所有文件，支持列表/卡片视图切换
- **文件预览**：支持常见格式文件的在线预览
- **文件下载**：支持单个或批量下载文件
- **文件删除**：支持单个或批量删除文件
- **文件重命名**：支持修改文件名称

### 2.3 分类与标签

- **文件夹管理**：支持创建多层级文件夹结构，便于分类整理
- **标签系统**：为知识文件添加自定义标签，支持多标签
- **拖拽操作**：支持文件在文件夹间的拖拽移动

### 2.4 搜索与筛选

- **文件名搜索**：根据文件名进行模糊搜索
- **标签筛选**：根据标签快速筛选文件
- **文件类型筛选**：按文件格式进行筛选
- **时间筛选**：按上传时间、修改时间筛选

### 2.5 权限管理

- **空间权限继承**：知识库权限与工作空间成员权限保持一致
- **基础权限控制**：
  - 空间管理员：完整的增删改查权限
  - 空间成员：查看和下载权限
  - 非空间成员：无访问权限

## 3. 技术实现

### 3.1 数据结构设计

#### 知识库表 (KnowledgeBase)

```typescript
interface KnowledgeBase {
  id: string;
  spaceId: string; // 关联工作空间ID
  name: string; // 知识库名称
  description?: string; // 知识库描述
  createdBy: string; // 创建者ID
  createdAt: string; // 创建时间
  updatedAt?: string; // 更新时间
}
```

#### 知识文件表 (KnowledgeFile)

```typescript
interface KnowledgeFile {
  id: string;
  knowledgeBaseId: string; // 关联知识库ID
  folderId?: string; // 所属文件夹ID（可选）
  name: string; // 文件名
  originalName: string; // 原始文件名
  fileType: string; // 文件类型
  fileSize: number; // 文件大小
  filePath: string; // 文件存储路径
  tags: string[]; // 标签数组
  uploadedBy: string; // 上传者ID
  createdAt: string; // 上传时间
  updatedAt?: string; // 更新时间
}
```

#### 文件夹表 (KnowledgeFolder)

```typescript
interface KnowledgeFolder {
  id: string;
  knowledgeBaseId: string; // 关联知识库ID
  parentId?: string; // 父文件夹ID（可选）
  name: string; // 文件夹名称
  createdBy: string; // 创建者ID
  createdAt: string; // 创建时间
}
```

### 3.2 API接口设计

#### 知识库管理接口

- `GET /api/spaces/{spaceId}/knowledge-bases` - 获取知识库列表
- `POST /api/spaces/{spaceId}/knowledge-bases` - 创建知识库
- `PUT /api/knowledge-bases/{id}` - 更新知识库信息
- `DELETE /api/knowledge-bases/{id}` - 删除知识库

#### 知识文件管理接口

- `GET /api/knowledge-bases/{id}/files` - 获取文件列表
- `POST /api/knowledge-bases/{id}/files/upload` - 上传文件
- `GET /api/knowledge-files/{id}/preview` - 文件预览
- `GET /api/knowledge-files/{id}/download` - 文件下载
- `PUT /api/knowledge-files/{id}` - 更新文件信息
- `DELETE /api/knowledge-files/{id}` - 删除文件

#### 文件夹管理接口

- `GET /api/knowledge-bases/{id}/folders` - 获取文件夹列表
- `POST /api/knowledge-bases/{id}/folders` - 创建文件夹
- `PUT /api/knowledge-folders/{id}` - 更新文件夹
- `DELETE /api/knowledge-folders/{id}` - 删除文件夹

#### 搜索接口

- `GET /api/knowledge-bases/{id}/search` - 搜索知识文件

### 3.3 前端模块结构

```
src/features/knowledge/
├── components/           # 组件
│   ├── KnowledgeBaseList.vue
│   ├── KnowledgeFileList.vue
│   ├── FileUpload.vue
│   ├── FilePreview.vue
│   └── FolderTree.vue
├── composables/         # 组合式函数
│   ├── useKnowledgeBase.ts
│   ├── useKnowledgeFile.ts
│   └── useFileUpload.ts
├── services/           # API服务
│   ├── knowledgeBase.ts
│   └── knowledgeFile.ts
├── stores/             # 状态管理
│   └── knowledge.ts
└── types/              # 类型定义
    └── knowledge.ts
```

## 4. 用户界面设计

### 4.1 页面结构

- **知识库首页**：展示当前空间下的所有知识库，支持创建新知识库
- **知识库详情页**：展示知识库内的文件和文件夹，支持上传、搜索、筛选
- **文件预览页**：支持常见格式文件的在线预览

### 4.2 交互设计

- **拖拽上传**：支持拖拽文件到指定区域进行上传
- **批量操作**：支持多选文件进行批量下载、删除、移动
- **右键菜单**：文件和文件夹支持右键操作菜单
- **面包屑导航**：清晰展示当前位置层级

## 5. 非功能需求

### 5.1 性能要求

- 文件上传支持断点续传
- 大文件预览采用分页或流式加载
- 文件列表支持虚拟滚动，优化大量文件展示性能

### 5.2 安全要求

- 文件上传类型白名单限制
- 文件大小限制（单文件不超过100MB）
- 文件存储路径安全处理，防止路径遍历攻击

### 5.3 存储要求

- 支持本地文件存储和云存储（如OSS、S3）
- 文件存储路径按空间和知识库进行隔离
- 定期清理已删除的文件

## 6. 未来扩展

### 6.1 工作流集成

- 提供知识库内容查询API，供工作流节点调用
- 支持在工作流中引用特定知识文件内容
- 支持基于知识库内容的智能推荐

### 6.2 功能增强

- 支持文件内容全文检索
- 支持知识文件的版本管理
- 支持知识内容的导入导出
- 支持AI辅助的内容摘要和标签生成

## 7. 开发计划

### 第一阶段（基础功能）

- 知识库的增删改查
- 文件上传、下载、删除
- 基础的文件夹管理
- 简单的搜索和筛选

### 第二阶段（体验优化）

- 文件预览功能
- 拖拽操作支持
- 批量操作功能
- 标签系统

### 第三阶段（集成扩展）

- 工作流集成API
- 性能优化
- 安全加固
- 监控和日志

## 8. 验收标准

- [ ] 能够在工作空间下创建和管理多个知识库
- [ ] 支持上传、预览、下载各种格式的文件
- [ ] 支持文件夹分类和标签管理
- [ ] 支持文件名搜索和标签筛选
- [ ] 权限控制与工作空间权限保持一致
- [ ] 界面操作流畅，用户体验良好
- [ ] 文件存储安全可靠，支持大文件处理
