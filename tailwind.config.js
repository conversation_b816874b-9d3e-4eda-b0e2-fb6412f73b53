import containerQueries from '@tailwindcss/container-queries'
import typography from '@tailwindcss/typography'
import colors from 'tailwindcss/colors'
import plugin from 'tailwindcss/plugin'
import primeui from 'tailwindcss-primeui'

const defaultDarkModeClass = 'theme-dark'

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/**/*.{ts,vue}',
  ],

  darkMode: ['selector', `[class="${process.env.NUXT_PUBLIC_DARK_MODE_CLASS || defaultDarkModeClass}"]`],

  theme: {
    extend: {
      colors: {
        success: colors.emerald,
        danger: colors.red,
        warning: colors.amber,
        info: colors.sky,
        divider: 'var(--p-content-border-color)',
        'app-accent': colors.indigo,
        'node-accent': colors.indigo,
        emphasis: 'var(--p-content-hover-background)',
      },

      containers: {
        'wide-sider': '100px',
      },

      backgroundColor: {
        /** 页面背景色 */
        page: 'var(--color-page-background)',
        /** 内容区域背景色 */
        content: 'var(--p-content-background)',
        'content-hover': 'var(--p-content-hover-background)',
        /** 工作流画布背景色 */
        canvas: 'var(--color-canvas-background)',
      },

      gradientColorStops: {
        page: 'var(--color-page-background)',
        content: 'var(--p-content-background)',
      },

      space: (theme) => ({
        'form-field': theme('spacing.5'),
        'form-field-large': theme('spacing.8'),
      }),

      padding: (theme) => ({
        'admin-layout': 'var(--layout-admin-container-padding)',
        'admin-layout-container': 'var(--layout-admin-container-padding)',
        'admin-layout-content': 'var(--layout-admin-content-padding)',
        'client-layout-page': 'var(--layout-client-page-padding)',
        'dialog-content': 'var(--p-dialog-content-padding)',
        'admin-sider': theme('spacing.4'),
        'admin-sider-mini': theme('spacing.2'),
        'form-field': theme('spacing.5'),
        'form-field-large': theme('spacing.8'),
        'in-tab': theme('spacing.4'),
        'in-fieldset': theme('spacing.4'), // <Fieldset /> 的内容在视觉上会贴近上边缘，所以需要添加 margin-top 来拉开距离
        'in-splitter': theme('spacing.4'),
        'card-container': theme('spacing.4'),
        'node-content': theme('spacing.3'),
        'node-items': theme('spacing.2'),
      }),

      margin: (theme) => ({
        'form-field': theme('spacing.5'),
        'form-field-large': theme('spacing.8'),
        'node-items': theme('spacing.2'),
      }),

      borderRadius: {
        'admin-layout-content': 'var(--layout-admin-content-rounded)',
      },

      gap: (theme) => ({
        'admin-layout': 'var(--layout-admin-container-padding)',
        'admin-layout-content': 'var(--layout-admin-content-padding)',
        'card-container': theme('spacing.4'),
        'client-layout-page': 'var(--layout-client-page-padding)',
        'form-field': theme('spacing.5'),
        'form-field-large': theme('spacing.8'),
        'btn-group-in-table': theme('spacing.1'),
        'node-items': theme('spacing.2'),
      }),

      minWidth: (theme) => ({
        'admin-layout': '600px',
        'table-actions': theme('spacing.20'),
        'table-actions-wide': theme('spacing.48'),
      }),

      width: () => ({
        'admin-layout-gap': 'var(--layout-admin-container-padding)',
      }),

      height: (theme) => ({
        'admin-header': theme('spacing.14'),
      }),
    },
  },

  plugins: [
    plugin(({ addUtilities }) => {
      addUtilities({
        // 由于 flex + items-center 的组合在项目中使用频率很高，所以单独定义一个 utility class
        '.flex-center': {
          '@apply flex items-center': {},
        },
        '.inline-flex-center': {
          '@apply inline-flex items-center': {},
        },

        '.table-action-group': {
          '@apply flex-center gap-0.5 justify-end flex-wrap': {},
        },
        '.table-action-group-start': {
          '@apply flex-center gap-0.5 justify-start flex-wrap': {},
        },

        '.text-secondary': {
          '@apply text-surface-500 dark:text-surface-400': {},
        },

        '.space-y-form-field': {
          '& > * + *': {
            '@apply mt-form-field': {},
          },
        },
        '.space-y-form-field-large': {
          '& > * + *': {
            '@apply mt-form-field-large': {},
          },
        },
      })
    }),

    primeui,
    containerQueries,
    typography,
  ],
}
