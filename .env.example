# 这是 .env 文件的示例，请根据实际情况修改，切勿直接使用

# 发布环境标识，用于区分不同环境
NUXT_PUBLIC_RELEASE_ENV=

# 站点名称，用于显示在页面标题中
NUXT_PUBLIC_SITE_NAME=

# 站点 Logo，支持相对路径和绝对路径，默认为 /images/logo.webp
NUXT_PUBLIC_SITE_LOGO=/path/to/logo_image

# 接口服务地址
NUXT_PUBLIC_API_HOST=http://x.x.x.x

# 暗黑模式的类名，用于控制全局暗色主题切换，默认值为 theme-dark
NUXT_PUBLIC_DARK_MODE_CLASS=

# 是否启用 Mock 数据
NUXT_PUBLIC_ENABLE_MOCK=false

# 企业微信 App ID
NUXT_PUBLIC_WECOM_APP_ID=
# 企业微信 Agent ID
NUXT_PUBLIC_WECOM_AGENT_ID=

# AI API 配置
NUXT_PUBLIC_AI_BASE_URL=
NUXT_PUBLIC_AI_API_KEY=

# N8N 地址
NUXT_PUBLIC_N8N_HOST=
