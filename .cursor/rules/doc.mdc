---
description: 项目文档编写规范
globs: 
alwaysApply: false
---
---
description: 项目文档编写规范
globs: 
alwaysApply: false
---
# 项目文档编写规范

## 文档结构规范

### 目录结构

- 使用二级标题 `##` 作为主要章节
- 使用三级标题 `###` 作为子章节
- 最多使用到四级标题 `####`
- 标题层级不跳级使用

### 文件命名

- 使用中文命名，如：`异常处理.md`
- 文件名应简洁明了，表达完整含义
- 多个词组间使用短横线连接，如：`项目开发-规范.md`

## 内容编写规范

### 格式规范

- 中文与英文之间加空格，如：`Vue 组件`
- 中文与数字之间加空格，如：`共 3 个模块`
- 代码块使用 ``` 包裹，并标注语言
- 重要内容使用加粗 `**内容**` 标注
- 文件路径使用反引号标注，如：`src/components`

### 代码展示

- 代码块必须指定语言类型
- 关键代码需添加注释说明
- 代码片段使用 `...` 表示省略
- 重要代码行需添加行号引用

### 列表规范

- 有序列表使用数字编号
- 无序列表使用短横线
- 列表层级不超过三级
- 同级列表保持语法格式一致

## 文档内容要求

### 基本要求

- 使用简洁清晰的语言
- 避免使用口语化表达
- 专业术语保持统一
- 中英文混排时保持格式统一

### 结构要求

- 开头需要简要说明文档用途
- 按逻辑顺序组织内容
- 相关内容应放在同一章节
- 每个章节都应该有明确的主题

### 示例要求

- 提供具体的代码示例
- 示例应该简单易懂
- 关键步骤需要配图说明
- 示例代码需要注释说明

## 文档维护

### 更新规范

- 定期检查文档时效性
- 及时更新过时的内容
- 记录文档的更新历史
- 标注最后更新时间

### 版本控制

- 遵循语义化版本规范
- 重要更新需要说明变更内容
- 保留历史版本文档
- 明确标注文档适用版本

## 注意事项

### 书写风格

- 使用陈述句
- 避免使用感叹号
- 保持客观中立的语气
- 使用规范的标点符号

### 安全考虑

- 不展示敏感配置信息
- 涉及安全的内容需要说明
- 密钥等信息使用占位符
- 标注安全注意事项
