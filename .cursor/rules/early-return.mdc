---
description: 
globs: *.vue,*.ts,*.js
alwaysApply: false
---
# 避免使用 Early Return

为了保持代码流程的一致性和可维护性，我们应当避免使用 early return 模式。Early return 指的是在函数体中提前返回结果的模式，虽然这种模式在某些场景下可以减少代码嵌套，但也可能导致代码逻辑分散，降低可读性。

## 推荐做法

### 1. 使用条件语句包裹主要逻辑

```typescript
// 不推荐
function processData(data) {
  if (!data) return null;
  if (data.status !== 'active') return null;
  
  // 处理数据...
  return result;
}

// 推荐
function processData(data) {
  if (data && data.status === 'active') {
    // 处理数据...
    return result;
  }
  return null;
}
```

### 2. 集中返回值

尽量将函数的返回点集中在函数的末尾，使代码流程更加清晰：

```typescript
// 不推荐
function getUserStatus(user) {
  if (!user) return 'unknown';
  if (!user.isActive) return 'inactive';
  if (user.isAdmin) return 'admin';
  return 'active';
}

// 推荐
function getUserStatus(user) {
  let status = 'active';
  
  if (!user) {
    status = 'unknown';
  } else if (!user.isActive) {
    status = 'inactive';
  } else if (user.isAdmin) {
    status = 'admin';
  }
  
  return status;
}
```

### 3. 例外情况

在以下情况下，early return 可能是合理的：

- 参数验证非常复杂的情况
- 性能关键的代码段
- 错误处理逻辑

这些情况下使用 early return 应当添加明确的注释说明原因。