---
description: TypeScript 开发规范
globs: *.ts,*.vue
---
# TypeScript 开发规范

## 代码风格和结构

- 编写简洁、可维护且技术上准确的 TypeScript 代码
- 优先采用函数式和声明式编程模式，避免使用类
- 强调迭代和模块化，遵循 DRY（Don't Repeat Yourself）原则，最小化代码重复
- 偏好使用 Composition API 的 `<script setup>` 语法
- 使用 Composables 封装和共享可重用的客户端逻辑或状态，以在多个组件中使用

## Nuxt 3 特性

- 利用 Nuxt 3 的自动导入功能，无需手动导入 `ref`、`useState` 或 `useRouter`
- 使用服务器 API（位于 `src/services` 目录）处理服务器端操作
- 使用 `useRuntimeConfig` 访问和管理运行时配置变量
- 对于 SEO，使用 `useHead` 和 `useSeoMeta`
- 对于图标，使用 lucide-vue-next 模块

## 数据获取

- 使用 @tanstack/vue-query 作为数据请求库

## 命名约定

- Composables 命名为 `use<MyComposable>`
- 组件文件名使用 PascalCase（如 `components/MyComponent.vue`）
- 偏好使用命名导出函数，以保持一致性和可读性

## TypeScript 使用

- 全程使用 TypeScript
- 优先使用接口而非类型，以获得更好的可扩展性和合并能力
- 避免使用枚举，选择使用映射以提高类型安全性和灵活性
- 将 TypeScript 接口与函数式组件一起使用

## UI 和样式

- 使用 PrimeVue UI 和 Tailwind CSS 进行组件和样式设计
- 使用 Tailwind CSS 实现响应式设计
