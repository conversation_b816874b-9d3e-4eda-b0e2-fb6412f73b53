import packageJson from 'eslint-plugin-package-json/configs/recommended'
import simpleImportSort from 'eslint-plugin-simple-import-sort'

import withNuxt from './.nuxt/eslint.config.mjs'

import tailwind from 'eslint-plugin-tailwindcss'

// import * as vueParser from 'vue-eslint-parser'
// import * as tsParser from '@typescript-eslint/parser'

export default withNuxt(
  {
    rules: {
      curly: 1,
      eqeqeq: 1,
      'no-console': [1, { allow: ['warn', 'error'] }],
      'newline-before-return': 1,
      'padding-line-between-statements': [
        1,
        { blankLine: 'always', prev: '*', next: 'block-like' },
        { blankLine: 'always', prev: 'block-like', next: '*' },
        { blankLine: 'always', prev: '*', next: ['export', 'case', 'default'] },
      ],

      '@typescript-eslint/no-use-before-define': [
        2,
        {
          functions: true,
          classes: true,
          variables: true,
          typedefs: false, // 允许类型在定义前使用
        },
      ],

      '@typescript-eslint/ban-ts-comment': 0,
    },
  },

  {
    plugins: {
      'simple-import-sort': simpleImportSort,
    },
    rules: {
      'import/order': 0,

      'simple-import-sort/exports': 1,
      // 'simple-import-sort/imports': 1,
      'simple-import-sort/imports': [
        1,
        {
          groups: [
            ['^vue'],
            ['^nuxt', '^@nuxt', '^@?\\w'],
            ['^#'],
            ['^(~|@)'],

            // Parent imports. Put `..` last.
            ['^\\.\\.(?!/?$)', '^\\.\\./?$'],
            // Other relative imports. Put same-folder imports and `.` last.
            ['^\\./(?=.*/)(?!/?$)', '^\\.(?!/?$)', '^\\./?$'],
            // Side effect imports.
            ['^\\u0000'],
            // Style imports.
            ['^.+\\.?(css)$'],
          ],
        },
      ],
    },
  },

  {
    files: ['**/*.vue'],
    rules: {
      'vue/attributes-order': [1, {
        order: [
          'DEFINITION',
          'LIST_RENDERING',
          'CONDITIONALS',
          'RENDER_MODIFIERS',
          'GLOBAL',
          ['UNIQUE', 'SLOT'],
          'TWO_WAY_BINDING',
          'OTHER_DIRECTIVES',
          'OTHER_ATTR',
          'EVENTS',
          'CONTENT',
        ],
        alphabetical: true,
      }],
      'vue/no-v-html': 0,
      'vue/prefer-separate-static-class': 1,
      'vue/prefer-true-attribute-shorthand': [1, 'always'],
      'vue/no-useless-v-bind': 1,
      'vue/no-unused-refs': 1,
      'vue/no-unused-emit-declarations': 1,
      'vue/no-unused-properties': 1,
      'vue/define-emits-declaration': [1, 'type-literal'],
      'vue/v-for-delimiter-style': [1, 'of'],
      'vue/attribute-hyphenation': [1, 'never'],
      'vue/v-on-event-hyphenation': [1, 'never'],
      'vue/block-order': [1, {
        order: ['script', 'template', 'style'],
      }],
    },
  },

  // HACK: 开启以下配置，会导致性能问题（eslint 解析时间过长），暂时关闭
  // {
  //   files: ['**/*.{vue,ts}'],
  //   languageOptions: {
  //     parser: vueParser,
  //     parserOptions: {
  //       parser: tsParser,
  //       project: ['./tsconfig.json'],
  //       tsconfigRootDir: process.cwd(),
  //       extraFileExtensions: ['.vue'],
  //     },
  //   },
  //   rules: {
  //     '@typescript-eslint/no-unnecessary-condition': 1,
  //     '@typescript-eslint/restrict-template-expressions': 1,
  //   },
  // },

  packageJson,
  ...tailwind.configs['flat/recommended'],

  {
    settings: {
      tailwindcss: {
        whitelist: [
          // PrimeVue
          'pi', '^pi-.*', '^p-.*',
          // 动画
          '.*-preview$', 'text-shimmer', 'rainbow-gradient',
          // VueFlow
          'nodrag', 'nowheel', 'workflow-card-background', 'bring-to-front',
        ],
      },
    },
  },
)
