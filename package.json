{"name": "vue-nuxt-starter", "version": "1.0.0", "private": true, "description": "Nuxt 初始化启动项目", "license": "UNLICENSED", "type": "module", "scripts": {"build": "nuxt build", "deps": "pnpm up --interactive --latest", "dev:fuyao": "nuxt dev --dotenv .env.dev-fuyao --host", "dev:mock-fuyao": "nuxt dev --dotenv .env.mock-fuyao", "dev:mock-shizhen": "nuxt dev --dotenv .env.mock-shizhen", "dev:shizhen": "nuxt dev --dotenv .env.dev-shizhen --host", "gen:prod-fuyao": "nuxt generate --dotenv .env.prod-fuyao", "gen:prod-shizhen": "nuxt generate --dotenv .env.prod-shizhen", "gen:prod-shizhen-sass": "nuxt generate --dotenv .env.prod-shizhen-sass", "gen:test-fuyao": "nuxt generate --dotenv .env.test-fuyao", "gen:test-shizhen": "nuxt generate --dotenv .env.test-shizhen", "lint": "run-p lint:ts lint:es lint:css", "lint:css": "stylelint 'src/assets/styles/**/*.css'", "lint:es": "eslint .", "lint:ts": "npx nuxi typecheck", "postinstall": "nuxt prepare", "preview": "nuxt preview"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.14", "@braintree/sanitize-url": "^7.1.1", "@codemirror/autocomplete": "^6.16.0", "@codemirror/commands": "^6.5.0", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-python": "^6.1.6", "@codemirror/language": "^6.10.1", "@codemirror/lint": "^6.8.0", "@codemirror/search": "^6.5.6", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.26.3", "@dagrejs/dagre": "^1.1.4", "@floating-ui/vue": "^1.1.6", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@lezer/common": "^1.2.3", "@lezer/highlight": "^1.2.1", "@n8n/api-types": "0.15.0", "@nuxt/icon": "^1.11.0", "@pinia/nuxt": "^0.10.1", "@primevue/core": "^4.3.3", "@primevue/forms": "^4.3.3", "@primevue/themes": "^4.3.3", "@replit/codemirror-indentation-markers": "^6.5.3", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/vue-query": "^5.67.2", "@typescript/vfs": "^1.6.1", "@uiw/codemirror-theme-github": "^4.23.13", "@vant/nuxt": "^1.0.6", "@vue-flow/background": "^1.3.2", "@vue-flow/core": "^1.42.5", "@vue-flow/minimap": "^1.5.3", "@vue-flow/node-resizer": "^1.4.0", "@vueuse/core": "^13.1.0", "@wecom/jssdk": "^2.2.5", "ai": "^4.3.16", "apexcharts": "^4.3.0", "array-to-tree": "^3.3.2", "change-case": "^5.4.4", "codemirror-lang-html-n8n": "^1.0.0", "comlink": "^4.4.2", "consola": "^3.4.2", "curlconverter": "^4.12.0", "d3": "^7.9.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "esprima-next": "5.8.4", "fast-json-stable-stringify": "^2.1.0", "flatted": "^3.3.3", "fuse.js": "^7.1.0", "json-schema": "^0.4.0", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.483.0", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "msw": "^2.7.5", "n8n-workflow": "1.78.0", "nanoid": "^5.1.5", "node-forge": "^1.3.1", "nuxt": "^3.15.4", "nuxt-svgo": "^4.0.17", "pinia-plugin-persistedstate": "^4.2.0", "primeicons": "^7.0.0", "primelocale": "^2.1.2", "primevue": "^4.3.3", "quill": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-primeui": "^0.5.1", "valibot": "1.0.0-rc.1", "vant": "^4.9.17", "vconsole": "^3.15.1", "vue-draggable-plus": "^0.6.0", "vue-json-pretty": "^2.4.0", "xlsx": "^0.18.5"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@nuxt/eslint": "^1.2.0", "@nuxt/kit": "^3.16.1", "@nuxtjs/stylelint-module": "^5.2.0", "@primevue/nuxt-module": "^4.3.3", "@shikijs/markdown-it": "^3.2.1", "@stagewise/toolbar-vue": "^0.4.8", "@tailwindcss/typography": "^0.5.16", "@types/d3": "^7.4.3", "@types/estree": "^1.0.8", "@types/json-schema": "^7.0.15", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/node-forge": "^1.3.11", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-package-json": "^0.26.3", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "jsonc-eslint-parser": "^2.4.0", "npm-run-all": "^4.1.5", "postcss": "^8.5.2", "sass": "^1.89.0", "shiki": "^3.2.1", "stylelint": "^16.15.0", "stylelint-config-clean-order": "^7.0.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^15.0.0", "stylelint-config-standard": "^37.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-prettier": "^5.0.2", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "vite-plugin-node-polyfills": "^0.23.0", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@9.15.3"}