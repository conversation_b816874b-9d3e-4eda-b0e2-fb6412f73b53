<script setup lang="ts">
import { GlobalEvent } from '~/enums/event'
import AppSettingDialog from '~/features/space/components/app/AppSettingDialog.vue'
import SpaceLayoutSide from '~/features/space/components/space/layout/SpaceLayoutSide.vue'
import SpaceSettingDialog from '~/features/space/components/space/SpaceSettingDialog.vue'
import { useSpaceStore } from '~/features/space/stores/space'

const spaceStore = useSpaceStore()
const { isEmptySpaces, isLoadingSpaces } = storeToRefs(spaceStore)
</script>

<template>
  <div class="h-full">
    <div
      v-if="isEmptySpaces && !isLoadingSpaces"
      class="size-full flex-col justify-center text-center flex-center"
    >
      <div class="mb-4 text-xl font-semibold">
        暂无工作空间
      </div>
      <div class="mb-6 text-secondary">
        工作空间用于组织和管理您的项目与协作内容。<br>
        立即创建一个，开启高效协作之旅！
      </div>

      <Button @click="emitter.emit(GlobalEvent.SpaceCreate)">
        创建工作空间
      </Button>
    </div>

    <div
      v-else
      class="flex h-full"
    >
      <div class="h-full overflow-hidden">
        <SpaceLayoutSide />
      </div>

      <div class="py-admin-layout-content">
        <Divider
          class="!m-0"
          layout="vertical"
        />
      </div>

      <div class="h-full flex-1 overflow-y-auto p-admin-layout-content">
        <slot />
      </div>
    </div>

    <SpaceSettingDialog />

    <AppSettingDialog />
  </div>
</template>
