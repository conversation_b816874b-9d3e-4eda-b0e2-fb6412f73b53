<script setup lang="ts">
import WorkflowLayoutHeader from '#wf/components/layout/WorkflowLayoutHeader.vue'
import WorkflowLayoutSider from '#wf/components/layout/WorkflowLayoutSider.vue'
</script>

<template>
  <div class="flex size-full flex-col overflow-hidden bg-content p-admin-layout">
    <div class="pb-admin-layout">
      <WorkflowLayoutHeader />
    </div>

    <div class="flex flex-1 gap-admin-layout">
      <div class="min-w-44 py-1">
        <WorkflowLayoutSider />
      </div>

      <CardContainer class="flex-1">
        <slot />
      </CardContainer>
    </div>
  </div>
</template>
