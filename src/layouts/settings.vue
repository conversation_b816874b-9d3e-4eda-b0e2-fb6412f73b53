<script setup lang="ts">
import { RouteKey } from '~/enums/route'

const activeTab = ref<RouteKey>(RouteKey.个人中心)

interface TabItem {
  label: string
  icon: Component | null
  routeKey: RouteKey
}

const tabs: TabItem[] = [
  {
    label: '个人信息',
    icon: getRouteIcon(RouteKey.个人中心),
    routeKey: RouteKey.个人中心,
  },
  ...(isFuYao()
    ? []
    : [
        {
          label: '账号安全',
          icon: getRouteIcon(RouteKey.账号安全),
          routeKey: RouteKey.账号安全,
        },
      ]),
  {
    label: '偏好设置',
    icon: getRouteIcon(RouteKey.偏好设置),
    routeKey: RouteKey.偏好设置,
  },
  ...(isFuYao()
    ? []
    : [
        {
          label: '许可证',
          icon: getRouteIcon(RouteKey.许可证),
          routeKey: RouteKey.许可证,
        },
      ]),
]

const route = useRoute()

watch(() => route.name, (newRouteName) => {
  const matchedTab = tabs.find((tab) => tab.routeKey === newRouteName)

  if (matchedTab) {
    activeTab.value = matchedTab.routeKey
  }
}, { immediate: true })

watch(activeTab, async (newTab) => {
  if (newTab !== route.name) {
    await navigateTo(getRoutePath(newTab))
  }
})
</script>

<template>
  <div class="flex h-full flex-col overflow-hidden">
    <div>
      <Tabs v-model:value="activeTab">
        <TabList>
          <Tab
            v-for="tab of tabs"
            :key="tab.routeKey"
            :value="tab.routeKey"
          >
            <span class="gap-2 flex-center">
              <Component
                :is="tab.icon"
                :size="18"
              />
              {{ tab.label }}
            </span>
          </Tab>
        </TabList>
      </Tabs>
    </div>

    <div class="flex-1 overflow-auto p-4">
      <slot />
    </div>
  </div>
</template>
