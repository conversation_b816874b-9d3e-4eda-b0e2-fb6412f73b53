<script setup lang="ts">
import { object, optional, safeParse, string } from 'valibot'

import { UserService } from '~/services-tw/org'

const route = useRoute()
const userStore = useUserStore()

onMounted(async () => {
  const parsed = safeParse(object({
    code: optional(string()),
  }), route.query)

  if (parsed.success) {
    const authCode = parsed.output.code

    if (authCode) {
      if (!userStore.TW_isClientAuthorized()) {
        const res = await UserService.clientLogin(authCode)
        userStore.TW_setClientInfo(res)
      }
    }
  }
})
</script>

<template>
  <div class="size-full justify-center overflow-auto flex-center">
    <div class="size-full md:min-h-max md:w-[475px]">
      <slot />
    </div>
  </div>
</template>
