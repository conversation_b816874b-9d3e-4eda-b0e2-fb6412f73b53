import { faker } from '@faker-js/faker/locale/zh_CN'
import { delay, http, HttpResponse } from 'msw'
import { customAlphabet } from 'nanoid'

import type { ProjectStatus } from '~/constants-tw/project'
import { assetConfig, assetCriticalityConfig, assetFieldTypeConfig, AssetType, serverPortStatusConfig, subnetIpStatusConfig, taskScanConfig, taskStatusConfig } from '~/enums/asset'
import { threatLevelConfig } from '~/enums/security'
import { ticketCommentActionStatusConfig, ticketGradeConfig, ticketStatusConfig, ticketTypeConfig } from '~/enums/ticket'
import { AUTH, userStatusConfig } from '~/enums/user'
import type { ProjectListItem, ProjectPublicDetail, TW_AutoFlow, TW_AutoFlowListItem, TW_Project, TW_ProjectFeedback, TW_ProjectMilestone, TW_ProjectTeam } from '~/types-tw/project'
import type { ClientListItem, PermissionItem, RoleListItem, TW_Client, TW_DepartmentListItem, TW_Role, TW_User, TW_UserListItem, WechatUser, WechatUserListItem } from '~/types-tw/user'

const apiBase = useRuntimeConfig().public.apiBase

/** 统一使用此函数来生成随机的唯一 ID */
function createId(): UniqueId {
  return Number(customAlphabet('0123456789', 4)())
}

/** 创建通用的 API 响应生成器 */
function createApiResponse<T>(data: T): ApiResponse<T> {
  return {
    status: 200,
    message: 'success',
    result: data,
  }
}

/** 创建分页数据响应 */
function createPageResponse<T>(list: T[], pageSize: number = 10): PageResult<T> {
  return {
    list,
    total: list.length,
    page: 1,
    page_sum: Math.ceil(list.length / pageSize),
  }
}

/** 创建 mock 处理器的工具函数 */
function createMockHandler(method: 'get' | 'post' | 'put' | 'delete') {
  return (
    path: string,
    responseData?: unknown,
    options?: {
      withPagination?: boolean
      pageSize?: number
      delayMs?: number
    },
  ) => {
    const fullPath = `${apiBase}${path}`

    return http[method](fullPath, async () => {
      await delay(500)

      if (options?.delayMs && options.delayMs > 0) {
        await delay(options.delayMs)
      }

      const data = options?.withPagination
        ? createPageResponse(responseData as unknown[], options.pageSize)
        : responseData

      return HttpResponse.json(createApiResponse(data))
    })
  }
}

const mockGet = createMockHandler('get')
const mockPost = createMockHandler('post')
const mockPut = createMockHandler('put')
const mockDelete = createMockHandler('delete')

const createMockUser = (): CurrentUser => ({
  id: createId(),
  create_time: faker.date.past().toISOString(),
  update_time: faker.date.recent().toISOString(),
  organize_name: faker.company.name(),
  role_name: faker.person.jobTitle(),
  username: faker.person.fullName(),
  mobile: faker.phone.number({ style: 'national' }),
  job_number: faker.string.numeric(4),
  email: faker.internet.email(),
  avatar: faker.image.avatar(),
  status: faker.helpers.arrayElement(Object.values(userStatusConfig).map((item) => item.value)),
  role_id: faker.number.int({ min: 1, max: 10 }),
  organize_id: faker.number.int({ min: 1, max: 10 }),
  menu_list: [AUTH.SKIP_FUYAO],
})

const createMockUserGroups = (count: number = 5): UserGroup[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    name: faker.company.name(),
    create_time: faker.date.past().toISOString(),
    update_time: faker.date.recent().toISOString(),
  }), { count })

const createMockAssetTemplates = (count: number = 3): AssetTemplate[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    name: `${faker.commerce.productName()}模板`,
    icon: faker.image.avatar(),
    asset_type: faker.helpers.arrayElement(Object.values(assetConfig).map((it) => it.value)),
    is_update: faker.datatype.boolean(),
  }), { count })

const createMockAssetFields = (count: number = 4): AssetField[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    name: faker.word.noun(),
    field_type: {
      id: faker.helpers.arrayElement(Object.values(assetFieldTypeConfig).map((it) => it.id)),
      name: faker.helpers.arrayElement(Object.values(assetFieldTypeConfig).map((it) => it.name)),
      form: faker.helpers.arrayElement(Object.values(assetFieldTypeConfig).map((it) => it.form)),
      sql: faker.helpers.arrayElement(Object.values(assetFieldTypeConfig).map((it) => it.sql)),
    },
    field_name: faker.word.noun(),
    field_type_id: faker.helpers.arrayElement(Object.values(assetFieldTypeConfig).map((it) => it.id)),
    template_id: faker.number.int({ min: 1, max: 10 }),
    is_update: faker.datatype.boolean(),
  }), { count })

const createMockRoles = (count: number = 5): Role[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    name: faker.person.jobTitle(),
    create_time: faker.date.past().toISOString(),
    update_time: faker.date.recent().toISOString(),
  }), { count })

const createMockTickets = (count: number = 20): Ticket[] =>
  faker.helpers.multiple<Ticket>(() => ({
    id: createId(),
    name: faker.lorem.sentence(),
    desc: faker.lorem.paragraph(),
    object_id: faker.number.int({ min: 1, max: 10 }),
    object_type: faker.helpers.arrayElement(Object.values(ticketTypeConfig).map((it) => it.value)),
    object_type_name: faker.helpers.arrayElement(Object.values(ticketTypeConfig).map((it) => it.label)),
    grade: faker.helpers.arrayElement(Object.values(ticketGradeConfig).map((it) => it.value)),
    status: faker.helpers.arrayElement(Object.values(ticketStatusConfig).map((it) => it.value)),
    create_user: createMockUser(),
    create_time: faker.date.past().toISOString(),
    update_time: faker.date.recent().toISOString(),
  }), { count })

const createMockTicketComments = (count: number = 5): TicketComment[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    ticket_id: faker.number.int({ min: 1, max: 20 }),
    content: faker.lorem.paragraph(),
    create_time: faker.date.past().toISOString(),
    user: createMockUser(),
    work_order_id: faker.number.int({ min: 1, max: 20 }),
    status: faker.helpers.arrayElement(Object.values(ticketCommentActionStatusConfig).map((it) => it.value)),
  }), { count })

const createMockCVEs = (count: number = 5): SecurityAlert[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    cve_id: faker.string.alphanumeric(10),
    cve_name: faker.lorem.sentence(),
    cve_desc: faker.lorem.paragraph(),
    cnnvd_id: faker.string.alphanumeric(10),
    name: faker.lorem.sentence(),
    publish_time: faker.date.past().toISOString(),
    update_time: faker.date.recent().toISOString(),
    vendor_name: faker.company.name(),
    level: faker.helpers.arrayElement(Object.values(threatLevelConfig).map((it) => it.value)),
    type_name: faker.lorem.sentence(),
    desc: faker.lorem.paragraph(),
  }), { count })

const createMockNetworks = (count: number = 5): Network[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    name: faker.company.name(),
    parent_id: faker.datatype.boolean() ? createId() : undefined,
    create_time: faker.date.past().toISOString(),
    count: {
      network_equipment: faker.number.int({ min: 0, max: 10 }),
      node: faker.number.int({ min: 0, max: 20 }),
    },
  }), { count })

const createMockNetworkDevices = (count: number = 5): NetworkDevice[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    network: createMockNetworks(1)[0],
    network_id: createId(),
    network_equipment_id: createId(),
    parent_id: faker.datatype.boolean() ? createId() : undefined,
    asset_type: assetConfig[AssetType.NetworkDevice].value,
    template: createMockAssetTemplates(1)[0],
    template_id: createId(),
    asset: {
      id: createId(),
      name: faker.company.name(),
    },
    node_url: faker.internet.url(),
    node_status: faker.datatype.boolean(),
    count: {
      ip_subnet: faker.number.int({ min: 0, max: 10 }),
      ip: faker.number.int({ min: 0, max: 50 }),
      server: faker.number.int({ min: 0, max: 20 }),
      web: faker.number.int({ min: 0, max: 10 }),
      database: faker.number.int({ min: 0, max: 5 }),
      other: faker.number.int({ min: 0, max: 5 }),
    },
  }), { count })

const createMockSubnets = (count: number = 5): Subnet[] =>
  faker.helpers.multiple<Subnet>(() => ({
    id: createId(),
    network_equipment: createMockNetworkDevices(1)[0],
    network_equipment_id: createId(),
    ip_segment: faker.internet.ipv4() + '/24',
    status: faker.helpers.arrayElement(Object.values(subnetIpStatusConfig).map((item) => item.value)),
    count: {
      online: faker.number.int({ min: 0, max: 20 }),
      offline: faker.number.int({ min: 0, max: 10 }),
      unknown: faker.number.int({ min: 0, max: 5 }),
    },
  }), { count })

const createMockServers = (count: number = 5): Server[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    network_equipment_id: createId(),
    template_id: createId(),
    asset_type: assetConfig[AssetType.Server].value,
    parent_id: faker.datatype.boolean() ? createId() : undefined,
    asset: {
      id: createId(),
      name: faker.company.name(),
    },
    level: [
      { level: faker.helpers.arrayElement(Object.values(threatLevelConfig).map((item) => item.value)), count: faker.number.int({ min: 0, max: 10 }) },
    ],
  }), { count })

const createMockSubnetIps = (count: number = 10): SubnetIp[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    network_id: createId(),
    template_id: createId(),
    ipv4: faker.internet.ipv4(),
    ipv6: faker.datatype.boolean() ? faker.internet.ipv6() : undefined,
    mac: faker.internet.mac(),
    status: faker.helpers.arrayElement(Object.values(subnetIpStatusConfig).map((item) => item.value)),
    create_time: faker.date.past().toISOString(),
    last_scan_time: faker.date.recent().toISOString(),
    // 可能关联的服务器信息
    server: faker.datatype.boolean()
      ? {
          id: createId(),
          name: faker.company.name(),
          template_id: createId(),
          asset_type: assetConfig[AssetType.Server].value,
          ipv4: faker.internet.ipv4(),
          ip_list: [{
            id: createId(),
            network_id: createId(),
            create_time: faker.date.past().toISOString(),
            ipv4: faker.internet.ipv4(),
            ipv6: faker.datatype.boolean() ? faker.internet.ipv6() : undefined,
            mac: faker.internet.mac(),
          }],
        }
      : undefined,
    server_id: faker.datatype.boolean() ? createId() : undefined,
    // 资产信息
    asset: faker.datatype.boolean()
      ? {
          id: createId(),
          name: faker.company.name(),
        }
      : undefined,
  }), { count })

const createMockIpLogs = (count: number = 5): IpLog[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    ip: createMockSubnetIps(1)[0],
    task_log: {
      id: createId(),
      task_id: createId(),
      create_time: faker.date.past().toISOString(),
    },
    old_ipv4: faker.internet.ipv4(),
    new_ipv4: faker.internet.ipv4(),
    create_time: faker.date.recent().toISOString(),
  }), { count })

const createMockDatabases = (count: number = 5): Database[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    network_equipment_id: createId(),
    template_id: createId(),
    asset_type: assetConfig[AssetType.Database].value,
    asset: {
      id: createId(),
      name: faker.company.name(),
    },
    table_name: faker.database.column(),
    server: faker.datatype.boolean()
      ? {
          id: createId(),
          name: faker.company.name(),
          template_id: createId(),
          asset_type: assetConfig[AssetType.Server].value,
          ipv4: faker.internet.ipv4(),
          ip_list: [{
            id: createId(),
            network_id: createId(),
            create_time: faker.date.past().toISOString(),
            ipv4: faker.internet.ipv4(),
          }],
        }
      : undefined,
    server_id: faker.datatype.boolean() ? createId() : undefined,
    port: faker.datatype.boolean()
      ? {
          id: createId(),
          port: faker.internet.port(),
          server_id: createId(),
          status: faker.helpers.arrayElement(Object.values(serverPortStatusConfig).map((item) => item.value)),
          fingerprint: faker.helpers.multiple(() => faker.string.sample(), { count: 3 }),
        }
      : undefined,
    port_id: faker.datatype.boolean() ? createId() : undefined,
  }), { count })

const createMockBusinessSystems = (count: number = 5): BusinessSystem[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    network_equipment_id: createId(),
    template_id: createId(),
    asset_type: assetConfig[AssetType.BusinessSystem].value,
    asset: {
      id: createId(),
      name: faker.company.name(),
    },
    server: faker.datatype.boolean()
      ? {
          id: createId(),
          name: faker.company.name(),
          template_id: createId(),
          asset_type: assetConfig[AssetType.Server].value,
          ipv4: faker.internet.ipv4(),
          ip_list: [{
            id: createId(),
            network_id: createId(),
            create_time: faker.date.past().toISOString(),
            ipv4: faker.internet.ipv4(),
          }],
        }
      : undefined,
    server_id: faker.datatype.boolean() ? createId() : undefined,
    port: faker.datatype.boolean()
      ? {
          id: createId(),
          port: faker.internet.port(),
          server_id: createId(),
          status: faker.helpers.arrayElement(Object.values(serverPortStatusConfig).map((item) => item.value)),
          status_name: faker.helpers.arrayElement(Object.values(serverPortStatusConfig).map((item) => item.label)),
        }
      : undefined,
    port_id: faker.datatype.boolean() ? createId() : undefined,
  }), { count })

const createMockOtherAssets = (count: number = 5): OtherAsset[] =>
  faker.helpers.multiple<OtherAsset>(() => ({
    id: createId(),
    network_equipment_id: createId(),
    template_id: createId(),
    asset_type: assetConfig[AssetType.Other].value,
    asset: {
      id: createId(),
      name: faker.company.name(),
    },
    server: faker.datatype.boolean()
      ? {
          id: createId(),
          name: faker.company.name(),
          template_id: createId(),
          asset_type: assetConfig[AssetType.Server].value,
          ip_list: [{
            id: createId(),
            network_id: createId(),
            create_time: faker.date.past().toISOString(),
            ipv4: faker.internet.ipv4(),
          }],
        }
      : undefined,
    server_id: faker.datatype.boolean() ? createId() : undefined,
    port: faker.datatype.boolean()
      ? {
          id: createId(),
          port: faker.internet.port(),
          server_id: createId(),
          status: faker.helpers.arrayElement(Object.values(serverPortStatusConfig).map((item) => item.value)),
        }
      : undefined,
    port_id: faker.datatype.boolean() ? createId() : undefined,
  }), { count })

const createMockAssetShareCollectionLinkInfo = (): AssetShareCollectionLinkInfo => ({
  key: faker.string.uuid(),
  network_equipment_id: createId(),
  expires: faker.date.future().toISOString(),
  password: faker.internet.password(),
  status: faker.number.int({ min: 0, max: 1 }),
})

const createMockServerPorts = (count: number = 5): ServerPort[] =>
  faker.helpers.multiple<ServerPort>(() => ({
    id: createId(),
    server_id: createId(),
    port: faker.internet.port(),
    status: faker.helpers.arrayElement(Object.values(serverPortStatusConfig).map((item) => item.value)),
    create_time: faker.date.past().toISOString(),
    update_time: faker.date.recent().toISOString(),
  }), { count })

const mockServerPorts = createMockServerPorts()

const mockUser = createMockUser()
const mockLogin = { token: faker.string.alphanumeric(32), user: mockUser }
const mockUserGroups = createMockUserGroups()
const mockUsers = [createMockUser()]
const mockAssetTemplates = createMockAssetTemplates(10)
const mockAssetFields = createMockAssetFields()
const mockRoles = createMockRoles()
const mockTickets = createMockTickets()
const mockTicketComments = createMockTicketComments()
const mockCVEs = createMockCVEs(20)
const mockNetworks = createMockNetworks()
const mockNetworkDevices = createMockNetworkDevices()
const mockSubnets = createMockSubnets()
const mockServers = createMockServers()
const mockSubnetIps = createMockSubnetIps()
const mockIpLogs = createMockIpLogs()
const mockDatabases = createMockDatabases()
const mockBusinessSystems = createMockBusinessSystems()
const mockOtherAssets = createMockOtherAssets()
const mockAssetShareCollectionLinkInfo = createMockAssetShareCollectionLinkInfo()

// 创建合规检查数据
const createMockComplianceChecks = (count: number = 5): ComplianceCheck[] =>
  faker.helpers.multiple<ComplianceCheck>(() => ({
    id: createId(),
    name: faker.lorem.words(3),
    desc: faker.lorem.paragraph(),
    create_time: faker.date.past().toISOString(),
    update_time: faker.date.recent().toISOString(),
    template_baseline: {
      name: faker.lorem.words(2),
      system_name: faker.lorem.words(2),
    },
    template_baseline_id: createId(),
    work_order: { total: 0 },
  }), { count })

// 创建漏洞数据
const createMockVuls = (count: number = 5): Vul[] =>
  faker.helpers.multiple<Vul>(() => ({
    id: createId(),
    name: faker.lorem.words(3),
    desc: faker.lorem.paragraph(),
    level: faker.helpers.arrayElement(Object.values(threatLevelConfig).map((item) => item.value)),
    create_time: faker.date.past().toISOString(),
    update_time: faker.date.recent().toISOString(),
    server_id: createId(),
    code: faker.string.alphanumeric(8),
    type: faker.helpers.arrayElement(['系统漏洞', '应用漏洞', '配置漏洞']),
    server: {
      id: createId(),
      name: faker.company.name(),
      template_id: createId(),
      asset_type: assetConfig[AssetType.Server].value,
      ip_list: [
        {
          id: createId(),
          network_id: createId(),
          create_time: faker.date.past().toISOString(),
          ipv4: faker.internet.ipv4(),
          ipv6: faker.datatype.boolean() ? faker.internet.ipv6() : undefined,
          mac: faker.internet.mac(),
        },
      ],
    },
    work_order: { total: 0 },
  }), { count })

const createMockTaskLogs = (count: number = 5): TaskExecutionLog[] =>
  faker.helpers.multiple<TaskExecutionLog>(() => ({
    id: createId(),
    task: {
      id: createId(),
      name: faker.lorem.words(2),
    },
    task_id: createId(),
    task_type: faker.helpers.arrayElement(Object.values(taskScanConfig).map((item) => item.value)),
    status: faker.helpers.arrayElement(Object.values(taskStatusConfig).map((item) => item.value)),
    run_type_name: faker.lorem.words(2),
    create_time: faker.date.past().toISOString(),
    result: faker.lorem.paragraph(),
  }), { count })

// 创建漏洞类型数据
const createMockVulTypes = (count: number = 5): VulTypeListItem[] =>
  faker.helpers.multiple<VulTypeListItem>(() => ({
    id: createId(),
    name: faker.lorem.words(2),
    cnnvd_id: faker.string.alphanumeric(10),
    pid: createId().toString(),
  }), { count })

// 创建厂商数据
const createMockVendors = (count: number = 5): VendorListItem[] =>
  faker.helpers.multiple<VendorListItem>(() => ({
    id: createId(),
    name: faker.company.name(),
    name_en: faker.company.name(),
    product_total: faker.number.int({ min: 1, max: 100 }),
  }), { count })

const mockComplianceChecks = createMockComplianceChecks()
const mockVuls = createMockVuls()
const mockVulTypes = createMockVulTypes()
const mockVendors = createMockVendors()

// 资产统计
const createMockAssetCountStats = (): AssetCountStats => ({
  server: faker.number.int({ min: 0, max: 100 }),
  database: faker.number.int({ min: 0, max: 50 }),
  web: faker.number.int({ min: 0, max: 30 }),
  other: faker.number.int({ min: 0, max: 20 }),
  network: faker.number.int({ min: 0, max: 20 }),
  network_equipment: faker.number.int({ min: 0, max: 30 }),
  ip_subnet: faker.number.int({ min: 0, max: 40 }),
  ip: faker.number.int({ min: 0, max: 200 }),
})

// 资产重要性统计
const createMockAssetImportanceStats = (count: number = 3): AssetImportanceStats[] =>
  faker.helpers.multiple<AssetImportanceStats>(() => ({
    grade: faker.helpers.arrayElement(Object.values(assetCriticalityConfig).map((item) => item.value)),
    count: faker.number.int({ min: 0, max: 50 }),
    name: faker.helpers.arrayElement(['服务器', '数据库', '业务系统', '其他']),
  }), { count })

// IP 变更统计
const createMockAssetIpLogStats = (count: number = 7): AssetIpLogStats[] =>
  faker.helpers.multiple(() => ({
    date: faker.date.recent().toISOString().split('T')[0],
    count: faker.number.int({ min: 0, max: 20 }),
  }), { count })

// IP 在线统计
const createMockIpOnlineStats = (count: number = 3): IpOnlineStats[] =>
  faker.helpers.multiple(() => ({
    name: faker.company.name(),
    data: faker.helpers.multiple(() => ({
      name: faker.helpers.arrayElement(['在线', '离线', '未知']),
      count: faker.number.int({ min: 0, max: 50 }),
    }), { count: 5 }),
  }), { count })

// IP 位置统计
const createMockIpLocationStats = (count: number = 5): IpLocationStats[] =>
  faker.helpers.multiple(() => ({
    name: faker.company.name(),
    data: {
      name: faker.helpers.arrayElement(['在线', '离线', '未知']),
      count: faker.number.int({ min: 0, max: 50 }),
    },
  }), { count })

// IP 资产变化统计
const createMockIpAssetChangeStats = (count: number = 7): IpAssetChangeStats[] =>
  faker.helpers.multiple(() => ({
    name: faker.company.name(),
    data: faker.helpers.multiple(() => ({
      name: faker.company.name(),
      count: faker.number.int({ min: 0, max: 50 }),
    }), { count: 5 }),
  }), { count })

// 漏洞等级统计
const createMockVulLevelStats = (): { total: number, list: VulLevelStats[] } => ({
  total: faker.number.int({ min: 50, max: 200 }),
  list: faker.helpers.multiple(() => ({
    level: faker.helpers.arrayElement(Object.values(threatLevelConfig).map((item) => item.value)),
    count: faker.number.int({ min: 0, max: 50 }),
  }), { count: 4 }),
})

// 漏洞类型统计
const createMockVulTypeStats = (count: number = 5): VulTypeStats[] =>
  faker.helpers.multiple(() => ({
    cnnvd_id: faker.string.alphanumeric(10),
    name: faker.helpers.arrayElement(['系统漏洞', '应用漏洞', '配置漏洞', '其他']),
    count: faker.number.int({ min: 0, max: 30 }),
  }), { count })

// 厂商公布的漏洞统计
const createMockVendorVulStats = (count: number = 5): VendorVulStats[] =>
  faker.helpers.multiple(() => ({
    id: createId(),
    name: faker.company.name(),
    count: faker.number.int({ min: 0, max: 30 }),
  }), { count })

// 漏洞状态统计
const createMockVulCountStats = (): VulCountStats => ({
  vul: faker.number.int({ min: 0, max: 100 }),
  server: faker.number.int({ min: 0, max: 50 }),
  recovered: faker.number.int({ min: 0, max: 30 }),
  processing: faker.number.int({ min: 0, max: 30 }),
  wontfix: faker.number.int({ min: 0, max: 20 }),
})

// 合规检查统计
const createMockComplianceCheckCountStats = (): ComplianceCheckCountStats => ({
  baseline: faker.number.int({ min: 0, max: 100 }),
  server: faker.number.int({ min: 0, max: 50 }),
  pass_count: faker.number.int({ min: 0, max: 30 }),
  not_pass_count: faker.number.int({ min: 0, max: 20 }),
  pass_rate: faker.number.int({ min: 0, max: 100 }),
})

// 工单统计
const createMockTicketCountStats = (): TicketCountStats => ({
  work_order: faker.number.int({ min: 0, max: 100 }),
  baseline: faker.number.int({ min: 0, max: 50 }),
  vul: faker.number.int({ min: 0, max: 30 }),
})

// 工单等级统计
const createMockTicketGradeStats = (count: number = 3): TicketGradeLevelStats[] =>
  faker.helpers.multiple<TicketGradeLevelStats>(() => ({
    grade: faker.helpers.arrayElement(Object.values(ticketGradeConfig).map((item) => item.value)),
    count: faker.number.int({ min: 0, max: 40 }),
    name: faker.helpers.arrayElement(Object.values(ticketGradeConfig).map((item) => item.label)),
  }), { count })

// 工单状态统计
const createMockTicketStatusStats = (count: number = 3): TicketStatusStats[] =>
  faker.helpers.multiple<TicketStatusStats>(() => ({
    status: faker.helpers.arrayElement(Object.values(ticketStatusConfig).map((item) => item.value)),
    count: faker.number.int({ min: 0, max: 50 }),
    name: faker.helpers.arrayElement(Object.values(ticketStatusConfig).map((item) => item.label)),
  }), { count })

// 工单日志统计
const createMockTicketLogs = (count: number = 5): TicketLog[] =>
  faker.helpers.multiple(() => ({
    date: faker.date.recent().toISOString().split('T')[0],
    count: faker.number.int({ min: 0, max: 20 }),
  }), { count })

// 创建基线模板数据
const createMockBaselineTpls = (count: number = 5): BaselineTplListItem[] =>
  faker.helpers.multiple<BaselineTplListItem>(() => ({
    id: createId(),
    name: `${faker.lorem.words(2)}基线模板`,
    desc: faker.lorem.paragraph(),
    system_name: faker.helpers.arrayElement(['Windows Server', 'Linux', 'MySQL', 'Oracle']),
    system_id: createId(),
    create_time: faker.date.past().toISOString(),
    update_time: faker.date.recent().toISOString(),
  }), { count })

// 创建基线系统数据
const createMockBaselineSystems = (count: number = 5): BaselineSystem[] =>
  faker.helpers.multiple<BaselineSystem>(() => ({
    id: createId(),
    name: faker.helpers.arrayElement(['Windows Server', 'Linux', 'MySQL', 'Oracle']),
    desc: faker.lorem.paragraph(),
    create_time: faker.date.past().toISOString(),
    update_time: faker.date.recent().toISOString(),
  }), { count })

// 创建基线策略选项数据
const createMockBaselinePolicyOptions = (count: number = 5): BaselinePolicyOptionListItem[] =>
  faker.helpers.multiple<BaselinePolicyOptionListItem>(() => ({
    id: createId(),
    name: faker.lorem.words(3),
    desc: faker.lorem.paragraph(),
    system_id: createId(),
    create_time: faker.date.past().toISOString(),
    update_time: faker.date.recent().toISOString(),
    is_update: faker.datatype.boolean(),
    level: faker.helpers.arrayElement(Object.values(threatLevelConfig).map((item) => item.value)),
  }), { count })

// 创建基线选项数据
const createMockBaselineOptions = (count: number = 5): BaselineOption[] =>
  faker.helpers.multiple<BaselineOption>(() => ({
    id: createId(),
    name: faker.lorem.words(3),
    desc: faker.lorem.paragraph(),
    template_baseline_id: createId(),
    system_option: {
      id: createId(),
      system_id: createId(),
      name: faker.lorem.words(3),
    },
    system_option_id: createId(),
    create_time: faker.date.past().toISOString(),
    update_time: faker.date.recent().toISOString(),
    kwargs: {},
  }), { count })

// 创建许可证数据
const createMockLicense = (): License => ({
  name: faker.company.name(),
  status: faker.datatype.boolean(),
  create_time: faker.date.past().toISOString(),
  public_key: faker.string.alphanumeric(10),
  version: faker.string.alphanumeric(10),
  machine_id: faker.string.alphanumeric(10),
})

const mockBaselineTpls = createMockBaselineTpls()
const mockBaselineSystems = createMockBaselineSystems()
const mockBaselinePolicyOptions = createMockBaselinePolicyOptions()
const mockBaselineOptions = createMockBaselineOptions()

const mockLicense = createMockLicense()

// MARK: 十阵
export const handlers = [
  mockPost('/auth/login', mockLogin),

  mockGet('/permission/role/', mockRoles, { withPagination: true, pageSize: 20 }),
  mockGet('/permission/role/:id', mockRoles[0]),
  mockPost('/permission/role/', mockRoles[0]),
  mockPut('/permission/role/:id', mockRoles[0]),
  mockDelete('/permission/role/:id'),

  mockGet('/permission/organize/', mockUserGroups),
  mockGet('/permission/organize/:id', mockUserGroups[0]),
  mockPost('/permission/organize/', mockUserGroups[0]),
  mockPut('/permission/organize/:id', mockUserGroups[0]),
  mockDelete('/permission/organize/:id'),

  mockGet('/permission/user/', mockUsers, { withPagination: true, pageSize: 20 }),
  mockGet('/permission/user/:id', mockUsers[0]),
  mockPost('/permission/user/', mockUsers[0]),
  mockPut('/permission/user/:id', mockUsers[0]),
  mockDelete('/permission/user/:id'),

  mockGet('/config/asset_template/field/', mockAssetFields),
  mockGet('/config/asset_template/field/:id', mockAssetFields[0]),
  mockPost('/config/asset_template/field/', mockAssetFields[0]),
  mockPut('/config/asset_template/field/:id', mockAssetFields[0]),
  mockDelete('/config/asset_template/field/:id'),

  mockGet('/config/asset_template/', mockAssetTemplates, { withPagination: true, pageSize: 20 }),
  mockGet('/config/asset_template/:id', mockAssetTemplates[0]),
  mockPost('/config/asset_template/', mockAssetTemplates[0]),
  mockPut('/config/asset_template/:id', mockAssetTemplates[0]),
  mockDelete('/config/asset_template/:id'),

  mockGet('/work_order/current/', mockTickets, { withPagination: true, pageSize: 20 }),
  mockGet('/work_order/', mockTickets, { withPagination: true, pageSize: 20 }),
  mockGet('/work_order/:id', mockTickets[0]),
  mockPost('/work_order/', mockTickets[0]),
  mockPut('/work_order/:id', mockTickets[0]),
  mockDelete('/work_order/:id'),

  mockGet('/work_order/comment/', mockTicketComments, { withPagination: true, pageSize: 20 }),
  mockPost('/work_order/comment/', mockTicketComments[0]),
  mockPut('/work_order/comment/:id', mockTicketComments[0]),
  mockDelete('/work_order/comment/:id'),

  mockGet('/security/vul_lib/', mockCVEs, { withPagination: true, pageSize: 20 }),

  mockGet('/asset/network_equipment/subnet/', mockSubnets, { withPagination: true }),
  mockPost('/asset/network_equipment/subnet/', mockSubnets[0]),
  mockPut('/asset/network_equipment/subnet/:id', mockSubnets[0]),
  mockDelete('/asset/network_equipment/subnet/:id'),

  mockGet('/asset/network_equipment/ip/', mockSubnetIps, { withPagination: true }),
  mockPost('/asset/network_equipment/ip/', mockSubnetIps[0]),
  mockPut('/asset/network_equipment/ip/:id', mockSubnetIps[0]),
  mockDelete('/asset/network_equipment/ip/:id'),

  mockGet('/asset/network_equipment/ip/log/', mockIpLogs, { withPagination: true }),

  mockPost('/asset/network_equipment/status/:id', {
    status: faker.datatype.boolean(),
    message: faker.helpers.arrayElement([
      '节点连接正常',
      '节点连接失败',
      '节点未响应',
    ]),
  }),

  // 网络设备相关
  mockGet('/asset/network_equipment/', mockNetworkDevices, { withPagination: true }),
  mockGet('/asset/network_equipment/:id', mockNetworkDevices[0]),
  mockPost('/asset/network_equipment/', mockNetworkDevices[0]),
  mockPut('/asset/network_equipment/:id', mockNetworkDevices[0]),
  mockDelete('/asset/network_equipment/:id'),

  // 服务器端口相关
  mockGet('/asset/server/port/', mockServerPorts, { withPagination: true }),
  mockPost('/asset/server/port/', mockServerPorts[0]),
  mockPut('/asset/server/port/:id', mockServerPorts[0]),
  mockDelete('/asset/server/port/:id'),

  // 服务器相关
  mockGet('/asset/server/', mockServers, { withPagination: true }),
  mockGet('/asset/server/:id', mockServers[0]),
  mockPost('/asset/server/', mockServers[0]),
  mockPut('/asset/server/:id', mockServers[0]),
  mockDelete('/asset/server/:id'),

  // 网络设备相关
  mockGet('/asset/network/', mockNetworks),
  mockPost('/asset/network/', mockNetworks[0]),
  mockPut('/asset/network/:id', mockNetworks[0]),
  mockDelete('/asset/network/:id'),

  // 数据库相关
  mockGet('/asset/database/', mockDatabases, { withPagination: true }),
  mockPost('/asset/database/', mockDatabases[0]),
  mockPut('/asset/database/:id', mockDatabases[0]),
  mockDelete('/asset/database/:id'),

  // 业务系统相关
  mockGet('/asset/web/', mockBusinessSystems, { withPagination: true }),
  mockPost('/asset/web/', mockBusinessSystems[0]),
  mockPut('/asset/web/:id', mockBusinessSystems[0]),
  mockDelete('/asset/web/:id'),

  // 其他资产相关
  mockGet('/asset/other/', mockOtherAssets, { withPagination: true }),
  mockPost('/asset/other/', mockOtherAssets[0]),
  mockPut('/asset/other/:id', mockOtherAssets[0]),
  mockDelete('/asset/other/:id'),

  // 资产分享链接相关
  mockGet('/asset/link/:id', mockAssetShareCollectionLinkInfo),
  mockPut('/asset/link/:id', mockAssetShareCollectionLinkInfo),

  // 资产信息填报相关
  mockGet('/asset/fill/asset_template/', mockAssetTemplates, { withPagination: true }),
  mockGet('/asset/fill/asset_template/field/', mockAssetFields),

  mockGet('/asset/fill/server/', mockServers, { withPagination: true }),
  mockPut('/asset/fill/server/:id', mockServers[0]),

  mockGet('/asset/fill/database/', mockDatabases, { withPagination: true }),
  mockPut('/asset/fill/database/:id', mockDatabases[0]),

  mockGet('/asset/fill/web/', mockBusinessSystems, { withPagination: true }),
  mockPut('/asset/fill/web/:id', mockBusinessSystems[0]),

  mockGet('/asset/fill/other/', mockOtherAssets, { withPagination: true }),
  mockPut('/asset/fill/other/:id', mockOtherAssets[0]),

  // 合规检查相关
  mockGet('/security/baseline/', mockComplianceChecks, { withPagination: true }),
  mockGet('/security/baseline/:id', mockComplianceChecks[0]),

  // 漏洞相关
  mockGet('/security/vul/', mockVuls, { withPagination: true }),
  mockGet('/security/vul/:id', mockVuls[0]),

  // 厂商相关
  mockGet('/security/vul_lib/vendor/', mockVendors, { withPagination: true }),

  // 漏洞类型相关
  mockGet('/security/vul_lib/type/', mockVulTypes),

  // 漏洞库相关
  mockGet('/security/vul_lib/', mockCVEs, { withPagination: true }),
  mockGet('/security/vul_lib/:id', mockCVEs[0]),

  mockGet('/task/log/', createMockTaskLogs(), { withPagination: true }),

  // 统计相关的 handlers
  mockGet('/overview/asset/count/', createMockAssetCountStats()),
  mockGet('/overview/asset/grade/', createMockAssetImportanceStats()),
  mockGet('/overview/asset/ip_log/', createMockAssetIpLogStats()),
  mockGet('/overview/asset/ip_status/', createMockIpOnlineStats()),
  mockGet('/overview/asset/ip/', createMockIpLocationStats()),
  mockGet('/overview/asset/asset/', createMockIpAssetChangeStats()),
  mockGet('/overview/vul_lib/level/', createMockVulLevelStats()),
  mockGet('/overview/vul_lib/vul_type/', createMockVulTypeStats()),
  mockGet('/overview/vul_lib/vendor/', createMockVendorVulStats()),
  mockGet('/overview/vul/count/', createMockVulCountStats()),
  mockGet('/overview/vul/level/', createMockVulLevelStats()),
  mockGet('/overview/baseline/count/', createMockComplianceCheckCountStats()),
  mockGet('/overview/work_order/count/', createMockTicketCountStats()),
  mockGet('/overview/work_order/grade/', createMockTicketGradeStats()),
  mockGet('/overview/work_order/status/', createMockTicketStatusStats()),
  mockGet('/overview/work_order/log/', createMockTicketLogs()),

  // 基线策略选项相关
  mockGet('/config/baseline_template/system/option/', mockBaselinePolicyOptions, { withPagination: true }),
  mockGet('/config/baseline_template/system/option/:id', mockBaselinePolicyOptions[0]),
  mockPost('/config/baseline_template/system/option/', mockBaselinePolicyOptions[0]),
  mockPut('/config/baseline_template/system/option/:id', mockBaselinePolicyOptions[0]),
  mockDelete('/config/baseline_template/system/option/:id'),

  // 基线系统相关
  mockGet('/config/baseline_template/system/', mockBaselineSystems, { withPagination: true }),
  mockGet('/config/baseline_template/system/:id', mockBaselineSystems[0]),
  mockPost('/config/baseline_template/system/', mockBaselineSystems[0]),
  mockPut('/config/baseline_template/system/:id', mockBaselineSystems[0]),
  mockDelete('/config/baseline_template/system/:id'),

  // 基线选项相关
  mockGet('/config/baseline_template/option/', mockBaselineOptions, { withPagination: true }),
  mockPost('/config/baseline_template/option/', mockBaselineOptions[0]),
  mockDelete('/config/baseline_template/option/:id'),

  // 基线模板相关
  mockGet('/config/baseline_template/', mockBaselineTpls, { withPagination: true }),
  mockGet('/config/baseline_template/:id', mockBaselineTpls[0]),
  mockPost('/config/baseline_template/', mockBaselineTpls[0]),
  mockPut('/config/baseline_template/:id', mockBaselineTpls[0]),
  mockDelete('/config/baseline_template/:id'),

  // 许可证相关
  mockGet('/auth/license', mockLicense),
  mockPost('/auth/license'),
  mockGet('/auth/public_key', { public_key: 'xx' }),
]

// TW相关工具函数
function createMockTWDepartments(count: number = 5): TW_DepartmentListItem[] {
  return faker.helpers.multiple(() => ({
    id: createId(),
    name: faker.company.name(),
    parent: faker.helpers.maybe(() => createId()),
  }), { count })
}

function createMockTWRole(): TW_Role {
  return {
    id: createId(),
    name: faker.person.jobTitle(),
    description: faker.lorem.sentence(),
    menu_ids: faker.helpers.multiple(() => createId(), { count: faker.number.int({ min: 1, max: 10 }) }),
    created_at: faker.date.past().toISOString(),
  }
}

function createMockTWRoles(count: number = 5): RoleListItem[] {
  return faker.helpers.multiple(() => createMockTWRole(), { count })
}

function createMockTWUser(): TW_User {
  return {
    id: createId(),
    user_id: faker.string.uuid(),
    name: faker.person.fullName(),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    appointment: faker.person.jobTitle(),
    avatar: faker.image.avatar(),
    roles: faker.helpers.maybe(() => createMockTWRoles(faker.number.int({ min: 1, max: 3 }))),
    department: faker.helpers.maybe(() => createMockTWDepartments(1)[0]),
    permissions: [AUTH.SKIP_FUYAO],
    created_at: faker.date.past().toISOString(),
    updated_at: faker.date.recent().toISOString(),
  }
}

function createMockTWUsers(count: number = 5): TW_UserListItem[] {
  return faker.helpers.multiple(() => createMockTWUser(), { count })
}

function createMockTWPermission(): PermissionItem {
  return {
    id: createId(),
    parent_id: faker.helpers.maybe(() => createId()),
    name: faker.lorem.word(),
    code: faker.lorem.slug(),
    description: faker.lorem.sentence(),
  }
}

function createMockTWPermissions(count: number = 10): PermissionItem[] {
  return faker.helpers.multiple(() => createMockTWPermission(), { count })
}

function createMockTWWechatUser(): WechatUser {
  return {
    id: createId(),
    openid: faker.string.uuid(),
    nickname: faker.internet.username(),
    avatar: faker.image.avatar(),
    created_at: faker.date.past().toISOString(),
    updated_at: faker.date.recent().toISOString(),
    is_follow: faker.datatype.boolean(),
    username: faker.person.fullName(),
    phone: faker.phone.number(),
    email: faker.internet.email(),
  }
}

function createMockTWWechatUsers(count: number = 5): WechatUserListItem[] {
  return faker.helpers.multiple(() => createMockTWWechatUser(), { count })
}

function createMockTWClient(): TW_Client {
  return {
    id: createId(),
    name: faker.company.name(),
    number: faker.string.numeric(8),
    address: faker.location.streetAddress(),
    work_phone: faker.phone.number(),
    country: '中国',
    country_province: faker.location.state(),
    country_province_city: faker.location.city(),
    invoice_unit_address: faker.location.streetAddress(),
    invoice_unit_bank_name: faker.company.name() + '银行',
    invoice_unit_bank_number: faker.finance.accountNumber(),
    invoice_unit_name: faker.company.name(),
    invoice_unit_number: faker.finance.accountNumber(18),
    invoice_unit_phone: faker.phone.number(),
    type: faker.helpers.arrayElement(['潜在客户', '现有客户', '重点客户']),
    type_id: faker.number.int({ min: 1, max: 3 }),
    user: {
      id: createId(),
      avatar: faker.image.avatar(),
      name: faker.person.fullName(),
      phone: faker.phone.number(),
    },
    wechat_users: faker.helpers.maybe(() => createMockTWWechatUsers(faker.number.int({ min: 1, max: 3 }))),
    created_at: faker.date.past().toISOString(),
    updated_at: faker.date.recent().toISOString(),
  }
}

function createMockTWClients(count: number = 5): ClientListItem[] {
  return faker.helpers.multiple(() => createMockTWClient(), { count })
}

function createMockTWAutoFlow(): TW_AutoFlow {
  return {
    id: faker.string.uuid(),
    name: faker.lorem.words(3),
    description: faker.lorem.sentence(),
    active: faker.datatype.boolean(),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
  }
}

function createMockTWProject(): TW_Project {
  return {
    id: createId(),
    number: faker.string.numeric(8),
    name: faker.company.catchPhrase(),
    flow_id: faker.string.uuid(),
    leader_object: {
      user: createMockTWUser(),
    },
    sale_object: {
      user: createMockTWUser(),
    },
    type: faker.helpers.multiple(() => faker.number.int({ min: 1, max: 5 }), { count: faker.number.int({ min: 1, max: 3 }) }),
    status: faker.helpers.arrayElement([0, 1, 2, 4, 5]) as ProjectStatus,
    start_date: [
      faker.date.past().toISOString().split('T')[0],
      faker.date.future().toISOString().split('T')[0],
    ],
    customer_id: createId(),
    statistics: {
      team: faker.number.int({ min: 2, max: 10 }),
      task: {
        close: faker.number.int({ min: 0, max: 50 }),
        count: faker.number.int({ min: 50, max: 100 }),
      },
    },
    contract: {
      id: createId(),
      customer_id: createId(),
      name: faker.commerce.productName() + '合同',
      number: faker.string.numeric(10),
    },
    flow_detail: createMockTWAutoFlow(),
  }
}

function createMockTWProjectPublicDetail(): ProjectPublicDetail {
  const project = createMockTWProject()
  const { leader_object, sale_object, ...publicDetail } = project

  return publicDetail
}

function createMockTWProjects(count: number = 10): ProjectListItem[] {
  return faker.helpers.multiple(() => createMockTWProject(), { count })
}

function createMockTWAutoFlows(count: number = 5): TW_AutoFlowListItem[] {
  return faker.helpers.multiple(() => createMockTWAutoFlow(), { count })
}

function createMockTWProjectTeam(count: number = 5): TW_ProjectTeam[] {
  return faker.helpers.multiple(() => ({
    user: createMockTWUser(),
  }), { count })
}

function createMockTWProjectFeedback(): TW_ProjectFeedback {
  return {
    hot_tags: {
      专业: faker.number.int({ min: 1, max: 10 }),
      高效: faker.number.int({ min: 1, max: 10 }),
      响应快: faker.number.int({ min: 1, max: 10 }),
      沟通顺畅: faker.number.int({ min: 1, max: 10 }),
    },
    satisfaction: {
      overall: {
        score: faker.number.float({ min: 4, max: 5, fractionDigits: 1 }).toString(),
        total: '5',
      },
      communication: {
        score: faker.number.float({ min: 4, max: 5, fractionDigits: 1 }).toString(),
      },
      professional: {
        score: faker.number.float({ min: 4, max: 5, fractionDigits: 1 }).toString(),
      },
      response: {
        score: faker.number.float({ min: 4, max: 5, fractionDigits: 1 }).toString(),
      },
    },
    judge_list: faker.helpers.multiple(() => ({
      comment: faker.lorem.paragraph(),
      communication: faker.number.int({ min: 3, max: 5 }),
      created_at: faker.date.recent().toISOString(),
      id: createId(),
      professional: faker.number.int({ min: 3, max: 5 }),
      project_id: createId(),
      response: faker.number.int({ min: 3, max: 5 }),
      score: faker.number.int({ min: 3, max: 5 }),
      tags: faker.helpers.multiple(() => faker.lorem.word(), { count: faker.number.int({ min: 1, max: 5 }) }),
      type: faker.helpers.arrayElement(['客户', '管理层']),
      user_id: createId(),
      user_name: faker.person.fullName(),
    }), { count: faker.number.int({ min: 1, max: 5 }) }),
  }
}

function createMockTWProjectMilestones(count: number = 5): TW_ProjectMilestone[] {
  return faker.helpers.multiple(() => ({
    id: createId(),
    created_at: faker.date.past().toISOString(),
    created_users: createMockTWUser(),
    name: faker.lorem.words(3),
    projects_id: createId(),
    sort: faker.number.int({ min: 1, max: 10 }),
    statistics: {
      sum_day_consumesume: faker.number.int({ min: 10, max: 100 }),
      task: {
        count: faker.number.int({ min: 50, max: 100 }),
        jxz: faker.number.int({ min: 0, max: 20 }),
        wks: faker.number.int({ min: 0, max: 10 }),
        ygb: faker.number.int({ min: 0, max: 5 }),
        yqx: faker.number.int({ min: 0, max: 5 }),
        ywc: faker.number.int({ min: 20, max: 50 }),
        yyq: faker.number.int({ min: 0, max: 5 }),
        yzt: faker.number.int({ min: 0, max: 5 }),
      },
      task_day_consume: faker.number.int({ min: 5, max: 20 }),
      wo: {
        count: faker.number.int({ min: 10, max: 30 }),
        jxz: faker.number.int({ min: 0, max: 10 }),
        wks: faker.number.int({ min: 0, max: 5 }),
        ygb: faker.number.int({ min: 0, max: 3 }),
        yqx: faker.number.int({ min: 0, max: 3 }),
        ywc: faker.number.int({ min: 5, max: 15 }),
        yzt: faker.number.int({ min: 0, max: 3 }),
      },
      wo_day_consume: faker.number.int({ min: 2, max: 10 }),
    },
  }), { count })
}

// 创建工作流空间相关的模拟函数
function createMockWorkflowSpace() {
  return {
    id: createId().toString(),
    name: faker.lorem.words(2),
    type: faker.helpers.arrayElement(['default', 'custom']),
    icon: {
      type: 'emoji',
      value: faker.helpers.arrayElement(['🚀', '🔧', '🌈', '🛠️', '📊']),
    },
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
  }
}

function createMockWorkflowSpaces(count: number = 5) {
  return faker.helpers.multiple(() => createMockWorkflowSpace(), { count })
}

// 创建工作流应用相关的模拟函数
function createMockWorkflowResource() {
  return {
    id: createId().toString(),
    name: faker.lorem.words(3),
    value: faker.lorem.sentence(),
    key: faker.string.alphanumeric(10),
    updatedAt: faker.date.recent().toISOString(),
    createdAt: faker.date.past().toISOString(),
    homeProject: {
      id: createId().toString(),
      name: faker.lorem.words(2),
    },
    type: faker.helpers.arrayElement(['workflow', 'credential']),
    sharedWithProjects: faker.helpers.multiple(() => ({
      id: createId().toString(),
      name: faker.lorem.words(2),
    }), { count: faker.number.int({ min: 0, max: 3 }) }),
  }
}

function createMockWorkflowResources(count: number = 5) {
  return faker.helpers.multiple(() => createMockWorkflowResource(), { count })
}

function createMockWorkflowDetails() {
  return {
    id: createId().toString(),
    name: faker.lorem.words(3),
    active: faker.datatype.boolean(),
    createdAt: faker.date.past().getTime().toString(),
    updatedAt: faker.date.recent().getTime().toString(),
    nodes: faker.helpers.multiple(() => ({
      id: createId().toString(),
      name: faker.lorem.word(),
      type: faker.helpers.arrayElement(['n8n-nodes-base.httpRequest', 'n8n-nodes-base.set']),
      parameters: {},
      typeVersion: 1,
      position: [faker.number.int({ min: 100, max: 800 }), faker.number.int({ min: 100, max: 600 })],
    }), { count: faker.number.int({ min: 1, max: 5 }) }),
    connections: {
      [createId().toString()]: faker.helpers.multiple(() => ({
        main: [{ node: createId().toString(), type: 'main', index: 0 }],
      }), { count: faker.number.int({ min: 0, max: 3 }) }),
    },
    settings: {
      executionOrder: faker.helpers.arrayElement(['v1', 'v2']),
      saveExecutionProgress: faker.datatype.boolean(),
      saveManualExecutions: faker.datatype.boolean(),
      saveDataErrorExecution: faker.helpers.arrayElement(['all', 'none']),
      saveDataSuccessExecution: faker.helpers.arrayElement(['all', 'none']),
      callerPolicy: faker.helpers.arrayElement(['any', 'none', 'workflowsFromSameOwner']),
      errorWorkflow: '',
    },
    tags: faker.helpers.multiple(() => faker.lorem.word(), { count: faker.number.int({ min: 0, max: 4 }) }),
    pinData: {},
    versionId: createId().toString(),
    meta: {
      templateId: faker.datatype.boolean() ? createId().toString() : undefined,
    },
  }
}

// MARK: 扶摇
export const TW_handlers = [
  // 用户认证相关
  mockPost('/public/login', {
    token: faker.string.alphanumeric(32),
    user_info: createMockTWUser(),
  }),

  mockPost('/public/ac-login', {
    token: faker.string.alphanumeric(32),
    user_info: createMockTWUser(),
  }),

  mockGet('/public/wechat/user_id', {
    openid: faker.string.uuid(),
  }),

  // 部门相关
  mockGet('/depts', createMockTWDepartments()),

  // 用户相关
  mockGet('/depts/users', createMockTWUsers()),
  mockGet('/depts/users/:id', createMockTWUser()),
  mockPut('/depts/users/:id', createMockTWUser()),

  // 角色相关
  mockGet('/roles', createPageResponse(createMockTWRoles())),
  mockGet('/roles/:id', createMockTWRole()),
  mockPost('/roles', createMockTWRole()),
  mockPut('/roles/:id', createMockTWRole()),
  mockDelete('/roles/:id'),

  // 权限相关
  mockGet('/menus', createMockTWPermissions()),
  mockPost('/menus', createMockTWPermission()),
  mockPut('/menus/:id', createMockTWPermission()),
  mockDelete('/menus/:id'),

  // 客户相关
  mockGet('/customers', createPageResponse(createMockTWClients())),
  mockGet('/customers/:id', createMockTWClient()),
  mockPut('/customers/:id', createMockTWClient()),

  // 微信用户相关
  mockGet('/wechat/users', createPageResponse(createMockTWWechatUsers())),
  mockGet('/wechat/users/:id', createMockTWWechatUser()),

  // 项目相关
  mockGet('/projects/list', createPageResponse(createMockTWProjects())),
  mockGet('/projects/outline/:id', createMockTWProject()),
  mockGet('/public/wechat/project/:id', createMockTWProjectPublicDetail()),
  mockPut('/projects/:id', createMockTWProject()),

  // 项目关联自动化流程
  mockPut('/projects/flow/:id', createMockTWProject()),

  // 自动化流程相关
  mockGet('/workflow/list', {
    data: createMockTWAutoFlows(),
    nextCursor: faker.string.alphanumeric(10),
  }),
  mockDelete('/workflow/list/:id'),

  // 项目团队相关
  mockGet('/projects/team/:id', createMockTWProjectTeam()),

  // 项目描述相关
  mockGet('/projects/desc/:id', { contract_id: faker.string.numeric(6) }),

  // 项目反馈相关
  mockGet('/projects/feedback/:id', createMockTWProjectFeedback()),

  // 项目里程碑相关
  mockGet('/projects/milestone/:id', createMockTWProjectMilestones()),

  // 客户提交项目评价
  mockPost('/public/wechat/project/evaluate', {}),

  // 工作流空间相关 - SpaceService
  mockGet('/workflows/project', createPageResponse(createMockWorkflowSpaces())),
  mockGet('/workflows/project/:id', createMockWorkflowSpace()),
  mockPost('/workflows/project', createMockWorkflowSpace()),
  mockPut('/workflows/project/:id', createMockWorkflowSpace()),
  mockDelete('/workflows/project/:id'),

  // 工作流空间成员相关
  mockGet('/workflows/project/user/:id', createPageResponse(createMockTWUsers())),
  mockPost('/workflows/project/user', {}),
  mockDelete('/workflows/project/user/:id'),

  // 工作流应用相关 - AppService
  mockGet('/workflows', createPageResponse(createMockWorkflowResources())),
  mockGet('/workflows/:id', createMockWorkflowDetails()),
  mockPost('/workflows', createMockWorkflowResource()),
  mockPut('/workflows/:id', createMockWorkflowResource()),
  mockDelete('/workflows/:id'),

  // 工作流复制和移动
  mockPost('/workflows/copy', createMockWorkflowResource()),
  mockPut('/workflows/move/:id', createMockWorkflowResource()),
]
