/**
 * 应用全局配置文件
 *
 * 本文件用于定义整个应用的全局配置信息，在运行时可通过 useAppConfig() 组合式函数访问。
 * defineAppConfig 是 Nuxt 提供的函数，用于定义应用级别的配置。
 *
 * @example
 * const appConfig = useAppConfig() // 在组件中使用配置
 * appConfig.auth.xxx // 然后读取配置属性
 */
import { RouteKey } from '~/enums/route'
import { getRoutePath } from '~/utils/route'

export default defineAppConfig({
  auth: {
    /**
     * 不需要鉴权的路由列表
     *
     * 在 auth 中间件中，会根据该列表中的路由进行鉴权。
     * 如果用户已登录，并且访问了该列表中的路由，会被重定向到首页
     *
     * @see middleware/auth.ts - 路由鉴权中间件实现
     */
    publicPages: [
      getRoutePath(RouteKey.首页),
      getRoutePath(RouteKey.登录),
      getRoutePath(RouteKey.注册),
    ] as string[],

    /**
     * 不需要鉴权的 API 列表
     *
     * 在请求中，如果请求路径在 publicApis 列表中，则不会传递 token
     *
     * @see plugins/api.ts - API 请求拦截器实现
     */
    publicApis: ['/auth/image_code', '/auth/login'],
  },
})
