/** 用户状态 */
export const enum UserStatus {
  /** 在职 */
  Employed = 1,
  /** 离职 */
  Resigned = 2,
}

export const userStatusConfig = {
  [UserStatus.Employed]: {
    value: UserStatus.Employed,
    label: '在职',
    severity: 'success',
  },
  [UserStatus.Resigned]: {
    value: UserStatus.Resigned,
    label: '离职',
    severity: 'danger',
  },
} as const satisfies EnumConfig<UserStatus, { label: string, severity: TagSeverity }>

const enum RouteKey {
  首页 = 'index',
  登录 = 'login',
  注册 = 'signup',
  仪表盘 = 'dashboard',
  资产概览 = 'dashboard-asset',
  安全概览 = 'dashboard-security',
  工单概览 = 'dashboard-ticket',
  智能中心 = 'dashboard-ai',
  智能分析 = 'dashboard-ai-analysis',

  资产管理 = 'asset',
  资产详情 = 'asset-network',
  资产拓扑图 = 'asset-topology',
  资产填报 = 'asset-collect',
  资产搜索 = 'asset-search',

  安全中心 = 'security',
  漏洞管理 = 'security-vul',
  基线管理 = 'security-compliance',
  安全通告 = 'security-cve',
  漏洞详情 = 'security-vul-detail',

  工单管理 = 'ticket',
  工单列表 = 'ticket-list',
  待办工单 = 'ticket-assigned',

  配置中心 = 'configuration',
  资产模板 = 'configuration-model-asset',
  基线模板 = 'configuration-model-compliance',

  权限管理 = 'permission',
  成员与部门 = 'permission-org',
  角色管理 = 'permission-role',

  TW_组织架构 = 'org',
  TW_成员与部门 = 'org-user',
  TW_角色管理 = 'org-role',
  TW_权限资源 = 'org-permission',

  系统设置 = 'settings',
  个人中心 = 'settings-profile',
  账号安全 = 'settings-account',
  偏好设置 = 'settings-preference',
  许可证 = 'settings-license',

  错误页 = 'error',
  无路由权限 = 'no-permission',

  项目管理 = 'project',
}

/** 权限资源点映射 */
export const AUTH = {
  SKIP_SHIZHEN: 'skip.shizhen',

  DASHBOARD_VIEW: RouteKey.仪表盘,
  DASHBOARD_ASSET_VIEW: RouteKey.资产概览,
  DASHBOARD_SECURITY_VIEW: RouteKey.安全概览,
  DASHBOARD_TICKET_VIEW: RouteKey.工单概览,
  DASHBOARD_AI_VIEW: RouteKey.智能中心,
  DASHBOARD_AI_ANALYSIS_VIEW: RouteKey.智能分析,

  ASSET_MANAGE: RouteKey.资产管理,
  ASSET_DETAIL_VIEW: RouteKey.资产详情,
  ASSET_TOPOLOGY_VIEW: RouteKey.资产拓扑图,
  ASSET_COLLECT_VIEW: RouteKey.资产填报,
  ASSET_SEARCH_VIEW: RouteKey.资产搜索,

  SECURITY_MANAGE: RouteKey.安全中心,
  VUL_MANAGE: RouteKey.漏洞管理,
  COMPLIANCE_MANAGE: RouteKey.基线管理,
  CVE_MANAGE: RouteKey.安全通告,
  VUL_DETAIL_VIEW: RouteKey.漏洞详情,

  CONFIGURATION_MANAGE: RouteKey.系统设置,
  ASSET_TEMPLATE_MANAGE: RouteKey.资产模板,
  COMPLIANCE_TEMPLATE_MANAGE: RouteKey.基线模板,

  TICKET_MANAGE: RouteKey.工单管理,
  TICKET_LIST_MANAGE: RouteKey.工单列表,
  TICKET_ASSIGNED_MANAGE: RouteKey.待办工单,

  PERMISSION_MANAGE: RouteKey.权限管理,
  USER_MANAGE: RouteKey.成员与部门,
  ROLE_MANAGE: RouteKey.角色管理,

  SYSTEM_SETTINGS_MANAGE: 'sys.manage',
  SYSTEM_SETTINGS_PROFILE_MANAGE: 'sys.profile.manage',
  SYSTEM_SETTINGS_ACCOUNT_MANAGE: 'sys.account.manage',
  SYSTEM_SETTINGS_PREFERENCE_MANAGE: 'sys.preference.manage',

  // ================================ 以下是扶摇平台的权限资源点 ================================

  SKIP_FUYAO: 'skip.fuyao',
  WORKSPACE_VIEW: 'workspace.read',

  PROJECT_LIST_READ: 'project.list.read',
  PROJECT_LIST_WRITE: 'project.list.write',
  PROJECT_DETAIL_READ: 'project.detail.read',
  PROJECT_DETAIL_WRITE: 'project.detail.write',

  WORKFLOW_LIST_READ: 'workflow.list.read',
  WORKFLOW_LIST_WRITE: 'workflow.list.write',
  WORKFLOW_CREATE: 'workflow.create',
  WORKFLOW_READ: 'workflow.read',
  WORKFLOW_WRITE: 'workflow.write',
  WORKFLOW_DELETE: 'workflow.delete',

  KNOWLEDGE_BASE_MANAGE: 'knowledge_base.manage',
  KNOWLEDGE_BASE_READ: 'knowledge_base.read',
  KNOWLEDGE_BASE_WRITE: 'knowledge_base.write',
  KNOWLEDGE_BASE_DELETE: 'knowledge_base.delete',

  AI_MANAGE: 'ai.manage',
  CLIENT_LIST_READ: 'client.list.read',
  CLIENT_DETAIL_READ: 'client.detail.read',
  CLIENT_WECHAT_USER_LIST_READ: 'client.wechat_user_list.read',
  CLIENT_WECHAT_USER_DETAIL_READ: 'client.wechat_user.detail.read',
  ORG_USER_MANAGE: 'org.user.manage',
  ORG_ROLE_MANAGE: 'org.role.manage',
  ORG_PERMISSION_MANAGE: 'org.permission.manage',
  SETTINGS_MANAGE: 'settings.manage',
}
