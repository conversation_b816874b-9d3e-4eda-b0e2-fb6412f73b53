import { $dt } from '@primevue/themes'

import type { EnumConfig } from '~/types/common'

/** 漏洞的危险等级 */
export const enum VulThreatLevel {
  /** 严重 */
  Super = 4,
  /** 高危 */
  High = 3,
  /** 中危 */
  Medium = 2,
  /** 低危 */
  Low = 1,
}

/** 合规检查结果状态 */
export const enum ComplianceCheckResultStatus {
  /** 通过 */
  Pass = 1,
  /** 不通过 */
  Fail = 0,
}

export const complianceCheckResultStatusConfig = {
  [ComplianceCheckResultStatus.Pass]: {
    value: ComplianceCheckResultStatus.Pass,
    label: '通过',
    severity: 'success',
  },
  [ComplianceCheckResultStatus.Fail]: {
    value: ComplianceCheckResultStatus.Fail,
    label: '不通过',
    severity: 'danger',
  },
} satisfies EnumConfig<ComplianceCheckResultStatus>

export const threatLevelConfig = {
  [VulThreatLevel.Super]: {
    value: VulThreatLevel.Super,
    label: '超危',
    severity: 'danger',
    bgColor: $dt('red.500').value || $dt('red.500').variable,
  },
  [VulThreatLevel.High]: {
    value: VulThreatLevel.High,
    label: '高危',
    severity: 'warn',
    bgColor: $dt('amber.400').value || $dt('amber.400').variable,
  },
  [VulThreatLevel.Medium]: {
    value: VulThreatLevel.Medium,
    label: '中危',
    severity: 'info',
    bgColor: $dt('blue.500').value || $dt('blue.500').variable,
  },
  [VulThreatLevel.Low]: {
    value: VulThreatLevel.Low,
    label: '低危',
    severity: 'secondary',
    bgColor: $dt('gray.500').value || $dt('gray.500').variable,
  },
} satisfies EnumConfig<VulThreatLevel>

/** 统一获取威胁等级标签的 severity */
export function getThreatLevelSeverity(level: VulThreatLevel | undefined): TagSeverity {
  if (level) {
    return threatLevelConfig[level].severity
  }

  return 'secondary'
}

/** 统一获取威胁等级标签的 label */
export function getThreatLevelLabel(level: VulThreatLevel | undefined): string {
  if (level) {
    return threatLevelConfig[level].label
  }

  return '未知'
}
