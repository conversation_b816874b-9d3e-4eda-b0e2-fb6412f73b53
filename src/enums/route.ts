import { AirplayIcon, BookDashedIcon, BookOpenTextIcon, BookUserIcon, BotIcon, BriefcaseBusinessIcon, BugIcon, ChartGanttIcon, ChartPieIcon, ChartSpline, FingerprintIcon, FolderKanbanIcon, GaugeIcon, GitCompareIcon, HouseIcon, KeyRoundIcon, KeySquareIcon, LayoutGridIcon, LightbulbIcon, ListTodoIcon, ListVideoIcon, MessageSquareIcon, NetworkIcon, NotebookPenIcon, OrbitIcon, PackageIcon, PaletteIcon, RocketIcon, RouterIcon, ScanFaceIcon, ScanSearchIcon, ScrollTextIcon, SettingsIcon, ShieldCheck, ShieldCheckIcon, SparklesIcon, SquareUserIcon, TargetIcon, TextSearchIcon, TicketIcon, UserIcon, UsersIcon, WorkflowIcon } from 'lucide-vue-next'

import { AUTH } from '~/enums/user'

const appSpacePageTitle = '智能体应用'

/**
 * 路由的唯一标识
 *
 * 用于在代码中引用特定路由，避免使用魔法字符串。
 *
 * 注意：这里的枚举值（如 'index', 'login' 等）仅作为路由的标识符使用，
 * 不会影响实际的路由路径。实际路由路径配置在下方的 `routeConfig` 中定义。
 *
 * 这些标识符可以根据需要修改，但是要注意应该和后端存储的 Key 一致。
 */
export const enum RouteKey {
  首页 = 'index',
  登录 = 'login',
  注册 = 'signup',
  仪表盘 = 'dashboard',
  智能中心 = 'dashboard-ai',
  智能分析 = 'dashboard-ai-analysis',
  资产概览 = 'dashboard-asset',
  安全概览 = 'dashboard-security',
  工单概览 = 'dashboard-ticket',

  资产管理 = 'asset',
  资产详情 = 'asset-network',
  网络设备详情 = 'asset-network-device',
  资产拓扑图 = 'asset-topology',
  资产填报 = 'asset-collect',
  资产搜索 = 'asset-search',
  资产搜索结果 = 'asset-search-result',
  安全中心 = 'security',
  漏洞管理 = 'security-vul',
  基线管理 = 'security-compliance',
  安全通告 = 'security-cve',
  漏洞详情 = 'security-vul-detail',

  工单管理 = 'ticket',
  工单列表 = 'ticket-list',
  待办工单 = 'ticket-assigned',

  配置中心 = 'configuration',
  资产模板 = 'configuration-model-asset',
  基线模板 = 'configuration-model-compliance',

  权限管理 = 'permission',
  成员与部门 = 'permission-org',
  角色管理 = 'permission-role',

  TW_工作台 = 'workspace',
  TW_自动化工作流 = 'autoflow',
  TW_自动化工作流详情 = 'autoflow-detail',
  TW_工作空间 = 'space',
  TW_工作空间_ID = 'space-id',
  TW_工作空间_应用 = 'space-app',
  TW_工作空间_知识库 = 'space-knowledge-base',
  TW_工作空间_设置 = 'space-setting',
  TW_工作空间_凭证 = 'space-credential',
  TW_应用_工作流 = 'space-workflow',
  TW_应用_工作流执行记录 = 'space-workflow-executions',
  TW_应用_工作流执行记录画布 = 'space-workflow-demo',
  TW_项目管理 = 'project',
  TW_项目列表 = 'project-list',
  TW_项目详情 = 'project-detail',
  TW_项目评价 = 'project-feedback',
  TW_客户管理 = 'client',
  TW_客户列表 = 'client-list',
  TW_微信端用户 = 'client-wechat',
  TW_组织架构 = 'org',
  TW_成员与部门 = 'org-user',
  TW_角色管理 = 'org-role',
  TW_权限资源 = 'org-permission',
  TW_AI工具 = 'assistant',
  TW_AI_智能助手 = 'assistant-five',
  TW_AI_灵感中心 = 'assistant-one',
  TW_AI_文本改写 = 'assistant-two',
  TW_AI_报告润色 = 'assistant-three',
  TW_AI_报告生成引擎 = 'assistant-four',
  TW_AI_文档对比 = 'assistant-six',
  TW_应急响应报告生成器 = 'report-generator',

  TW_客户_项目详情 = 'client-project-detail',
  TW_客户_项目评价 = 'client-project-feedback',
  TW_客户_项目评价_成功 = 'client-project-feedback-success',

  系统设置 = 'settings',
  个人中心 = 'settings-profile',
  账号安全 = 'settings-account',
  偏好设置 = 'settings-preference',
  许可证 = 'settings-license',
}

/**
 * 路由元数据配置表
 *
 * 用于集中管理所有路由的元数据信息，主要包括：
 * - title: 页面标题，用于展示在浏览器标签页
 * - route: 实际的路由路径
 * - icon: 路由图标
 * - permissions: 权限列表，位于该列表的权限可以访问该路由
 *
 * @remarks
 * - 需要确保 RouteKey 中定义的每个键都在此处有对应的配置
 * - 添加新路由时，需要同时在 RouteKey 和此处添加对应配置
 *
 * @example
 * 添加新路由的方式：
 *
 * // 1. 首先在 RouteKey 中添加新路由的标识
 * export const enum RouteKey {
 *   新页面 = 'new-page',
 * }
 *
 * // 2. 然后在 routeConfig 中添加新路由的配置
 * export const routeConfig = {
 *   [RouteKey.新页面]: {
 *     title: '新页面标题',
 *     route: '/path/to/new-page',
 *   },
 * }
 */
export const routeConfig = {
  [RouteKey.首页]: {
    title: '首页',
    route: '/',
    icon: HouseIcon,
  },

  [RouteKey.登录]: {
    title: '登录',
    route: '/login',
  },

  [RouteKey.注册]: {
    title: '注册',
    route: '/signup',
  },

  [RouteKey.仪表盘]: {
    title: '仪表盘',
    route: '/dashboard',
    icon: GaugeIcon,
    permissions: [AUTH.DASHBOARD_VIEW],
  },
  [RouteKey.资产概览]: {
    title: '资产概览',
    route: '/dashboard/asset',
    icon: ChartPieIcon,
    permissions: [AUTH.DASHBOARD_ASSET_VIEW],
  },
  [RouteKey.安全概览]: {
    title: '安全概览',
    route: '/dashboard/security',
    icon: ChartSpline,
    permissions: [AUTH.DASHBOARD_SECURITY_VIEW],
  },
  [RouteKey.工单概览]: {
    title: '工单概览',
    route: '/dashboard/ticket',
    icon: ChartGanttIcon,
    permissions: [AUTH.DASHBOARD_TICKET_VIEW],
  },
  [RouteKey.智能中心]: {
    title: 'AI 智能中心',
    route: '/dashboard-ai',
    icon: BotIcon,
    permissions: [AUTH.DASHBOARD_VIEW],
  },
  [RouteKey.智能分析]: {
    title: '智能安全专家工作室',
    route: '/dashboard-ai-analysis',
    icon: BotIcon,
    permissions: [AUTH.DASHBOARD_VIEW],
  },

  [RouteKey.资产管理]: {
    title: '资产管理',
    route: '/asset',
    icon: OrbitIcon,
    permissions: [AUTH.ASSET_MANAGE],
  },
  [RouteKey.资产详情]: {
    title: '资产详情',
    route: '/asset/network',
    icon: RouterIcon,
    permissions: [AUTH.ASSET_DETAIL_VIEW],
  },
  [RouteKey.网络设备详情]: {
    title: '网络设备详情',
    route: '/asset/network/device/[deviceId]',
    permissions: [AUTH.ASSET_DETAIL_VIEW],
  },
  [RouteKey.资产拓扑图]: {
    title: '资产拓扑图',
    route: '/asset/topology',
    icon: NetworkIcon,
    permissions: [AUTH.ASSET_TOPOLOGY_VIEW],
  },
  [RouteKey.资产填报]: {
    title: '资产填报',
    route: '/asset/collect/[collectionKey]',
    icon: NotebookPenIcon,
    permissions: [AUTH.ASSET_COLLECT_VIEW],
  },
  [RouteKey.资产搜索]: {
    title: '资产搜索',
    route: '/asset/search',
    icon: ScanSearchIcon,
    permissions: [AUTH.ASSET_SEARCH_VIEW],
  },
  [RouteKey.资产搜索结果]: {
    title: '资产搜索结果',
    route: '/asset/search/[searchKey]',
    icon: ScanSearchIcon,
    permissions: [AUTH.ASSET_SEARCH_VIEW],
  },

  [RouteKey.安全中心]: {
    title: '安全中心',
    route: '/security',
    icon: ShieldCheck,
    permissions: [AUTH.SECURITY_MANAGE],
  },
  [RouteKey.漏洞管理]: {
    title: '漏洞管理',
    route: '/security/vul',
    icon: BugIcon,
    permissions: [AUTH.VUL_MANAGE],
  },
  [RouteKey.基线管理]: {
    title: '基线管理',
    route: '/security/compliance',
    icon: TextSearchIcon,
    permissions: [AUTH.COMPLIANCE_MANAGE],
  },
  [RouteKey.安全通告]: {
    title: '安全通告',
    route: '/security/cve',
    icon: ShieldCheckIcon,
    permissions: [AUTH.CVE_MANAGE],
  },
  [RouteKey.漏洞详情]: {
    title: '漏洞详情',
    route: '/security/vul-detail/[vulDetailId]',
    permissions: [AUTH.VUL_DETAIL_VIEW],
  },

  [RouteKey.配置中心]: {
    title: '配置中心',
    route: '/configuration',
    icon: PackageIcon,
    permissions: [AUTH.CONFIGURATION_MANAGE],
  },
  [RouteKey.资产模板]: {
    title: '资产模板',
    route: '/configuration/model-asset',
    icon: BookDashedIcon,
    permissions: [AUTH.ASSET_TEMPLATE_MANAGE],
  },
  [RouteKey.基线模板]: {
    title: '基线模板',
    route: '/configuration/model-compliance',
    icon: BookDashedIcon,
    permissions: [AUTH.COMPLIANCE_TEMPLATE_MANAGE],
  },

  [RouteKey.工单管理]: {
    title: '工单管理',
    route: '/ticket',
    icon: BriefcaseBusinessIcon,
    permissions: [AUTH.TICKET_MANAGE],
  },
  [RouteKey.工单列表]: {
    title: '工单列表',
    route: '/ticket/list',
    icon: TicketIcon,
    permissions: [AUTH.TICKET_LIST_MANAGE],
  },
  [RouteKey.待办工单]: {
    title: '待办工单',
    route: '/ticket/assigned',
    icon: ListTodoIcon,
    permissions: [AUTH.TICKET_ASSIGNED_MANAGE],
  },

  [RouteKey.权限管理]: {
    title: '权限管理',
    route: '/permission',
    icon: KeyRoundIcon,
    permissions: [AUTH.PERMISSION_MANAGE],
  },
  [RouteKey.成员与部门]: {
    title: '成员与部门',
    route: '/permission/user',
    icon: UsersIcon,
    permissions: [AUTH.USER_MANAGE],
  },
  [RouteKey.角色管理]: {
    title: '角色管理',
    route: '/permission/role',
    icon: ScanFaceIcon,
    permissions: [AUTH.ROLE_MANAGE],
  },

  [RouteKey.系统设置]: {
    title: '系统设置',
    route: '/settings',
    icon: SettingsIcon,
    permissions: [AUTH.SKIP_SHIZHEN, AUTH.SETTINGS_MANAGE],
  },
  [RouteKey.个人中心]: {
    title: '个人中心',
    route: '/settings/profile',
    icon: UserIcon,
    permissions: [AUTH.SKIP_SHIZHEN, AUTH.SETTINGS_MANAGE],
  },
  [RouteKey.账号安全]: {
    title: '账号安全',
    route: '/settings/account',
    icon: ShieldCheckIcon,
    permissions: [AUTH.SKIP_SHIZHEN, AUTH.SETTINGS_MANAGE],
  },
  [RouteKey.偏好设置]: {
    title: '偏好设置',
    route: '/settings/preference',
    icon: PaletteIcon,
    permissions: [AUTH.SKIP_SHIZHEN, AUTH.SETTINGS_MANAGE],
  },
  [RouteKey.许可证]: {
    title: '许可证',
    route: '/settings/license',
    icon: KeyRoundIcon,
    permissions: [AUTH.SKIP_SHIZHEN],
  },

  // =============================================================================

  [RouteKey.TW_工作台]: {
    title: '工作台',
    route: '/workspace',
    icon: AirplayIcon,
    permissions: [AUTH.WORKSPACE_VIEW],
  },

  // =============================================================================

  [RouteKey.TW_项目管理]: {
    title: '项目管理',
    route: '/project',
    icon: TargetIcon,
    permissions: [AUTH.PROJECT_LIST_READ],
  },
  [RouteKey.TW_项目列表]: {
    title: '项目列表',
    route: '/project/list',
    icon: FolderKanbanIcon,
    permissions: [AUTH.PROJECT_LIST_READ],
  },
  [RouteKey.TW_项目详情]: {
    title: '项目详情',
    route: '/project/[projectId]/detail',
    permissions: [AUTH.PROJECT_DETAIL_READ],
  },
  [RouteKey.TW_项目评价]: {
    title: '项目评价',
    route: '/project/[projectId]/feedback',
    permissions: [AUTH.PROJECT_DETAIL_READ],
  },

  // =============================================================================
  [RouteKey.TW_工作空间]: {
    title: appSpacePageTitle,
    route: '/space',
    icon: FolderKanbanIcon,
    permissions: [AUTH.WORKFLOW_LIST_READ],
  },
  [RouteKey.TW_工作空间_ID]: {
    title: appSpacePageTitle,
    route: '/space/[spaceId]',
    permissions: [AUTH.WORKFLOW_LIST_READ],
  },
  [RouteKey.TW_工作空间_应用]: {
    title: appSpacePageTitle,
    route: '/space/[spaceId]/app',
    icon: LayoutGridIcon,
    permissions: [AUTH.WORKFLOW_LIST_READ],
  },
  [RouteKey.TW_工作空间_知识库]: {
    title: '知识库',
    route: '/space/[spaceId]/knowledge-base',
    icon: BookOpenTextIcon,
    permissions: [AUTH.KNOWLEDGE_BASE_READ],
  },
  [RouteKey.TW_工作空间_设置]: {
    title: '成员与设置',
    route: '/space/[spaceId]/setting',
    icon: SettingsIcon,
    permissions: [AUTH.WORKFLOW_LIST_READ],
  },
  [RouteKey.TW_工作空间_凭证]: {
    title: '授权凭证管理',
    route: '/space/[spaceId]/credential',
    icon: KeySquareIcon,
    permissions: [AUTH.WORKFLOW_LIST_READ],
  },
  [RouteKey.TW_应用_工作流]: {
    title: '工作流详情',
    route: '/space/[spaceId]/workflow/[workflowId]',
    icon: WorkflowIcon,
    permissions: [AUTH.WORKFLOW_LIST_READ],
  },
  [RouteKey.TW_应用_工作流执行记录]: {
    title: '执行记录',
    route: '/space/[spaceId]/workflow/[workflowId]/executions',
    icon: ListVideoIcon,
    permissions: [AUTH.WORKFLOW_LIST_READ],
  },
  [RouteKey.TW_应用_工作流执行记录画布]: {
    title: '执行记录画布',
    route: '/space/[spaceId]/workflow/demo',
    permissions: [AUTH.WORKFLOW_LIST_READ],
  },

  [RouteKey.TW_自动化工作流]: {
    title: '自动化流程',
    route: '/autoflow',
    icon: WorkflowIcon,
    permissions: [AUTH.WORKFLOW_LIST_READ],
  },
  [RouteKey.TW_自动化工作流详情]: {
    title: '自动化流程详情',
    route: '/autoflow/[autoflowId]',
    permissions: [AUTH.WORKFLOW_LIST_READ],
  },

  [RouteKey.TW_客户管理]: {
    title: '客户管理',
    route: '/client',
    icon: SquareUserIcon,
    permissions: [AUTH.CLIENT_LIST_READ],
  },
  [RouteKey.TW_客户列表]: {
    title: '客户列表',
    route: '/client/list',
    icon: BookUserIcon,
    permissions: [AUTH.CLIENT_LIST_READ],
  },
  [RouteKey.TW_微信端用户]: {
    title: '微信端用户',
    route: '/client/wechat',
    icon: UserIcon,
    permissions: [AUTH.CLIENT_WECHAT_USER_LIST_READ],
  },

  [RouteKey.TW_组织架构]: {
    title: '组织架构',
    route: '/org',
    icon: NetworkIcon,
    permissions: [AUTH.ORG_USER_MANAGE],
  },
  [RouteKey.TW_成员与部门]: {
    title: '成员与部门',
    route: '/org/user',
    icon: UsersIcon,
    permissions: [AUTH.ORG_USER_MANAGE],
  },
  [RouteKey.TW_角色管理]: {
    title: '角色管理',
    route: '/org/role',
    icon: ScanFaceIcon,
    permissions: [AUTH.ORG_ROLE_MANAGE],
  },
  [RouteKey.TW_权限资源]: {
    title: '权限资源',
    route: '/org/permission',
    icon: FingerprintIcon,
    permissions: [AUTH.ORG_PERMISSION_MANAGE],
  },

  [RouteKey.TW_AI工具]: {
    title: 'AI 效率工具',
    route: '/aigc',
    icon: SparklesIcon,
    permissions: [AUTH.AI_MANAGE],
  },
  [RouteKey.TW_AI_智能助手]: {
    title: '智能助手',
    route: '/aigc/digital-assistant',
    icon: BotIcon,
    permissions: [AUTH.AI_MANAGE],
  },
  [RouteKey.TW_应急响应报告生成器]: {
    title: '应急响应报告生成',
    route: '/aigc/er-report',
    icon: ScrollTextIcon,
    permissions: [AUTH.AI_MANAGE],
  },
  [RouteKey.TW_AI_灵感中心]: {
    title: '灵感中心',
    route: '/aigc/inspiration',
    icon: LightbulbIcon,
    permissions: [AUTH.AI_MANAGE],
  },
  [RouteKey.TW_AI_文本改写]: {
    title: '文本改写',
    route: '/aigc/paragraph-rewrite',
    icon: NotebookPenIcon,
    permissions: [AUTH.AI_MANAGE],
  },
  [RouteKey.TW_AI_报告润色]: {
    title: '智能报告润色',
    route: '/aigc/report-polish',
    icon: BookOpenTextIcon,
    permissions: [AUTH.AI_MANAGE],
  },
  [RouteKey.TW_AI_报告生成引擎]: {
    title: '报告生成引擎',
    route: '/aigc/report-assistant',
    icon: RocketIcon,
    permissions: [AUTH.AI_MANAGE],
  },
  [RouteKey.TW_AI_文档对比]: {
    title: '文档对比',
    route: '/aigc/document-compare',
    icon: GitCompareIcon,
    permissions: [AUTH.AI_MANAGE],
  },

  [RouteKey.TW_客户_项目详情]: {
    title: '项目详情',
    route: '/client/project/[projectId]',
    icon: TargetIcon,
    permissions: [AUTH.SKIP_FUYAO],
  },
  [RouteKey.TW_客户_项目评价]: {
    title: '用户反馈',
    route: '/client/project/[projectId]/feedback',
    icon: MessageSquareIcon,
    permissions: [AUTH.SKIP_FUYAO],
  },
  [RouteKey.TW_客户_项目评价_成功]: {
    title: '评价成功',
    route: '/client/project/[projectId]/feedback-success',
    permissions: [AUTH.SKIP_FUYAO],
  },
} as const satisfies AppRouteConfig
