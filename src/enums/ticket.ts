import { $dt } from '@primevue/themes'
import { CircleCheckBigIcon, CircleDotDashedIcon, CircleSlashIcon, FilePenIcon, LoaderIcon, Undo2Icon, XIcon } from 'lucide-vue-next'

/** 工单类型 */
export const enum TicketType {
  /** 合规检查 */
  Compliance = 1,
  /** 漏洞检查 */
  Vul = 2,
}

export const ticketTypeConfig = {
  [TicketType.Compliance]: {
    value: TicketType.Compliance,
    label: '基线检查',
    severity: 'info',
  },
  [TicketType.Vul]: {
    value: TicketType.Vul,
    label: '漏洞检查',
    severity: 'danger',
  },
} as const satisfies EnumConfig<TicketType>

/** 工单等级 */
export const enum TicketGrade {
  /** 一般 */
  General = 1,
  /** 重要 */
  Important = 2,
  /** 非常重要 */
  VeryImportant = 3,
}

/** 工单等级配置 */
export const ticketGradeConfig = {
  [TicketGrade.General]: {
    value: TicketGrade.General,
    label: '一般',
    severity: 'secondary',
    bgColor: $dt('gray.300').value || $dt('gray.300').variable,
  },
  [TicketGrade.Important]: {
    value: TicketGrade.Important,
    label: '重要',
    severity: 'info',
    bgColor: $dt('blue.500').value || $dt('blue.500').variable,
  },
  [TicketGrade.VeryImportant]: {
    value: TicketGrade.VeryImportant,
    label: '紧急',
    severity: 'contrast',
    bgColor: $dt('gray.800').value || $dt('gray.800').variable,
  },
} as const satisfies EnumConfig<TicketGrade>

/** 工单状态 */
export const enum TicketStatus {
  /** 草稿 */
  Draft = 0,
  /** 待处理 */
  Pending = 1,
  /** 审核中 */
  Processing = 2,
  /** 已通过 */
  Approved = 3,
  /** 已驳回 */
  Rejected = 4,
  /** 已终止 */
  Terminated = 5,
}

export const ticketStatusConfig = {
  [TicketStatus.Draft]: {
    value: TicketStatus.Draft,
    label: '草稿',
    severity: 'secondary',
    bgColor: $dt('gray.500').value || $dt('gray.500').variable,
    icon: FilePenIcon,
  },
  [TicketStatus.Pending]: {
    value: TicketStatus.Pending,
    label: '待处理',
    severity: 'warn',
    bgColor: $dt('amber.500').value || $dt('amber.500').variable,
    icon: CircleDotDashedIcon,
  },
  [TicketStatus.Processing]: {
    value: TicketStatus.Processing,
    label: '审核中',
    severity: 'info',
    bgColor: $dt('blue.500').value || $dt('blue.500').variable,
    icon: LoaderIcon,
  },
  [TicketStatus.Approved]: {
    value: TicketStatus.Approved,
    label: '已通过',
    severity: 'success',
    bgColor: $dt('emerald.500').value || $dt('emerald.500').variable,
    icon: CircleCheckBigIcon,
  },
  [TicketStatus.Rejected]: {
    value: TicketStatus.Rejected,
    label: '已拒绝',
    severity: 'danger',
    bgColor: $dt('red.500').value || $dt('red.500').variable,
    icon: XIcon,
  },
  [TicketStatus.Terminated]: {
    value: TicketStatus.Terminated,
    label: '已关闭',
    severity: 'secondary',
    bgColor: $dt('gray.300').value || $dt('gray.300').variable,
    icon: CircleSlashIcon,
  },
} as const satisfies EnumConfig<TicketStatus, BaseEnumConfig & { icon: Component }>

/** 工单审核记录状态 */
export const enum TicketCommentActionStatus {
  /** 审核中 */
  Process = 1,
  /** 通过 */
  Approve = 2,
  /** 驳回 */
  Reject = 3,
  /** 撤销 */
  Withdraw = 4,
}

export const ticketCommentActionStatusConfig = {
  [TicketCommentActionStatus.Process]: {
    value: TicketCommentActionStatus.Process,
    label: '审核中',
    severity: 'info',
    frontColor: $dt('blue.500').value || $dt('blue.500').variable,
    bgColor: $dt('blue.500').value || $dt('blue.500').variable,
    icon: LoaderIcon,
  },
  [TicketCommentActionStatus.Approve]: {
    value: TicketCommentActionStatus.Approve,
    label: '通过',
    severity: 'success',
    frontColor: $dt('emerald.500').value || $dt('emerald.500').variable,
    bgColor: $dt('emerald.500').value || $dt('emerald.500').variable,
    icon: CircleCheckBigIcon,
  },
  [TicketCommentActionStatus.Reject]: {
    value: TicketCommentActionStatus.Reject,
    label: '驳回',
    severity: 'danger',
    frontColor: $dt('red.500').value || $dt('red.500').variable,
    bgColor: $dt('red.500').value || $dt('red.500').variable,
    icon: XIcon,
  },
  [TicketCommentActionStatus.Withdraw]: {
    value: TicketCommentActionStatus.Withdraw,
    label: '撤销',
    severity: 'secondary',
    frontColor: $dt('gray.500').value || $dt('gray.500').variable,
    bgColor: $dt('gray.300').value || $dt('gray.300').variable,
    icon: Undo2Icon,
  },
} as const satisfies EnumConfig<TicketCommentActionStatus, BaseEnumConfig & { icon: Component }>
