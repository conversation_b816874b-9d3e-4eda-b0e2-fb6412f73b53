const position = { x: 0, y: 0 }

export const enum TopoElementTypes {
  SwitchBlue,
  SwitchRed,
  SwitchYellow,
}

interface Node {
  id: string
  position: typeof position
  type: 'custom'
  data: {
    label: string
    eleType: TopoElementTypes
  }
}

export const initialNodes: Node[] = [
  {
    id: '1',
    position,
    type: 'custom',
    data: {
      label: '网络主机',
      eleType: TopoElementTypes.SwitchBlue,
    },
  },
  {
    id: '2',
    position,
    type: 'custom',
    data: {
      label: '网络交换机',
      eleType: TopoElementTypes.SwitchBlue,
    },
  },
  {
    id: '2a',
    position,
    type: 'custom',
    data: {
      label: '网络防火墙',
      eleType: TopoElementTypes.SwitchRed,
    },
  },
  {
    id: '2b',
    position,
    type: 'custom',
    data: {
      label: '网络路由器',
      eleType: TopoElementTypes.SwitchYellow,
    },
  },
  {
    id: '2c',
    position,
    type: 'custom',
    data: {
      label: '网络无线AP',
      eleType: TopoElementTypes.SwitchBlue,
    },
  },
  {
    id: '2d',
    position,
    type: 'custom',
    data: {
      label: '网络无线AP',
      eleType: TopoElementTypes.SwitchYellow,
    },
  },
  {
    id: '3',
    position,
    type: 'custom',
    data: {
      label: '网络无线AP',
      eleType: TopoElementTypes.SwitchYellow,
    },
  },
  {
    id: '4',
    position,
    type: 'custom',
    data: {
      label: '网络主机',
      eleType: TopoElementTypes.SwitchBlue,
    },
  },
  {
    id: '5',
    position,
    type: 'custom',
    data: {
      label: '网络防火墙',
      eleType: TopoElementTypes.SwitchRed,
    },
  },
  {
    id: '6',
    position,
    type: 'custom',
    data: {
      label: '网络无线AP',
      eleType: TopoElementTypes.SwitchBlue,
    },
  },
  {
    id: '7',
    position,
    type: 'custom',
    data: {
      label: '网络无线AP',
      eleType: TopoElementTypes.SwitchBlue,
    },
  },
]

export const initialEdges = [
  { id: 'e1-2', source: '1', target: '2' },
  { id: 'e1-3', source: '1', target: '3' },
  { id: 'e2-2a', source: '2', target: '2a' },
  { id: 'e2-2b', source: '2', target: '2b' },
  { id: 'e2-2c', source: '2', target: '2c' },
  { id: 'e2c-2d', source: '2c', target: '2d' },
  { id: 'e3-7', source: '3', target: '4' },
  { id: 'e4-5', source: '4', target: '5' },
  { id: 'e5-6', source: '5', target: '6' },
  { id: 'e5-7', source: '5', target: '7' },
]
