/**
 * API 响应状态码
 *
 * 定义了所有后端 API 可能返回的状态码及其含义，用于统一管理和判断 API 响应的状态
 *
 * 建议使用此枚举而不是魔法数字来判断 API 响应状态
 */
export const enum ApiStatus {
  Success = 200,
  /** 未授权 */
  Unauthorized = 401,
  /** 登录令牌过期 */
  TokenExpired = 411,
  /** 分享采集链接失效：过期/无效 */
  LinkExpiredOrInvalid = 4001,
  /** 许可证失效：过期/无效 */
  LicenseInvalid = 4002,
  /** 环境变量缺失：必要的环境变量未设置 */
  MissingEnvVars = 0,
}

export const enum StoreKey {
  App = 'app',
  User = 'user',
  AssetInfo = 'assetInfo',
  AdminUserGroup = 'adminUserGroup',
  AdminNavMenu = 'adminNavMenu',
  AdminDepartment = 'adminDepartment',
  WorkspaceStats = 'workspaceStats',
}

/**
 * 日期格式化，适配 dayjs
 * 所有格式均遵循 dayjs 的格式化字符串规则：
 *
 * - YYYY: 4位数年份
 * - MM: 2位数月份 (01-12)
 * - DD: 2位数日期 (01-31)
 * - HH: 24小时制 (00-23)
 * - mm: 分钟 (00-59)
 * - ss: 秒钟 (00-59)
 */
export const enum DateFormat {
  /** 年-月-日 (例：2024-03-21) */
  YYYY_MM_DD = 'YYYY-MM-DD',
  /** 年-月-日 时:分 (例：2024-03-21 15:30) */
  YYYY_MM_DD_HH_MM = 'YYYY-MM-DD HH:mm',
  /** 年-月-日 时:分:秒 (例：2024-03-21 15:30:45) */
  YYYY_MM_DD_HH_MM_SS = 'YYYY-MM-DD HH:mm:ss',
  /** 年-月-日 时:分 (例：2024年03月21日 15:30) */
  YYYY年MM月DD日HH时MM分 = 'YYYY年MM月DD日 HH:mm',
  /** 月-日 时:分 (例：03-21 15:30) */
  MM_DD_HH_MM = 'MM-DD HH:mm',
  /** 月-日 (例：03-21) */
  MM_DD = 'MM-DD',
  /** 时:分 (例：15:30) */
  HH_MM = 'HH:mm',
  /** xx 时 xx 分 (例：15 时 30 分) */
  HH时MM分 = 'HH 时 MM 分',
}

export const enum TooltipShowDelay {
  Slower = 850,
  Slow = 500,
  Fast = 150,
}
