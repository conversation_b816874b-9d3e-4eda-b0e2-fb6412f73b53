import { CopyIcon, EditIcon, EyeIcon, PlusIcon, TextSearchIcon, TrashIcon } from 'lucide-vue-next'

/** Table Cell 渲染值类型 */
export const enum ProValueType {
  /** 文本 */
  Text = 'text',
  /** 文本域 */
  Textarea = 'textarea',
  /** 密码 */
  Password = 'password',

  /** 数字输入框 */
  Digit = 'digit',
  /** 金额 */
  Money = 'money',

  /** 下拉框 */
  Select = 'select',
  /** 树形下拉框 */
  TreeSelect = 'treeSelect',

  /** 日期 */
  Date = 'date',
  /** 日期时间 */
  DateTime = 'datetime',
  /** 时间 */
  Time = 'time',

  /** 多选框 */
  Checkbox = 'checkbox',
  /** 星级组件 */
  Rate = 'rate',
  /** 单选框 */
  Radio = 'radio',
  /** 按钮单选框 */
  RadioButton = 'radioButton',
  /** 进度条 */
  Progress = 'progress',
  /** 百分比组件 */
  Percent = 'percent',
  /** 标签 */
  Tag = 'tag',
  /** 图片 */
  Image = 'image',

  /** 开关 */
  Switch = 'switch',
  /** 级联选择器 */
  Cascader = 'cascader',

  /** 操作按钮组 */
  ActionGroup = 'actionGroup',

  /** 分割线 */
  Divider = 'divider',
}

/** 表格排序方向 */
export const enum ProTableSortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * 常用的菜单操作类型，用于菜单项的渲染。
 * 当对菜单项声明为这些特殊的操作类型时，会自动渲染对应的图标和文字
 */
export const enum ProMenuActionType {
  /** 添加 */
  Add = 'add',
  /** 删除 */
  Delete = 'delete',
  /** 编辑 */
  Edit = 'edit',
  /** 查看 */
  View = 'view',
  /** 复制 */
  Copy = 'copy',
  /** 移动 */
  Move = 'move',
  /** 导出 */
  Export = 'export',
  /** 详情 */
  Detail = 'detail',
  /** 分割线 */
  Separator = 'separator',
}

/**
 * 获取菜单项的图标
 */
export function getMenuIcon(item: ProMenuItem) {
  if (item.itemIcon) {
    return item.itemIcon
  }

  if (item.itemIcon === null) {
    return null
  }

  if (item.actionType) {
    switch (item.actionType) {
      case ProMenuActionType.Edit:
        return EditIcon

      case ProMenuActionType.Delete:
        return TrashIcon

      case ProMenuActionType.Add:
        return PlusIcon

      case ProMenuActionType.View:
        return EyeIcon

      case ProMenuActionType.Copy:
        return CopyIcon

      default:
        return TextSearchIcon
    }
  }

  return null
}
