import { $dt } from '@primevue/themes'
import { CalendarDaysIcon, CalendarIcon, CircleChevronDownIcon, CircleIcon, ClockIcon, ComputerIcon, DatabaseZapIcon, HashIcon, LetterTextIcon, ListCheckIcon, MapPinIcon, MonitorCogIcon, PackageCheckIcon, ScrollTextIcon, SearchCheckIcon, ServerIcon, SquareCheckIcon, TextIcon, ToggleRightIcon } from 'lucide-vue-next'

/** 资产类型 */
export const enum AssetType {
  /** 网络设备 */
  NetworkDevice = 1,
  /** 服务器 */
  Server = 2,
  /** 数据库 */
  Database = 3,
  /** 业务系统 */
  BusinessSystem = 4,
  /** 其他 */
  Other = 5,
}

/** 资产详情页标签 */
export const AssetDetailTab = {
  /** 网络设备 */
  NetworkDevice: AssetType.NetworkDevice,
  /** 服务器 */
  Server: AssetType.Server,
  /** 数据库 */
  Database: AssetType.Database,
  /** 业务系统 */
  BusinessSystem: AssetType.BusinessSystem,
  /** 其他 */
  Other: AssetType.Other,
  /** 资产搜索 */
  Search: 'search',
  /** IP 变更记录 */
  IpLog: 'ip-log',
}

/** 资产模板的字段类型 */
export const enum AssetFieldType {
  /** 输入框 */
  Input = 1,
  /** 文本域输入框 */
  InputTextarea = 2,
  /** 数字输入框 */
  InputNumber = 3,
  /** 单选框 */
  Radio = 4,
  /** 多选框 */
  Checkbox = 5,
  /** 选择器 */
  Select = 6,
  /** 开关 */
  Switch = 7,
  /** 日期选择器 */
  DateSelect = 8,
  /** 时间选择器 */
  TimeSelect = 9,
  /** 日期时间选择器 */
  DatetimeSelect = 10,
  /** 富文本输入框 */
  RichText = 11,
}

type AssetFieldTypeConfigType = {
  [K in AssetFieldType]: {
    id: K
    /** 是否可枚举 */
    enumable?: boolean
    /** 是否可多选 */
    multiple?: boolean
    /** 是否可输入 */
    hasPlaceholder?: boolean
    /** 图标 */
    icon: Component
  } & Omit<NonNullable<AssetField['field_type']>, 'id'>
}

export const assetFieldTypeConfig: AssetFieldTypeConfigType = {
  [AssetFieldType.Input]: {
    id: AssetFieldType.Input,
    name: '文本',
    form: 'input',
    sql: 'varchar(64)',
    hasPlaceholder: true,
    icon: LetterTextIcon,
  },
  [AssetFieldType.InputTextarea]: {
    id: AssetFieldType.InputTextarea,
    name: '多行文本',
    form: 'input-textarea',
    sql: 'varchar(256)',
    hasPlaceholder: true,
    icon: TextIcon,
  },
  [AssetFieldType.RichText]: {
    id: AssetFieldType.RichText,
    name: '富文本',
    form: 'markdown',
    sql: 'text',
    hasPlaceholder: true,
    icon: ScrollTextIcon,
  },
  [AssetFieldType.InputNumber]: {
    id: AssetFieldType.InputNumber,
    name: '数字',
    form: 'input-number',
    sql: 'int',
    hasPlaceholder: true,
    icon: HashIcon,
  },

  [AssetFieldType.Radio]: {
    id: AssetFieldType.Radio,
    name: '单选',
    form: 'radio',
    sql: 'varchar(128)',
    enumable: true,
    icon: CircleIcon,
  },
  [AssetFieldType.Checkbox]: {
    id: AssetFieldType.Checkbox,
    name: '复选',
    form: 'checkbox',
    sql: 'json',
    enumable: true,
    icon: SquareCheckIcon,
  },
  [AssetFieldType.Select]: {
    id: AssetFieldType.Select,
    name: '下拉单选',
    form: 'select',
    sql: 'varchar(128)',
    enumable: true,
    hasPlaceholder: true,
    icon: CircleChevronDownIcon,
  },

  [AssetFieldType.Switch]: {
    id: AssetFieldType.Switch,
    name: '开关',
    form: 'switch',
    sql: 'tinyint(1)',
    icon: ToggleRightIcon,
  },

  [AssetFieldType.DateSelect]: {
    id: AssetFieldType.DateSelect,
    name: '日期',
    form: 'date-select',
    sql: 'date',
    hasPlaceholder: true,
    icon: CalendarIcon,
  },
  [AssetFieldType.TimeSelect]: {
    id: AssetFieldType.TimeSelect,
    name: '时间',
    form: 'time-select',
    sql: 'time',
    hasPlaceholder: true,
    icon: ClockIcon,
  },
  [AssetFieldType.DatetimeSelect]: {
    id: AssetFieldType.DatetimeSelect,
    name: '日期和时间',
    form: 'datetime-select',
    sql: 'datetime',
    hasPlaceholder: true,
    icon: CalendarDaysIcon,
  },
}

export const assetConfig = {
  [AssetType.NetworkDevice]: {
    value: AssetType.NetworkDevice,
    label: '网络设备',
  },
  [AssetType.Server]: {
    value: AssetType.Server,
    label: '终端设备',
  },
  [AssetType.Database]: {
    value: AssetType.Database,
    label: '数据库',
  },
  [AssetType.BusinessSystem]: {
    value: AssetType.BusinessSystem,
    label: '业务系统',
  },
  [AssetType.Other]: {
    value: AssetType.Other,
    label: '其他资产',
  },
} as const satisfies EnumConfig<AssetType, { label: string }>

type AssetDetailConfigType = {
  [K in AssetType]: {
    type: K
    label: string
    icon?: Component
  }
}

export const assetDetailConfig = {
  [AssetType.NetworkDevice]: {
    type: AssetType.NetworkDevice,
    label: 'IP 资产',
    icon: MapPinIcon,
  },
  [AssetType.Server]: {
    type: AssetType.Server,
    label: assetConfig[AssetType.Server].label,
    icon: ServerIcon,
  },
  [AssetType.Database]: {
    type: AssetType.Database,
    label: assetConfig[AssetType.Database].label,
    icon: DatabaseZapIcon,
  },
  [AssetType.BusinessSystem]: {
    type: AssetType.BusinessSystem,
    label: assetConfig[AssetType.BusinessSystem].label,
    icon: ComputerIcon,
  },
  [AssetType.Other]: {
    type: AssetType.Other,
    label: assetConfig[AssetType.Other].label,
    icon: MonitorCogIcon,
  },
} as const satisfies AssetDetailConfigType

export const enum TaskScanType {
  AssetScan,
  ComplianceScan,
  VulScan,
}

/** 任务扫描配置 */
export const taskScanConfig = {
  [TaskScanType.AssetScan]: {
    value: TaskScanType.AssetScan,
    label: '资产扫描',
    icon: PackageCheckIcon,
  },
  [TaskScanType.ComplianceScan]: {
    value: TaskScanType.ComplianceScan,
    label: '基线扫描',
    icon: ListCheckIcon,
  },
  [TaskScanType.VulScan]: {
    value: TaskScanType.VulScan,
    label: '漏洞扫描',
    icon: SearchCheckIcon,
  },
} satisfies EnumConfig<TaskScanType, { label: string, icon: Component }>

/** 子网 IP 状态 */
export const enum SubnetIpStatus {
  未知 = 1,
  在线 = 2,
  离线 = 3,
}

export const subnetIpStatusConfig = {
  [SubnetIpStatus.未知]: {
    value: SubnetIpStatus.未知,
    label: '未知',
    severity: 'secondary',
  },
  [SubnetIpStatus.在线]: {
    value: SubnetIpStatus.在线,
    label: '在线',
    severity: 'success',
  },
  [SubnetIpStatus.离线]: {
    value: SubnetIpStatus.离线,
    label: '离线',
    severity: 'warn',
  },
} satisfies EnumConfig<SubnetIpStatus>

/** 服务器端口状态 */
export const enum ServerPortStatus {
  未知 = 1,
  在线 = 2,
  离线 = 3,
}

export const serverPortStatusConfig = {
  [ServerPortStatus.未知]: {
    value: ServerPortStatus.未知,
    label: '未知',
    severity: 'secondary',
  },
  [ServerPortStatus.在线]: {
    value: ServerPortStatus.在线,
    label: '在线',
    severity: 'success',
  },
  [ServerPortStatus.离线]: {
    value: ServerPortStatus.离线,
    label: '离线',
    severity: 'warn',
  },
} satisfies EnumConfig<ServerPortStatus>

export enum TaskScheduleType {
  /** 时间间隔 */
  TimeInterval = 2,
  /** 计划时间 */
  PlanTime = 1,
  /** 定时时间 */
  TimingTime = 4,
}

export const enum TaskType {
  AssetScan = 1,
  BaselineScan = 2,
  VulScan = 3,
}

export const enum TaskStatus {
  Running = 1,
  Success = 2,
  Failed = 3,
}

export const taskStatusConfig = {
  [TaskStatus.Running]: {
    value: TaskStatus.Running,
    label: '执行中',
    severity: 'warn',
  },
  [TaskStatus.Success]: {
    value: TaskStatus.Success,
    label: '执行成功',
    severity: 'success',
  },
  [TaskStatus.Failed]: {
    value: TaskStatus.Failed,
    label: '执行失败',
    severity: 'danger',
  },
} satisfies EnumConfig<TaskStatus>

/** 资产重要程度 */
export const enum AssetCriticality {
  /** 一般 */
  Medium = '一般',
  /** 重要 */
  High = '重要',
  /** 非常重要 */
  Critical = '非常重要',
}

export const assetCriticalityConfig = {
  [AssetCriticality.Medium]: {
    value: AssetCriticality.Medium,
    label: '一般',
    severity: 'secondary',
    bgColor: $dt('slate.500').value || $dt('slate.500').variable,
  },
  [AssetCriticality.High]: {
    value: AssetCriticality.High,
    label: '重要',
    severity: 'info',
    bgColor: $dt('blue.500').value || $dt('blue.500').variable,
  },
  [AssetCriticality.Critical]: {
    value: AssetCriticality.Critical,
    label: '非常重要',
    severity: 'danger',
    bgColor: $dt('red.500').value || $dt('red.500').variable,
  },
} satisfies EnumConfig<AssetCriticality>

/** 协议 */
export const enum Protocol {
  SSH = 1,
  WMIC = 2,
}

/** 协议配置 */
export const protocolConfig = {
  [Protocol.SSH]: {
    value: Protocol.SSH,
    label: 'SSH',
  },
  [Protocol.WMIC]: {
    value: Protocol.WMIC,
    label: 'WMIC',
  },
} satisfies Record<Protocol, { label: string, value: Protocol }>

/** 网络拓扑节点类型 */
export const enum TopologyNodeType {
  /** 根节点 */
  Root = 0,
  /** 加载更多 */
  LoadMore = 999,

  /** 网络 */
  Network = 1,
  /** 网络设备 */
  NetworkDevice = 2,
  /** 服务器模板类型 */
  ServerTemplateType = 3,
  /** 服务器 */
  Server = 4,
  /** 服务器资产类型 */
  ServerAssetType = 5,
  /** 数据库 */
  Database = 6,
  /** Web资产 */
  Web = 7,
  /** 其他资产 */
  Other = 8,
}

/** 资产信息采集链接状态 */
export const enum AssetShareCollectionLinkStatus {
  /** 启用 */
  Enable = 1,
  /** 禁用 */
  Disable = 0,
}

/** 资产搜索类型 */
export const enum AssetSearchType {
  /** 网络设备 */
  NetworkDevice = 1,
  /** IP 资产 */
  Ip = 2,
  /** 服务器 */
  Server = 3,
  /** 数据库 */
  Database = 4,
  /** Web 资产 */
  Web = 5,
  /** 其他资产 */
  Other = 6,
}

export const assetSearchConfig = {
  [AssetSearchType.NetworkDevice]: {
    value: AssetSearchType.NetworkDevice,
    label: assetConfig[AssetType.NetworkDevice].label,
    icon: assetDetailConfig[AssetType.NetworkDevice].icon,
    assetType: AssetType.NetworkDevice,
  },
  [AssetSearchType.Ip]: {
    value: AssetSearchType.Ip,
    label: 'IP 资产',
    icon: assetDetailConfig[AssetType.NetworkDevice].icon,
    assetType: undefined,
  },
  [AssetSearchType.Server]: {
    value: AssetSearchType.Server,
    label: assetConfig[AssetType.Server].label,
    icon: assetDetailConfig[AssetType.Server].icon,
    assetType: AssetType.Server,
  },
  [AssetSearchType.Database]: {
    value: AssetSearchType.Database,
    label: assetConfig[AssetType.Database].label,
    icon: assetDetailConfig[AssetType.Database].icon,
    assetType: AssetType.Database,
  },
  [AssetSearchType.Web]: {
    value: AssetSearchType.Web,
    label: assetConfig[AssetType.BusinessSystem].label,
    icon: assetDetailConfig[AssetType.BusinessSystem].icon,
    assetType: AssetType.BusinessSystem,
  },
  [AssetSearchType.Other]: {
    value: AssetSearchType.Other,
    label: assetConfig[AssetType.Other].label,
    icon: assetDetailConfig[AssetType.Other].icon,
    assetType: AssetType.Other,
  },
} as const satisfies EnumConfig<AssetSearchType, { label: string, icon: Component, assetType: AssetType | undefined }>
