import type { AssetType } from '@/enums/asset'

export const queryKeys = {
  asset: {
    all: ['assets'],
    detail: (id: BasicAssetDetail['id']) => ['assets', 'detail', id],
    search: (query?: MaybeRefOrGetter<AnyType>) => ['asset', 'search', { query }],
    network: {
      all: () => ['asset', 'networks'],
    },
    networkDevice: {
      all: (networkId?: MaybeRefOrGetter<Network['id'] | undefined>) => ['asset', 'network-devices', networkId],
      detail: (id: MaybeRefOrGetter<NetworkDevice['id'] | undefined>) => ['asset', 'network-devices', 'detail', id],
      status: ['asset', 'network-device-status'],
      list: (assetType: MaybeRefOrGetter<AssetType>) => ['asset', 'network-device', 'list', assetType],
      ipLog: (query?: MaybeRefOrGetter<AnyType>) => ['asset', 'network-device', 'ip-log', { query }],
      linkInfo: (id: MaybeRefOrGetter<NetworkDevice['id'] | undefined>) => ['asset', 'network-device', 'link-info', id],
    },
    task: {
      all: () => ['asset', 'tasks'],
      executionList: (taskId: MaybeRefOrGetter<Task['id'] | undefined>) => ['asset', 'tasks', 'execution', taskId],
      executionLog: (logId: MaybeRefOrGetter<TaskExecutionLog['id'] | undefined>) => ['asset', 'tasks', 'execution-log', logId],
    },
    template: {
      all: (assetType?: MaybeRefOrGetter<AssetType>) => ['asset', 'templates', assetType],
      field: {
        all: (assetTemplateId?: MaybeRefOrGetter<AssetTemplate['id'] | undefined>) => ['asset', 'templates', 'fields', assetTemplateId],
      },
    },
    subnet: {
      all: (query?: MaybeRefOrGetter<AnyType>) => ['asset', 'subnets', { query }],
      ip: {
        all: (subnetId: MaybeRefOrGetter<Subnet['id'] | undefined>) => ['asset', 'subnets', 'ip', subnetId],
      },
    },
    server: {
      all: (
        params?:
        Partial<{ serverTemplateId: MaybeRefOrGetter<AssetTemplate['id'] | undefined>, networkId: MaybeRefOrGetter<Network['id'] | undefined> }>,
      ) => ['asset', 'servers', { filter: { template_id: params?.serverTemplateId, network_id: params?.networkId } }],
      detail: (assetType: MaybeRefOrGetter<AssetType | undefined>, assetId: MaybeRefOrGetter<Server['id'] | undefined>) => ['asset', 'servers', 'detail', assetType, assetId],
      port: {
        all: (
          params?:
          Partial<{ serverId: MaybeRefOrGetter<Server['id'] | undefined>, port: MaybeRefOrGetter<string | undefined> }>,
        ) => ['asset', 'servers', 'port', { filter: { server_id: params?.serverId, port: params?.port } }],
      },
      config: {
        all: (id?: MaybeRefOrGetter<Server['id'] | undefined>) => ['asset', 'servers', 'config', id],
      },
    },
    topo: {
      all: (query?: MaybeRefOrGetter<AnyType>) => ['topology', { query }],
    },
  },

  security: {
    vulLib: {
      all: (params?: Partial<{ query: MaybeRefOrGetter<AnyType> }>) => ['security', 'vul-lib', { query: params?.query }],
      detail: (id: MaybeRefOrGetter<SecurityAlert['cnnvd_id'] | undefined>) => ['security', 'vul-lib', id],
      stats: ['security', 'vul-lib', 'stats'],
      type: ['security', 'vul-lib', 'type'],
      vendor: ['security', 'vul-lib', 'vendors'],
    },
    vul: {
      all: ['security', 'vul'],
      detail: (id: MaybeRefOrGetter<Vul['id'] | undefined>) => ['security', 'vul', id],
      stats: ['security', 'vul', 'stats'],
    },
    compliance: {
      all: () => ['security', 'compliances'],
      templates: () => ['security', 'compliance', 'templates'],
      detail: (id: MaybeRefOrGetter<ComplianceCheck['id'] | undefined>) => ['security', 'compliance', id],
      result: (id: MaybeRefOrGetter<ComplianceCheck['id'] | undefined>) => ['security', 'compliance', 'result', id],
      option: {
        all: (id?: MaybeRefOrGetter<BaselineTplListItem['id'] | undefined>) => ['security', 'compliance', 'option', id],
        result: (id: MaybeRefOrGetter<ComplianceResult['id'] | undefined>) => ['security', 'compliance', 'option', 'result', id],
      },
      systemPolicy: {
        all: () => ['security', 'compliance', 'system-policy'],
        options: (id?: MaybeRefOrGetter<BaselinePolicyOption['id'] | undefined>) => ['security', 'compliance', 'system-policy', 'options', id],
      },
    },
  },

  tickets: {
    all: ['tickets'],
    detail: (id: MaybeRefOrGetter<Ticket['id'] | undefined>) => ['tickets', 'detail', id],
    assigned: ['tickets', 'assigned'],
    comments: (id: MaybeRefOrGetter<Ticket['id'] | undefined>) => ['tickets', 'comments', id],
  },

  stats: {
    asset: {
      count: ['stats', 'asset', 'count'],
      ip: {
        change: ['stats', 'ip', 'change'],
        log: ['stats', 'ip', 'log'],
        online: (networkId: MaybeRefOrGetter<Network['id'] | undefined>, networkDeviceId: MaybeRefOrGetter<NetworkDevice['id'] | undefined>) => ['stats', 'ip', 'online', { filter: { network_id: networkId, network_device_id: networkDeviceId } }],
        location: (networkId: MaybeRefOrGetter<Network['id'] | undefined>, networkDeviceId: MaybeRefOrGetter<NetworkDevice['id'] | undefined>) => ['stats', 'ip', 'location', { filter: { network_id: networkId, network_device_id: networkDeviceId } }],
      },
      importance: (assetType: MaybeRefOrGetter<AssetType | undefined>) => ['stats', 'asset', 'importance', assetType],
    },
    compliance: {
      count: ['stats', 'compliance', 'count'],
    },
    ticket: {
      count: ['stats', 'ticket', 'count'],
      grade: ['stats', 'ticket', 'grade'],
      log: ['stats', 'ticket', 'log'],
      status: ['stats', 'ticket', 'status'],
    },
    vul: {
      level: ['stats', 'vul', 'level'],
      status: ['stats', 'vul', 'status'],
      type: ['stats', 'vul', 'type'],
      vendor: ['stats', 'vul', 'vendor'],
      lib: {
        level: (vendorId: MaybeRefOrGetter<SecurityAlert['vendor_id'] | undefined>, vulTypeId: MaybeRefOrGetter<SecurityAlert['vul_type_id'] | undefined>) => ['stats', 'vul', 'lib', 'level', { filter: { vendor_id: vendorId, vul_type_id: vulTypeId } }],
      },
    },
  },

  user: {
    all: ['users'],
    detail: (id: MaybeRefOrGetter<User['id'] | undefined>) => ['users', 'detail', id],
    navMenu: ['user-nav-menu-list'],
    permission: {
      all: () => ['users', 'permissions'],
    },
  },

  auth: {
    license: ['auth', 'license'],
  },

  role: {
    all: () => ['roles'],
  },
} as const
