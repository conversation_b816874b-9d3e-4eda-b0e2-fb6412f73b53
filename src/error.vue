<script setup lang="ts">
import { consola } from 'consola'
import { XIcon } from 'lucide-vue-next'

import type { NuxtError } from '#app'

import { ApiStatus } from '~/enums/common'
import { RouteKey } from '~/enums/route'

const props = defineProps<{
  error: NuxtError<CustomErrorData>
}>()

const errorStatusCode = computed(() => {
  const cause = props.error.cause

  if (typeof cause === 'object' && cause !== null && 'statusCode' in cause) {
    return cause.statusCode
  }

  return null
})

watch(() => props.error, (newVal) => {
  consola.error('网页发生错误：', { ...newVal })
}, { immediate: true })

const handleUploadSuccess = () => {
  clearError()
  reloadNuxtApp()
}

const goBack = () => {
  clearError({ redirect: getRoutePath(RouteKey.首页) })
}

const handleCloseErrorPage = () => {
  goBack()
}
</script>

<template>
  <div class="flex h-screen flex-col bg-content">
    <div class="p-4 flex-center">
      <div class="ml-auto">
        <Button
          size="small"
          variant="text"
          @click="handleCloseErrorPage()"
        >
          <template #icon>
            <XIcon :size="16" />
          </template>
        </Button>
      </div>
    </div>

    <div class="flex-1 justify-center overflow-auto p-8 flex-center">
      <div
        v-if="errorStatusCode === ApiStatus.LinkExpiredOrInvalid"
        class="w-full max-w-md flex-col gap-8 flex-center"
      >
        <div class="flex-col gap-10 flex-center">
          <ErrorTitle title="页面链接已过期或无效" />

          <div class="flex-col gap-2 flex-center">
            <Message
              severity="secondary"
              variant="simple"
            >
              抱歉，您要访问的页面页面链接已过期或无效。
            </Message>

            <Message
              severity="secondary"
              variant="simple"
            >
              不用担心，这可能是因为 URL 输入错误或页面已过期。
            </Message>
          </div>
        </div>
      </div>

      <div
        v-else-if="errorStatusCode === ApiStatus.Unauthorized"
        class="w-full max-w-md flex-col gap-8 flex-center"
      >
        <div class="flex-col gap-10 flex-center">
          <ErrorTitle title="无权限访问" />

          <Message
            severity="secondary"
            variant="simple"
          >
            您暂无权限访问此页面，请联系管理员获取权限。
          </Message>

          <div class="flex gap-3">
            <Button
              label="浏览其他页面"
              variant="outlined"
              @click="goBack()"
            />
            <Button
              label="重新登录"
              @click="handleLogout()"
            />
          </div>
        </div>
      </div>

      <div
        v-else-if="errorStatusCode === ApiStatus.LicenseInvalid"
        class="w-full max-w-md flex-col gap-8 flex-center"
      >
        <div class="flex-col gap-10 flex-center">
          <ErrorTitle title="许可证已失效" />

          <div class="flex-col gap-5 flex-center">
            <Message
              severity="secondary"
              variant="simple"
            >
              当前许可证已失效，需要更新才能继续使用系统。
            </Message>

            <div class="flex-col gap-2 text-surface-500">
              <p>您可以：</p>
              <ul class="list-inside list-disc">
                <li>点击下方按钮上传新的许可证文件</li>
                <li>联系系统管理员获取新的许可证</li>
              </ul>
            </div>

            <div class="gap-3 pt-3 flex-center">
              <Button
                label="返回"
                variant="outlined"
                @click="clearError()"
              />
              <LicenseUpload @uploadSuccess="handleUploadSuccess()" />
            </div>
          </div>
        </div>
      </div>

      <div
        v-else-if="errorStatusCode === ApiStatus.MissingEnvVars"
        class="w-full max-w-md flex-col gap-8 flex-center"
      >
        <div class="flex-col gap-10 flex-center">
          <ErrorTitle title="环境变量配置不完整" />

          <div class="flex-col gap-5 flex-center">
            <Message
              severity="warning"
              variant="simple"
            >
              系统检测到缺少必要的环境变量，无法正常启动。
            </Message>

            <div class="flex-col gap-2 text-secondary">
              <p>可能缺少以下环境变量：</p>
              <ul class="list-inside list-disc">
                <li>NUXT_PUBLIC_RELEASE_ENV（发布环境标识）</li>
                <li>NUXT_PUBLIC_SITE_NAME（站点名称）</li>
                <li>NUXT_PUBLIC_API_HOST（接口服务地址）</li>
              </ul>
            </div>

            <div class="flex-col gap-3 pt-3 flex-center">
              <p class="text-sm text-secondary">
                请检查你的 .env 文件并确保所有必要的环境变量已正确设置。
              </p>
              <Button
                label="刷新页面"
                @click="reloadNuxtApp()"
              />
            </div>
          </div>
        </div>
      </div>

      <div v-else>
        <ErrorTitle :title="error.message || '发生未知错误'" />

        <p class="mt-5 text-center">
          如果你不清楚发生了什么，请联系管理员处理
        </p>
      </div>
    </div>
  </div>
</template>
