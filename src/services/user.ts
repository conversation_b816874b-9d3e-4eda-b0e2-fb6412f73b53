import type { UserStatus } from '~/enums/user'

export const UserService = {
  /**
   * 获取图形验证码
   */
  getCaptcha() {
    return useRequest<Captcha>('/auth/image_code')
  },

  /**
   * 获取公钥
   */
  getPublicKey() {
    return useRequest<{ public_key: string }>('/auth/public_key')
  },

  /**
   * 登录
   */
  login(payload: LoginFormValues) {
    return useRequest<AuthResponse>('/auth/login', {
      method: 'POST',
      body: payload,
    })
  },

  /**
   * 使用企业微信授权码登录，使用第三方的授权码来换取用户的登录信息
   */
  loginWithCode(code: string) {
    return useRequest<AuthResponse>('/auth/wecom/login', {
      method: 'POST',
      body: { code },
    })
  },

  /**
   * 上传许可证
   */
  getLicense() {
    return useRequest<License>('/auth/license')
  },

  /**
   * 上传许可证
   */
  uploadLicense(payload: FormData) {
    return useRequest('/auth/license', {
      method: 'POST',
      body: payload,
    })
  },

  /**
   * 获取角色菜单列表
   */
  getRoleNavMenuList() {
    return useRequest<NavMenuItemWithId[]>('/permission/role/menu/')
  },

  /**
   * 获取角色列表
   */
  getRoleList(query?: WithPageQuery<Partial<RoleListItem>>) {
    return useRequest<PageResult<RoleListItem>>('/permission/role/', {
      query,
    })
  },

  /**
   * 获取角色详情
   */
  getRole(roleId: Role['id']) {
    return useRequest<Role>(`/permission/role/${roleId}`)
  },

  /**
   * 创建角色
   */
  createRole(payload: RoleFormValues) {
    return useRequest('/permission/role/', {
      method: 'POST',
      body: payload,
    })
  },

  /**
   * 更新角色
   */
  updateRole(roleId: Role['id'], payload: RoleFormValues) {
    return useRequest(`/permission/role/${roleId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  /**
   * 删除角色
   */
  deleteRole(roleId: Role['id']) {
    return useRequest(`/permission/role/${roleId}`, {
      method: 'DELETE',
    })
  },

  /**
   * 获取用户列表
   */
  getUserList(query?: WithPageQuery<Partial<Pick<User, 'username' | 'organize_id'> & { order_by?: string }>>) {
    return useRequest<PageResult<UserListItem>>('/permission/user/', {
      query,
    })
  },

  getUser(id: User['id']) {
    return useRequest<User>(`/permission/user/${id}`)
  },

  createUser(payload: UserFormValues & Pick<User, 'organize_id'>) {
    return useRequest('/permission/user/', {
      method: 'POST',
      body: payload,
    })
  },

  updateUser(userId: User['id'], payload: Partial<UserFormValues> | Partial<{ avatar: User['avatar'], status: UserStatus }>) {
    return useRequest(`/permission/user/${userId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  getCurrentUser() {
    return useRequest<CurrentUser>('/permission/user/current')
  },

  /** 更新当前用户的信息 */
  updateCurrentUser(payload: Partial<UserFormValues> | { avatar: User['avatar'] }) {
    return useRequest('/permission/user/current', {
      method: 'PUT',
      body: payload,
    })
  },

  resetUserPassword(userId: User['id'], payload: { password: LoginFormValues['password'] }) {
    return useRequest(`/permission/user/${userId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  resetPassword(payload: Pick<ResetPasswordFormValues, 'oldPassword' | 'newPassword'>) {
    return useRequest('/permission/user/reset_password', {
      method: 'PUT',
      body: {
        old_password: payload.oldPassword,
        new_password: payload.newPassword,
      },
    })
  },

  deleteUser(userId: User['id']) {
    return useRequest(`/permission/user/${userId}`, {
      method: 'DELETE',
    })
  },

  getUserGroups() {
    return useRequest<UserGroup[]>('/permission/organize/')
  },

  createUserGroup(payload: UserGroupFormValues) {
    return useRequest<User['avatar']>('/permission/organize/', {
      method: 'POST',
      body: payload,
    })
  },

  updateUserGroup(groupId: UserGroup['id'], payload: UserGroupFormValues) {
    return useRequest(`/permission/organize/${groupId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  deleteUserGroup(groupId: UserGroup['id']) {
    return useRequest(`/permission/organize/${groupId}`, {
      method: 'DELETE',
    })
  },

  uploadAvatar(payload: FormData) {
    return useRequest<User['avatar']>('/permission/user/upload_avatar', {
      method: 'POST',
      body: payload,
    })
  },
}
