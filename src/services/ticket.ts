export const TicketService = {
  /** 获取工单列表 */
  getTickets(query?: WithPageQuery<Partial<Pick<Ticket, 'name' | 'status' | 'object_type' | 'grade'>>>) {
    return useRequest<PageResult<TicketListItem>>(
      '/work_order/',
      {
        query,
      },
    )
  },

  /** 获取工单详情 */
  getTicket(ticketId: Ticket['id']) {
    return useRequest<Ticket>(`/work_order/${ticketId}`)
  },

  /** 创建工单 */
  createTicket(data: TicketFormUpdateValues) {
    return useRequest('/work_order/', {
      method: 'POST',
      body: data,
    })
  },

  /** 更新工单 */
  updateTicket(ticketId: Ticket['id'], data: Partial<TicketFormUpdateValues>) {
    return useRequest(`/work_order/${ticketId}`, {
      method: 'PUT',
      body: data,
    })
  },

  /** 获取当前用户的待办工单 */
  getAssignedTicketList(query?: WithPageQuery<
    Partial<
      Pick<
        Ticket,
        'grade' | 'status' | 'object_id' | 'object_type'
      >
    >
  >) {
    return useRequest<PageResult<TicketListItem>>(
      '/work_order/current/',
      {
        query,
      },
    )
  },

  /** 获取工单的评论 */
  getTicketComments(query?: WithPageQuery<{ work_order_id: Ticket['id'] }>) {
    return useRequest<TicketComment[]>(`/work_order/comment/`, {
      query,
    })
  },

  /** 创建工单评论 */
  createTicketComment(data: TicketCommentFormValues) {
    return useRequest('/work_order/comment/', {
      method: 'POST',
      body: data,
    })
  },

  /** 更新工单评论 */
  updateTicketComment(commentId: TicketComment['id'], data: TicketCommentFormValues) {
    return useRequest(`/work_order/comment/${commentId}`, {
      method: 'PUT',
      body: data,
    })
  },
}
