export const SecurityService = {
  /** 获取合规检查列表 */
  getComplianceList(query?: WithPageQuery<{ order_by?: string }>) {
    return useRequest<PageResult<ComplianceListItem>>('/security/baseline/', {
      query,
    })
  },

  /** 获取合规检查详情 */
  getComplianceDetail(complianceId: ComplianceCheck['id']) {
    return useRequest<ComplianceCheck>(`/security/baseline/${complianceId}`)
  },

  /** 获取合规检查扫描结果列表 */
  getComplianceCheckResultList(complianceId: ComplianceCheck['id']) {
    return useRequest<PageResult<ComplianceResult>>('/security/baseline/option/', {
      query: {
        baseline_id: complianceId,
      },
    })
  },

  /** 获取合规检查扫描结果 */
  getComplianceCheckOptionResult(optionId: ComplianceResult['id']) {
    return useRequest<ComplianceOptionResult>(`/config/baseline_template/system/option/${optionId}`)
  },

  /** 获取漏洞列表 */
  getVulList(query?: WithPageQuery<AnyType>) {
    return useRequest<PageResult<Vul>>('/security/vul/', {
      query,
    })
  },

  /** 获取漏洞详情 */
  getVulDetail(vulId: Vul['id']) {
    return useRequest<Vul>(`/security/vul/${vulId}`)
  },

  /** 获取漏洞库列表 */
  getVulLibList(query?: VulLibListQuery) {
    return useRequest<PageResult<SecurityAlert>>('/security/vul_lib/', {
      query,
    })
  },

  /** 获取漏洞库记录的漏洞的详情 */
  getVulLibDetail(cveKey: CveKey) {
    return useRequest<SecurityAlert>(`/security/vul_lib/${cveKey}`)
  },

  /** 获取厂商列表 */
  getVendorList(query?: WithPageQuery<{ name?: SecurityAlert['vendor_name'] }>) {
    return useRequest<PageResult<VendorListItem>>('/security/vul_lib/vendor/', {
      query,
    })
  },

  /** 获取漏洞类型 */
  getVulTypes() {
    return useRequest<VulTypeListItem[]>('/security/vul_lib/type/')
  },
}
