import type { AssetType } from '~/enums/asset'

export const StatsService = {
  /** 获取资产统计 */
  getAssetCountStats() {
    return useRequest<AssetCountStats>('/overview/asset/count/')
  },

  /** 获取资产重要性统计 */
  getAssetImportanceStats(query?: { asset_type?: AssetType }) {
    return useRequest<AssetImportanceStats[]>('/overview/asset/grade/', {
      query,
    })
  },

  /** 获取资产 IP 变更统计 */
  getAssetIpLogStats() {
    return useRequest<AssetIpLogStats[]>('/overview/asset/ip_log/')
  },

  /** 获取 IP 在线统计 */
  getIpOnlineStats(query?: { network_id?: Network['id'], network_device_id?: NetworkDevice['id'] }) {
    return useRequest<IpOnlineStats[]>('/overview/asset/ip_status/', {
      query,
    })
  },

  /** 获取 IP 位置统计 */
  getIpLocationStats(query?: { network_id?: Network['id'], network_device_id?: NetworkDevice['id'] }) {
    return useRequest<IpLocationStats[]>('/overview/asset/ip/', {
      query,
    })
  },

  /** 获取 IP 资产变化统计 */
  getIpAssetChangeStats() {
    return useRequest<IpAssetChangeStats[]>('/overview/asset/asset/')
  },

  /** 获取漏洞库的等级统计 */
  getVulLibLevelStats(query?: VulLevelQuery) {
    return useRequest<{
      total: number
      list: VulLevelStats[]
    }>('/overview/vul_lib/level/', {
      query,
    })
  },

  /** 获取漏洞库的类型统计 */
  getVulTypeStats() {
    return useRequest<VulTypeStats[]>('/overview/vul_lib/vul_type/')
  },

  /** 获取厂商公布的漏洞统计 */
  getVendorVulStats(query?: Partial<Pick<SecurityAlert, 'level' | 'vul_type_id'>>) {
    return useRequest<PageResult<VendorVulStats>>('/overview/vul_lib/vendor/', {
      query,
    })
  },

  /** 获取漏洞状态统计 */
  getVulStatusStats() {
    return useRequest<VulCountStats>('/overview/vul/count/')
  },

  /** 获取本系统内的漏洞等级统计 */
  getVulLevelStats() {
    return useRequest<{
      total: number
      list: VulLevelStats[]
    }>('/overview/vul/level/')
  },

  /** 获取合规检查项状态统计 */
  getComplianceCheckCountStats() {
    return useRequest<ComplianceCheckCountStats>('/overview/baseline/count/')
  },

  /** 获取工单数量统计 */
  getTicketCountStats() {
    return useRequest<TicketCountStats>('/overview/work_order/count/')
  },

  /** 获取工单等级统计 */
  getTicketGradeStats() {
    return useRequest<TicketGradeLevelStats[]>('/overview/work_order/grade/')
  },

  /** 获取工单状态统计 */
  getTicketStatusStats() {
    return useRequest<TicketStatusStats[]>('/overview/work_order/status/')
  },

  /** 获取工单变更记录 */
  getTicketLog() {
    return useRequest<TicketLog[]>('/overview/work_order/log/')
  },
}
