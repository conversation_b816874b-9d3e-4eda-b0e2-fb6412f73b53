export const ConfigService = {
  getBaselineTplList(query?: WithPageQuery<Pick<BaselineTpl, 'system_id'>>) {
    return useRequest<PageResult<BaselineTplListItem>>(
      '/config/baseline_template/',
      {
        query,
      },
    )
  },

  createBaselineTpl(data: BaselineFormValues) {
    return useRequest(`/config/baseline_template/`, {
      method: 'POST',
      body: data,
    })
  },

  updateBaselineTpl(baselineId: BaselineTpl['id'], data: BaselineFormValues) {
    return useRequest(`/config/baseline_template/${baselineId}`, {
      method: 'PUT',
      body: data,
    })
  },

  deleteBaselineTpl(baselineId: BaselineTpl['id']) {
    return useRequest(`/config/baseline_template/${baselineId}`, {
      method: 'DELETE',
    })
  },

  getBaselineSystemList(query?: WithPageQuery) {
    return useRequest<PageResult<BaselineSystem>>(`/config/baseline_template/system/`, {
      method: 'GET',
      query,
    })
  },

  /** 获取基线策略的系统检查项 */
  getBaselinePolicyOptions(policyId: BaselineSystem['id'], query?: WithPageQuery<UnsafeAny>) {
    return useRequest<PageResult<BaselinePolicyOptionListItem>>(`/config/baseline_template/system/option/`, {
      method: 'GET',
      query: {
        ...query,
        system_id: policyId,
      },
    })
  },

  /** 获取基线策略的系统检查项详情 */
  getBaselinePolicyOption(policyId: BaselinePolicyOption['id']) {
    return useRequest<BaselinePolicyOption>(`/config/baseline_template/system/option/${policyId}`, {
      method: 'GET',
    })
  },

  /** 创建基线策略的系统检查项 */
  createBaselinePolicyOption(data: BaselinePolicyFormValues) {
    return useRequest('/config/baseline_template/system/option/', {
      method: 'POST',
      body: data,
    })
  },

  /** 更新基线策略的系统检查项 */
  updateBaselinePolicyOption(optionId: BaselinePolicyOption['id'], data: BaselinePolicyFormValues) {
    return useRequest(`/config/baseline_template/system/option/${optionId}`, {
      method: 'PUT',
      body: data,
    })
  },

  /** 删除基线策略的系统检查项 */
  deleteBaselinePolicyOption(optionId: BaselinePolicyOption['id']) {
    return useRequest(`/config/baseline_template/system/option/${optionId}`, {
      method: 'DELETE',
    })
  },

  /** 获取基线模板检查项 */
  getBaselineSystemOptions(query?: WithPageQuery<{ template_baseline_id?: BaselineTplListItem['id'] }>) {
    return useRequest<PageResult<BaselineOption>>(`/config/baseline_template/option/`, {
      method: 'GET',
      query,
    })
  },

  /** 创建基线模板检查项 */
  createSystemOption(baselineTplId: BaselineTplListItem['id'], data: SystemOptionFormValues) {
    return useRequest('/config/baseline_template/option/', {
      method: 'POST',
      body: {
        ...data,
        template_baseline_id: baselineTplId,
      },
    })
  },

  /** 删除基线模板检查项 */
  deleteSystemOption(optionId: BaselineOption['id']) {
    return useRequest(`/config/baseline_template/option/${optionId}`, {
      method: 'DELETE',
    })
  },
}
