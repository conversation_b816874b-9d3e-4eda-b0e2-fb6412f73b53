import type { AssetSearchType, AssetType } from '~/enums/asset'
import { ApiStatus } from '~/enums/common'

export const AssetService = {
  // ============================== MARK: 资产模板

  /** 获取资产模板列表 */
  getAssetTemplateList: (query?: WithPageQuery<Partial<Pick<AssetTemplate, 'asset_type'>>>) => {
    return useRequest<PageResult<AssetTemplateListItem>>('/config/asset_template/', {
      query,
    })
  },

  /** 创建资产模板 */
  createAssetTemplate: (assetType: AssetTemplate['asset_type'], payload: AssetTemplateFormValues) => {
    return useRequest('/config/asset_template/', {
      method: 'POST',
      body: {
        ...payload,
        asset_type: assetType,
      },
    })
  },

  /** 更新资产模板 */
  updateAssetTemplate: (templateId: AssetTemplate['id'], payload: AssetTemplateFormValues) => {
    return useRequest(`/config/asset_template/${templateId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  /** 删除资产模板 */
  deleteAssetTemplate: (templateId: AssetTemplate['id']) => {
    return useRequest(`/config/asset_template/${templateId}`, {
      method: 'DELETE',
    })
  },

  /** 获取资产模板字段列表 */
  getAssetTemplateFieldList: (query?: { template_id?: AssetTemplate['id'] }) => {
    return useRequest<AssetField[]>('/config/asset_template/field/', {
      query,
    })
  },

  createAssetTemplateField: (payload: AssetFieldFormValues & Pick<AssetField, 'template_id'>) => {
    return useRequest(`/config/asset_template/field/`, {
      method: 'POST',
      body: payload,
    })
  },

  updateAssetTemplateField: (fieldId: AssetField['id'], payload: AssetFieldFormValues) => {
    return useRequest(`/config/asset_template/field/${fieldId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  deleteAssetTemplateField: (fieldId: AssetField['id']) => {
    return useRequest(`/config/asset_template/field/${fieldId}`, {
      method: 'DELETE',
    })
  },

  /** 获取资产模板的所有字段类型 */
  getAssetFieldTypeList: () => {
    return useRequest<AssetField['field_type'][]>('/config/asset_template/field_type/')
  },

  // ------------------------------ MARK: 资产模板

  // ============================== MARK: 资产管理

  getNetworkList: () => {
    return useRequest<Network[]>('/asset/network/')
  },

  createNetwork: (payload: NetworkFormValues) => {
    return useRequest('/asset/network/', {
      method: 'POST',
      body: payload,
    })
  },

  updateNetwork: (networkId: Network['id'], payload: NetworkFormValues) => {
    return useRequest(`/asset/network/${networkId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  deleteNetwork: (networkId: Network['id']) => {
    return useRequest(`/asset/network/${networkId}`, {
      method: 'DELETE',
    })
  },

  getNetDeviceList: (query?: WithPageQuery<{ network_id?: Network['id'] }>) => {
    return useRequest<PageResult<NetworkDevice>>('/asset/network_equipment/', {
      query,
    })
  },

  getNetDeviceDetail: (netDeviceId: NetworkDevice['id']) => {
    return useRequest<NetworkDevice>(`/asset/network_equipment/${netDeviceId}`)
  },

  createNetDevice: (payload: NetworkDeviceFormValues) => {
    return useRequest('/asset/network_equipment/', {
      method: 'POST',
      body: payload,
    })
  },

  updateNetDevice: (netDeviceId: NetworkDevice['id'], payload: NetworkDeviceFormValues) => {
    return useRequest(`/asset/network_equipment/${netDeviceId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  deleteNetDevice: (netDeviceId: NetworkDevice['id']) => {
    return useRequest(`/asset/network_equipment/${netDeviceId}`, {
      method: 'DELETE',
    })
  },

  // ------------------------------ MARK: 资产管理

  // ============================== MARK: 子网

  getSubnetList: (query?: WithPageQuery<Partial<Pick<Subnet, 'network_equipment_id' | 'ip_segment'>>>) => {
    return useRequest<PageResult<Subnet>>('/asset/network_equipment/subnet/', {
      query,
    })
  },

  createSubnet: (payload: SubnetFormValues) => {
    return useRequest('/asset/network_equipment/subnet/', {
      method: 'POST',
      body: payload,
    })
  },

  updateSubnet: (subnetId: Subnet['id'], payload: SubnetFormValues) => {
    return useRequest(`/asset/network_equipment/subnet/${subnetId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  deleteSubnet: (subnetId: Subnet['id']) => {
    return useRequest(`/asset/network_equipment/subnet/${subnetId}`, {
      method: 'DELETE',
    })
  },

  // ------------------------------ MARK: 子网

  // ============================== MARK: 子网 IP

  getSubnetIpList: (query?: SubnetIpListQuery) => {
    return useRequest<PageResult<SubnetIp>>('/asset/network_equipment/ip/', {
      query,
    })
  },

  getSubnetIpDetail: (subnetIpId: SubnetIp['id']) => {
    return useRequest<SubnetIp>(`/asset/network_equipment/ip/${subnetIpId}`)
  },

  createSubnetIp: (subnetId: Subnet['id'], payload: SubnetIpFormValues) => {
    return useRequest('/asset/network_equipment/ip/', {
      method: 'POST',
      body: {
        ...payload,
        subnet_id: subnetId,
      },
    })
  },

  updateSubnetIp: (subnetIpId: SubnetIp['id'], payload: SubnetIpFormValues) => {
    return useRequest(`/asset/network_equipment/ip/${subnetIpId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  deleteSubnetIp: (subnetIpId: SubnetIp['id']) => {
    return useRequest(`/asset/network_equipment/ip/${subnetIpId}`, {
      method: 'DELETE',
    })
  },

  /** 获取 IP 变更记录 */
  getIpChangeRecord: (query?: WithPageQuery<{ ip_id?: SubnetIp['id'], asset_scan_task_id?: AssetScanTask['id'], ip?: string }>) => {
    return useRequest<PageResult<IpLog>>('/asset/network_equipment/ip/log/', {
      query,
    })
  },

  /** 获取网络设备的节点状态 */
  getNetworkDeviceStatus: (netDeviceId: NetworkDevice['id'], payload: { nodeUrl?: string }) => {
    return useRequest<RawResult>(`/asset/network_equipment/status/${netDeviceId}`, {
      method: 'POST',
      body: { node_url: payload.nodeUrl },
      ignoreResponseError: true,
      raw: true,
    })
  },

  // ------------------------------ MARK: 子网

  // ============================== MARK: 服务器

  getServerList: (query?: WithPageQuery<Pick<Server, 'network_equipment_id' | 'template_id'> & { search?: string }>) => {
    return useRequest<PageResult<ServerListItem>>('/asset/server/', {
      query,
    })
  },

  /** 获取服务器详情 */
  getServerDetail: (serverId: Server['id']) => {
    return useRequest<ServerDetail>(`/asset/server/${serverId}`)
  },

  createServer: (networkDeviceId: NetworkDevice['id'], payload: ServerFormValues) => {
    return useRequest('/asset/server/', {
      method: 'POST',
      body: {
        ...payload,
        network_equipment_id: networkDeviceId,
      },
    })
  },

  updateServer: (networkDeviceId: NetworkDevice['id'], serverId: Server['id'], payload: ServerFormValues) => {
    return useRequest(`/asset/server/${serverId}`, {
      method: 'PUT',
      body: {
        ...payload,
        network_equipment_id: networkDeviceId,
      },
    })
  },

  deleteServer: (serverId: Server['id']) => {
    return useRequest(`/asset/server/${serverId}`, {
      method: 'DELETE',
    })
  },

  getServerConfig: (serverId: Server['id']) => {
    return useRequest<ServerConfig>(`/asset/server/config/`, {
      query: {
        server_id: serverId,
      },
    })
  },

  createServerConfig: (serverId: Server['id'], payload: ServerConfigFormValues) => {
    return useRequest('/asset/server/config/', {
      method: 'POST',
      body: {
        ...payload,
        server_id: serverId,
      },
    })
  },

  updateServerConfig: (serverConfigId: ServerConfig['id'], payload: Partial<ServerConfigFormValues>) => {
    return useRequest(`/asset/server/config/${serverConfigId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  getServerPortList: (query?: WithPageQuery<{ server_id?: Server['id'], port?: string }>) => {
    return useRequest<PageResult<ServerPort>>('/asset/server/port/', {
      query,
    })
  },

  createServerPort: (serverId: Server['id'], payload: ServerPortFormValues) => {
    return useRequest('/asset/server/port/', {
      method: 'POST',
      body: {
        ...payload,
        server_id: serverId,
      },
    })
  },

  updateServerPort: (serverId: Server['id'], serverPortId: ServerPort['id'], payload: ServerPortFormValues) => {
    return useRequest(`/asset/server/port/${serverPortId}`, {
      method: 'PUT',
      body: {
        ...payload,
        server_id: serverId,
      },
    })
  },

  deleteServerPort: (serverPortId: ServerPort['id']) => {
    return useRequest(`/asset/server/port/${serverPortId}`, {
      method: 'DELETE',
    })
  },

  // ------------------------------ MARK: 服务器

  // ============================== MARK: 数据库

  getDatabaseList: (query?: WithPageQuery<Pick<Database, 'network_equipment_id' | 'template_id'>>) => {
    return useRequest<PageResult<Database>>('/asset/database/', {
      query,
    })
  },

  getDatabaseDetail: (databaseId: Database['id']) => {
    return useRequest<Database>(`/asset/database/${databaseId}`)
  },

  createDatabase: (networkDeviceId: NetworkDevice['id'], payload: DatabaseFormValues) => {
    return useRequest('/asset/database/', {
      method: 'POST',
      body: {
        ...payload,
        network_equipment_id: networkDeviceId,
      },
    })
  },

  updateDatabase: (networkDeviceId: NetworkDevice['id'], databaseId: Database['id'], payload: Omit<DatabaseFormValues, 'template_id'>) => {
    return useRequest(`/asset/database/${databaseId}`, {
      method: 'PUT',
      body: {
        ...payload,
        network_equipment_id: networkDeviceId,
      },
    })
  },

  deleteDatabase: (databaseId: Database['id']) => {
    return useRequest(`/asset/database/${databaseId}`, {
      method: 'DELETE',
    })
  },

  // ------------------------------ MARK: 数据库

  // ============================== MARK: 业务系统

  getBusinessSystemList: (query?: WithPageQuery<Pick<BusinessSystem, 'network_equipment_id' | 'template_id'>>) => {
    return useRequest<PageResult<BusinessSystem>>('/asset/web/', {
      query,
    })
  },

  getBusinessSystemDetail: (businessSystemId: BusinessSystem['id']) => {
    return useRequest<BusinessSystem>(`/asset/web/${businessSystemId}`)
  },

  createBusinessSystem: (networkDeviceId: NetworkDevice['id'], payload: BusinessSystemFormValues) => {
    return useRequest('/asset/web/', {
      method: 'POST',
      body: {
        ...payload,
        network_equipment_id: networkDeviceId,
      },
    })
  },

  updateBusinessSystem: (networkDeviceId: NetworkDevice['id'], businessSystemId: BusinessSystem['id'], payload: BusinessSystemFormValues) => {
    return useRequest(`/asset/web/${businessSystemId}`, {
      method: 'PUT',
      body: {
        ...payload,
        network_equipment_id: networkDeviceId,
      },
    })
  },

  deleteBusinessSystem: (businessSystemId: BusinessSystem['id']) => {
    return useRequest(`/asset/web/${businessSystemId}`, {
      method: 'DELETE',
    })
  },

  // ------------------------------ MARK: 业务系统

  // ============================== MARK: 其他资产

  getOtherAssetList: (query?: WithPageQuery<Pick<OtherAsset, 'network_equipment_id' | 'template_id'>>) => {
    return useRequest<PageResult<OtherAsset>>('/asset/other/', {
      query,
    })
  },

  getOtherAssetDetail: (otherAssetId: OtherAsset['id']) => {
    return useRequest<OtherAsset>(`/asset/other/${otherAssetId}`)
  },

  createOtherAsset: (networkDeviceId: NetworkDevice['id'], payload: OtherAssetFormValues) => {
    return useRequest('/asset/other/', {
      method: 'POST',
      body: {
        ...payload,
        network_equipment_id: networkDeviceId,
      },
    })
  },

  updateOtherAsset: (networkDeviceId: NetworkDevice['id'], otherAssetId: OtherAsset['id'], payload: OtherAssetFormValues) => {
    return useRequest(`/asset/other/${otherAssetId}`, {
      method: 'PUT',
      body: {
        ...payload,
        network_equipment_id: networkDeviceId,
      },
    })
  },

  deleteOtherAsset: (otherAssetId: OtherAsset['id']) => {
    return useRequest(`/asset/other/${otherAssetId}`, {
      method: 'DELETE',
    })
  },

  // ------------------------------ MARK: 其他资产

  // ============================== MARK: 网络拓扑

  getTopology: (query?: SubNode) => {
    return useRequest<PageResult<Topology>>('/asset/topology/', {
      method: 'POST',
      body: query,
    })
  },

  // ------------------------------ MARK: 网络拓扑

  // ============================== MARK: 资产信息采集
  getAssetShareCollectionLinkInfo: (networkDeviceId: NetworkDevice['id']) => {
    return useRequest<AssetShareCollectionLinkInfo>(`/asset/link/${networkDeviceId}`)
  },

  updateAssetShareCollectionLinkInfo: (networkDeviceId: NetworkDevice['id'], payload: Partial<Omit<AssetShareCollectionLinkInfo, 'key' | 'network_equipment_id'>>) => {
    return useRequest(`/asset/link/${networkDeviceId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  getAssetCollectTemplates: (collectionKey: string, query?: WithPageQuery<Partial<Pick<AssetTemplate, 'asset_type'>>>) => {
    return useRequest<PageResult<AssetTemplateListItem>>(`/asset/fill/asset_template/`, {
      query: {
        ...query,
        key: collectionKey,
      },
    })
  },

  /** 获取资产采集字段列表 */
  getAssetCollectFieldList: (collectionKey: string, query?: { template_id?: AssetTemplate['id'] }) => {
    return useRequest<AssetField[]>('/asset/fill/asset_template/field/', {
      query: {
        ...query,
        key: collectionKey,
      },
    })
  },

  /** 获取资产采集服务器列表 */
  getAssetCollectServers: (collectionKey: string, query?: WithPageQuery<{ search?: string }>) => {
    return useRequest<PageResult<ServerListItem>>('/asset/fill/server/', {
      query: {
        ...query,
        key: collectionKey,
      },
    })
  },

  /** 更新资产采集服务器 */
  updateAssetCollectServer: (serverId: Server['id'], collectionKey: string, payload: ServerFormValues) => {
    return useRequest(`/asset/fill/server/${serverId}`, {
      method: 'PUT',
      body: {
        ...payload,
        key: collectionKey,
      },
    })
  },

  /** 获取资产采集数据库列表 */
  getAssetCollectDatabases: (collectionKey: string, query?: WithPageQuery<{ search?: string }>) => {
    return useRequest<PageResult<Database>>('/asset/fill/database/', {
      query: {
        ...query,
        key: collectionKey,
      },
    })
  },

  /** 更新资产采集数据库 */
  updateAssetCollectDatabase: (databaseId: Database['id'], collectionKey: string, payload: DatabaseFormValues) => {
    return useRequest(`/asset/fill/database/${databaseId}`, {
      method: 'PUT',
      body: {
        ...payload,
        key: collectionKey,
      },
    })
  },

  /** 获取资产采集业务系统列表 */
  getAssetCollectBusinessSystems: (collectionKey: string, query?: WithPageQuery<{ search?: string }>) => {
    return useRequest<PageResult<BusinessSystem>>('/asset/fill/web/', {
      query: {
        ...query,
        key: collectionKey,
      },
    })
  },

  /** 更新资产采集业务系统 */
  updateAssetCollectBusinessSystem: (businessSystemId: BusinessSystem['id'], collectionKey: string, payload: BusinessSystemFormValues) => {
    return useRequest(`/asset/fill/web/${businessSystemId}`, {
      method: 'PUT',
      body: {
        ...payload,
        key: collectionKey,
      },
    })
  },

  /** 获取资产采集其他资产列表 */
  getAssetCollectOtherAssets: (collectionKey: string, query?: WithPageQuery<{ search?: string }>) => {
    return useRequest<PageResult<OtherAsset>>('/asset/fill/other/', {
      query: {
        ...query,
        key: collectionKey,
      },
    })
  },

  /** 更新资产采集其他资产 */
  updateAssetCollectOtherAsset: (otherAssetId: OtherAsset['id'], collectionKey: string, payload: OtherAssetFormValues) => {
    return useRequest(`/asset/fill/other/${otherAssetId}`, {
      method: 'PUT',
      body: {
        ...payload,
        key: collectionKey,
      },
    })
  },

  // ------------------------------ MARK: 资产信息采集

  // ============================== MARK: 资产·搜索

  getAssetSearch: (query?: WithPageQuery<{ network_equipment_id?: NetworkDevice['id'], search?: string, type?: AssetSearchType }>) => {
    return useRequest<AssetSearch>('/asset/search/', {
      query,
    })
  },

  // ------------------------------ MARK: 资产搜索

  /** 导出资产 */
  exportAssetTemplate: (query?: {
    network_equipment_id?: NetworkDevice['id']
    asset_type?: AssetType
  }) => {
    return useRequest<Blob>('/asset/export/', {
      query,
      responseAdapter: (data) => {
        return {
          status: ApiStatus.Success,
          message: '导出成功',
          result: data,
        }
      },
    })
  },

  /** 导入资产 */
  importAsset: (payload: FormData) => {
    return useRequest<AssetImportResult>('/asset/import/', {
      method: 'POST',
      body: payload,
    })
  },
}
