import type { TaskType } from '~/enums/asset'

export const TaskService = {
  /** 获取任务列表 */
  getTaskList(query?: WithPageQuery<{ name?: string }>) {
    return useRequest<PageResult<TaskListItem>>('/task/', {
      method: 'GET',
      query,
    })
  },

  /** 创建资产扫描任务 */
  createAssetScanTask(netDeviceId: NetworkDevice['id'], payload: TaskScanFormValues) {
    return useRequest(
      '/task/asset_scan/',
      {
        method: 'POST',
        body: {
          ...payload,
          network_equipment_id: netDeviceId,
        },
      },
    )
  },

  /** 创建合规检查扫描任务 */
  createComplianceScanTask(netDeviceId: NetworkDevice['id'], payload: TaskScanFormValues) {
    return useRequest(
      '/task/baseline_scan/',
      {
        method: 'POST',
        body: {
          ...payload,
          network_equipment_id: netDeviceId,
        },
      },
    )
  },

  /** 创建漏洞扫描任务 */
  createVulnerabilityScanTask(netDeviceId: NetworkDevice['id'], payload: TaskScanFormValues) {
    return useRequest(
      '/task/vul_scan/',
      {
        method: 'POST',
        body: {
          ...payload,
          network_equipment_id: netDeviceId,
        },
      },
    )
  },

  /** 更新扫描任务 */
  updateScanTask(taskId: Task['id'], payload: TaskScanFormValues) {
    return useRequest(`/task/${taskId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  /** 删除任务 */
  deleteTask(taskId: Task['id']) {
    return useRequest(`/task/${taskId}`, {
      method: 'DELETE',
    })
  },

  /** 执行任务 */
  executeTask(taskId: Task['id']) {
    return useRequest<RawResult>(`/task/start/${taskId}`, {
      method: 'POST',
      raw: true,
    })
  },

  /** 获取任务执行记录 */
  getTaskExecutionList(query?: WithPageQuery<{ task_id?: Task['id'], task_type?: TaskType }>) {
    return useRequest<PageResult<TaskExecutionLog>>('/task/log/', {
      method: 'GET',
      query: query,
    })
  },

  /** 获取任务执行日志 */
  getTaskExecutionLog(logId: TaskExecutionLog['id']) {
    return useRequest<TaskExecutionLog>(`/task/log/${logId}`, {
      method: 'GET',
    })
  },
}
