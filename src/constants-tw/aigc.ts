import {
  BookIcon,
  CheckCircleIcon,
  ExpandIcon,
  FilterIcon,
  FlameIcon,
  PaletteIcon,
  ShrinkIcon,
  SnowflakeIcon,
  ThermometerIcon,
  ZapIcon,
} from 'lucide-vue-next'

/** 文本改写风格选项 */
export const styleOptions = [
  {
    label: '标准',
    value: 'standard',
    icon: CheckCircleIcon,
    prompt: '以标准自然的风格改写这段文字，保持原意但表达方式更加流畅',
  },
  {
    label: '学术',
    value: 'academic',
    icon: BookIcon,
    prompt: '以学术风格改写这段文字，使用更专业的词汇和句式，增加学术性',
  },
  {
    label: '简洁',
    value: 'simple',
    icon: FilterIcon,
    prompt: '以简洁明了的风格改写这段文字，减少冗余，保持简洁清晰',
  },
  {
    label: '流畅',
    value: 'creative',
    icon: ZapIcon,
    prompt: '以流畅生动的风格改写这段文字，增加表达的流畅性和创意性',
  },
  {
    label: '随意',
    value: 'casual',
    icon: PaletteIcon,
    prompt: '以随意轻松的风格改写这段文字，使用更口语化的表达，像朋友间交流',
  },
  {
    label: '扩写',
    value: 'expanded',
    icon: ExpandIcon,
    prompt: '扩展并丰富这段文字，添加更多细节和解释，但保持原意',
  },
  {
    label: '缩写',
    value: 'condensed',
    icon: ShrinkIcon,
    prompt: '缩写这段文字，保留核心意思，但使用更精简的表达',
  },
]

export const stylePrompts = styleOptions.reduce((acc, option) => {
  acc[option.value] = option.prompt

  return acc
}, {} as Record<string, string>)

/** 默认最大 token 数量 */
export const DEFAULT_MAX_TOKENS = 8 * 1024

/** 温度选项 */
export const temperatureOptions = [
  {
    label: '保守',
    value: 'low',
    icon: SnowflakeIcon,
    temperature: 0.3,
    description: '输出更加稳定和可预测，适合需要准确性的场景',
  },
  {
    label: '平衡',
    value: 'medium',
    icon: ThermometerIcon,
    temperature: 0.7,
    description: '平衡创造性和一致性，适合大多数改写需求',
  },
  {
    label: '创意',
    value: 'high',
    icon: FlameIcon,
    temperature: 1.0,
    description: '输出更加多样化和创新，适合需要创造性表达的场景',
  },
]
