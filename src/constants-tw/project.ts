export const enum ProjectType {
  长期项目 = 2,
  短期项目 = 3,
  存储 = 4,
  安服 = 5,
  集成 = 6,
  工程 = 7,
  其他 = 8,
  设备 = 9,
  运维 = 10,
  开发 = 11,
}

export const ProjectTypeConfig = {
  [ProjectType.长期项目]: {
    value: ProjectType.长期项目,
    label: '长期项目',
  },
  [ProjectType.短期项目]: {
    value: ProjectType.短期项目,
    label: '短期项目',
  },
  [ProjectType.存储]: {
    value: ProjectType.存储,
    label: '存储',
  },
  [ProjectType.安服]: {
    value: ProjectType.安服,
    label: '安服',
  },
  [ProjectType.集成]: {
    value: ProjectType.集成,
    label: '集成',
  },
  [ProjectType.工程]: {
    value: ProjectType.工程,
    label: '工程',
  },
  [ProjectType.其他]: {
    value: ProjectType.其他,
    label: '其他',
  },
  [ProjectType.设备]: {
    value: ProjectType.设备,
    label: '设备',
  },
  [ProjectType.运维]: {
    value: ProjectType.运维,
    label: '运维',
  },
  [ProjectType.开发]: {
    value: ProjectType.开发,
    label: '开发',
  },
} as const satisfies EnumConfig<ProjectType, { label: string, value: ProjectType }>

export const enum ProjectStatus {
  /** 未开始 */
  NotStarted = 0,
  /** 进行中 */
  Pending = 1,
  /** 已暂停 */
  Paused = 2,
  /** 待验收 */
  AwaitingAcceptance = 5,
  /** 已结束 */
  Done = 4,
}

export const ProjectStatusConfig = {
  [ProjectStatus.NotStarted]: {
    value: ProjectStatus.NotStarted,
    label: '未开始',
    severity: 'info',
  },
  [ProjectStatus.Pending]: {
    value: ProjectStatus.Pending,
    label: '进行中',
    severity: 'info',
  },
  [ProjectStatus.AwaitingAcceptance]: {
    value: ProjectStatus.AwaitingAcceptance,
    label: '待验收',
    severity: 'warn',
  },
  [ProjectStatus.Paused]: {
    value: ProjectStatus.Paused,
    label: '已暂停',
    severity: 'warning',
  },
  [ProjectStatus.Done]: {
    value: ProjectStatus.Done,
    label: '已结束',
    severity: 'secondary',
  },
} as const satisfies EnumConfig<ProjectStatus>
