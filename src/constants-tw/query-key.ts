import type { SpaceData } from '~/features/space/types/space'
import type { TW_Project } from '~/types-tw/project'
import type { TW_Client, WechatUser } from '~/types-tw/user'

export const queryKeys = {
  user: {
    list: () => ['user', 'list'],
    detail: (id: MaybeRefOrGetter<User['id'] | undefined>) => ['users', 'detail', id],
  },

  project: {
    list: () => ['project', 'list'],
    detail: (projectId: MaybeRefOrGetter<TW_Project['id'] | undefined>) => ['project', 'detail', projectId],
    feedback: (projectId: MaybeRefOrGetter<TW_Project['id'] | undefined>) => ['project', 'feedback', projectId],
    publicDetail: (projectId: MaybeRefOrGetter<TW_Project['id'] | undefined>) => ['project', 'public-detail', projectId],
    clientFeedback: (projectId: MaybeRefOrGetter<TW_Project['id'] | undefined>) => ['project', 'client-feedback', projectId],
    desc: (projectId: MaybeRefOrGetter<TW_Project['id'] | undefined>) => ['project', 'desc', projectId],
    team: (projectId: MaybeRefOrGetter<TW_Project['id'] | undefined>) => ['project', 'team', projectId],
    milestone: (projectId: MaybeRefOrGetter<TW_Project['id'] | undefined>) => ['project', 'milestone', projectId],
  },

  client: {
    list: () => ['client', 'list'],
    detail: (clientId: MaybeRefOrGetter<TW_Client['id'] | undefined>) => ['client', 'detail', clientId],
  },

  wechatUser: {
    list: () => ['wechat-user', 'list'],
    detail: (wechatUserId: MaybeRefOrGetter<WechatUser['id'] | undefined>) => ['wechat-user', 'detail', wechatUserId],
  },

  role: {
    list: () => ['role', 'list'],
  },

  autoflow: {
    list: () => ['autoflow', 'list'],
  },

  space: {
    all: () => ['space', 'all'],
    list: (query: MaybeRefOrGetter) => ['space', 'list', { query }],
    members: (spaceId: MaybeRefOrGetter<SpaceData['id'] | undefined>) => ['space', 'members', spaceId],
    credentials: (query: MaybeRefOrGetter<WithPageQuery<Partial<{ projectId: string, name?: string }>>>) => ['space', 'credentials', query],
    communityNodes: () => ['space', 'community-nodes'],
  },
} as const
