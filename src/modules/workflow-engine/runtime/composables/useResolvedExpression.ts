import { debounce } from 'lodash-es'
import type { IDataObject, Result } from 'n8n-workflow'
import { createResultError, createResultOk } from 'n8n-workflow/dist/result.js'

import { useNDVStore } from '#wf/stores/ndv.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import { isExpression as isExpressionUtil, stringifyExpressionResult } from '#wf/utils/expressions'

import { type ResolveParameterOptions, useWorkflowHelpers } from './useWorkflowHelpers'

export function useResolvedExpression({
  expression,
  additionalData,
  isForCredential,
  stringifyObject,
}: {
  expression: MaybeRefOrGetter<unknown>
  additionalData?: MaybeRefOrGetter<IDataObject>
  isForCredential?: MaybeRefOrGetter<boolean>
  stringifyObject?: MaybeRefOrGetter<boolean>
}) {
  const ndvStore = useNDVStore()
  const workflowsStore = useWorkflowStore()

  const { resolveExpression } = useWorkflowHelpers()

  const resolvedExpression = ref<unknown>()
  const resolvedExpressionString = ref('')

  const targetItem = computed(() => ndvStore.expressionTargetItem ?? undefined)
  const activeNode = computed(() => ndvStore.activeNode)
  const hasRunData = computed(() =>
    Boolean(
      workflowsStore.workflowExecutionData?.data?.resultData?.runData[activeNode.value?.name ?? ''],
    ),
  )
  const isExpression = computed(() => isExpressionUtil(toValue(expression)))

  function resolve(): Result<unknown, Error> {
    const expressionString = toValue(expression)

    if (!isExpression.value || typeof expressionString !== 'string') {
      return { ok: true, result: '' }
    }

    let options: ResolveParameterOptions = {
      isForCredential: toValue(isForCredential),
      additionalKeys: toValue(additionalData),
    }

    if (ndvStore.isInputParentOfActiveNode) {
      options = {
        ...options,
        targetItem: targetItem.value ?? undefined,
        inputNodeName: ndvStore.ndvInputNodeName,
        inputRunIndex: ndvStore.ndvInputRunIndex,
        inputBranchIndex: ndvStore.ndvInputBranchIndex,
      }
    }

    try {
      const resolvedValue = resolveExpression(
        expressionString,
        undefined,
        options,
        toValue(stringifyObject) ?? true,
      ) as unknown

      return createResultOk(resolvedValue)
    }
    catch (error) {
      if (error instanceof Error) {
        return createResultError(error)
      }

      return createResultError(new Error(String(error)))
    }
  }

  function updateExpression() {
    const resolved = resolve()
    resolvedExpression.value = resolved.ok ? resolved.result : null
    resolvedExpressionString.value = stringifyExpressionResult(resolved, hasRunData.value)
  }

  const debouncedUpdateExpression = debounce(updateExpression, 200)

  watch(
    [
      toRef(expression),
      () => workflowsStore.getWorkflowExecution,
      () => workflowsStore.getWorkflowRunData,
      targetItem,
    ],
    debouncedUpdateExpression,
  )

  onMounted(updateExpression)

  return { resolvedExpression, resolvedExpressionString, isExpression }
}
