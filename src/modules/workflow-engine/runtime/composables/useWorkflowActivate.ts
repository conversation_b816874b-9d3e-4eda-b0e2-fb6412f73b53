import { useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import {
  LOCAL_STORAGE_ACTIVATION_FLAG,
  WORKFLOW_ACTIVE_MODAL_KEY,
} from '#wf/constants/common'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'

export function useWorkflowActivate() {
  const updatingWorkflowActivation = ref(false)

  const workflowHelpers = useWorkflowHelpers()
  const workflowsStore = useWorkflowStore()
  const uiStore = useUIStore()

  const { $toast } = useNuxtApp()

  // methods

  const updateWorkflowActivation = async (
    workflowId: string | undefined,
    newActiveState: boolean,
  ): Promise<boolean> => {
    updatingWorkflowActivation.value = true
    const nodesIssuesExist = workflowsStore.nodesIssuesExist

    const currWorkflowId: string | undefined = workflowId

    if (!currWorkflowId) {
      return false
    }

    const isCurrentWorkflow = currWorkflowId === workflowsStore.workflowId

    const activeWorkflows = workflowsStore.activeWorkflows
    const isWorkflowActive = activeWorkflows.includes(currWorkflowId)

    // void useExternalHooks().run('workflowActivate.updateWorkflowActivation', telemetryPayload)

    try {
      if (isWorkflowActive && newActiveState) {
        $toast.success({
          summary: '工作流已处于激活状态',
        })
        updatingWorkflowActivation.value = false

        // 如果工作流已处于激活状态，直接返回 true
        return true
      }

      if (isCurrentWorkflow && nodesIssuesExist && newActiveState) {
        $toast.error({
          summary: '激活工作流时出现问题',
          detail: '请先解决激活前存在的问题',
        })

        updatingWorkflowActivation.value = false

        // 如果存在节点问题，返回 false
        return false
      }

      await workflowHelpers.updateWorkflow(
        { workflowId: currWorkflowId, active: newActiveState },
        !uiStore.stateIsDirty,
      )
    }
    catch (error) {
      const newStateName = newActiveState ? '激活' : '停用'

      $toast.error({
        summary: `激活工作流时出现问题：${newStateName}`,
        detail: error instanceof Error ? error.message : undefined,
      })

      updatingWorkflowActivation.value = false

      // 如果更新失败，返回 false
      return false
    }

    // const activationEventName = isCurrentWorkflow
    //   ? 'workflow.activeChangeCurrent'
    //   : 'workflow.activeChange'
    // void useExternalHooks().run(activationEventName, {
    //   workflowId: currWorkflowId,
    //   active: newActiveState,
    // })

    updatingWorkflowActivation.value = false

    if (isCurrentWorkflow) {
      if (newActiveState && useLocalStorage(LOCAL_STORAGE_ACTIVATION_FLAG).value !== 'true') {
        uiStore.openModal(WORKFLOW_ACTIVE_MODAL_KEY)
      }
    }

    // 返回更新后的状态
    return newActiveState
  }

  const activateCurrentWorkflow = async () => {
    const workflowId = workflowsStore.workflowId

    return await updateWorkflowActivation(workflowId, true)
  }

  return {
    activateCurrentWorkflow,
    updateWorkflowActivation,
    updatingWorkflowActivation,
  }
}
