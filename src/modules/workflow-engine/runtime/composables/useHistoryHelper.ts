import { useDebounce } from '#wf/composables/useDebounce'
import { useDeviceSupport } from '#wf/composables/useDeviceSupport'
import { BulkCommand, Command } from '#wf/models/history'
import { useHistoryStore } from '#wf/stores/history.store'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useUIStore } from '#wf/stores/ui.store'

const UNDO_REDO_DEBOUNCE_INTERVAL = 100
const ELEMENT_UI_OVERLAY_SELECTOR = '.el-overlay'

export function useHistoryHelper() {
  const ndvStore = useNDVStore()
  const historyStore = useHistoryStore()
  const uiStore = useUIStore()

  const { callDebounced } = useDebounce()
  const { isCtrlKeyPressed } = useDeviceSupport()

  const undo = async () =>
    await callDebounced(
      async () => {
        const command = historyStore.popUndoableToUndo()

        if (!command) {
          return
        }

        if (command instanceof BulkCommand) {
          historyStore.bulkInProgress = true
          const commands = command.commands
          const reverseCommands: Command[] = []

          for (let i = commands.length - 1; i >= 0; i--) {
            await commands[i].revert()
            reverseCommands.push(commands[i].getReverseCommand())
          }

          historyStore.pushUndoableToRedo(new BulkCommand(reverseCommands))
          await nextTick()
          historyStore.bulkInProgress = false
        }

        if (command instanceof Command) {
          await command.revert()
          historyStore.pushUndoableToRedo(command.getReverseCommand())
          uiStore.stateIsDirty = true
        }
      },
      { debounceTime: UNDO_REDO_DEBOUNCE_INTERVAL },
    )

  const redo = async () =>
    await callDebounced(
      async () => {
        const command = historyStore.popUndoableToRedo()

        if (!command) {
          return
        }

        if (command instanceof BulkCommand) {
          historyStore.bulkInProgress = true
          const commands = command.commands
          const reverseCommands = []

          for (let i = commands.length - 1; i >= 0; i--) {
            await commands[i].revert()
            reverseCommands.push(commands[i].getReverseCommand())
          }

          historyStore.pushBulkCommandToUndo(new BulkCommand(reverseCommands), false)
          await nextTick()
          historyStore.bulkInProgress = false
        }

        if (command instanceof Command) {
          await command.revert()
          historyStore.pushCommandToUndo(command.getReverseCommand(), false)
          uiStore.stateIsDirty = true
        }
      },
      { debounceTime: UNDO_REDO_DEBOUNCE_INTERVAL },
    )

  /**
   * Checks if there is a Element UI dialog open by querying
   * for the visible overlay element.
   */
  function isMessageDialogOpen(): boolean {
    return (
      document.querySelector(`${ELEMENT_UI_OVERLAY_SELECTOR}:not([style*="display: none"])`)
      !== null
    )
  }

  function handleKeyDown(event: KeyboardEvent) {
    const isNDVOpen = ndvStore.isNDVOpen
    const isAnyModalOpen = uiStore.isAnyModalOpen || isMessageDialogOpen()
    const undoKeysPressed = isCtrlKeyPressed(event) && event.key.toLowerCase() === 'z'

    if (event.repeat) {
      return
    }
    // if (event.repeat || currentNodeViewTab !== MAIN_HEADER_TABS.WORKFLOW) {
    //   return
    // }

    if (isNDVOpen || isAnyModalOpen) {
      return
    }

    if (undoKeysPressed) {
      event.preventDefault()

      if (event.shiftKey) {
        void redo()
      }
      else {
        void undo()
      }
    }
  }

  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown)
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
  })

  return {
    undo,
    redo,
  }
}
