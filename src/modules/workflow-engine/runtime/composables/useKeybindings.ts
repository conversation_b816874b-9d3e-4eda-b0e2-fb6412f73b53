import { useActiveElement, useEventListener } from '@vueuse/core'

import { useDeviceSupport } from '#wf/composables/useDeviceSupport'

type KeyMap = Record<string, (event: KeyboardEvent) => void>

/**
 * 将 `keydown` 事件绑定到 `document` 上，并根据给定的 `keyMap` 调用相应的处理函数。
 * keyMap 是一个从快捷键字符串到处理函数的映射。快捷键字符串可以包含多个由 `|` 分隔的快捷键。
 *
 * @example
 * ```ts
 * {
 *  'ctrl+a': () => log('ctrl+a'),
 *  'ctrl+b|ctrl+c': () => log('ctrl+b 或 ctrl+c'),
 * }
 * ```
 */
export function useKeybindings(
  keyMap: Ref<KeyMap>,
  options?: {
    disabled: MaybeRef<boolean>
    /** 粘贴事件处理函数，一般由 Ctrl+V 触发，调用方需自行处理防抖 */
    onPaste?: (event: ClipboardEvent) => void
  },
) {
  const activeElement = useActiveElement()

  const { isCtrlKeyPressed } = useDeviceSupport()

  const isDisabled = computed(() => unref(options?.disabled))

  const ignoreKeyPresses = computed(() => {
    if (!activeElement.value) {
      return false
    }

    const active = activeElement.value
    const isInput = ['INPUT', 'TEXTAREA'].includes(active.tagName)
    const isContentEditable = active.closest('[contenteditable]') !== null
    const isIgnoreClass = active.closest('.ignore-key-press-canvas') !== null

    return isInput || isContentEditable || isIgnoreClass
  })

  const shortcutPartsToString = (parts: string[]) => {
    return parts
      .map((key) => key.toLowerCase())
      .sort((a, b) => a.localeCompare(b))
      .join('+')
  }

  const normalizeShortcutString = (shortcut: string) => {
    if (shortcut.length === 1) {
      return shortcut.toLowerCase()
    }

    const splitChars = ['+', '_', '-']
    const splitCharsRegEx = splitChars.reduce((acc, char) => {
      if (shortcut.startsWith(char) || shortcut.endsWith(char)) {
        return acc
      }

      return char + acc
    }, '')

    return shortcutPartsToString(shortcut.split(new RegExp(`[${splitCharsRegEx}]`)))
  }

  const normalizedKeymap = computed(() => Object.fromEntries(
    Object.entries(keyMap.value).flatMap(([shortcut, handler]) => {
      const shortcuts = shortcut.split('|')

      return shortcuts.map((s) => [normalizeShortcutString(s), handler])
    }),
  ),
  )

  /**
   * 将键盘事件代码转换为键字符串。
   *
   * @example
   * keyboardEventCodeToKey('Digit0') -> '0'
   * keyboardEventCodeToKey('KeyA') -> 'a'
   */
  const keyboardEventCodeToKey = (code: string) => {
    if (code.startsWith('Digit')) {
      return code.replace('Digit', '').toLowerCase()
    }
    else if (code.startsWith('Key')) {
      return code.replace('Key', '').toLowerCase()
    }

    return code.toLowerCase()
  }

  /**
   * 将键盘事件转换为 `key` 和 `code` 两种格式的快捷键字符串。
   *
   * @example
   * keyboardEventToShortcutString({ key: 'a', code: 'KeyA', ctrlKey: true })
   * // --> { byKey: 'ctrl+a', byCode: 'ctrl+a' }
   */
  const toShortcutString = (event: KeyboardEvent) => {
    const { shiftKey, altKey } = event
    const ctrlKey = isCtrlKeyPressed(event)
    const keys = 'key' in event ? [event.key] : []
    const codes = 'code' in event ? [keyboardEventCodeToKey(event.code)] : []
    const modifiers: string[] = []

    if (shiftKey) {
      modifiers.push('shift')
    }

    if (ctrlKey) {
      modifiers.push('ctrl')
    }

    if (altKey) {
      modifiers.push('alt')
    }

    return {
      byKey: shortcutPartsToString([...modifiers, ...keys]),
      byCode: shortcutPartsToString([...modifiers, ...codes]),
    }
  }

  const handleKeyDown = (event: KeyboardEvent) => {
    if (ignoreKeyPresses.value || isDisabled.value) {
      return
    }

    const { byKey, byCode } = toShortcutString(event)

    // 优先使用 `byKey` 而非 `byCode`，以确保：
    // - ANSI 键盘布局正常工作
    // - Dvorak 键盘布局正常工作
    // - 非 ANSI 键盘布局正常工作
    const handler = normalizedKeymap.value[byKey] ?? normalizedKeymap.value[byCode]

    if (handler) {
      event.preventDefault()
      event.stopPropagation()
      handler(event)
    }
  }

  const handlePaste = (event: ClipboardEvent) => {
    if (!isDisabled.value) {
      options?.onPaste?.(event)
    }
  }

  let unregisterKeyDown: ReturnType<typeof useEventListener> | undefined
  let unregisterPaste: ReturnType<typeof useEventListener> | undefined

  const registerKeybindings = () => {
    unregisterKeyDown = useEventListener(document, 'keydown', handleKeyDown)
    unregisterPaste = useEventListener(document, 'paste', handlePaste)
  }

  const unregisterKeybindings = () => {
    unregisterKeyDown?.()
    unregisterPaste?.()
  }

  registerKeybindings()

  useEventListener(window, 'blur', unregisterKeybindings)
  useEventListener(window, 'focus', registerKeybindings)
}
