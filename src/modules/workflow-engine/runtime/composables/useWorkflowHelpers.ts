import { consola } from 'consola'
import { get, pick } from 'lodash-es'
import type { IDataObject, IExecuteData, INode, INodeConnection, INodeCredentials, INodeExecutionData, INodeParameters, INodeProperties, INodeTypes, IRunExecutionData, IWebhookDescription, IWorkflowDataProxyAdditionalKeys, NodeParameterValue, Workflow } from 'n8n-workflow'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'

import { NodeConnectionType } from '#wf/constants/canvas'
import { HTTP_REQUEST_NODE_TYPE, PLACEHOLDER_EMPTY_WORKFLOW_ID, PLACEHOLDER_FILLED_AT_EXECUTION_TIME } from '#wf/constants/common'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useRootStore } from '#wf/stores/root.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { ICredentialsResponse, INodeTypesMaxCount, INodeUi, IWorkflowData, IWorkflowDataUpdate, IWorkflowDb, TargetItem, WorkflowTitleStatus, XYPosition } from '#wf/types/interface'
import { getSourceItems } from '#wf/utils/pairedItemUtils'

import { getCredentialTypeName, isCredentialOnlyNodeType } from '../utils/credentialOnlyNodes'

import { useNodeHelpers } from './useNodeHelpers'

export interface ResolveParameterOptions {
  targetItem?: TargetItem
  inputNodeName?: string
  inputRunIndex?: number
  inputBranchIndex?: number
  additionalKeys?: IWorkflowDataProxyAdditionalKeys
  isForCredential?: boolean
  contextNodeName?: string
}

/**
 * 获取当前工作流
 * @param copyData - 是否复制数据
 * @returns 当前工作流
 */
export function getCurrentWorkflow(copyData?: boolean): Workflow {
  return useWorkflowStore().getCurrentWorkflow(copyData)
}

export function executeData(
  parentNodes: string[],
  currentNode: string,
  inputName: string,
  runIndex: number,
): IExecuteData {
  const executeData = {
    node: {},
    data: {},
    source: null,
  } as IExecuteData

  const workflowStore = useWorkflowStore()

  // Find the parent node which has data
  for (const parentNodeName of parentNodes) {
    if (workflowStore.shouldReplaceInputDataWithPinData) {
      const parentPinData = workflowStore.pinnedWorkflowData![parentNodeName]

      // populate `executeData` from `pinData`

      if (parentPinData) {
        executeData.data = { main: [parentPinData] }
        executeData.source = { main: [{ previousNode: parentNodeName }] }

        return executeData
      }
    }

    // populate `executeData` from `runData`
    const workflowRunData = workflowStore.getWorkflowRunData

    if (workflowRunData === null) {
      return executeData
    }

    if (
      !workflowRunData[parentNodeName]
      || workflowRunData[parentNodeName].length <= runIndex
      || !workflowRunData[parentNodeName][runIndex]
      || !Object.hasOwn(workflowRunData[parentNodeName][runIndex], 'data')
      || workflowRunData[parentNodeName][runIndex].data === undefined
      || !Object.hasOwn(workflowRunData[parentNodeName][runIndex].data, inputName)
    ) {
      executeData.data = {}
    }
    else {
      executeData.data = workflowRunData[parentNodeName][runIndex].data!

      if (workflowRunData[currentNode] && workflowRunData[currentNode][runIndex]) {
        executeData.source = {
          [inputName]: workflowRunData[currentNode][runIndex].source,
        }
      }
      else {
        const workflow = getCurrentWorkflow()

        let previousNodeOutput: number | undefined

        // As the node can be connected through either of the outputs find the correct one
        // and set it to make pairedItem work on not executed nodes
        if (workflow.connectionsByDestinationNode[currentNode]?.main) {
          mainConnections: for (const mainConnections of workflow.connectionsByDestinationNode[
            currentNode
          ].main) {
            for (const connection of mainConnections ?? []) {
              if (
                // @ts-expect-error
                connection.type === NodeConnectionType.Main
                && connection.node === parentNodeName
              ) {
                previousNodeOutput = connection.index
                break mainConnections
              }
            }
          }
        }

        // The current node did not get executed in UI yet so build data manually
        executeData.source = {
          [inputName]: [
            {
              previousNode: parentNodeName,
              previousNodeOutput,
            },
          ],
        }
      }

      return executeData
    }
  }

  return executeData
}

function connectionInputData(
  parentNode: string[],
  currentNode: string,
  inputName: string,
  runIndex: number,
  nodeConnection: INodeConnection = { sourceIndex: 0, destinationIndex: 0 },
): INodeExecutionData[] | null {
  let connectionInputData: INodeExecutionData[] | null = null
  const _executeData = executeData(parentNode, currentNode, inputName, runIndex)

  if (parentNode.length) {
    if (
      !Object.keys(_executeData.data).length
      || _executeData.data[inputName].length <= nodeConnection.sourceIndex
    ) {
      connectionInputData = []
    }
    else {
      connectionInputData = _executeData.data[inputName][nodeConnection.sourceIndex]

      if (connectionInputData !== null) {
        // Update the pairedItem information on items
        connectionInputData = connectionInputData.map((item, itemIndex) => {
          return {
            ...item,
            pairedItem: {
              item: itemIndex,
              input: nodeConnection.destinationIndex,
            },
          }
        })
      }
    }
  }

  return connectionInputData
}

export function useWorkflowHelpers() {
  const workflowStore = useWorkflowStore()
  const rootStore = useRootStore()
  const uiStore = useUIStore()
  const nodeTypesStore = useNodeTypesStore()

  const ndvStore = useNDVStore()
  const { activeNode: activeNodeRef } = storeToRefs(ndvStore)

  const nodeHelpers = useNodeHelpers()

  const { $toast } = useNuxtApp()

  const resolveParameter = <T = IDataObject>(
    parameter: NodeParameterValue | INodeParameters | NodeParameterValue[] | INodeParameters[],
    opts: ResolveParameterOptions = {},
  ): T | null => {
    let itemIndex = opts?.targetItem?.itemIndex || 0

    const workflow = getCurrentWorkflow()

    const additionalKeys: IWorkflowDataProxyAdditionalKeys = {
      $execution: {
        id: PLACEHOLDER_FILLED_AT_EXECUTION_TIME,
        mode: 'test',
        resumeUrl: PLACEHOLDER_FILLED_AT_EXECUTION_TIME,
        resumeFormUrl: PLACEHOLDER_FILLED_AT_EXECUTION_TIME,
      },
      // $vars: useEnvironmentsStore().variablesAsObject,

      // deprecated
      $executionId: PLACEHOLDER_FILLED_AT_EXECUTION_TIME,
      $resumeWebhookUrl: PLACEHOLDER_FILLED_AT_EXECUTION_TIME,

      ...opts.additionalKeys,
    }

    if (opts.isForCredential) {
      // node-less expression resolution
      return workflow.expression.getParameterValue(
        parameter,
        null,
        0,
        itemIndex,
        '',
        [],
        'manual',
        additionalKeys,
        undefined,
        false,
        undefined,
        '',
      ) as T
    }

    const inputName = NodeConnectionType.Main

    const activeNode
        = activeNodeRef.value ?? useWorkflowStore().getNodeByName(opts.contextNodeName || '')
    let contextNode = activeNode

    if (activeNode) {
      contextNode = workflow.getParentMainInputNode(activeNode)
    }

    const workflowRunData = useWorkflowStore().getWorkflowRunData
    // @ts-expect-error
    let parentNode = workflow.getParentNodes(contextNode!.name, inputName, 1)
    const executionData = useWorkflowStore().getWorkflowExecution

    let runIndexParent = opts?.inputRunIndex ?? 0
    const nodeConnection = workflow.getNodeConnectionIndexes(contextNode!.name, parentNode[0])

    if (opts.targetItem && opts?.targetItem?.nodeName === contextNode!.name && executionData) {
      const sourceItems = getSourceItems(executionData, opts.targetItem)

      if (!sourceItems.length) {
        return null
      }

      parentNode = [sourceItems[0].nodeName]
      runIndexParent = sourceItems[0].runIndex
      itemIndex = sourceItems[0].itemIndex

      if (nodeConnection) {
        nodeConnection.sourceIndex = sourceItems[0].outputIndex
      }
    }
    else {
      parentNode = opts.inputNodeName ? [opts.inputNodeName] : parentNode

      if (nodeConnection) {
        nodeConnection.sourceIndex = opts.inputBranchIndex ?? nodeConnection.sourceIndex
      }

      if (opts?.inputRunIndex === undefined && workflowRunData !== null && parentNode.length) {
        const firstParentWithWorkflowRunData = parentNode.find(
          (parentNodeName) => workflowRunData[parentNodeName],
        )

        if (firstParentWithWorkflowRunData) {
          runIndexParent = workflowRunData[firstParentWithWorkflowRunData].length - 1
        }
      }
    }

    let _connectionInputData = connectionInputData(
      parentNode,
      contextNode!.name,
      inputName,
      runIndexParent,
      nodeConnection,
    )

    if (_connectionInputData === null && contextNode && activeNode?.name !== contextNode.name) {
      // For Sub-Nodes connected to Trigger-Nodes use the data of the root-node
      // (Gets for example used by the Memory connected to the Chat-Trigger-Node)
      const _executeData = executeData([contextNode.name], contextNode.name, inputName, 0)
      _connectionInputData = get(_executeData, ['data', inputName, 0], null)
    }

    let runExecutionData: IRunExecutionData

    if (!executionData?.data) {
      runExecutionData = {
        resultData: {
          runData: {},
        },
      }
    }
    else {
      runExecutionData = executionData.data
    }

    if (_connectionInputData === null) {
      _connectionInputData = []
    }

    if (activeNode?.type === HTTP_REQUEST_NODE_TYPE) {
      const EMPTY_RESPONSE = { statusCode: 200, headers: {}, body: {} }
      const EMPTY_REQUEST = { headers: {}, body: {}, qs: {} }
      // Add $request,$response,$pageCount for HTTP Request-Nodes as it is used
      // in pagination expressions
      additionalKeys.$pageCount = 0
      additionalKeys.$response = get(
        executionData,
        ['data', 'executionData', 'contextData', `node:${activeNode?.name}`, 'response'],
        EMPTY_RESPONSE,
      )
      additionalKeys.$request = EMPTY_REQUEST
    }

    let runIndexCurrent = opts?.targetItem?.runIndex ?? 0

    if (
      opts?.targetItem === undefined
      && workflowRunData !== null
      && workflowRunData[contextNode!.name]
    ) {
      runIndexCurrent = workflowRunData[contextNode!.name].length - 1
    }

    let _executeData = executeData(parentNode, contextNode!.name, inputName, runIndexCurrent)

    if (!_executeData.source) {
      // fallback to parent's run index for multi-output case
      _executeData = executeData(parentNode, contextNode!.name, inputName, runIndexParent)
    }

    // ExpressionEvaluatorProxy.setEvaluator(
    //   useSettingsStore().settings.expressions?.evaluator ?? 'tmpl',
    // )

    return workflow.expression.getParameterValue(
      parameter,
      runExecutionData,
      runIndexCurrent,
      itemIndex,
      activeNode!.name,
      _connectionInputData,
      'manual',
      additionalKeys,
      _executeData,
      false,
      {},
      contextNode!.name,
    ) as T
  }

  const getNodeTypeCount = (nodeType: string) => {
    const nodes = workflowStore.workflow.nodes

    let count = 0

    for (const node of nodes) {
      if (node.type === nodeType) {
        count++
      }
    }

    return count
  }

  const getConnectedNodes = (
    direction: 'upstream' | 'downstream',
    workflow: Workflow,
    nodeName: string,
  ): string[] => {
    let checkNodes: string[]

    if (direction === 'downstream') {
      checkNodes = workflow.getChildNodes(nodeName)
    }
    else if (direction === 'upstream') {
      checkNodes = workflow.getParentNodes(nodeName)
    }
    else {
      throw new Error(`The direction "${direction}" is not supported!`)
    }

    // Find also all nodes which are connected to the child nodes via a non-main input
    let connectedNodes: string[] = []
    checkNodes.forEach((checkNode) => {
      connectedNodes = [
        ...connectedNodes,
        checkNode,
        ...workflow.getParentNodes(checkNode, 'ALL_NON_MAIN'),
      ]
    })

    // Remove duplicates
    return [...new Set(connectedNodes)]
  }

  const initState = (workflowData: IWorkflowDb) => {
    workflowStore.addWorkflow(workflowData)

    workflowStore.setWorkflowPartial(pick(workflowData, ['updatedAt', 'createdAt']))
    workflowStore.setActive(workflowData.active || false)
    workflowStore.setWorkflowId(workflowData.id)
    workflowStore.setWorkflowName({
      newName: workflowData.name,
      setStateDirty: false,
    })
    workflowStore.setWorkflowSettings(workflowData.settings ?? {})
    workflowStore.setWorkflowPinData(workflowData.pinData ?? {})
    workflowStore.setWorkflowVersionId(workflowData.versionId)
    workflowStore.setWorkflowMetadata(workflowData.meta)
    workflowStore.setWorkflowScopes(workflowData.scopes)

    if (workflowData.usedCredentials) {
      workflowStore.setUsedCredentials(workflowData.usedCredentials)
    }
  }

  function resolveExpression(
    expression: string,
    siblingParameters: INodeParameters = {},
    opts: ResolveParameterOptions & { c?: number } = {},
    stringifyObject = true,
  ) {
    const parameters = {
      __xxxxxxx__: expression,
      ...siblingParameters,
    }
    const returnData: IDataObject | null = resolveParameter(parameters, opts)

    if (!returnData) {
      return null
    }

    const obj = returnData.__xxxxxxx__

    if (typeof obj === 'object' && stringifyObject) {
      const proxy = obj as { isProxy: boolean, toJSON?: () => unknown } | null

      if (proxy?.isProxy && proxy.toJSON) {
        return JSON.stringify(proxy.toJSON())
      }

      const workflow = getCurrentWorkflow()

      return workflow.expression.convertObjectValueToString(obj as object)
    }

    return obj
  }

  const getWebhookExpressionValue = (
    webhookData: IWebhookDescription,
    key: string,
    stringify = true,
    nodeName?: string,
  ): string => {
    if (webhookData[key] === undefined) {
      return 'empty'
    }

    try {
      return resolveExpression(
        webhookData[key] as string,
        undefined,
        { contextNodeName: nodeName },
        stringify,
      ) as string
    }
    catch {
      return '无效表达式'
    }
  }

  const getWebhookUrl = (
    webhookData: IWebhookDescription,
    node: INode,
    showUrlFor?: string,
  ): string => {
    const { isForm, restartWebhook } = webhookData

    if (restartWebhook === true) {
      return isForm ? '$execution.resumeFormUrl' : '$execution.resumeUrl'
    }

    let baseUrl

    if (showUrlFor === 'test') {
      baseUrl = isForm ? rootStore.formTestUrl : rootStore.webhookTestUrl
    }
    else {
      baseUrl = isForm ? rootStore.formUrl : rootStore.webhookUrl
    }

    const path = getWebhookExpressionValue(webhookData, 'path', true, node.name) ?? ''
    const isFullPath = (getWebhookExpressionValue(
      webhookData,
      'isFullPath',
      true,
      node.name,
    ) as unknown as boolean) || false

    return NodeHelpers.getNodeWebhookUrl(baseUrl, workflowStore.workflowId, node, path, isFullPath)
  }

  function getNodeDataToSave(node: INodeUi): INodeUi {
    const skipKeys = [
      'color',
      'continueOnFail',
      'credentials',
      'disabled',
      'issues',
      'onError',
      'notes',
      'parameters',
      'status',
    ]

    // @ts-ignore
    const nodeData: INodeUi = {
      parameters: {},
    }

    for (const key in node) {
      if (key.charAt(0) !== '_' && skipKeys.indexOf(key) === -1) {
        // @ts-ignore
        nodeData[key] = node[key]
      }
    }

    // Get the data of the node type that we can get the default values
    // TODO: Later also has to care about the node-type-version as defaults could be different
    const nodeType = nodeTypesStore.getNodeType(node.type, node.typeVersion)

    if (nodeType !== null) {
      const isCredentialOnly = isCredentialOnlyNodeType(nodeType.name)

      if (isCredentialOnly) {
        nodeData.type = HTTP_REQUEST_NODE_TYPE
        nodeData.extendsCredential = getCredentialTypeName(nodeType.name)
      }

      // Node-Type is known so we can save the parameters correctly
      const nodeParameters = NodeHelpers.getNodeParameters(
        nodeType.properties,
        node.parameters,
        isCredentialOnly,
        false,
        node,
      )
      nodeData.parameters = nodeParameters !== null ? nodeParameters : {}

      // Add the node credentials if there are some set and if they should be displayed
      if (node.credentials !== undefined && nodeType.credentials !== undefined) {
        const saveCredentials: INodeCredentials = {}

        for (const nodeCredentialTypeName of Object.keys(node.credentials)) {
          if (
            nodeHelpers.hasProxyAuth(node)
            || Object.keys(node.parameters).includes('genericAuthType')
          ) {
            saveCredentials[nodeCredentialTypeName] = node.credentials[nodeCredentialTypeName]
            continue
          }

          const credentialTypeDescription = nodeType.credentials
          // filter out credentials with same name in different node versions
            .filter((c) => nodeHelpers.displayParameter(node.parameters, c, '', node))
            .find((c) => c.name === nodeCredentialTypeName)

          if (credentialTypeDescription === undefined) {
            // Credential type is not know so do not save
            continue
          }

          if (!nodeHelpers.displayParameter(node.parameters, credentialTypeDescription, '', node)) {
            // Credential should not be displayed so do also not save
            continue
          }

          saveCredentials[nodeCredentialTypeName] = node.credentials[nodeCredentialTypeName]
        }

        // Set credential property only if it has content
        if (Object.keys(saveCredentials).length !== 0) {
          nodeData.credentials = saveCredentials
        }
      }
    }
    else {
      // Node-Type is not known so save the data as it is
      nodeData.credentials = node.credentials
      nodeData.parameters = node.parameters

      if (nodeData.color !== undefined) {
        nodeData.color = node.color
      }
    }

    // Save the disabled property, continueOnFail and onError only when is set
    if (node.disabled === true) {
      nodeData.disabled = true
    }

    if (node.continueOnFail === true) {
      nodeData.continueOnFail = true
    }

    if (node.onError !== 'stopWorkflow') {
      nodeData.onError = node.onError
    }

    // Save the notes only if when they contain data
    if (![undefined, ''].includes(node.notes)) {
      nodeData.notes = node.notes
    }

    return nodeData
  }

  async function getWorkflowDataToSave(externalWorkflow?: IWorkflowDb) {
    const workflow = externalWorkflow || workflowStore.workflow
    const workflowNodes = workflow.nodes || []
    const workflowConnections = workflow.connections || {}

    let nodeData

    const nodes: INode[] = []

    for (let nodeIndex = 0; nodeIndex < workflowNodes.length; nodeIndex++) {
      nodeData = getNodeDataToSave(workflowNodes[nodeIndex])

      nodes.push(nodeData)
    }

    const data: IWorkflowData = {
      name: workflow.name,
      nodes,
      pinData: externalWorkflow?.pinData || workflowStore.pinnedWorkflowData,
      connections: workflowConnections,
      active: workflow.active || false,
      settings: workflow.settings || {},
      tags: (workflow.tags as string[]) || [],
      versionId: workflow.versionId,
      meta: workflow.meta || {},
    }

    const workflowId = externalWorkflow?.id || workflowStore.workflowId

    if (workflowId !== PLACEHOLDER_EMPTY_WORKFLOW_ID) {
      data.id = workflowId
    }

    return data
  }

  async function updateWorkflow(
    { workflowId, active }: { workflowId: string, active?: boolean },
    partialData = false,
  ) {
    let data: IWorkflowDataUpdate = {}

    const isCurrentWorkflow = workflowId === workflowStore.workflowId

    if (isCurrentWorkflow) {
      data = partialData
        ? { versionId: workflowStore.workflowVersionId }
        : await getWorkflowDataToSave()
    }
    else {
      const { versionId } = await workflowStore.fetchWorkflow(workflowId)
      data.versionId = versionId
    }

    if (active !== undefined) {
      data.active = active
    }

    let workflow = await workflowStore.updateWorkflow(workflowId, data)
    // HACK: 应该让接口返回更新后的对象
    workflow = data as IWorkflowDb

    workflowStore.setWorkflowVersionId(workflow.versionId)

    if (isCurrentWorkflow) {
      workflowStore.setActive(!!workflow.active)
      uiStore.stateIsDirty = false
    }

    if (workflow.active) {
      workflowStore.setWorkflowActive(workflowId)
    }
    else {
      workflowStore.setWorkflowInactive(workflowId)
    }
  }

  async function saveCurrentWorkflow(
    { forceSave = false }: { redirect?: boolean, forceSave?: boolean } = {},
  ): Promise<boolean> {
    // const readOnlyEnv = useSourceControlStore().preferences.branchReadOnly

    // if (readOnlyEnv) {
    //   return false
    // }

    const currentWorkflow = workflowStore.workflow.id

    try {
      uiStore.addActiveAction('workflowSaving')

      const workflowDataRequest: IWorkflowDataUpdate = await getWorkflowDataToSave()

      workflowDataRequest.versionId = workflowStore.workflow.versionId

      const workflowData = await workflowStore.updateWorkflow(
        currentWorkflow,
        workflowDataRequest,
        forceSave,
      )
      workflowStore.setWorkflowVersionId(workflowData.versionId)

      uiStore.stateIsDirty = false
      uiStore.removeActiveAction('workflowSaving')
      // void useExternalHooks().run('workflow.afterUpdate', { workflowData })

      return true
    }
    catch (error) {
      consola.error(error)

      uiStore.removeActiveAction('workflowSaving')

      if (error instanceof Error) {
        // if (error.errorCode === 100) {
        //   const url = router.resolve({
        //     name: VIEWS.WORKFLOW,
        //     params: { name: currentWorkflow },
        //   }).href

        //   const overwrite = await message.confirm(
        //     i18n.baseText('workflows.concurrentChanges.confirmMessage.message', {
        //       interpolate: {
        //         url,
        //       },
        //     }),
        //     i18n.baseText('workflows.concurrentChanges.confirmMessage.title'),
        //     {
        //       confirmButtonText: i18n.baseText(
        //         'workflows.concurrentChanges.confirmMessage.confirmButtonText',
        //       ),
        //       cancelButtonText: i18n.baseText(
        //         'workflows.concurrentChanges.confirmMessage.cancelButtonText',
        //       ),
        //     },
        //   )

        //   if (overwrite === MODAL_CONFIRM) {
        //     return await saveCurrentWorkflow({ id, name, tags }, redirect, true)
        //   }

        //   return false
        // }

        $toast.error({
          summary: '保存工作流时出现问题',
          detail: error.message,
        })
      }

      return false
    }
  }

  const setDocumentTitle = (workflowName: string, status: WorkflowTitleStatus) => {
    let icon = '⚠️'

    if (status === 'EXECUTING') {
      icon = '🔄'
    }
    else if (status === 'IDLE') {
      icon = '▶️'
    }

    document.title = `${icon} ${workflowName}`
  }

  function getNodeTypes(): INodeTypes {
    return useWorkflowStore().getNodeTypes()
  }

  function removeForeignCredentialsFromWorkflow(
    workflow: IWorkflowData | IWorkflowDataUpdate,
    usableCredentials: ICredentialsResponse[],
  ): void {
    (workflow.nodes ?? []).forEach((node: INode) => {
      if (!node.credentials) {
        return
      }

      node.credentials = Object.entries(node.credentials).reduce<INodeCredentials>(
        (acc, [credentialType, credential]) => {
          const isUsableCredential = usableCredentials.some(
            (ownCredential) => `${ownCredential.id}` === `${credential.id}`,
          )

          if (credential.id && isUsableCredential) {
            acc[credentialType] = node.credentials![credentialType]
          }

          return acc
        },
        {},
      )
    })
  }

  function getNodeTypesMaxCount() {
    const nodes = workflowStore.workflow.nodes

    const returnData: INodeTypesMaxCount = {}

    const nodeTypes = nodeTypesStore.allNodeTypes

    for (const nodeType of nodeTypes) {
      if (nodeType.maxNodes !== undefined) {
        returnData[nodeType.name] = {
          exist: 0,
          max: nodeType.maxNodes,
          nodeNames: [],
        }
      }
    }

    for (const node of nodes) {
      if (returnData[node.type] !== undefined) {
        returnData[node.type].exist += 1
        returnData[node.type].nodeNames.push(node.name)
      }
    }

    return returnData
  }

  // Updates the position of all the nodes that the top-left node
  // is at the given position
  function updateNodePositions(
    workflowData: IWorkflowData | IWorkflowDataUpdate,
    position: XYPosition,
  ): void {
    if (workflowData.nodes === undefined) {
      return
    }

    // Find most top-left node
    const minPosition = [99999999, 99999999]

    for (const node of workflowData.nodes) {
      if (node.position[1] < minPosition[1]) {
        minPosition[0] = node.position[0]
        minPosition[1] = node.position[1]
      }
      else if (node.position[1] === minPosition[1]) {
        if (node.position[0] < minPosition[0]) {
          minPosition[0] = node.position[0]
          minPosition[1] = node.position[1]
        }
      }
    }

    // Update the position on all nodes so that the
    // most top-left one is at given position
    const offsetPosition = [position[0] - minPosition[0], position[1] - minPosition[1]]

    for (const node of workflowData.nodes) {
      node.position[0] += offsetPosition[0]
      node.position[1] += offsetPosition[1]
    }
  }

  const resolveRequiredParameters = (
    currentParameter: INodeProperties,
    parameters: INodeParameters,
    opts: {
      targetItem?: TargetItem
      inputNodeName?: string
      inputRunIndex?: number
      inputBranchIndex?: number
    } = {},
  ): IDataObject | null => {
    const loadOptionsDependsOn = new Set(currentParameter?.typeOptions?.loadOptionsDependsOn ?? [])

    const resolvedParameters = Object.fromEntries(
      Object.entries(parameters).map(([name, parameter]): [string, IDataObject | null] => {
        const required = loadOptionsDependsOn.has(name)

        if (required) {
          return [name, resolveParameter(parameter as NodeParameterValue, opts)]
        }
        else {
          try {
            return [name, resolveParameter(parameter as NodeParameterValue, opts)]
          }
          catch {
            // ignore any expressions errors for non required parameters
            return [name, null]
          }
        }
      }),
    )

    return resolvedParameters
  }

  return {
    /** 初始化工作流信息 */
    initState,
    getWebhookExpressionValue,
    getWebhookUrl,
    resolveExpression,
    getNodeTypeCount,
    getConnectedNodes,
    updateWorkflow,
    saveCurrentWorkflow,
    getWorkflowDataToSave,
    setDocumentTitle,
    getNodeTypes,
    getNodeDataToSave,
    removeForeignCredentialsFromWorkflow,
    getNodeTypesMaxCount,
    updateNodePositions,

    resolveParameter,
    resolveRequiredParameters,
  }
}
