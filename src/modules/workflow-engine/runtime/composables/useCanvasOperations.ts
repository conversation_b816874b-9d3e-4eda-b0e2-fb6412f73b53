import type { Connection } from '@vue-flow/core'
import { consola } from 'consola'
import type { IConnection, IConnections, IDataObject, INode, INodeConnections, INodeCredentials, INodeInputConfiguration, INodeOutputConfiguration, INodeTypeDescription, INodeTypeNameVersion, IPinData, NodeInputConnections, NodeParameterValueType, Workflow } from 'n8n-workflow'
import { FORM_TRIGGER_NODE_TYPE, STICKY_NODE_TYPE, WEBHOOK_NODE_TYPE } from 'n8n-workflow/dist/Constants.js'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'
import { deepCopy } from 'n8n-workflow/dist/utils.js'

import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { usePinnedData } from '#wf/composables/usePinnedData'
import { useUniqueNodeName } from '#wf/composables/useUniqueNodeName'
import { getCurrentWorkflow, useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { CanvasConnectionMode, NodeConnectionType } from '#wf/constants/canvas'
import { EnterpriseEditionFeature, UPDATE_WEBHOOK_ID_NODE_TYPES } from '#wf/constants/common'
import { AddConnectionCommand, AddNodeCommand, MoveNodeCommand, RemoveConnectionCommand, RemoveNodeCommand, RenameNodeCommand } from '#wf/models/history'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import { useExecutionsStore } from '#wf/stores/executions.store'
import { useHistoryStore } from '#wf/stores/history.store'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeCreatorStore } from '#wf/stores/nodeCreator.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useRootStore } from '#wf/stores/root.store'
import { useSettingsStore } from '#wf/stores/setting.store'
import { useTagsStore } from '#wf/stores/tags.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { CanvasConnection, CanvasConnectionCreateData, CanvasConnectionPort, CanvasNode, CanvasNodeMoveEvent } from '#wf/types/canvas'
import type { AddedNodesAndConnections, IExecutionResponse, INodeUi, ITag, IUsedCredential, IWorkflowData, IWorkflowDataUpdate, IWorkflowDb, XYPosition } from '#wf/types/interface'
import { createCanvasConnectionHandleString, mapCanvasConnectionToLegacyConnection, mapLegacyConnectionsToCanvasConnections, mapLegacyConnectionToCanvasConnection, parseCanvasConnectionHandleString } from '#wf/utils/canvasUtil'
import { CONFIGURABLE_NODE_SIZE, CONFIGURATION_NODE_SIZE, DEFAULT_NODE_SIZE, generateOffsets, getNewNodePosition, GRID_SIZE, NODE_SIZE, PUSH_NODES_OFFSET } from '#wf/utils/nodeViewUtils'
import { isPresent } from '#wf/utils/typesUtils'

import type { UnsafeAny } from '~/types/common'

type AddNodeData = Partial<INodeUi> & {
  type: string
}

type AddNodeDataWithTypeVersion = AddNodeData & {
  typeVersion: INodeUi['typeVersion']
}
type AddNodesBaseOptions = {
  dragAndDrop?: boolean
  trackHistory?: boolean
  keepPristine?: boolean
  telemetry?: boolean
}

type AddNodesOptions = AddNodesBaseOptions & {
  position?: XYPosition
  trackBulk?: boolean
}

type AddNodeOptions = AddNodesBaseOptions & {
  openNDV?: boolean
  isAutoAdd?: boolean
}

export function useCanvasOperations() {
  const rootStore = useRootStore()
  const workflowStore = useWorkflowStore()
  const settingsStore = useSettingsStore()
  const ndvStore = useNDVStore()
  const nodeTypesStore = useNodeTypesStore()
  const nodeCreatorStore = useNodeCreatorStore()
  const historyStore = useHistoryStore()
  const uiStore = useUIStore()
  const credentialsStore = useCredentialsStore()
  const tagsStore = useTagsStore()
  const executionsStore = useExecutionsStore()

  const workflowHelpers = useWorkflowHelpers()
  const nodeHelpers = useNodeHelpers()

  const { uniqueNodeName } = useUniqueNodeName()

  const setNodeActiveByName = (name: string) => {
    ndvStore.setActiveNodeName(name)
  }

  const editableWorkflow = computed(() => workflowStore.workflow)
  const editableWorkflowObject = computed(() => workflowStore.getCurrentWorkflow())

  const triggerNodes = computed<INodeUi[]>(() => {
    return workflowStore.workflowTriggerNodes
  })

  const requireNodeTypeDescription = (
    type: INodeUi['type'],
    version?: INodeUi['typeVersion'],
  ): INodeTypeDescription => {
    return (
      nodeTypesStore.getNodeType(type, version) ?? {
        properties: [],
        displayName: type,
        name: type,
        group: [],
        description: '',
        version: version ?? 1,
        defaults: {},
        inputs: [],
        outputs: [],
      }
    )
  }

  const updateNodePosition = (
    id: string,
    position: CanvasNode['position'],
    { trackHistory = false }: { trackHistory?: boolean } = {},
  ) => {
    const node = workflowStore.getNodeById(id)

    if (!node) {
      return
    }

    const oldPosition: XYPosition = [...node.position]
    const newPosition: XYPosition = [position.x, position.y]

    workflowStore.setNodePositionById(id, newPosition)

    if (trackHistory) {
      historyStore.pushCommandToUndo(new MoveNodeCommand(node.name, oldPosition, newPosition))
    }
  }

  const updateNodesPosition = (
    events: CanvasNodeMoveEvent[],
    { trackHistory = false, trackBulk = true } = {},
  ) => {
    if (trackHistory && trackBulk) {
      historyStore.startRecordingUndo()
    }

    events.forEach(({ id, position }) => {
      updateNodePosition(id, position, { trackHistory })
    })

    if (trackHistory && trackBulk) {
      historyStore.stopRecordingUndo()
    }
  }

  const lastClickPosition = ref<XYPosition>([0, 0])

  /**
   * Moves all downstream nodes of a node
   */
  const shiftDownstreamNodesPosition = (
    sourceNodeName: string,
    margin: number,
    { trackHistory = false }: { trackHistory?: boolean },
  ) => {
    const sourceNode = workflowStore.getNodeByName(sourceNodeName)
    const checkNodes = workflowHelpers.getConnectedNodes(
      'downstream',
      editableWorkflowObject.value,
      sourceNodeName,
    )

    for (const nodeName of checkNodes) {
      const node = workflowStore.getNodeByName(nodeName)

      if (node && sourceNode) {
        if (node.position[0] < sourceNode.position[0]) {
          continue
        }

        updateNodePosition(
          node.id,
          {
            x: node.position[0] + margin,
            y: node.position[1],
          },
          { trackHistory },
        )
      }
    }
  }

  const resolveNodeName = (node: INodeUi) => {
    node.name = uniqueNodeName(node.name)
  }

  function resolveNodePosition(
    node: Omit<INodeUi, 'position'> & { position?: INodeUi['position'] },
    nodeTypeDescription: INodeTypeDescription,
  ) {
    let position: XYPosition | undefined = node.position
    let pushOffsets: XYPosition = [40, 40]

    if (position) {
      return getNewNodePosition(workflowStore.workflow.nodes, position, pushOffsets)
    }

    // Available when
    // - clicking the plus button of a node handle
    // - dragging an edge / connection of a node handle
    // - selecting a node, adding a node via the node creator
    const lastInteractedWithNode = uiStore.lastInteractedWithNode
    // Available when clicking the plus button of a node edge / connection
    const lastInteractedWithNodeConnection = uiStore.lastInteractedWithNodeConnection
    // Available when dragging an edge / connection from a node
    const lastInteractedWithNodeHandle = uiStore.lastInteractedWithNodeHandle

    const { type: connectionType, index: connectionIndex } = parseCanvasConnectionHandleString(
      lastInteractedWithNodeHandle ?? lastInteractedWithNodeConnection?.sourceHandle ?? '',
    )

    const nodeSize = connectionType === NodeConnectionType.Main ? DEFAULT_NODE_SIZE : CONFIGURATION_NODE_SIZE

    if (lastInteractedWithNode) {
      const lastInteractedWithNodeTypeDescription = nodeTypesStore.getNodeType(
        lastInteractedWithNode.type,
        lastInteractedWithNode.typeVersion,
      )
      const lastInteractedWithNodeObject = editableWorkflowObject.value.getNode(
        lastInteractedWithNode.name,
      )

      const newNodeInsertPosition = uiStore.lastCancelledConnectionPosition

      if (newNodeInsertPosition) {
        // When pulling / cancelling a connection.
        // The new node should be placed at the same position as the mouse up event,
        // designated by the `newNodeInsertPosition` value.

        const xOffset = connectionType === NodeConnectionType.Main ? 0 : -nodeSize[0] / 2
        const yOffset = connectionType === NodeConnectionType.Main ? -nodeSize[1] / 2 : 0

        position = [newNodeInsertPosition[0] + xOffset, newNodeInsertPosition[1] + yOffset]

        uiStore.lastCancelledConnectionPosition = undefined
      }
      else if (lastInteractedWithNodeTypeDescription && lastInteractedWithNodeObject) {
        // When
        // - clicking the plus button of a node handle
        // - clicking the plus button of a node edge / connection
        // - selecting a node, adding a node via the node creator

        const lastInteractedWithNodeInputs = NodeHelpers.getNodeInputs(
          editableWorkflowObject.value,
          lastInteractedWithNodeObject,
          lastInteractedWithNodeTypeDescription,
        )
        const lastInteractedWithNodeInputTypes = NodeHelpers.getConnectionTypes(
          lastInteractedWithNodeInputs,
        )

        const lastInteractedWithNodeScopedInputTypes = (
          lastInteractedWithNodeInputTypes || []
        ).filter((input) => (input as unknown as NodeConnectionType) !== NodeConnectionType.Main)

        const lastInteractedWithNodeOutputs = NodeHelpers.getNodeOutputs(
          editableWorkflowObject.value,
          lastInteractedWithNodeObject,
          lastInteractedWithNodeTypeDescription,
        )
        const lastInteractedWithNodeOutputTypes = NodeHelpers.getConnectionTypes(
          lastInteractedWithNodeOutputs,
        )

        const lastInteractedWithNodeMainOutputs = lastInteractedWithNodeOutputTypes.filter(
          (output) => (output as unknown as NodeConnectionType) === NodeConnectionType.Main,
        )

        let yOffset = 0

        if (lastInteractedWithNodeConnection) {
          // When clicking the plus button of a node edge / connection
          // Compute the y offset for the new node based on the number of main outputs of the source node
          // and shift the downstream nodes accordingly

          shiftDownstreamNodesPosition(lastInteractedWithNode.name, PUSH_NODES_OFFSET, {
            trackHistory: true,
          })
        }

        if (lastInteractedWithNodeMainOutputs.length > 1) {
          const yOffsetValues = generateOffsets(
            lastInteractedWithNodeMainOutputs.length,
            NODE_SIZE,
            GRID_SIZE,
          )

          yOffset = yOffsetValues[connectionIndex]
        }

        let outputs: Array<NodeConnectionType | INodeOutputConfiguration> = []

        try {
          // It fails when the outputs are an expression. As those nodes have
          // normally no outputs by default and the only reason we need the
          // outputs here is to calculate the position, it is fine to assume
          // that they have no outputs and are so treated as a regular node
          // with only "main" outputs.
          outputs = NodeHelpers.getNodeOutputs(
            editableWorkflowObject.value,
            node as INode,
            nodeTypeDescription,
          ) as Array<NodeConnectionType | INodeOutputConfiguration>
        }
        catch {
          //
        }

        const outputTypes = NodeHelpers.getConnectionTypes(outputs as UnsafeAny)

        pushOffsets = [100, 0]

        if (
          outputTypes.length > 0
          && outputTypes.every((outputName) => (outputName as unknown as NodeConnectionType) !== NodeConnectionType.Main)
        ) {
          // When the added node has only non-main outputs (configuration nodes)
          // We want to place the new node directly below the last interacted with node.

          const scopedConnectionIndex = lastInteractedWithNodeScopedInputTypes.findIndex(
            (inputType) => (outputs[0] as unknown) === inputType,
          )

          const lastInteractedWithNodeWidthDivisions = Math.max(
            lastInteractedWithNodeScopedInputTypes.length + 1,
            1,
          )

          position = [
            lastInteractedWithNode.position[0]
            + (CONFIGURABLE_NODE_SIZE[0] / lastInteractedWithNodeWidthDivisions)
            * (scopedConnectionIndex + 1)
            - nodeSize[0] / 2,
            lastInteractedWithNode.position[1] + PUSH_NODES_OFFSET,
          ]
        }
        else {
          // When the node has only main outputs, mixed outputs, or no outputs at all
          // We want to place the new node directly to the right of the last interacted with node.

          let pushOffset = PUSH_NODES_OFFSET

          if (
            lastInteractedWithNodeInputTypes.find((input) => (input as unknown as NodeConnectionType) !== NodeConnectionType.Main)
          ) {
            // If the node has scoped inputs, push it down a bit more
            pushOffset += 140
          }

          // If a node is active then add the new node directly after the current one
          position = [
            lastInteractedWithNode.position[0] + pushOffset,
            lastInteractedWithNode.position[1] + yOffset,
          ]
        }
      }
    }

    if (!position) {
      if (nodeTypesStore.isTriggerNode(node.type) && triggerNodes.value.length === 0) {
        // When added node is a trigger, and it's the first one added to the canvas
        // we place it at root to replace the canvas add button

        position = [0, 0]
      }
      else {
        // When no position is set, we place the node at the last clicked position

        position = lastClickPosition.value
      }
    }

    return getNewNodePosition(workflowStore.workflow.nodes, position, pushOffsets)
  }

  const resolveNodeParameters = (node: INodeUi, nodeTypeDescription: INodeTypeDescription) => {
    const nodeParameters = NodeHelpers.getNodeParameters(
      nodeTypeDescription?.properties ?? [],
      node.parameters,
      true,
      false,
      node,
    )

    node.parameters = nodeParameters ?? {}
  }

  const resolveNodeWebhook = (node: INodeUi, nodeTypeDescription: INodeTypeDescription) => {
    if (nodeTypeDescription.webhooks?.length && !node.webhookId) {
      nodeHelpers.assignWebhookId(node)
    }

    // 如果节点是 webhook 或 form trigger 类型，并且 path 为空，则将 webhookId 作为默认路径
    if (
      [WEBHOOK_NODE_TYPE, FORM_TRIGGER_NODE_TYPE].includes(node.type)
      && node.parameters.path === ''
    ) {
      node.parameters.path = node.webhookId as string
    }
  }

  const { $toast } = useNuxtApp()

  const isConnectionAllowed = (
    sourceNode: INodeUi,
    targetNode: INodeUi,
    sourceConnection: IConnection | CanvasConnectionPort,
    targetConnection: IConnection | CanvasConnectionPort,
  ): boolean => {
    const blocklist = [STICKY_NODE_TYPE]

    if (sourceConnection.type !== targetConnection.type) {
      return false
    }

    if (blocklist.includes(sourceNode.type) || blocklist.includes(targetNode.type)) {
      return false
    }

    const sourceNodeType = nodeTypesStore.getNodeType(sourceNode.type, sourceNode.typeVersion)
    const sourceWorkflowNode = editableWorkflowObject.value.getNode(sourceNode.name)

    if (!sourceWorkflowNode) {
      return false
    }

    let sourceNodeOutputs: Array<NodeConnectionType | INodeOutputConfiguration> = []

    if (sourceNodeType) {
      sourceNodeOutputs = NodeHelpers.getNodeOutputs(
        editableWorkflowObject.value,
        sourceWorkflowNode,
        sourceNodeType,
      ) as Array<NodeConnectionType | INodeOutputConfiguration> || []
    }

    const sourceNodeHasOutputConnectionOfType = !!sourceNodeOutputs.find((output) => {
      const outputType = typeof output === 'string' ? output : output.type

      return outputType === sourceConnection.type
    })

    const sourceNodeHasOutputConnectionPortOfType = sourceConnection.index < sourceNodeOutputs.length

    if (!sourceNodeHasOutputConnectionOfType || !sourceNodeHasOutputConnectionPortOfType) {
      return false
    }

    const targetNodeType = nodeTypesStore.getNodeType(targetNode.type, targetNode.typeVersion)
    const targetWorkflowNode = editableWorkflowObject.value.getNode(targetNode.name)

    if (!targetWorkflowNode) {
      return false
    }

    let targetNodeInputs: Array<NodeConnectionType | INodeInputConfiguration> = []

    if (targetNodeType) {
      targetNodeInputs = NodeHelpers.getNodeInputs(
        editableWorkflowObject.value,
        targetWorkflowNode,
        targetNodeType,
      ) as Array<NodeConnectionType | INodeInputConfiguration> || []
    }

    const targetNodeHasInputConnectionOfType = !!targetNodeInputs.find((input) => {
      const inputType = typeof input === 'string' ? input : input.type

      if (inputType !== targetConnection.type) {
        return false
      }

      const filter = typeof input === 'object' && 'filter' in input ? input.filter : undefined

      if (filter?.nodes.length && !filter.nodes.includes(sourceNode.type)) {
        $toast.error({
          summary: '无法连接',
          detail: `节点 "${sourceNode.name}" 无法与节点 "${targetNode.name}" 连接，因为它们不兼容。`,
        })

        return false
      }

      return true
    })

    const targetNodeHasInputConnectionPortOfType = targetConnection.index < targetNodeInputs.length

    return targetNodeHasInputConnectionOfType && targetNodeHasInputConnectionPortOfType
  }

  function createConnection(
    connection: Connection,
    { trackHistory = false } = {},
  ) {
    const sourceNode = workflowStore.getNodeById(connection.source)
    const targetNode = workflowStore.getNodeById(connection.target)

    if (!sourceNode || !targetNode) {
      return
    }

    if (trackHistory) {
      historyStore.pushCommandToUndo(
        new AddConnectionCommand(
          mapCanvasConnectionToLegacyConnection(sourceNode, targetNode, connection),
        ),
      )
    }

    const mappedConnection = mapCanvasConnectionToLegacyConnection(
      sourceNode,
      targetNode,
      connection,
    )

    if (!isConnectionAllowed(sourceNode, targetNode, mappedConnection[0], mappedConnection[1])) {
      return
    }

    workflowStore.addConnection({
      connection: mappedConnection,
    })

    void nextTick(() => {
      nodeHelpers.updateNodeInputIssues(sourceNode)
      nodeHelpers.updateNodeInputIssues(targetNode)
    })
  }

  const resolveNodeVersion = (nodeTypeDescription: INodeTypeDescription) => {
    let nodeVersion = nodeTypeDescription.defaultVersion

    if (typeof nodeVersion === 'undefined') {
      nodeVersion = Array.isArray(nodeTypeDescription.version)
        ? nodeTypeDescription.version.slice(-1)[0]
        : nodeTypeDescription.version
    }

    return nodeVersion
  }

  /**
   * Resolves the data for a new node
   */
  function resolveNodeData(
    node: AddNodeDataWithTypeVersion,
    nodeTypeDescription: INodeTypeDescription,
  ) {
    const id = node.id ?? nodeHelpers.assignNodeId(node as INodeUi)
    const name = node.name ?? (nodeTypeDescription.defaults.name as string)
    const type = nodeTypeDescription.name
    const typeVersion = node.typeVersion
    const position = resolveNodePosition(node as INodeUi, nodeTypeDescription)
    const disabled = node.disabled ?? false
    const parameters = node.parameters ?? {}

    const nodeData: INodeUi = {
      ...node,
      id,
      name,
      type,
      typeVersion,
      position,
      disabled,
      parameters,
    }

    resolveNodeName(nodeData)
    resolveNodeParameters(nodeData, nodeTypeDescription)
    resolveNodeWebhook(nodeData, nodeTypeDescription)

    return nodeData
  }

  /**
   * Check if maximum allowed number of this type of node has been reached
   */
  function checkMaxNodesOfTypeReached(nodeTypeDescription: INodeTypeDescription) {
    if (
      nodeTypeDescription.maxNodes !== undefined
      && workflowHelpers.getNodeTypeCount(nodeTypeDescription.name) >= nodeTypeDescription.maxNodes
    ) {
      throw new Error(`在工作流中仅允许一个 '${nodeTypeDescription.displayName}' 节点 | 在工作流中仅允许 ${nodeTypeDescription.maxNodes} 个 '${nodeTypeDescription.displayName}' 节点`)
    }
  }

  function deleteConnection(
    connection: Connection,
    { trackHistory = false, trackBulk = true } = {},
  ) {
    const sourceNode = workflowStore.getNodeById(connection.source)
    const targetNode = workflowStore.getNodeById(connection.target)

    if (!sourceNode || !targetNode) {
      return
    }

    const mappedConnection = mapCanvasConnectionToLegacyConnection(
      sourceNode,
      targetNode,
      connection,
    )

    if (trackHistory && trackBulk) {
      historyStore.startRecordingUndo()
    }

    workflowStore.removeConnection({
      connection: mappedConnection,
    })

    if (trackHistory) {
      historyStore.pushCommandToUndo(new RemoveConnectionCommand(mappedConnection))

      if (trackBulk) {
        historyStore.stopRecordingUndo()
      }
    }
  }

  function createConnectionToLastInteractedWithNode(node: INodeUi, options: AddNodeOptions = {}) {
    const lastInteractedWithNode = uiStore.lastInteractedWithNode

    if (!lastInteractedWithNode) {
      return
    }

    const lastInteractedWithNodeId = lastInteractedWithNode.id
    const lastInteractedWithNodeConnection = uiStore.lastInteractedWithNodeConnection
    const lastInteractedWithNodeHandle = uiStore.lastInteractedWithNodeHandle

    // If we have a specific endpoint to connect to
    if (lastInteractedWithNodeHandle) {
      const { type: connectionType, mode } = parseCanvasConnectionHandleString(
        lastInteractedWithNodeHandle,
      )

      const nodeId = node.id
      const nodeHandle = createCanvasConnectionHandleString({
        mode: CanvasConnectionMode.Input,
        type: connectionType,
        index: 0,
      })

      if (mode === CanvasConnectionMode.Input) {
        createConnection({
          source: nodeId,
          sourceHandle: nodeHandle,
          target: lastInteractedWithNodeId,
          targetHandle: lastInteractedWithNodeHandle,
        })
      }
      else {
        createConnection({
          source: lastInteractedWithNodeId,
          sourceHandle: lastInteractedWithNodeHandle,
          target: nodeId,
          targetHandle: nodeHandle,
        })
      }
    }
    else {
      // If a node is last selected then connect between the active and its child ones
      // Connect active node to the newly created one
      createConnection({
        source: lastInteractedWithNodeId,
        sourceHandle: createCanvasConnectionHandleString({
          mode: CanvasConnectionMode.Output,
          type: NodeConnectionType.Main,
          index: 0,
        }),
        target: node.id,
        targetHandle: createCanvasConnectionHandleString({
          mode: CanvasConnectionMode.Input,
          type: NodeConnectionType.Main,
          index: 0,
        }),
      })
    }

    if (lastInteractedWithNodeConnection) {
      deleteConnection(lastInteractedWithNodeConnection, { trackHistory: options.trackHistory })

      const targetNode = workflowStore.getNodeById(lastInteractedWithNodeConnection.target)

      if (targetNode) {
        createConnection({
          source: node.id,
          sourceHandle: createCanvasConnectionHandleString({
            mode: CanvasConnectionMode.Input,
            type: NodeConnectionType.Main,
            index: 0,
          }),
          target: lastInteractedWithNodeConnection.target,
          targetHandle: lastInteractedWithNodeConnection.targetHandle,
        })
      }
    }
  }

  const addNode = (
    node: AddNodeDataWithTypeVersion,
    nodeTypeDescription: INodeTypeDescription,
    options: AddNodeOptions = {},
  ): INodeUi => {
    checkMaxNodesOfTypeReached(nodeTypeDescription)

    const nodeData = resolveNodeData(node, nodeTypeDescription)

    if (!nodeData) {
      throw new Error('无法创建节点')
    }

    workflowStore.addNode(nodeData)

    if (options.trackHistory) {
      historyStore.pushCommandToUndo(new AddNodeCommand(nodeData))
    }

    if (!options.isAutoAdd) {
      createConnectionToLastInteractedWithNode(nodeData, options)
    }

    void nextTick(() => {
      workflowStore.setNodePristine(nodeData.name, true)
      nodeHelpers.matchCredentials(nodeData)
      nodeHelpers.updateNodeParameterIssues(nodeData)
      nodeHelpers.updateNodeCredentialIssues(nodeData)
      nodeHelpers.updateNodeInputIssues(nodeData)

      if (nodeData.type !== STICKY_NODE_TYPE) {
        // void externalHooks.run('nodeView.addNodeButton', { nodeTypeName: nodeData.type })

        if (options.openNDV) {
          ndvStore.setActiveNodeName(nodeData.name)
        }
      }
    })

    return nodeData
  }

  const updatePositionForNodeWithMultipleInputs = (node: INodeUi) => {
    const inputNodes = editableWorkflowObject.value.getParentNodesByDepth(node.name, 1)

    if (inputNodes.length > 1) {
      inputNodes.slice(1).forEach((inputNode, index) => {
        const nodeUi = workflowStore.getNodeByName(inputNode.name)

        if (!nodeUi) {
          return
        }

        updateNodePosition(nodeUi.id, {
          x: nodeUi.position[0],
          y: nodeUi.position[1] + 100 * (index + 1),
        })
      })
    }
  }

  async function loadNodeTypesProperties(
    nodes: Array<Pick<INodeUi, 'type' | 'typeVersion'>>,
  ): Promise<void> {
    const allNodeTypeDescriptions: INodeTypeDescription[] = nodeTypesStore.allNodeTypes

    const nodesToBeFetched: INodeTypeNameVersion[] = []
    allNodeTypeDescriptions.forEach((nodeTypeDescription) => {
      const nodeVersions = Array.isArray(nodeTypeDescription.version)
        ? nodeTypeDescription.version
        : [nodeTypeDescription.version]

      if (
        !!nodes.find(
          (n) => n.type === nodeTypeDescription.name && nodeVersions.includes(n.typeVersion),
        )
        && !Object.hasOwn(nodeTypeDescription, 'properties')
      ) {
        nodesToBeFetched.push({
          name: nodeTypeDescription.name,
          version: Array.isArray(nodeTypeDescription.version)
            ? nodeTypeDescription.version.slice(-1)[0]
            : nodeTypeDescription.version,
        })
      }
    })

    if (nodesToBeFetched.length > 0) {
      // Only call API if node information is actually missing
      await nodeTypesStore.getNodesInformation(nodesToBeFetched)
    }
  }

  async function addNodes(nodes: AddedNodesAndConnections['nodes'], options: AddNodesOptions = {}) {
    let insertPosition = options.position
    let lastAddedNode: INodeUi | undefined
    const addedNodes: INodeUi[] = []

    const nodesWithTypeVersion = nodes.map((node) => {
      const typeVersion = node.typeVersion ?? resolveNodeVersion(requireNodeTypeDescription(node.type))

      return {
        ...node,
        typeVersion,
      }
    })

    await loadNodeTypesProperties(nodesWithTypeVersion)

    if (options.trackHistory && options.trackBulk) {
      historyStore.startRecordingUndo()
    }

    for (const nodeAddData of nodesWithTypeVersion) {
      const { isAutoAdd, openDetail: openNDV, ...node } = nodeAddData
      const position = node.position ?? insertPosition
      const nodeTypeDescription = requireNodeTypeDescription(node.type, node.typeVersion)

      try {
        const newNode = addNode(
          {
            ...node,
            position,
          },
          nodeTypeDescription,
          {
            ...options,
            openNDV,
            isAutoAdd,
          },
        )
        lastAddedNode = newNode
        addedNodes.push(newNode)
      }
      catch (error) {
        $toast.error({
          summary: '错误',
        })
        consola.error(error)
        continue
      }

      // When we're adding multiple nodes, increment the X position for the next one
      insertPosition = [
        lastAddedNode.position[0] + NODE_SIZE * 2 + GRID_SIZE,
        lastAddedNode.position[1],
      ]
    }

    if (lastAddedNode) {
      updatePositionForNodeWithMultipleInputs(lastAddedNode)
    }

    if (options.trackHistory && options.trackBulk) {
      historyStore.stopRecordingUndo()
    }

    return addedNodes
  }

  const addConnections = async (
    connections: CanvasConnectionCreateData[] | CanvasConnection[],
    { trackBulk = true, trackHistory = false } = {},
  ) => {
    // Connection creation relies on the nodes being already added to the store
    await nextTick()

    if (trackBulk && trackHistory) {
      historyStore.startRecordingUndo()
    }

    for (const connection of connections) {
      createConnection(connection, { trackHistory })
    }

    if (trackBulk && trackHistory) {
      historyStore.stopRecordingUndo()
    }
  }

  // MARK: Workspace 操作

  const resetWorkspace = () => {
    workflowStore.resetState()

    nodeCreatorStore.closeNodeCreator()
    ndvStore.reset()
    historyStore.reset()

    // Reset node creator
    nodeCreatorStore.setNodeCreatorState({ createNodeActive: false })
    // nodeCreatorStore.setShowScrim(false)

    // Reset actions
    uiStore.resetLastInteractedWith()
    uiStore.removeActiveAction('workflowRunning')
    uiStore.stateIsDirty = false

    // Reset executions
    executionsStore.activeExecution = null

    // Reset credentials updates
    // nodeHelpers.credentialsUpdated.value = false
  }

  const initWorkspace = (data: IWorkflowDb) => {
    data.nodes.forEach((node) => {
      const nodeTypeDescription = requireNodeTypeDescription(node.type, node.typeVersion)
      nodeHelpers.matchCredentials(node)
      resolveNodeParameters(node, nodeTypeDescription)
      resolveNodeWebhook(node, nodeTypeDescription)
    })

    workflowStore.setNodes(data.nodes)
    workflowStore.setConnections(data.connections)

    // HACK: initState 需要在 setNodes 后调用，否则会存在缓存的节点数据丢失的问题
    workflowHelpers.initState(data)
  }

  function connectAdjacentNodes(id: string, { trackHistory = false } = {}) {
    const node = workflowStore.getNodeById(id)

    if (!node) {
      return
    }

    const outputConnectionsByType = workflowStore.outgoingConnectionsByNodeName(node.name)
    const incomingConnectionsByType = workflowStore.incomingConnectionsByNodeName(node.name)

    for (const [type, incomingConnectionsByInputIndex] of Object.entries(
      incomingConnectionsByType,
    ) as Array<[NodeConnectionType, NodeInputConnections]>) {
      // Only connect nodes connected to the first input of a type
      for (const incomingConnection of incomingConnectionsByInputIndex.at(0) ?? []) {
        const incomingNodeId = workflowStore.getNodeByName(incomingConnection.node)?.id

        if (!incomingNodeId) {
          continue
        }

        // Only connect to nodes connected to the first output of a type
        // For example on an If node, connect to the "true" main output
        for (const outgoingConnection of outputConnectionsByType[type]?.at(0) ?? []) {
          const outgoingNodeId = workflowStore.getNodeByName(outgoingConnection.node)?.id

          if (!outgoingNodeId) {
            continue
          }

          if (trackHistory) {
            historyStore.pushCommandToUndo(
              new AddConnectionCommand([
                {
                  node: incomingConnection.node,
                  type: type as UnsafeAny,
                  index: incomingConnection.index,
                },
                {
                  node: outgoingConnection.node,
                  type: type as UnsafeAny,
                  index: outgoingConnection.index,
                },
              ]),
            )
          }

          createConnection({
            source: incomingNodeId,
            sourceHandle: createCanvasConnectionHandleString({
              mode: CanvasConnectionMode.Output,
              type,
              index: incomingConnection.index,
            }),
            target: outgoingNodeId,
            targetHandle: createCanvasConnectionHandleString({
              mode: CanvasConnectionMode.Input,
              type,
              index: outgoingConnection.index,
            }),
          })
        }
      }
    }
  }

  function deleteConnectionsByNodeId(
    targetNodeId: string,
    { trackHistory = false, trackBulk = true } = {},
  ) {
    const targetNode = workflowStore.getNodeById(targetNodeId)

    if (!targetNode) {
      return
    }

    if (trackHistory && trackBulk) {
      historyStore.startRecordingUndo()
    }

    const connections = workflowStore.workflow.connections

    for (const nodeName of Object.keys(connections)) {
      const node = workflowStore.getNodeByName(nodeName)

      if (!node) {
        continue
      }

      for (const type of Object.keys(connections[nodeName])) {
        for (const index of Object.keys(connections[nodeName][type])) {
          const connectionsToDelete = connections[nodeName][type][parseInt(index, 10)] ?? []

          for (const connectionIndex of Object.keys(connectionsToDelete)) {
            const connectionData = connectionsToDelete[parseInt(connectionIndex, 10)]

            if (!connectionData) {
              continue
            }

            const connectionDataNode = workflowStore.getNodeByName(connectionData.node)

            if (
              connectionDataNode
              && (connectionDataNode.id === targetNode.id || node.name === targetNode.name)
            ) {
              deleteConnection(
                {
                  source: node.id,
                  sourceHandle: createCanvasConnectionHandleString({
                    mode: CanvasConnectionMode.Output,
                    type: type as NodeConnectionType,
                    index: parseInt(index, 10),
                  }),
                  target: connectionDataNode.id,
                  targetHandle: createCanvasConnectionHandleString({
                    mode: CanvasConnectionMode.Input,
                    type: connectionData.type as unknown as NodeConnectionType,
                    index: connectionData.index,
                  }),
                },
                { trackHistory, trackBulk: false },
              )
            }
          }
        }
      }
    }

    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
    delete workflowStore.workflow.connections[targetNode.name]

    if (trackHistory && trackBulk) {
      historyStore.stopRecordingUndo()
    }
  }

  function removeUnknownCredentials(workflow: IWorkflowDataUpdate) {
    if (!workflow?.nodes) {
      return
    }

    for (const node of workflow.nodes) {
      if (!node.credentials) {
        continue
      }

      for (const [name, credential] of Object.entries(node.credentials)) {
        if (typeof credential === 'string' || credential.id === null) {
          continue
        }

        if (!credentialsStore.getCredentialById(credential.id)) {
          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
          delete node.credentials[name]
        }
      }
    }
  }

  async function addImportedNodesToWorkflow(
    data: IWorkflowDataUpdate,
    { trackBulk = true, trackHistory = false } = {},
  ): Promise<IWorkflowDataUpdate> {
    // Because nodes with the same name maybe already exist, it could
    // be needed that they have to be renamed. Also could it be possible
    // that nodes are not allowed to be created because they have a create
    // limit set. So we would then link the new nodes with the already existing ones.
    // In this object all that nodes get saved in the format:
    //   old-name -> new-name
    const nodeNameTable: {
      [key: string]: string
    } = {}
    const newNodeNames = new Set<string>()

    if (!data.nodes) {
      // No nodes to add
      throw new Error('未指定要添加的节点')
    }

    // Get how many of the nodes of the types which have
    // a max limit set already exist
    const nodeTypesCount = workflowHelpers.getNodeTypesMaxCount()

    let oldName: string
    let newName: string
    const createNodes: INode[] = []

    await nodeHelpers.loadNodesProperties(
      data.nodes.map((node) => ({ name: node.type, version: node.typeVersion })),
    )

    data.nodes.forEach((node) => {
      if (nodeTypesCount[node.type] !== undefined) {
        if (nodeTypesCount[node.type].exist >= nodeTypesCount[node.type].max) {
          // Node is not allowed to be created so
          // do not add it to the create list but
          // add the name of the existing node
          // that this one gets linked up instead.
          nodeNameTable[node.name] = nodeTypesCount[node.type].nodeNames[0]

          return
        }
        else {
          // Node can be created but increment the
          // counter in case multiple ones are
          // supposed to be created
          nodeTypesCount[node.type].exist += 1
        }
      }

      oldName = node.name

      const localized = node.name

      newName = uniqueNodeName(localized, Array.from(newNodeNames))

      newNodeNames.add(newName)
      nodeNameTable[oldName] = newName

      createNodes.push(node)
    })

    // Get only the connections of the nodes that get created
    const newConnections: IConnections = {}
    const currentConnections = data.connections ?? {}
    const createNodeNames = createNodes.map((node) => node.name)
    let sourceNode, type, sourceIndex, connectionIndex, connectionData

    for (sourceNode of Object.keys(currentConnections)) {
      if (!createNodeNames.includes(sourceNode)) {
        // Node does not get created so skip output connections
        continue
      }

      const connection: INodeConnections = {}

      for (type of Object.keys(currentConnections[sourceNode])) {
        connection[type] = []

        for (
          sourceIndex = 0;
          sourceIndex < currentConnections[sourceNode][type].length;
          sourceIndex++
        ) {
          const nodeSourceConnections = []
          const connectionsToCheck = currentConnections[sourceNode][type][sourceIndex]

          if (connectionsToCheck) {
            for (
              connectionIndex = 0;
              connectionIndex < connectionsToCheck.length;
              connectionIndex++
            ) {
              connectionData = connectionsToCheck[connectionIndex]

              if (!createNodeNames.includes(connectionData.node)) {
                // Node does not get created so skip input connection
                continue
              }

              nodeSourceConnections.push(connectionData)
              // Add connection
            }
          }

          connection[type].push(nodeSourceConnections)
        }
      }

      newConnections[sourceNode] = connection
    }

    // Create a workflow with the new nodes and connections that we can use
    // the rename method
    const tempWorkflow: Workflow = workflowStore.getWorkflow(createNodes, newConnections)

    // Rename all the nodes of which the name changed
    for (oldName in nodeNameTable) {
      if (oldName === nodeNameTable[oldName]) {
        // Name did not change so skip
        continue
      }

      tempWorkflow.renameNode(oldName, nodeNameTable[oldName])
    }

    if (data.pinData) {
      let pinDataSuccess = true

      for (const nodeName of Object.keys(data.pinData)) {
        // Pin data limit reached
        if (!pinDataSuccess) {
          $toast.error({
            summary: '固定数据过大',
            detail: '工作流已达到允许的最大固定数据大小',
          })
          continue
        }

        const node = tempWorkflow.nodes[nodeNameTable[nodeName]]

        try {
          const pinnedDataForNode = usePinnedData(node)
          pinnedDataForNode.setData(data.pinData[nodeName], 'add-nodes')
          pinDataSuccess = true
        }
        catch (error) {
          pinDataSuccess = false
          console.error(error)
        }
      }
    }

    // Add the nodes with the changed node names, expressions and connections
    if (trackBulk && trackHistory) {
      historyStore.startRecordingUndo()
    }

    await addNodes(Object.values(tempWorkflow.nodes), { trackBulk: false, trackHistory })
    await addConnections(
      mapLegacyConnectionsToCanvasConnections(
        tempWorkflow.connectionsBySourceNode,
        Object.values(tempWorkflow.nodes),
      ),
      { trackBulk: false, trackHistory },
    )

    if (trackBulk && trackHistory) {
      historyStore.stopRecordingUndo()
    }

    uiStore.stateIsDirty = true

    return {
      nodes: Object.values(tempWorkflow.nodes),
      connections: tempWorkflow.connectionsBySourceNode,
    }
  }

  async function importWorkflowTags(workflowData: IWorkflowDataUpdate) {
    const allTags = await tagsStore.fetchAll()
    const tagNames = new Set(allTags.map((tag) => tag.name))

    const workflowTags = workflowData.tags as ITag[]
    const notFound = workflowTags.filter((tag) => !tagNames.has(tag.name))

    const creatingTagPromises: Array<Promise<ITag>> = []

    for (const tag of notFound) {
      const creationPromise = tagsStore.create(tag.name).then((newTag: ITag) => {
        allTags.push(newTag)

        return newTag
      })

      creatingTagPromises.push(creationPromise)
    }

    await Promise.all(creatingTagPromises)

    const tagIds = workflowTags.reduce((accu: string[], imported: ITag) => {
      const tag = allTags.find((t) => t.name === imported.name)

      if (tag) {
        accu.push(tag.id)
      }

      return accu
    }, [])

    workflowStore.addWorkflowTagIds(tagIds)
  }

  async function importWorkflowData(
    workflowData: IWorkflowDataUpdate,
    source: string,
    importTags = true,
    { trackBulk = true, trackHistory = true } = {},
  ): Promise<IWorkflowDataUpdate> {
    uiStore.resetLastInteractedWith()

    // If it is JSON check if it looks on the first look like data we can use
    if (!Object.hasOwn(workflowData, 'nodes') || !Object.hasOwn(workflowData, 'connections')) {
      return {}
    }

    try {
      const nodeIdMap: { [prev: string]: string } = {}

      if (workflowData.nodes) {
        const nodeNames = new Set(workflowData.nodes.map((node) => node.name))
        workflowData.nodes.forEach((node: INode) => {
          // Provide a new name for nodes that don't have one
          if (!node.name) {
            const nodeType = nodeTypesStore.getNodeType(node.type)
            const newName = uniqueNodeName(
              nodeType?.displayName ?? node.type,
              Array.from(nodeNames),
            )
            node.name = newName
            nodeNames.add(newName)
          }

          // Generate new webhookId if workflow already contains a node with the same webhookId
          if (node.webhookId && UPDATE_WEBHOOK_ID_NODE_TYPES.includes(node.type)) {
            const isDuplicate = Object.values(getCurrentWorkflow().nodes).some(
              (n) => n.webhookId === node.webhookId,
            )

            if (isDuplicate) {
              nodeHelpers.assignWebhookId(node)

              if (node.parameters.path) {
                node.parameters.path = node.webhookId as string
              }
              else if ((node.parameters.options as IDataObject).path) {
                (node.parameters.options as IDataObject).path = node.webhookId as string
              }
            }
          }

          // Set all new ids when pasting/importing workflows
          if (node.id) {
            const previousId = node.id
            const newId = nodeHelpers.assignNodeId(node)
            nodeIdMap[newId] = previousId
          }
          else {
            nodeHelpers.assignNodeId(node)
          }
        })
      }

      removeUnknownCredentials(workflowData)

      // Fix the node position as it could be totally offscreen
      // and the pasted nodes would so not be directly visible to
      // the user
      workflowHelpers.updateNodePositions(
        workflowData,
        getNewNodePosition(editableWorkflow.value.nodes, lastClickPosition.value),
      )

      await addImportedNodesToWorkflow(workflowData, { trackBulk, trackHistory })

      if (importTags && settingsStore.areTagsEnabled && Array.isArray(workflowData.tags)) {
        await importWorkflowTags(workflowData)
      }

      return workflowData
    }
    catch (err) {
      $toast.error({
        summary: '导入工作流问题',
        detail: err instanceof Error ? err.message : '导入工作流失败',
      })

      return {}
    }
  }

  const deleteNode = (id: string, { trackHistory = false, trackBulk = true } = {}) => {
    const node = workflowStore.getNodeById(id)

    if (!node) {
      return
    }

    if (trackHistory && trackBulk) {
      historyStore.startRecordingUndo()
    }

    if (uiStore.lastInteractedWithNodeId === id) {
      uiStore.lastInteractedWithNodeId = undefined
    }

    connectAdjacentNodes(id, { trackHistory })
    deleteConnectionsByNodeId(id, { trackHistory, trackBulk: false })

    workflowStore.removeNodeExecutionDataById(id)
    workflowStore.removeNodeById(id)

    if (trackHistory) {
      historyStore.pushCommandToUndo(new RemoveNodeCommand(node))

      if (trackBulk) {
        historyStore.stopRecordingUndo()
      }
    }
  }

  const deleteNodes = (ids: string[], { trackHistory = true, trackBulk = true } = {}) => {
    if (trackHistory && trackBulk) {
      historyStore.startRecordingUndo()
    }

    ids.forEach((id) => deleteNode(id, { trackHistory, trackBulk: false }))

    if (trackHistory && trackBulk) {
      historyStore.stopRecordingUndo()
    }
  }

  const setNodeParameters = (id: string, parameters: Record<string, unknown>) => {
    const node = workflowStore.getNodeById(id)

    if (!node) {
      return
    }

    workflowStore.setNodeParameters(
      {
        name: node.name,
        value: parameters as NodeParameterValueType,
      },
      true,
    )
  }

  const revertUpdateNodePosition = (nodeName: string, position: CanvasNode['position']) => {
    const node = workflowStore.getNodeByName(nodeName)

    if (!node) {
      return
    }

    updateNodePosition(node.id, position)
  }

  const revertDeleteNode = (node: INodeUi) => {
    workflowStore.addNode(node)
  }

  const revertAddNode = async (nodeName: string) => {
    const node = workflowStore.getNodeByName(nodeName)

    if (!node) {
      return
    }

    deleteNode(node.id)
  }

  const revertCreateConnection = (connection: [IConnection, IConnection]) => {
    const sourceNodeName = connection[0].node
    const sourceNode = workflowStore.getNodeByName(sourceNodeName)
    const targetNodeName = connection[1].node
    const targetNode = workflowStore.getNodeByName(targetNodeName)

    if (!sourceNode || !targetNode) {
      return
    }

    deleteConnection(mapLegacyConnectionToCanvasConnection(sourceNode, targetNode, connection))
  }

  const revertDeleteConnection = (connection: [IConnection, IConnection]) => {
    workflowStore.addConnection({
      connection,
    })
  }

  async function renameNode(
    currentName: string,
    newName: string,
    { trackHistory = false, trackBulk = true } = {},
  ) {
    if (currentName === newName) {
      return
    }

    if (trackHistory && trackBulk) {
      historyStore.startRecordingUndo()
    }

    newName = uniqueNodeName(newName)

    // Rename the node and update the connections
    const workflow = workflowStore.getCurrentWorkflow(true)
    workflow.renameNode(currentName, newName)

    if (trackHistory) {
      historyStore.pushCommandToUndo(new RenameNodeCommand(currentName, newName))
    }

    // Update also last selected node and execution data
    workflowStore.renameNodeSelectedAndExecution({ old: currentName, new: newName })

    workflowStore.setNodes(Object.values(workflow.nodes))
    workflowStore.setConnections(workflow.connectionsBySourceNode)

    const isRenamingActiveNode = ndvStore.activeNodeName === currentName

    if (isRenamingActiveNode) {
      ndvStore.setActiveNodeName(newName)
    }

    if (trackHistory && trackBulk) {
      historyStore.stopRecordingUndo()
    }
  }

  const revertRenameNode = async (currentName: string, previousName: string) => {
    await renameNode(currentName, previousName)
  }

  const revertToggleNodeDisabled = (nodeName: string) => {
    const node = workflowStore.getNodeByName(nodeName)

    if (node) {
      nodeHelpers.disableNodes([node])
    }
  }

  function filterAllowedCredentials(
    credentials: INodeCredentials,
    usedCredentials: Record<string, IUsedCredential>,
  ): INodeCredentials {
    return Object.fromEntries(
      Object.entries(credentials).filter(([, credential]) => {
        return (
          credential.id
          && (!usedCredentials[credential.id] || usedCredentials[credential.id]?.currentUserHasAccess)
        )
      }),
    )
  }

  function filterConnectionsByNodes(
    connections: INodeConnections,
    includeNodeNames: Set<string>,
  ): INodeConnections {
    const filteredConnections: INodeConnections = {}

    for (const [type, typeConnections] of Object.entries(connections)) {
      const validConnections = typeConnections.map((sourceConnections) =>
        (sourceConnections ?? []).filter((connection) => includeNodeNames.has(connection.node)),
      )

      if (validConnections.length) {
        filteredConnections[type] = validConnections
      }
    }

    return filteredConnections
  }

  function getConnectionsForNodes(
    nodes: INodeUi[],
    includeNodeNames: Set<string>,
  ): Record<string, INodeConnections> {
    const connections: Record<string, INodeConnections> = {}

    for (const node of nodes) {
      const outgoingConnections = workflowStore.outgoingConnectionsByNodeName(node.name)

      if (!Object.keys(outgoingConnections).length) {
        continue
      }

      const filteredConnections = filterConnectionsByNodes(outgoingConnections, includeNodeNames)

      if (Object.keys(filteredConnections).length) {
        connections[node.name] = filteredConnections
      }
    }

    return connections
  }

  function getNodesToSave(nodes: INode[]): IWorkflowData {
    const data = {
      nodes: [] as INodeUi[],
      connections: {} as IConnections,
      pinData: {} as IPinData,
    } satisfies IWorkflowData

    const exportedNodeNames = new Set<string>()

    for (const node of nodes) {
      const nodeSaveData = workflowHelpers.getNodeDataToSave(node)
      const pinDataForNode = workflowStore.pinDataByNodeName(node.name)

      if (pinDataForNode) {
        data.pinData[node.name] = pinDataForNode
      }

      if (
        nodeSaveData.credentials
        && settingsStore.isEnterpriseFeatureEnabled[EnterpriseEditionFeature.Sharing]
      ) {
        nodeSaveData.credentials = filterAllowedCredentials(
          nodeSaveData.credentials,
          workflowStore.usedCredentials,
        )
      }

      data.nodes.push(nodeSaveData)
      exportedNodeNames.add(node.name)
    }

    data.connections = getConnectionsForNodes(data.nodes, exportedNodeNames)

    workflowHelpers.removeForeignCredentialsFromWorkflow(data, credentialsStore.allCredentials)

    return data
  }

  async function copyNodes(ids: string[]) {
    const workflowData = deepCopy(getNodesToSave(workflowStore.getNodesByIds(ids)))

    workflowData.meta = {
      ...workflowData.meta,
      ...workflowStore.workflow.meta,
      instanceId: rootStore.instanceId,
    }

    await copyToClipboard(JSON.stringify(workflowData, null, 2))
  }

  async function duplicateNodes(ids: string[]) {
    const workflowData = deepCopy(getNodesToSave(workflowStore.getNodesByIds(ids)))
    const result = await importWorkflowData(workflowData, 'duplicate', false)

    return result.nodes?.map((node) => node.id).filter(isPresent) ?? []
  }

  async function openExecution(executionId: string) {
    let data: IExecutionResponse | undefined

    try {
      data = await workflowStore.getExecution(executionId)
    }
    catch (err) {
      $toast.error({
        summary: '加载执行时出错',
        detail: err instanceof Error ? err.message : '打开执行失败',
      })

      return
    }

    if (data === undefined) {
      throw new Error(`Execution with id "${executionId}" could not be found!`)
    }

    initWorkspace(data.workflowData)

    workflowStore.setWorkflowExecutionData(data)

    if (!['manual', 'evaluation'].includes(data.mode)) {
      workflowStore.setWorkflowPinData({})
    }

    uiStore.stateIsDirty = false

    return data
  }

  return {
    resetWorkspace,
    initWorkspace,

    addNodes,
    addConnections,
    deleteNode,
    deleteNodes,
    renameNode,
    copyNodes,
    duplicateNodes,

    createConnection,
    deleteConnection,

    setNodeActiveByName,
    editableWorkflow,
    editableWorkflowObject,
    setNodeParameters,
    importWorkflowData,
    openExecution,

    updateNodePosition,
    updateNodesPosition,

    revertUpdateNodePosition,
    revertDeleteNode,
    revertAddNode,
    revertCreateConnection,
    revertDeleteConnection,
    revertRenameNode,
    revertToggleNodeDisabled,
  }
}
