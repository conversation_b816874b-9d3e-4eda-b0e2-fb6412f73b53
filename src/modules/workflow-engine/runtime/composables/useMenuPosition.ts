import type { XYPosition } from '#wf/types/interface'

interface MenuPositionOptions {
  /** 安全边距 */
  safeMargin?: number
  /** 鼠标/触发点位置 */
  position: Ref<XYPosition>
  /** 菜单宽度 */
  menuWidth: Ref<number>
  /** 菜单高度 */
  menuHeight: Ref<number>
  /** 特殊的源类型，会影响位置计算 */
  sourceType?: Ref<string | undefined>
}

/**
 * 计算菜单在屏幕中的最佳显示位置
 * @param options 菜单位置配置选项
 */
export function useMenuPosition(options: MenuPositionOptions) {
  const {
    position,
    menuWidth,
    menuHeight,
    sourceType = ref(undefined),
    safeMargin = 50,
  } = options

  // 判断菜单是否靠近浏览器左侧边缘
  const isNearLeftEdge = computed(() => {
    if (typeof window === 'undefined') {
      return false
    }

    return position.value[0] < safeMargin
  })

  // 判断菜单是否靠近浏览器右侧边缘
  const isNearRightEdge = computed(() => {
    if (typeof window === 'undefined') {
      return false
    }

    const windowWidth = window.innerWidth
    const menuRightPosition = position.value[0] + menuWidth.value

    return menuRightPosition > windowWidth - safeMargin
  })

  // 判断菜单是否靠近浏览器顶部边缘
  const isNearTopEdge = computed(() => {
    if (typeof window === 'undefined') {
      return false
    }

    return position.value[1] < safeMargin
  })

  // 判断菜单是否靠近浏览器底部边缘
  const isNearBottomEdge = computed(() => {
    if (typeof window === 'undefined') {
      return false
    }

    const windowHeight = window.innerHeight
    const menuBottomPosition = position.value[1] + menuHeight.value

    return menuBottomPosition > windowHeight - safeMargin
  })

  // 计算水平方向的位置类
  const horizontalPositionClass = computed(() => {
    // 特定源类型的处理
    if (sourceType.value === 'node-setting-header') {
      return 'right-0'
    }

    // 处理左右边缘冲突情况
    if (isNearLeftEdge.value && isNearRightEdge.value) {
      // 菜单宽度超过屏幕宽度，居中对齐
      return 'left-1/2 -translate-x-1/2'
    }

    // 根据是否靠近右侧边缘决定对齐方式
    if (isNearRightEdge.value) {
      return 'right-0'
    }

    return 'left-0'
  })

  // 计算垂直方向的位置类
  const verticalPositionClass = computed(() => {
    // 处理上下边缘冲突情况
    if (isNearTopEdge.value && isNearBottomEdge.value) {
      // 菜单高度超过屏幕高度，居中对齐
      return 'top-1/2 -translate-y-1/2'
    }

    // 根据是否靠近底部边缘决定对齐方式
    if (isNearBottomEdge.value) {
      return 'bottom-0'
    }

    return 'top-0'
  })

  return {
    isNearLeftEdge,
    isNearRightEdge,
    isNearTopEdge,
    isNearBottomEdge,
    horizontalPositionClass,
    verticalPositionClass,
  }
}
