import type { ExecutionSummary, RelatedExecution } from 'n8n-workflow'

import { DateFormat } from '~/enums/common'
import { RouteKey } from '~/enums/route'

export interface IExecutionUIData {
  name: string
  label: string
  createdAt: string
  startTime: string
  runningTime: string
  showTimestamp: boolean
  tags: Array<{ id: string, name: string }>
}

export function useExecutionHelpers() {
  const displayTimer = (msPassed: number, showMs = false): string => {
    const oneMinute = 60000

    if (msPassed < oneMinute) {
      if (!showMs) {
        return `${Math.floor(msPassed / 1000)} 秒`
      }

      return `${msPassed / 1000} 秒`
    }

    const secondsPassed = Math.floor(msPassed / 1000)
    const minutesPassed = Math.floor(secondsPassed / 60)
    const secondsLeft = (secondsPassed - minutesPassed * 60).toString().padStart(2, '0')

    return `${minutesPassed}:${secondsLeft} 分钟`
  }

  const getUIDetails = (execution: ExecutionSummary): IExecutionUIData => {
    const status = {
      name: 'unknown',
      createdAt: execution.createdAt?.toString() ?? '',
      startTime: formatDate(execution.startedAt, DateFormat.YYYY_MM_DD_HH_MM_SS)!,
      label: 'Status unknown',
      runningTime: '',
      showTimestamp: true,
      tags: execution.annotation?.tags ?? [],
    }

    if (execution.status === 'new') {
      status.name = 'new'
      status.label = '排队中'
      status.showTimestamp = false
    }
    else if (execution.status === 'waiting') {
      status.name = 'waiting'
      status.label = '等待执行'
      status.showTimestamp = false
    }
    else if (execution.status === 'canceled') {
      status.label = '已取消'
    }
    else if (execution.status === 'running') {
      status.name = 'running'
      status.label = '运行中'
    }
    else if (execution.status === 'success') {
      status.name = 'success'
      status.label = '执行成功'
    }
    else if (execution.status === 'error' || execution.status === 'crashed') {
      status.name = 'error'
      status.label = '执行失败'
    }

    if (!execution.status) {
      execution.status = 'unknown'
    }

    if (execution.startedAt && execution.stoppedAt) {
      const stoppedAt = execution.stoppedAt ? new Date(execution.stoppedAt).getTime() : Date.now()
      const startedAt = new Date(execution.startedAt).getTime()

      status.runningTime = displayTimer(
        stoppedAt - startedAt,
        true,
      )
    }

    return status
  }

  const isExecutionRetriable = (execution: ExecutionSummary): boolean => {
    return ['crashed', 'error'].includes(execution.status) && !execution.retrySuccessId
  }

  function resolveRelatedExecutionUrl(metadata: {
    parentExecution?: RelatedExecution
    subExecution?: RelatedExecution
  }): string {
    const info = metadata.parentExecution || metadata.subExecution

    if (!info) {
      return ''
    }

    const { workflowId, executionId } = info

    return getRoutePath(RouteKey.TW_工作台, {
      name: workflowId,
      executionId,
    })
  }

  return {
    getUIDetails,
    formatDate,
    isExecutionRetriable,
    resolveRelatedExecutionUrl,
  }
}
