import { refThrottled } from '@vueuse/core'

import { CanvasConnectionMode, CanvasNodeRenderType } from '#wf/constants/canvas'
import { CanvasNodeKey } from '#wf/constants/injection'
import type { CanvasNodeData } from '#wf/types/canvas'

const defaultNodeData: CanvasNodeData = {
  id: '',
  name: '',
  subtitle: '',
  type: '',
  typeVersion: 1,
  disabled: false,
  inputs: [],
  outputs: [],
  connections: { [CanvasConnectionMode.Input]: {}, [CanvasConnectionMode.Output]: {} },
  issues: { items: [], visible: false },
  pinnedData: { count: 0, visible: false },
  execution: {
    running: false,
  },
  runData: { iterations: 0, outputMap: {}, visible: false },
  render: {
    type: CanvasNodeRenderType.Default,
    options: {},
  },
}

export function useCanvasNode() {
  const node = inject(CanvasNodeKey)

  const data = computed(() => node?.data.value ?? defaultNodeData)

  const nodeId = computed(() => node?.id.value ?? '')
  const label = computed(() => node?.label.value ?? '')

  const subtitle = computed(() => data.value.subtitle)
  const name = computed(() => data.value.name)
  const inputs = computed(() => data.value.inputs)
  const outputs = computed(() => data.value.outputs)
  const connections = computed(() => data.value.connections)

  const isDisabled = computed(() => data.value.disabled)
  const isReadOnly = computed(() => node?.readOnly.value)
  const isSelected = computed(() => node?.selected.value)

  const pinnedDataCount = computed(() => data.value.pinnedData.count)
  const hasPinnedData = computed(() => data.value.pinnedData.count > 0)

  const issues = computed(() => data.value.issues.items ?? [])
  const hasIssues = computed(() => data.value.issues.visible)

  const executionStatus = computed(() => data.value.execution.status)
  const executionWaiting = computed(() => data.value.execution.waiting)
  const executionRunning = computed(() => data.value.execution.running)
  const executionRunningThrottled = refThrottled(executionRunning, 50)

  const runDataOutputMap = computed(() => data.value.runData.outputMap)
  const runDataIterations = computed(() => data.value.runData.iterations)
  const hasRunData = computed(() => data.value.runData.visible)

  const render = computed(() => data.value.render)

  // const eventBus = computed(() => node?.eventBus.value)

  return {
    node,
    nodeId,
    name,
    label,
    subtitle,
    inputs,
    outputs,
    connections,

    isDisabled,
    isReadOnly,
    isSelected,

    pinnedDataCount,
    hasPinnedData,
    runDataIterations,
    runDataOutputMap,
    hasRunData,

    issues,
    hasIssues,

    executionStatus,
    executionWaiting,
    executionRunning,
    executionRunningThrottled,
    render,
    // eventBus,
  }
}
