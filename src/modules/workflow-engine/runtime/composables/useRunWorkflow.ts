import { get } from 'lodash-es'
import type {
  IDataObject,
  INode,
  IPinData,
  IRun,
  IRunData,
  IRunExecutionData,
  ITaskData,
  StartNodeData,
  Workflow,
} from 'n8n-workflow'

import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { executeData as executeDataFn, getCurrentWorkflow, useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { NodeConnectionType } from '#wf/constants/canvas'
import { CHAT_TRIGGER_NODE_TYPE, SINGLE_WEBHOOK_TRIGGERS } from '#wf/constants/common'
import { useExecutionsStore } from '#wf/stores/executions.store'
import { usePushConnectionStore } from '#wf/stores/pushConnection.store'
import { useRootStore } from '#wf/stores/root.store'
import { useSettingsStore } from '#wf/stores/setting.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type {
  IExecutionPushResponse,
  IExecutionResponse,
  IStartRunData,
  IWorkflowDb,
} from '#wf/types/interface'
import { displayForm } from '#wf/utils/executionUtils'
import { isEmpty } from '#wf/utils/typesUtils'

const getDirtyNodeNames = (
  runData: IRunData,
  getParametersLastUpdate: (nodeName: string) => number | undefined,
): string[] | undefined => {
  const dirtyNodeNames = Object.entries(runData).reduce<string[]>((acc, [nodeName, tasks]) => {
    if (!tasks.length) {
      return acc
    }

    const updatedAt = getParametersLastUpdate(nodeName) ?? 0

    if (updatedAt > tasks[0].startTime) {
      acc.push(nodeName)
    }

    return acc
  }, [])

  return dirtyNodeNames.length ? dirtyNodeNames : undefined
}

export function useRunWorkflow() {
  const nodeHelpers = useNodeHelpers()
  const workflowHelpers = useWorkflowHelpers()

  const rootStore = useRootStore()
  const pushConnectionStore = usePushConnectionStore()
  const uiStore = useUIStore()
  const workflowStore = useWorkflowStore()
  const executionsStore = useExecutionsStore()

  const { $toast } = useNuxtApp()

  // 在服务端开始执行工作流
  async function runWorkflowApi(runData: IStartRunData): Promise<IExecutionPushResponse> {
    if (!pushConnectionStore.isConnected) {
      // 如果与服务器的连接未激活，就不要启动执行工作流
      // 这会导致无法接收工作流执行过程中的数据，确保连接正常对于实时监控和控制工作流执行至关重要
      throw new Error('与服务器的连接丢失')
    }

    workflowStore.subWorkflowExecutionError = null

    uiStore.addActiveAction('workflowRunning')

    let response: IExecutionPushResponse

    try {
      response = await workflowStore.runWorkflow(runData)
    }
    catch (error) {
      uiStore.removeActiveAction('workflowRunning')
      throw error
    }

    if (response.executionId !== undefined) {
      workflowStore.activeExecutionId = response.executionId
    }

    if (response.waitingForWebhook === true && useWorkflowStore().nodesIssuesExist) {
      uiStore.removeActiveAction('workflowRunning')
      throw new Error('请在执行之前修复问题')
    }

    if (response.waitingForWebhook === true) {
      workflowStore.executionWaitingForWebhook = true
    }

    return response
  }

  function consolidateRunDataAndStartNodes(
    directParentNodes: string[],
    runData: IRunData | null,
    pinData: IPinData | undefined,
    workflow: Workflow,
  ): { runData: IRunData | undefined, startNodeNames: string[] } {
    const startNodeNames = new Set<string>()

    let newRunData: IRunData | undefined

    if (runData !== null && Object.keys(runData).length !== 0) {
      newRunData = {}

      // Go over the direct parents of the node
      for (const directParentNode of directParentNodes) {
        // Go over the parents of that node so that we can get a start
        // node for each of the branches
        const parentNodes = workflow.getParentNodes(directParentNode, NodeConnectionType.Main as UnsafeAny)

        // Add also the enabled direct parent to be checked
        if (workflow.nodes[directParentNode].disabled) {
          continue
        }

        parentNodes.push(directParentNode)

        for (const parentNode of parentNodes) {
          // We want to execute nodes that don't have run data neither pin data
          // in addition, if a node failed we want to execute it again
          if (
            (!runData[parentNode]?.length && !pinData?.[parentNode]?.length)
            || runData[parentNode]?.[0]?.error !== undefined
          ) {
            // When we hit a node which has no data we stop and set it
            // as a start node the execution from and then go on with other
            // direct input nodes
            startNodeNames.add(parentNode)
            break
          }

          if (runData[parentNode] && !runData[parentNode]?.[0]?.error) {
            newRunData[parentNode] = runData[parentNode]?.slice(0, 1)
          }
        }
      }

      if (isEmpty(newRunData)) {
        // If there is no data for any of the parent nodes make sure
        // that run data is empty that it runs regularly
        newRunData = undefined
      }
    }

    return { runData: newRunData, startNodeNames: [...startNodeNames] }
  }

  interface RunWorkflowOptions {
    /** 目标节点 */
    destinationNode?: string
    /** 触发节点 */
    triggerNode?: string
    /** 节点数据 */
    nodeData?: ITaskData
    /** 来源 */
    source?: string
  }

  const runWorkflow = async (options: RunWorkflowOptions): Promise<IExecutionPushResponse | undefined> => {
    const workflow = getCurrentWorkflow()

    // 检查是否已有工作流正在运行
    if (uiStore.isActionActive['workflowRunning']) {
      return
    }

    // 清除所有提示消息
    $toast.removeAllGroups()

    try {
      // 步骤 1: 获取目标节点的所有父节点
      let directParentNodes: string[] = []

      if (options.destinationNode !== undefined) {
        directParentNodes = workflow.getParentNodes(
          options.destinationNode,
          NodeConnectionType.Main as UnsafeAny,
          -1,
        )
      }

      // 步骤 2: 获取工作流运行数据和保存数据
      const runData = workflowStore.getWorkflowRunData
      const workflowData = await workflowHelpers.getWorkflowDataToSave()

      // 步骤 3: 整合运行数据并确定起始节点
      const consolidatedData = consolidateRunDataAndStartNodes(
        directParentNodes,
        runData,
        workflowData.pinData,
        workflow,
      )

      const { startNodeNames } = consolidatedData

      // 步骤 4: 获取目标节点类型
      const destinationNodeType = options.destinationNode
        ? workflowStore.getNodeByName(options.destinationNode)?.type
        : ''

      // 步骤 5: 初始化执行相关变量
      let { runData: newRunData } = consolidatedData
      let executedNode: string | undefined
      let triggerToStartFrom: IStartRunData['triggerToStartFrom']

      // 步骤 6: 处理起始节点配置
      if (
        startNodeNames.length === 0
        && 'destinationNode' in options
        && options.destinationNode !== undefined
      ) {
        // 如果没有起始节点但指定了目标节点，则以目标节点作为起始节点
        executedNode = options.destinationNode
        startNodeNames.push(options.destinationNode)
      }
      else if (options.triggerNode && options.nodeData) {
        // 如果指定了触发节点，则获取其子节点作为起始节点
        startNodeNames.push(
          ...workflow.getChildNodes(options.triggerNode, NodeConnectionType.Main as UnsafeAny, 1),
        )
        newRunData = { [options.triggerNode]: [options.nodeData] }
        executedNode = options.triggerNode
      }

      // 步骤 7: 设置触发起始点
      if (options.triggerNode) {
        triggerToStartFrom = {
          name: options.triggerNode,
          data: options.nodeData,
        }
      }

      // 步骤 8: 处理聊天节点的特殊逻辑
      if (
        options.destinationNode
        && (workflowStore.checkIfNodeHasChatParent(options.destinationNode)
          || destinationNodeType === CHAT_TRIGGER_NODE_TYPE)
        && options.source !== 'RunData.ManualChatMessage'
      ) {
        const startNode = workflow.getStartNode(options.destinationNode)

        if (startNode && startNode.type === CHAT_TRIGGER_NODE_TYPE) {
          // 检查聊天节点是否有输入数据或固定数据
          const chatHasInputData = nodeHelpers.getNodeInputData(startNode, 0, 0, 'input')?.length > 0
          const chatHasPinData = !!workflowData.pinData?.[startNode.name]

          // 如果聊天节点没有输入数据或固定数据，则打开聊天模态框并停止执行
          if (!chatHasInputData && !chatHasPinData) {
            workflowStore.chatPartialExecutionDestinationNode = options.destinationNode
            workflowStore.setPanelOpen('chat', true)

            return
          }
        }
      }

      // 步骤 9: 获取所有触发器节点
      const triggers = workflowData.nodes.filter(
        (node) => node.type.toLowerCase().includes('trigger') && !node.disabled,
      )

      // 步骤 10: 处理聊天触发器的禁用逻辑
      // 如果没有指定目标节点且不是从聊天触发，并且工作流中有其他触发器
      // 则禁用聊天触发器节点以避免模态框打开和 webhook 创建
      if (
        !options.destinationNode
        && options.source !== 'RunData.ManualChatMessage'
        && workflowData.nodes.some((node) => node.type === CHAT_TRIGGER_NODE_TYPE)
      ) {
        const otherTriggers = triggers.filter((node) => node.type !== CHAT_TRIGGER_NODE_TYPE)

        if (otherTriggers.length) {
          const chatTriggerNode = workflowData.nodes.find(
            (node) => node.type === CHAT_TRIGGER_NODE_TYPE,
          )

          if (chatTriggerNode) {
            chatTriggerNode.disabled = true
          }
        }
      }

      // 步骤 11: 构建起始节点数据
      const startNodes: StartNodeData[] = startNodeNames.map((name) => {
        // 为每个起始节点查找源数据
        let sourceData = get(runData, [name, 0, 'source', 0], null)

        if (sourceData === null) {
          const parentNodes = workflow.getParentNodes(name, NodeConnectionType.Main as UnsafeAny, 1)
          const executeData = executeDataFn(
            parentNodes,
            name,
            NodeConnectionType.Main,
            0,
          )
          sourceData = get(executeData, ['source', NodeConnectionType.Main, 0], null)
        }

        return {
          name,
          sourceData,
        }
      })

      // 步骤 12: 检查单一 Webhook 触发器限制
      const singleWebhookTrigger = triggers.find((node) =>
        SINGLE_WEBHOOK_TRIGGERS.includes(node.type),
      )

      if (
        singleWebhookTrigger
        && workflowStore.isWorkflowActive
        && !workflowData.pinData?.[singleWebhookTrigger.name]
      ) {
        $toast.error({
          summary: '停用工作流以执行',
          detail: `因为 ${singleWebhookTrigger.name} 的限制，系统不能同时监听测试执行和生产执行`,
          keepAlive: true,
        })

        return undefined
      }

      // 步骤 13: 构建执行启动数据
      // 部分执行必须有目标节点
      const isPartialExecution = options.destinationNode !== undefined

      const settingsStore = useSettingsStore()
      const version = settingsStore.partialExecutionVersion

      const startRunData: IStartRunData = {
        workflowData,
        // 根据执行类型和版本决定运行数据处理策略：
        // 1. 完整执行：不发送任何运行数据，让后端从头开始执行
        // 2. 部分执行 v2：发送原始运行数据，由后端智能决定使用哪些数据
        // 3. 部分执行 v1：发送前端预处理的运行数据
        runData: isPartialExecution
          ? version === 2
            ? (runData ?? undefined)
            : newRunData
          : undefined,
        startNodes,
        triggerToStartFrom,
      }

      if ('destinationNode' in options) {
        startRunData.destinationNode = options.destinationNode
      }

      // 步骤 14: 设置脏节点名称（需要重新执行的节点）
      if (startRunData.runData) {
        startRunData.dirtyNodeNames = getDirtyNodeNames(
          startRunData.runData,
          workflowStore.getParametersLastUpdate,
        )
      }

      // 步骤 15: 初始化执行数据以表示执行的开始
      // 可重用的数据已经设置，新执行节点的数据会在推送时添加
      const executionData: IExecutionResponse = {
        id: '__IN_PROGRESS__',
        finished: false,
        mode: 'manual',
        status: 'running',
        createdAt: new Date(),
        startedAt: new Date(),
        stoppedAt: undefined,
        workflowId: workflow.id,
        executedNode,
        triggerNode: triggerToStartFrom?.name,
        data: {
          resultData: {
            runData: startRunData.runData ?? {},
            pinData: workflowData.pinData,
            workflowData,
          },
        } as IRunExecutionData,
        workflowData: {
          id: workflowStore.workflowId,
          name: workflowData.name!,
          active: workflowData.active!,
          createdAt: 0,
          updatedAt: 0,
          ...workflowData,
        } as IWorkflowDb,
      }

      // 步骤 16: 设置执行状态和 UI 更新
      workflowStore.setWorkflowExecutionData(executionData)
      nodeHelpers.updateNodesExecutionIssues()
      workflowHelpers.setDocumentTitle(workflow.name as string, 'EXECUTING')

      // 步骤 17: 调用 API 开始执行工作流
      const runWorkflowApiResponse = await runWorkflowApi(startRunData)
      const pinData = workflowData.pinData ?? {}

      // 步骤 18: 构建测试 URL 生成器
      const getTestUrl = (() => {
        return (node: INode) => {
          const path = node.parameters.path
            || (node.parameters.options as IDataObject)?.path
            || node.webhookId

          return `${rootStore.formTestUrl}/${path as string}`
        }
      })()

      // 步骤 19: 尝试显示表单（如果适用）
      try {
        displayForm({
          nodes: workflowData.nodes,
          runData: workflowStore.getWorkflowExecution?.data?.resultData?.runData,
          destinationNode: options.destinationNode,
          triggerNode: options.triggerNode,
          pinData,
          directParentNodes,
          source: options.source,
          getTestUrl,
        })
      }
      catch {
        // 忽略表单显示错误
      }

      // 步骤 20: 返回 API 响应
      return runWorkflowApiResponse
    }
    catch (error) {
      // 错误处理：记录错误、更新文档标题、显示错误提示
      console.error(error)
      workflowHelpers.setDocumentTitle(workflow.name as string, 'ERROR')

      if (error instanceof Error) {
        $toast.error({
          summary: '运行工作流时出现问题',
          detail: error.message,
          keepAlive: true,
        })
      }

      return undefined
    }
  }

  async function stopCurrentExecution() {
    const executionId = workflowStore.activeExecutionId

    if (executionId === null) {
      return
    }

    try {
      await executionsStore.stopCurrentExecution(executionId)
    }
    catch {
      // Execution stop might fail when the execution has already finished. Let's treat this here.
      const execution = await workflowStore.getExecution(executionId)

      if (execution === undefined) {
        // execution finished but was not saved (e.g. due to low connectivity)
        $toast.success({
          summary: '执行已取消',
          detail: '此执行已取消',
        })
      }
      else if (execution?.finished) {
        // execution finished before it could be stopped
        const executedData = {
          data: execution.data,
          finished: execution.finished,
          mode: execution.mode,
          startedAt: execution.startedAt,
          stoppedAt: execution.stoppedAt,
        } as IRun

        workflowStore.setWorkflowExecutionData(executedData as IExecutionResponse)

        $toast.success({
          summary: '工作流完成执行',
          detail: '在它能被停止之前就已完成',
        })
      }
      else {
        $toast.error({
          summary: '执行已取消',
          detail: '此执行已取消',
        })
      }
    }
    finally {
      workflowStore.markExecutionAsStopped()
    }
  }

  async function stopWaitingForWebhook() {
    try {
      await workflowStore.removeTestWebhook(workflowStore.workflowId)
    }
    catch (error) {
      if (error instanceof Error) {
        $toast.error({
          summary: '删除测试 Webhook 问题',
          detail: error.message,
        })
      }

      return
    }
  }

  /**
   * 运行整个工作流
   * @param source - 工作流的来源
   * @param triggerNode - 触发节点
   */
  async function runEntireWorkflow(source: 'node' | 'main', triggerNode?: string) {
    // const workflow = workflowHelpers.getCurrentWorkflow()

    void workflowHelpers.getWorkflowDataToSave().then(() => {
      // const telemetryPayload = {
      //   workflow_id: workflow.id,
      //   node_graph_string: JSON.stringify(
      //     TelemetryHelpers.generateNodesGraph(
      //       workflowData as IWorkflowBase,
      //       workflowHelpers.getNodeTypes(),
      //       { isCloudDeployment: settingsStore.isCloudDeployment },
      //     ).nodeGraph,
      //   ),
      //   button_type: source,
      // }
      // void externalHooks.run('nodeView.onRunWorkflow', telemetryPayload)
    })

    void runWorkflow({ triggerNode })
  }

  return {
    consolidateRunDataAndStartNodes,
    runEntireWorkflow,
    runWorkflow,
    runWorkflowApi,
    stopCurrentExecution,
    stopWaitingForWebhook,
  }
}
