import type { INode, INodeTypeDescription } from 'n8n-workflow'

import { NOT_DUPLICATABLE_NODE_TYPES, STICKY_NODE_TYPE } from '#wf/constants/common'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { ActionDropdownItem } from '#wf/types/action-dropdown'
import { ContextMenuActionType } from '#wf/types/action-dropdown'
import type { XYPosition } from '#wf/types/interface'
import { getMousePosition } from '#wf/utils/nodeViewUtils'
import { isPresent } from '#wf/utils/typesUtils'

export type ContextMenuTarget =
  | { source: 'canvas', nodeIds: string[] }
  | { source: 'node-right-click', nodeId: string }
  | { source: 'node-button', nodeId: string }
  | { source: 'node-setting-header', nodeId: string }

/** 节点操作构建参数接口 */
interface NodeActionParams {
  selectedNodes: INode[]
  onlyStickies: boolean
  subject: string
  nodeCount: number
}

const isOpen = ref(false)

const position = ref<XYPosition>([0, 0])

const target = ref<ContextMenuTarget>()

const actions = ref<ActionDropdownItem[]>([])

export function useContextMenu() {
  const uiStore = useUIStore()
  const nodeTypesStore = useNodeTypesStore()

  const workflowStore = useWorkflowStore()
  const { isCanvasReadOnly: isReadOnly } = storeToRefs(workflowStore)

  const targetNodeIds = computed(() => {
    if (!isOpen.value || !target.value) {
      return []
    }

    const currentTarget = target.value

    return currentTarget.source === 'canvas' ? currentTarget.nodeIds : [currentTarget.nodeId]
  })

  const targetNodes = computed(() => targetNodeIds.value.map((nodeId) => workflowStore.getNodeById(nodeId)).filter(isPresent),
  )

  const canAddNodeOfType = (nodeType: INodeTypeDescription) => {
    const sameTypeNodes = workflowStore.workflow.nodes.filter((n) => n.type === nodeType.name)

    return nodeType.maxNodes === undefined || sameTypeNodes.length < nodeType.maxNodes
  }

  const canDuplicateNode = (node: INode): boolean => {
    const nodeType = nodeTypesStore.getNodeType(node.type, node.typeVersion)

    if (!nodeType) {
      return false
    }

    if (NOT_DUPLICATABLE_NODE_TYPES.includes(nodeType.name)) {
      return false
    }

    return canAddNodeOfType(nodeType)
  }

  // const hasPinData = (node: INode): boolean => {
  //   return !!workflowStore.pinDataByNodeName(node.name)
  // }
  /** 构建空选择时的菜单项 */
  const buildEmptySelectionActions = (actionParams: NodeActionParams): ActionDropdownItem[] => {
    const { nodeCount } = actionParams

    const allNodesCount = workflowStore.workflow.nodes.length
    const allSelected = nodeCount === allNodesCount

    return [
      {
        id: ContextMenuActionType.ADD_NODE,
        shortcut: { keys: ['Tab'] },
        label: '添加节点',
        disabled: isReadOnly.value,
      },
      {
        id: ContextMenuActionType.ADD_STICKY,
        shortcut: { shiftKey: true, keys: ['s'] },
        label: '添加注释',
        disabled: isReadOnly.value,
      },
      allSelected
        ? {
            id: ContextMenuActionType.DESELECT_ALL,
            label: '取消全选',
            divided: true,
          }
        : {
            id: ContextMenuActionType.SELECT_ALL,
            divided: true,
            label: '全选',
            shortcut: { metaKey: true, keys: ['A'] },
            disabled: allNodesCount === 0,
          },
    ]
  }

  /** 构建通用节点操作（适用于单选或多选） */
  const buildCommonNodeActions = (actionParams: NodeActionParams): ActionDropdownItem[] => {
    const { selectedNodes, onlyStickies, subject, nodeCount } = actionParams

    const isMultipleSelection = nodeCount > 1
    const allDisabled = selectedNodes.every((node) => node.disabled)

    const actions: ActionDropdownItem[] = []

    // 仅对非注释节点添加激活/禁用选项
    if (!onlyStickies) {
      actions.push({
        id: ContextMenuActionType.TOGGLE_ACTIVATION,
        label: allDisabled
          ? isMultipleSelection ? `激活 ${nodeCount} 个${subject}` : '激活'
          : isMultipleSelection ? `禁用 ${nodeCount} 个${subject}` : '禁用',
        shortcut: { keys: ['D'] },
        disabled: isReadOnly.value,
      })
    }

    // 添加复制和删除操作
    actions.push(
      {
        id: ContextMenuActionType.COPY,
        label: isMultipleSelection ? `拷贝 ${nodeCount} 个${subject}` : '拷贝',
        shortcut: { metaKey: true, keys: ['C'] },
      },
      {
        id: ContextMenuActionType.DUPLICATE,
        label: isMultipleSelection ? `复制 ${nodeCount} 个${subject}` : '复制',
        shortcut: { metaKey: true, keys: ['D'] },
        disabled: isReadOnly.value || !selectedNodes.every(canDuplicateNode),
      },
      {
        id: ContextMenuActionType.DELETE,
        divided: true,
        label: isMultipleSelection ? `删除 ${nodeCount} 个${subject}` : '删除',
        shortcut: { keys: ['Del'] },
        disabled: isReadOnly.value,
      },
    )

    return actions
  }

  /** 构建单节点专用操作 */
  const buildSingleNodeActions = (actionParams: NodeActionParams): ActionDropdownItem[] => {
    const { onlyStickies } = actionParams

    const actions: ActionDropdownItem[] = []

    if (onlyStickies) {
      actions.push({
        id: ContextMenuActionType.OPEN,
        label: '编辑注释',
        shortcut: { keys: ['↵'] },
        disabled: isReadOnly.value,
      })
    }
    else {
      const isInNodeSetting = target.value?.source === 'node-setting-header'

      if (!isInNodeSetting) {
        actions.push({
          id: ContextMenuActionType.OPEN,
          label: '编辑配置',
          shortcut: { keys: ['↵'] },
        })
      }

      actions.push(
        {
          id: ContextMenuActionType.EXECUTE,
          label: '调试此步骤',
          disabled: isReadOnly.value,
        },
      )
    }

    return actions
  }

  /** 构建上下文菜单动作项 */
  const buildContextMenuActions = (
    selectedNodes: INode[],
    onlyStickies: boolean,
    subject: string,
    nodeCount: number,
  ) => {
    const actionParams: NodeActionParams = {
      selectedNodes,
      onlyStickies,
      subject,
      nodeCount,
    }

    if (nodeCount > 0) {
      const commonActions = buildCommonNodeActions(actionParams)

      // 如果只选中了一个节点，添加单节点专用操作
      if (nodeCount === 1) {
        const singleNodeActions = buildSingleNodeActions(actionParams)
        actions.value = [...singleNodeActions, ...commonActions]
      }
      else {
        actions.value = commonActions
      }
    }
    else {
      // 没有选中节点的情况（即右键画布空白区域时）
      actions.value = buildEmptySelectionActions(actionParams)
    }
  }

  const close = () => {
    target.value = undefined
    isOpen.value = false
    actions.value = []
    position.value = [0, 0]
  }

  const open = (ev: MouseEvent, menuTarget: ContextMenuTarget) => {
    ev.stopPropagation()

    if (isOpen.value && menuTarget.source === target.value?.source) {
      // 关闭上下文菜单，让浏览器打开原生上下文菜单
      close()

      return
    }

    ev.preventDefault()

    target.value = menuTarget
    position.value = getMousePosition(ev)
    isOpen.value = true

    const selectedNodes = targetNodes.value
    const nodeCount = selectedNodes.length
    const onlyStickies = selectedNodes.every((node) => node.type === STICKY_NODE_TYPE)
    const subject = onlyStickies ? '注释' : '节点'

    buildContextMenuActions(selectedNodes, onlyStickies, subject, nodeCount)
  }

  watch(
    () => uiStore.nodeViewOffsetPosition,
    () => {
      close()
    },
  )

  return {
    isOpen,
    position,
    target,
    actions,
    targetNodeIds,
    open,
    close,
  }
}
