import type { INodeExecutionData, IPinData } from 'n8n-workflow'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'
import { jsonParse, jsonStringify } from 'n8n-workflow/dist/utils.js'

import { useDataSchema } from '#wf/composables/useDataSchema'
import { useNodeType } from '#wf/composables/useNodeType'
import { NodeConnectionType } from '#wf/constants/canvas'
import {
  MAX_EXPECTED_REQUEST_SIZE,
  MAX_PINNED_DATA_SIZE,
  MAX_WORKFLOW_SIZE,
  PIN_DATA_NODE_TYPES_DENYLIST,
} from '#wf/constants/common'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { INodeUi, IRunDataDisplayMode } from '#wf/types/interface'
import { stringSizeInBytes, toMegaBytes } from '#wf/utils/typesUtils'

export type PinDataSource =
  | 'pin-icon-click'
  | 'save-edit'
  | 'on-ndv-close-modal'
  | 'duplicate-node'
  | 'add-nodes'
  | 'context-menu'
  | 'keyboard-shortcut'
  | 'banner-link'

export type UnpinDataSource =
  | 'unpin-and-execute-modal'
  | 'context-menu'
  | 'keyboard-shortcut'
  | 'unpin-and-send-chat-message-modal'

export function usePinnedData(
  node: MaybeRef<INodeUi | null>,
  options: {
    displayMode?: MaybeRef<IRunDataDisplayMode>
    runIndex?: MaybeRef<number>
  } = {},
) {
  const workflowStore = useWorkflowStore()

  const { $toast } = useNuxtApp()
  const { getInputDataWithPinned } = useDataSchema()

  const { isSubNodeType, isMultipleOutputsNodeType } = useNodeType({
    node,
  })

  const data = computed<IPinData[string] | undefined>(() => {
    const targetNode = unref(node)

    return targetNode ? workflowStore.pinDataByNodeName(targetNode.name) : undefined
  })

  const hasData = computed<boolean>(() => {
    const targetNode = unref(node)

    return !!targetNode && typeof data.value !== 'undefined'
  })

  const isValidNodeType = computed(() => {
    const targetNode = unref(node)

    return (
      !!targetNode
      && !isSubNodeType.value
      && !isMultipleOutputsNodeType.value
      && !PIN_DATA_NODE_TYPES_DENYLIST.includes(targetNode.type)
    )
  })

  function canPinNode(checkDataEmpty = false, outputIndex?: number) {
    const targetNode = unref(node)

    if (targetNode === null || PIN_DATA_NODE_TYPES_DENYLIST.includes(targetNode.type)) {
      return false
    }

    const nodeType = useNodeTypesStore().getNodeType(targetNode.type, targetNode.typeVersion)
    const dataToPin = getInputDataWithPinned(targetNode)

    if (!nodeType || (checkDataEmpty && dataToPin.length === 0)) {
      return false
    }

    const workflow = workflowStore.getCurrentWorkflow()
    const outputs = NodeHelpers.getNodeOutputs(workflow, targetNode, nodeType).map((output) =>
      typeof output === 'string' ? { type: output } : output,
    )

    const mainOutputs = outputs.filter(
      (output) => (output.type as unknown as NodeConnectionType) === NodeConnectionType.Main && output.category !== 'error',
    )

    let indexAcceptable = true

    if (outputIndex !== undefined) {
      const output = outputs[outputIndex]

      if (outputs[outputIndex] === undefined) {
        return false
      }

      indexAcceptable = (output.type as unknown as NodeConnectionType) === NodeConnectionType.Main && output.category !== 'error'
    }

    return mainOutputs.length === 1 && indexAcceptable
  }

  function isValidJSON(data: string): boolean {
    try {
      JSON.parse(data)

      return true
    }
    catch (error) {
      if (error instanceof Error) {
        const toRemove = new RegExp(/JSON\.parse:|of the JSON data/, 'g')
        const message = error.message.replace(toRemove, '').trim()
        const positionMatchRegEx = /at position (\d+)/
        const positionMatch = error.message.match(positionMatchRegEx)

        error.message = message.charAt(0).toUpperCase() + message.slice(1)
        error.message = error.message.replace(
          'Unexpected token \' in JSON',
          '意外的单引号。请改用双引号 ()。',
        )

        if (positionMatch) {
          const position = parseInt(positionMatch[1], 10)
          const lineBreaksUpToPosition = (data.slice(0, position).match(/\n/g) || []).length

          error.message = error.message.replace(
            positionMatchRegEx,
            `（在位置 ${position}）`,
          )

          error.message = `${`在第 ${lineBreaksUpToPosition + 1} 行`} ${error.message}`
        }

        $toast.error({
          summary: '输出数据存在问题',
          detail: error.message,
        })
      }

      return false
    }
  }

  function getMaxPinnedDataSize() {
    return window.maxPinnedDataSize ?? MAX_PINNED_DATA_SIZE
  }

  function isValidSize(data: string | object): boolean {
    const targetNode = unref(node)

    if (!targetNode) {
      return false
    }

    if (typeof data === 'object') {
      data = JSON.stringify(data)
    }

    const { pinData: currentPinData, ...workflow } = workflowStore.getCurrentWorkflow()
    const workflowJson = jsonStringify(workflow, { replaceCircularRefs: true })

    const newPinData = { ...currentPinData, [targetNode.name]: data }
    const newPinDataSize = workflowStore.getPinDataSize(newPinData)

    if (newPinDataSize > getMaxPinnedDataSize()) {
      $toast.error({
        summary: '固定数据过大',
        detail: `工作流最大固定数据大小 (${toMegaBytes(newPinDataSize)} mb / ${toMegaBytes(getMaxPinnedDataSize())} mb)`,
      })

      return false
    }

    const workflowSize = stringSizeInBytes(workflowJson) + newPinDataSize
    const limit = MAX_WORKFLOW_SIZE - MAX_EXPECTED_REQUEST_SIZE

    if (workflowSize > limit) {
      $toast.error({
        summary: '由于工作流过大，无法固定数据',
        detail: `工作流大小已达到最大限制 (${toMegaBytes(workflowSize)} mb / ${toMegaBytes(limit)} mb)`,
      })

      return false
    }

    return true
  }

  function onSetDataSuccess({ source }: { source: PinDataSource }) {
    console.log('onSetDataSuccess', source)
    // void externalHooks.run('runData.onDataPinningSuccess', telemetryPayload)
  }

  function onSetDataError({
    errorType,
    source,
  }: {
    errorType: 'data-too-large' | 'invalid-json'
    source: PinDataSource
  }) {
    // const targetNode = unref(node)
    // const displayMode = unref(options.displayMode)
    // const runIndex = unref(options.runIndex)
    console.log('onSetDataError', errorType, source)
  }

  function setData(data: string | INodeExecutionData[], source: PinDataSource) {
    const targetNode = unref(node)

    if (!targetNode) {
      return
    }

    if (typeof data === 'string') {
      if (!isValidJSON(data)) {
        onSetDataError({ errorType: 'invalid-json', source })
        throw new Error('Invalid JSON')
      }

      data = jsonParse(data)
    }

    if (!isValidSize(data)) {
      onSetDataError({ errorType: 'data-too-large', source })
      throw new Error('Data too large')
    }

    workflowStore.pinData({ node: targetNode, data: data as INodeExecutionData[] })
    onSetDataSuccess({ source })
  }

  function onUnsetData({ source }: { source: PinDataSource | UnpinDataSource }) {
    // const targetNode = unref(node)
    // const runIndex = unref(options.runIndex)
    console.log('onUnsetData', source)
  }

  function unsetData(source: PinDataSource | UnpinDataSource): void {
    const targetNode = unref(node)

    if (!targetNode) {
      return
    }

    onUnsetData({ source })
    workflowStore.unpinData({ node: targetNode })
  }

  return {
    data,
    hasData,
    isValidNodeType,
    canPinNode,
    setData,
    onSetDataSuccess,
    onSetDataError,
    unsetData,
    onUnsetData,
    isValidJSON,
    isValidSize,
  }
}
