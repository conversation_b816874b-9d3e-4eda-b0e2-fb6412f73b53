import { get } from 'lodash-es'
import type { IBinaryKeyData, ICredentialType, IDataObject, INode, INodeCredentialDescription, INodeCredentialsDetails, INodeExecutionData, INodeInputConfiguration, INodeIssueObjectProperty, INodeIssues, INodeParameters, INodeProperties, INodePropertyOptions, INodeTypeDescription, INodeTypeNameVersion, IRunData, ITaskDataConnections } from 'n8n-workflow'
import * as ExpressionEvaluatorProxy from 'n8n-workflow/dist/ExpressionEvaluatorProxy.js'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'
import type { Workflow } from 'n8n-workflow/dist/Workflow.js'
import { nanoid } from 'nanoid'

import { NodeConnectionType } from '#wf/constants/canvas'
import { CUSTOM_API_CALL_KEY, PLACEHOLDER_FILLED_AT_EXECUTION_TIME, SPLIT_IN_BATCHES_NODE_TYPE } from '#wf/constants/common'
import { EnableNodeToggleCommand } from '#wf/models/history'
import { useCanvasStore } from '#wf/stores/canvas.store'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import { useHistoryStore } from '#wf/stores/history.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useSettingsStore } from '#wf/stores/setting.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { ICredentialsResponse, INodeUi, INodeUpdatePropertiesInformation, NodePanelType } from '#wf/types/interface'
import { isObject } from '#wf/utils/objectUtils'
import { isString } from '#wf/utils/typeGuards'

// eslint-disable-next-line @typescript-eslint/no-namespace
declare namespace HttpRequestNode {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace V2 {
    type AuthParams = {
      authentication: 'none' | 'genericCredentialType' | 'predefinedCredentialType'
      genericAuthType: string
      nodeCredentialType: string
    }
  }
}

export function useNodeHelpers() {
  const nodeTypesStore = useNodeTypesStore()
  const workflowStore = useWorkflowStore()
  const historyStore = useHistoryStore()
  const credentialsStore = useCredentialsStore()
  const canvasStore = useCanvasStore()

  const credentialsUpdated = ref(false)

  const hasProxyAuth = (node: INodeUi): boolean => {
    return Object.keys(node.parameters).includes('nodeCredentialType')
  }

  const getNodeSubtitle = (
    data: INode,
    nodeType: INodeTypeDescription,
    workflow: Workflow,
  ): string | undefined => {
    if (!data) {
      return undefined
    }

    if (data.notesInFlow) {
      return data.notes
    }

    if (nodeType?.subtitle !== undefined) {
      try {
        ExpressionEvaluatorProxy.setEvaluator(
          useSettingsStore().settings.expressions?.evaluator ?? 'tmpl',
        )

        return workflow.expression.getSimpleParameterValue(
          data,
          nodeType.subtitle,
          'internal',
          {},
          undefined,
          PLACEHOLDER_FILLED_AT_EXECUTION_TIME,
        ) as string | undefined
      }
      catch {
        return undefined
      }
    }

    if (data.parameters.operation !== undefined) {
      const operation = data.parameters.operation as string

      if (nodeType === null) {
        return operation
      }

      const operationData = nodeType.properties.find((property: INodeProperties) => {
        return property.name === 'operation'
      })

      if (operationData === undefined) {
        return operation
      }

      if (operationData.options === undefined) {
        return operation
      }

      const optionData = operationData.options.find((option) => {
        return (option as INodePropertyOptions).value === data.parameters.operation
      })

      if (optionData === undefined) {
        return operation
      }

      return optionData.name
    }

    return undefined
  }

  const assignNodeId = (node: INodeUi) => {
    const id = nanoid()
    // const id = window.crypto.randomUUID()
    node.id = id

    return id
  }

  const assignWebhookId = (node: INodeUi) => {
    const id = nanoid()
    // const id = window.crypto.randomUUID()
    node.webhookId = id

    return id
  }

  const isCustomApiCallSelected = (nodeValues: INodeParameters): boolean => {
    const { parameters } = nodeValues

    if (!isObject(parameters)) {
      return false
    }

    if ('resource' in parameters || 'operation' in parameters) {
      const { resource, operation } = parameters

      return (
        (isString(resource) && resource.includes(CUSTOM_API_CALL_KEY))
        || (isString(operation) && operation.includes(CUSTOM_API_CALL_KEY))
      )
    }

    return false
  }

  function hasNodeExecutionIssues(node: INodeUi): boolean {
    const workflowResultData = workflowStore.getWorkflowRunData

    if (workflowResultData === null || !Object.hasOwn(workflowResultData, node.name)) {
      return false
    }

    for (const taskData of workflowResultData[node.name]) {
      if (taskData.error !== undefined) {
        return true
      }
    }

    return false
  }

  function updateNodesExecutionIssues() {
    const nodes = workflowStore.workflow.nodes

    for (const node of nodes) {
      workflowStore.setNodeIssue({
        node: node.name,
        type: 'execution',
        value: hasNodeExecutionIssues(node) ? true : null,
      })
    }
  }

  const getNodeInputIssues = (
    workflow: Workflow,
    node: INodeUi,
    nodeType?: INodeTypeDescription,
  ): INodeIssues | null => {
    const foundIssues: INodeIssueObjectProperty = {}

    const workflowNode = workflow.getNode(node.name)
    let inputs: Array<NodeConnectionType | INodeInputConfiguration> = []

    if (nodeType && workflowNode) {
      inputs = NodeHelpers.getNodeInputs(workflow, workflowNode, nodeType) as Array<NodeConnectionType | INodeInputConfiguration>
    }

    inputs.forEach((input) => {
      if (typeof input === 'string' || input.required !== true) {
        return
      }

      const parentNodes = workflow.getParentNodes(node.name, input.type, 1)

      if (parentNodes.length === 0) {
        foundIssues[input.type] = [`未连接到所需输入 "${input.displayName || input.type}" 的节点`]
      }
    })

    if (Object.keys(foundIssues).length) {
      return {
        input: foundIssues,
      }
    }

    return null
  }

  const updateNodeInputIssues = (node: INodeUi): void => {
    const nodeType = nodeTypesStore.getNodeType(node.type, node.typeVersion)

    if (!nodeType) {
      return
    }

    const workflow = workflowStore.getCurrentWorkflow()
    const nodeInputIssues = getNodeInputIssues(workflow, node, nodeType)

    workflowStore.setNodeIssue({
      node: node.name,
      type: 'input',
      value: nodeInputIssues?.input ? nodeInputIssues.input : null,
    })
  }

  const getParameterValue = (nodeValues: INodeParameters, parameterName: string, path: string) => {
    const propPath = path ? `${path}.${parameterName}` : parameterName

    return get(nodeValues, propPath)
  }

  // Returns if the given parameter should be displayed or not
  const displayParameter = (
    nodeValues: INodeParameters,
    parameter: INodeProperties | INodeCredentialDescription,
    path: string,
    node: INodeUi | null,
    displayKey: 'displayOptions' | 'disabledOptions' = 'displayOptions',
  ) => {
    return NodeHelpers.displayParameterPath(nodeValues, parameter, path, node, displayKey)
  }

  const matchCredentials = (node: INodeUi) => {
    if (!node.credentials) {
      return
    }

    Object.entries(node.credentials).forEach(
      ([nodeCredentialType, nodeCredentials]: [string, INodeCredentialsDetails]) => {
        const credentialOptions = credentialsStore.getCredentialsByType(nodeCredentialType)

        // Check if workflows applies old credentials style
        if (typeof nodeCredentials === 'string') {
          nodeCredentials = {
            id: null,
            name: nodeCredentials,
          }
          credentialsUpdated.value = true
        }

        if (nodeCredentials.id) {
          // Check whether the id is matching with a credential
          const credentialsId = nodeCredentials.id.toString() // due to a fixed bug in the migration UpdateWorkflowCredentials (just sqlite) we have to cast to string and check later if it has been a number
          const credentialsForId = credentialOptions.find(
            (optionData: ICredentialsResponse) => optionData.id === credentialsId,
          )

          if (credentialsForId) {
            if (
              credentialsForId.name !== nodeCredentials.name
              || typeof nodeCredentials.id === 'number'
            ) {
              node.credentials![nodeCredentialType] = {
                id: credentialsForId.id,
                name: credentialsForId.name,
              }
              credentialsUpdated.value = true
            }

            return
          }
        }

        // No match for id found or old credentials type used
        node.credentials![nodeCredentialType] = nodeCredentials

        // check if only one option with the name would exist
        const credentialsForName = credentialOptions.filter(
          (optionData: ICredentialsResponse) => optionData.name === nodeCredentials.name,
        )

        // only one option exists for the name, take it
        if (credentialsForName.length === 1) {
          node.credentials![nodeCredentialType].id = credentialsForName[0].id
          credentialsUpdated.value = true
        }
      },
    )
  }

  const updateNodeParameterIssues = (node: INodeUi, nodeType?: INodeTypeDescription | null): void => {
    const localNodeType = nodeType ?? nodeTypesStore.getNodeType(node.type, node.typeVersion)

    if (localNodeType === null) {
      // Could not find localNodeType so can not update issues
      return
    }

    // All data got updated everywhere so update now the issues
    const fullNodeIssues: INodeIssues | null = NodeHelpers.getNodeParametersIssues(
      localNodeType.properties,
      node,
    )

    let newIssues: INodeIssueObjectProperty | null = null

    if (fullNodeIssues !== null) {
      newIssues = fullNodeIssues.parameters!
    }

    workflowStore.setNodeIssue({
      node: node.name,
      type: 'parameters',
      value: newIssues,
    })
  }

  const updateNodeParameterIssuesByName = (name: string): void => {
    const node = workflowStore.getNodeByName(name)

    if (node) {
      updateNodeParameterIssues(node)
    }
  }

  const reportUnsetCredential = (credentialType: ICredentialType) => {
    return {
      credentials: {
        [credentialType.name]: [
          `未设置 ${credentialType.displayName} 的凭证。`,
        ],
      },
    }
  }

  /**
   * Whether the node has no selected credentials, or none of the node's
   * selected credentials are of the specified type.
   */
  const selectedCredsAreUnusable = (node: INodeUi, credentialType: string) => {
    return !node.credentials || !Object.keys(node.credentials).includes(credentialType)
  }

  /**
   * Whether the node's selected credentials of the specified type
   * can no longer be found in the database.
   */
  const selectedCredsDoNotExist = (
    node: INodeUi,
    nodeCredentialType: string,
    storedCredsByType: ICredentialsResponse[] | null,
  ) => {
    if (!node.credentials || !storedCredsByType) {
      return false
    }

    const selectedCredsByType = node.credentials[nodeCredentialType]

    if (!selectedCredsByType) {
      return false
    }

    return !storedCredsByType.find((c) => c.id === selectedCredsByType.id)
  }

  const getNodeCredentialIssues = (
    node: INodeUi,
    nodeType?: INodeTypeDescription,
  ): INodeIssues | null => {
    const localNodeType = nodeType ?? nodeTypesStore.getNodeType(node.type, node.typeVersion)

    if (node.disabled) {
      // Node is disabled
      return null
    }

    if (!localNodeType?.credentials) {
      // Node does not need any credentials or nodeType could not be found
      return null
    }

    const foundIssues: INodeIssueObjectProperty = {}

    let userCredentials: ICredentialsResponse[] | null
    let credentialType: ICredentialType | undefined
    let credentialDisplayName: string
    let selectedCredentials: INodeCredentialsDetails

    const { authentication, genericAuthType, nodeCredentialType } = node.parameters as HttpRequestNode.V2.AuthParams

    if (
      authentication === 'genericCredentialType'
      && genericAuthType !== ''
      && selectedCredsAreUnusable(node, genericAuthType)
    ) {
      const credential = credentialsStore.getCredentialTypeByName(genericAuthType)

      return credential ? reportUnsetCredential(credential) : null
    }

    if (
      hasProxyAuth(node)
      && authentication === 'predefinedCredentialType'
      && nodeCredentialType !== ''
      && node.credentials !== undefined
    ) {
      const stored = credentialsStore.getCredentialsByType(nodeCredentialType)
      // Prevents HTTP Request node from being unusable if a sharee does not have direct
      // access to a credential
      const isCredentialUsedInWorkflow = workflowStore.usedCredentials?.[node.credentials?.[nodeCredentialType]?.id as string]

      if (
        selectedCredsDoNotExist(node, nodeCredentialType, stored)
        && !isCredentialUsedInWorkflow
      ) {
        const credential = credentialsStore.getCredentialTypeByName(nodeCredentialType)

        return credential ? reportUnsetCredential(credential) : null
      }
    }

    if (
      hasProxyAuth(node)
      && authentication === 'predefinedCredentialType'
      && nodeCredentialType !== ''
      && selectedCredsAreUnusable(node, nodeCredentialType)
    ) {
      const credential = credentialsStore.getCredentialTypeByName(nodeCredentialType)

      return credential ? reportUnsetCredential(credential) : null
    }

    for (const credentialTypeDescription of localNodeType.credentials) {
      // Check if credentials should be displayed else ignore
      if (!displayParameter(node.parameters, credentialTypeDescription, '', node)) {
        continue
      }

      // Get the display name of the credential type
      credentialType = credentialsStore.getCredentialTypeByName(credentialTypeDescription.name)

      if (!credentialType) {
        credentialDisplayName = credentialTypeDescription.name
      }
      else {
        credentialDisplayName = credentialType.displayName
      }

      if (!node.credentials?.[credentialTypeDescription.name]) {
        // Credentials are not set
        if (credentialTypeDescription.required) {
          foundIssues[credentialTypeDescription.name] = [
            `${localNodeType.displayName} 的凭据未设置。`,
          ]
        }
      }
      else {
        // If they are set check if the value is valid
        selectedCredentials = node.credentials[credentialTypeDescription.name]

        if (typeof selectedCredentials === 'string') {
          selectedCredentials = {
            id: null,
            name: selectedCredentials,
          }
        }

        userCredentials = credentialsStore.getCredentialsByType(credentialTypeDescription.name)

        if (userCredentials === null) {
          userCredentials = []
        }

        if (selectedCredentials.id) {
          const idMatch = userCredentials.find(
            (credentialData) => credentialData.id === selectedCredentials.id,
          )

          if (idMatch) {
            continue
          }
        }

        const nameMatches = userCredentials.filter(
          (credentialData) => credentialData.name === selectedCredentials.name,
        )

        if (nameMatches.length > 1) {
          foundIssues[credentialTypeDescription.name] = [
            `${credentialDisplayName} 中存在名称为 ${selectedCredentials.name} 的凭据。`,
            '您可以删除重复的凭据，然后它们将在刷新时自动选择。',
            '凭据未明确定义。请正确选择凭据。',
          ]
          continue
        }

        if (nameMatches.length === 0) {
          const isCredentialUsedInWorkflow = workflowStore.usedCredentials?.[selectedCredentials.id as string]

          if (!isCredentialUsedInWorkflow) {
            foundIssues[credentialTypeDescription.name] = [
              `在 ${credentialDisplayName} 中不存在名称为 ${selectedCredentials.name} 的凭据。`,
              '您可以创建具有相同名称的凭据，然后它们将在刷新时自动选择。',
            ]
          }
        }
      }
    }

    // TODO: Could later check also if the node has access to the credentials
    if (Object.keys(foundIssues).length === 0) {
      return null
    }

    return {
      credentials: foundIssues,
    }
  }

  function updateNodesInputIssues() {
    const nodes = workflowStore.workflow.nodes

    for (const node of nodes) {
      updateNodeInputIssues(node)
    }
  }

  const updateNodeCredentialIssues = (node: INodeUi): void => {
    const fullNodeIssues: INodeIssues | null = getNodeCredentialIssues(node)

    let newIssues: INodeIssueObjectProperty | null = null

    if (fullNodeIssues !== null) {
      newIssues = fullNodeIssues.credentials!
    }

    workflowStore.setNodeIssue({
      node: node.name,
      type: 'credentials',
      value: newIssues,
    })
  }

  const updateNodeCredentialIssuesByName = (name: string): void => {
    const node = workflowStore.getNodeByName(name)

    if (node) {
      updateNodeCredentialIssues(node)
    }
  }

  function disableNodes(nodes: INodeUi[], { trackHistory = false, trackBulk = true } = {}) {
    if (trackHistory && trackBulk) {
      historyStore.startRecordingUndo()
    }

    const newDisabledState = nodes.some((node) => !node.disabled)

    for (const node of nodes) {
      if (newDisabledState === node.disabled) {
        continue
      }

      // Toggle disabled flag
      const updateInformation = {
        name: node.name,
        properties: {
          disabled: newDisabledState,
        } as IDataObject,
      } as INodeUpdatePropertiesInformation

      workflowStore.updateNodeProperties(updateInformation)
      workflowStore.clearNodeExecutionData(node.name)
      updateNodeParameterIssues(node)
      updateNodeCredentialIssues(node)
      updateNodesInputIssues()

      if (trackHistory) {
        historyStore.pushCommandToUndo(
          new EnableNodeToggleCommand(node.name, node.disabled === true, newDisabledState),
        )
      }
    }

    if (trackHistory && trackBulk) {
      historyStore.stopRecordingUndo()
    }
  }

  function getNodeTaskData(node: INodeUi | null, runIndex = 0) {
    if (node === null) {
      return null
    }

    if (workflowStore.getWorkflowExecution === null) {
      return null
    }

    const executionData = workflowStore.getWorkflowExecution.data

    if (!executionData?.resultData) {
      // unknown status
      return null
    }

    const runData = executionData.resultData.runData

    const taskData = get(runData, [node.name, runIndex])

    if (!taskData) {
      return null
    }

    return taskData
  }

  function getInputData(
    connectionsData: ITaskDataConnections,
    outputIndex: number,
    connectionType: NodeConnectionType = NodeConnectionType.Main,
  ): INodeExecutionData[] {
    return connectionsData?.[connectionType]?.[outputIndex] ?? []
  }

  function getNodeInputData(
    node: INodeUi | null,
    runIndex = 0,
    outputIndex = 0,
    paneType: NodePanelType = 'output',
    connectionType: NodeConnectionType = NodeConnectionType.Main,
  ): INodeExecutionData[] {
    // TODO: check if this needs to be fixed in different place
    if (
      node?.type === SPLIT_IN_BATCHES_NODE_TYPE
      && paneType === 'input'
      && runIndex !== 0
      && outputIndex !== 0
    ) {
      runIndex = runIndex - 1
    }

    const taskData = getNodeTaskData(node, runIndex)

    if (taskData === null) {
      return []
    }

    let data: ITaskDataConnections | undefined = taskData.data

    if (paneType === 'input' && taskData.inputOverride) {
      data = taskData.inputOverride
    }

    if (!data) {
      return []
    }

    return getInputData(data, outputIndex, connectionType)
  }

  async function loadNodesProperties(nodeInfos: INodeTypeNameVersion[]): Promise<void> {
    const allNodes: INodeTypeDescription[] = nodeTypesStore.allNodeTypes

    const nodesToBeFetched: INodeTypeNameVersion[] = []
    allNodes.forEach((node) => {
      const nodeVersions = Array.isArray(node.version) ? node.version : [node.version]

      if (
        !!nodeInfos.find((n) => n.name === node.name && nodeVersions.includes(n.version))
        && !Object.hasOwn(node, 'properties')
      ) {
        nodesToBeFetched.push({
          name: node.name,
          version: Array.isArray(node.version) ? node.version.slice(-1)[0] : node.version,
        })
      }
    })

    if (nodesToBeFetched.length > 0) {
      // Only call API if node information is actually missing
      canvasStore.setIsLoading(true)
      await nodeTypesStore.getNodesInformation(nodesToBeFetched)
      canvasStore.setIsLoading(false)
    }
  }

  function updateNodesParameterIssues() {
    const nodes = workflowStore.workflow.nodes

    for (const node of nodes) {
      updateNodeParameterIssues(node)
    }
  }

  function updateNodesCredentialsIssues() {
    const nodes = workflowStore.workflow.nodes
    let issues: INodeIssues | null

    for (const node of nodes) {
      issues = getNodeCredentialIssues(node)

      workflowStore.setNodeIssue({
        node: node.name,
        type: 'credentials',
        value: issues?.credentials ?? null,
      })
    }
  }

  function getBinaryData(
    workflowRunData: IRunData | null,
    node: string | null,
    runIndex: number,
    outputIndex: number,
    connectionType: NodeConnectionType = NodeConnectionType.Main,
  ): IBinaryKeyData[] {
    if (node === null) {
      return []
    }

    const runData: IRunData | null = workflowRunData

    const runDataOfNode = runData?.[node]?.[runIndex]?.data

    if (!runDataOfNode) {
      return []
    }

    const inputData = getInputData(runDataOfNode, outputIndex, connectionType)

    const returnData: IBinaryKeyData[] = []

    for (let i = 0; i < inputData.length; i++) {
      const binaryDataInIdx = inputData[i]?.binary

      if (binaryDataInIdx !== undefined) {
        returnData.push(binaryDataInIdx)
      }
    }

    return returnData
  }

  return {
    hasProxyAuth,
    getNodeSubtitle,
    assignNodeId,
    assignWebhookId,
    isCustomApiCallSelected,
    displayParameter,
    getParameterValue,
    updateNodeInputIssues,

    matchCredentials,
    getBinaryData,
    getNodeTaskData,

    updateNodeParameterIssues,
    updateNodesInputIssues,
    updateNodesParameterIssues,
    updateNodesCredentialsIssues,
    updateNodeCredentialIssues,

    updateNodeParameterIssuesByName,
    updateNodeCredentialIssuesByName,

    disableNodes,
    getNodeInputData,
    updateNodesExecutionIssues,
    loadNodesProperties,
  }
}
