import type { PushMessage } from '@n8n/api-types'
import { consola } from 'consola'
import { parse } from 'flatted'
import type {
  ExpressionError,
  IExecuteContextData,
  INodeTypeDescription,
  INodeTypeNameVersion,
  IRunExecutionData,
  NodeError,
  NodeOperationError,
  SubworkflowOperationError,
} from 'n8n-workflow'

import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { getCurrentWorkflow, useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useOrchestrationStore } from '#wf/stores/orchestration.store'
import { usePushConnectionStore } from '#wf/stores/pushConnection.store'
import { useSettingsStore } from '#wf/stores/setting.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { IExecutionResponse } from '#wf/types/interface'
import type { PushMessageQueueItem } from '#wf/types/push-connection'
import { clearPopupWindowState, hasTrimmedData, hasTrimmedItem } from '#wf/utils/executionUtils'
import { getTriggerNodeServiceName } from '#wf/utils/nodeTypesUtils'

export function usePushConnection() {
  const workflowHelpers = useWorkflowHelpers()
  const nodeHelpers = useNodeHelpers()

  const credentialsStore = useCredentialsStore()
  const nodeTypesStore = useNodeTypesStore()
  const orchestrationManagerStore = useOrchestrationStore()
  const pushConnectionStore = usePushConnectionStore()
  const settingsStore = useSettingsStore()
  const uiStore = useUIStore()
  // const assistantStore = useAssistantStore()

  const workflowStore = useWorkflowStore()
  const { activeExecutionId } = storeToRefs(workflowStore)

  const retryTimeout = ref<NodeJS.Timeout | null>(null)
  const pushMessageQueue = ref<PushMessageQueueItem[]>([])
  const removeEventListener = ref<(() => void) | null>(null)

  const { $toast } = useNuxtApp()

  const getExecutionError = (data: IRunExecutionData | IExecuteContextData) => {
    const error = data.resultData.error

    let errorMessage: string

    if (data.resultData.lastNodeExecuted && error) {
      errorMessage = error.message || error.description
    }
    else {
      errorMessage = '执行工作流时出现问题！'

      if (error?.message) {
        let nodeName: string | undefined

        if ('node' in error) {
          nodeName = typeof error.node === 'string' ? error.node : error.node!.name
        }

        const receivedError = nodeName ? `${nodeName}: ${error.message}` : error.message
        errorMessage = `执行工作流时出现问题！${receivedError}`
      }
    }

    return errorMessage
  }

  /**
   * 处理队列中等待的推送消息
   */
  async function processWaitingPushMessages() {
    if (retryTimeout.value !== null) {
      clearTimeout(retryTimeout.value)
      retryTimeout.value = null
    }

    const queueLength = pushMessageQueue.value.length

    for (let i = 0; i < queueLength; i++) {
      const messageData = pushMessageQueue.value.shift() as PushMessageQueueItem

      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      const result = await pushMessageReceived(messageData.message, true)

      if (!result) {
        // 处理失败
        messageData.retriesLeft -= 1

        if (messageData.retriesLeft > 0) {
          // 如果还有重试次数，将消息重新加入队列并停止执行
          pushMessageQueue.value.unshift(messageData)
        }

        break
      }
    }

    if (pushMessageQueue.value.length !== 0 && retryTimeout.value === null) {
      retryTimeout.value = setTimeout(processWaitingPushMessages, 25)
    }
  }

  /**
   * 有时推送消息的速度比 REST API 返回结果还快，
   * 所以我们可能还不知道当前激活的执行 ID 是哪个。
   * 因此在内部会重新发送该消息几次。
   */
  function queuePushMessage(event: PushMessage, retryAttempts: number) {
    pushMessageQueue.value.push({ message: event, retriesLeft: retryAttempts })

    if (retryTimeout.value === null) {
      retryTimeout.value = setTimeout(processWaitingPushMessages, 20)
    }
  }

  /**
   * 处理新接收到的消息
   */
  async function pushMessageReceived(
    receivedData: PushMessage,
    isRetry?: boolean,
  ): Promise<boolean> {
    const retryAttempts = 5

    if (receivedData.type === 'sendWorkerStatusMessage') {
      const pushData = receivedData.data
      orchestrationManagerStore.updateWorkerStatus(pushData.status)

      return true
    }

    if (receivedData.type === 'sendConsoleMessage') {
      const pushData = receivedData.data
      consola.log(pushData.source, ...pushData.messages)

      return true
    }

    if (
      !['testWebhookReceived'].includes(receivedData.type)
      && isRetry !== true
      && pushMessageQueue.value.length
    ) {
      // 如果队列中已经有消息，将新消息添加到队列中，以便按顺序执行
      queuePushMessage(receivedData, retryAttempts)

      return false
    }

    if (
      receivedData.type === 'nodeExecuteAfter'
      || receivedData.type === 'nodeExecuteBefore'
      || receivedData.type === 'executionStarted'
    ) {
      if (!uiStore.isActionActive['workflowRunning']) {
        // 没有工作流正在运行，忽略消息
        return false
      }

      const pushData = receivedData.data

      if (workflowStore.activeExecutionId !== pushData.executionId) {
        // 数据不是当前活动执行的数据，或者我们还没有执行 ID
        if (isRetry !== true) {
          queuePushMessage(receivedData, retryAttempts)
        }

        return false
      }
    }

    if (
      receivedData.type === 'workflowFailedToActivate'
      && workflowStore.workflowId === receivedData.data.workflowId
    ) {
      workflowStore.setWorkflowInactive(receivedData.data.workflowId)
      workflowStore.setActive(false)

      $toast.error({
        summary: `无法激活工作流`,
        detail: receivedData.data.errorMessage,
      })

      return true
    }

    if (receivedData.type === 'workflowActivated') {
      workflowStore.setWorkflowActive(receivedData.data.workflowId)

      return true
    }

    if (receivedData.type === 'workflowDeactivated') {
      workflowStore.setWorkflowInactive(receivedData.data.workflowId)

      return true
    }

    if (receivedData.type === 'executionFinished' || receivedData.type === 'executionRecovered') {
      if (!uiStore.isActionActive['workflowRunning']) {
        // 没有工作流正在运行，忽略消息
        return false
      }

      if (receivedData.type === 'executionFinished') {
        clearPopupWindowState()
      }

      const { executionId } = receivedData.data

      if (executionId !== activeExecutionId.value) {
        // 完成执行的工作流要么不是由此会话启动的，要么我们还没有执行 ID
        if (isRetry !== true) {
          queuePushMessage(receivedData, retryAttempts)
        }

        return false
      }

      let showedSuccessToast = false

      let executionData: Pick<IExecutionResponse, 'workflowId' | 'data' | 'status'>

      if (receivedData.type === 'executionFinished' && receivedData.data.rawData) {
        const { workflowId, status, rawData } = receivedData.data
        executionData = { workflowId, data: parse(rawData), status }
      }
      else {
        uiStore.setProcessingExecutionResults(true)

        /**
         * 在没有数据的成功完成时，我们立即显示成功提示，
         * 即使我们仍需要获取和反序列化完整的执行数据，
         * 以最小化感知延迟。
         */
        if (receivedData.type === 'executionFinished' && receivedData.data.status === 'success') {
          workflowHelpers.setDocumentTitle(
            workflowStore.getWorkflowById(receivedData.data.workflowId)?.name,
            'IDLE',
          )
          uiStore.removeActiveAction('workflowRunning')

          $toast.success({
            summary: '工作流执行成功',
          })

          showedSuccessToast = true
        }

        let execution: IExecutionResponse | null

        try {
          execution = await workflowStore.fetchExecutionDataById(executionId)

          if (!execution?.data) {
            uiStore.setProcessingExecutionResults(false)

            return false
          }

          executionData = {
            workflowId: execution.workflowId,
            data: parse(execution.data as unknown as string),
            status: execution.status,
          }
        }
        catch {
          uiStore.setProcessingExecutionResults(false)

          return false
        }
      }

      const iRunExecutionData: IRunExecutionData = {
        startData: executionData.data?.startData,
        resultData: executionData.data?.resultData ?? { runData: {} },
        executionData: executionData.data?.executionData,
      }

      if (workflowStore.workflowExecutionData?.workflowId === executionData.workflowId) {
        const activeRunData = workflowStore.workflowExecutionData?.data?.resultData?.runData

        if (activeRunData) {
          for (const key of Object.keys(activeRunData)) {
            if (hasTrimmedItem(activeRunData[key])) {
              continue
            }

            iRunExecutionData.resultData.runData[key] = activeRunData[key]
          }
        }
      }

      uiStore.setProcessingExecutionResults(false)

      let runDataExecutedErrorMessage = getExecutionError(iRunExecutionData)

      if (executionData.status === 'crashed') {
        runDataExecutedErrorMessage = `可能没有足够的内存来完成执行。避免此问题的提示<a target="_blank" href="https://docs.n8n.io/flow-logic/error-handling/memory-errors/">在这里</a>`
      }
      else if (executionData.status === 'canceled') {
        runDataExecutedErrorMessage = `执行ID ${activeExecutionId} 已停止`
      }

      // const lineNumber = iRunExecutionData.resultData?.error?.lineNumber

      // codeNodeEditorEventBus.emit('highlightLine', lineNumber ?? 'last')

      const workflow = getCurrentWorkflow()

      if (executionData.data?.waitTill !== undefined) {
        const workflowSettings = workflowStore.workflowSettings
        const saveManualExecutions = settingsStore.saveManualExecutions

        const isSavingExecutions = workflowSettings.saveManualExecutions === undefined
          ? saveManualExecutions
          : workflowSettings.saveManualExecutions

        if (!isSavingExecutions) {
          // globalLinkActionsEventBus.emit('registerGlobalLinkAction', {
          //   key: 'open-settings',
          //   action: async () => {
          //     uiStore.openModal(WORKFLOW_SETTINGS_MODAL_KEY)
          //   },
          // })
        }

        // 工作流已启动但被设置为等待状态
        workflowHelpers.setDocumentTitle(workflow.name as string, 'IDLE')
      }
      else if (executionData.status === 'error' || executionData.status === 'canceled') {
        workflowHelpers.setDocumentTitle(workflow.name as string, 'ERROR')

        if (
          iRunExecutionData.resultData.error?.name === 'ExpressionError'
          && (iRunExecutionData.resultData.error as ExpressionError).functionality === 'pairedItem'
        ) {
          // const error = iRunExecutionData.resultData.error as ExpressionError

          // void workflowHelpers.getWorkflowDataToSave()
        }

        if (iRunExecutionData.resultData.error?.name === 'SubworkflowOperationError') {
          const error = iRunExecutionData.resultData.error as SubworkflowOperationError

          workflowStore.subWorkflowExecutionError = error

          consola.error('[SubworkflowOperationError] 子工作流执行错误')

          $toast.error({
            summary: error.message,
            detail: error.description,
            keepAlive: true,
          })
        }
        else if (
          (iRunExecutionData.resultData.error?.name === 'NodeOperationError'
            || iRunExecutionData.resultData.error?.name === 'NodeApiError')
          && (iRunExecutionData.resultData.error as NodeError).functionality === 'configuration-node'
        ) {
          // 如果错误是节点本身的配置错误，节点不会被执行，所以我们不能使用 lastNodeExecuted 作为标题
          let title: string

          const nodeError = iRunExecutionData.resultData.error as NodeOperationError

          if (nodeError.node.name) {
            title = `Error in sub-node '${nodeError.node.name}'`
          }
          else {
            title = 'Problem executing workflow'
          }

          $toast.error({
            summary: title,
            detail: nodeError.description ?? runDataExecutedErrorMessage,
            keepAlive: true,
          })
          // toast.showMessage({
          //   title,
          //   message: h(NodeExecutionErrorMessage, {
          //     errorMessage: nodeError?.description ?? runDataExecutedErrorMessage,
          //     nodeName: nodeError.node.name,
          //   }),
          //   type: 'error',
          //   duration: 0,
          // })
        }
        else {
          // 如果工作流被取消，不显示错误消息
          if (executionData.status === 'canceled') {
            $toast.success({
              summary: '执行已停止',
            })
          }
          else {
            let title: string

            if (iRunExecutionData.resultData.lastNodeExecuted) {
              title = `节点「${iRunExecutionData.resultData.lastNodeExecuted}」出现问题`
            }
            else {
              title = '执行工作流时出现问题'
            }

            consola.error('[WorkflowExecutionError] 工作流执行错误')

            $toast.error({
              summary: title,
              detail: runDataExecutedErrorMessage,
              keepAlive: true,
            })
          }
        }
      }
      else {
        workflowHelpers.setDocumentTitle(workflow.name as string, 'IDLE')

        const execution = workflowStore.getWorkflowExecution

        if (execution?.executedNode) {
          const node = workflowStore.getNodeByName(execution.executedNode)
          const nodeType = node && nodeTypesStore.getNodeType(node.type, node.typeVersion)
          const nodeOutput = execution
            && execution.executedNode
            && execution.data?.resultData?.runData?.[execution.executedNode]

          if (nodeType?.polling && !nodeOutput) {
            const serviceName = getTriggerNodeServiceName(nodeType)

            $toast.success({
              summary: `未找到 ${serviceName} 数据`,
              detail: `我们在 ${serviceName} 中未找到任何数据来模拟事件。请在 ${serviceName} 中创建一个并重试。`,
            })
          }
          else {
            $toast.success({
              summary: '节点执行成功',
            })
          }
        }
        else if (!showedSuccessToast) {
          $toast.success({
            summary: '工作流执行成功',
          })
        }
      }

      // 它不会推送 runData，因为每个完成的节点都已经推送了数据。
      // 因此在这里复制我们已有的数据。但如果存储中的运行数据被裁剪，
      // 我们跳过复制，以便使用最终消息中的完整数据。
      if (workflowStore.getWorkflowRunData && !hasTrimmedData(workflowStore.getWorkflowRunData)) {
        iRunExecutionData.resultData.runData = workflowStore.getWorkflowRunData
      }

      workflowStore.executingNode.length = 0
      workflowStore.setWorkflowExecutionData(executionData as IExecutionResponse)
      uiStore.removeActiveAction('workflowRunning')

      // 在所有产生错误的节点上设置节点执行问题，以便在节点视图中显示
      nodeHelpers.updateNodesExecutionIssues()

      // const lastNodeExecuted: string | undefined = iRunExecutionData.resultData.lastNodeExecuted
      // let itemsCount = 0

      // if (
      //   lastNodeExecuted
      //   && iRunExecutionData.resultData.runData[lastNodeExecuted]
      //   && !runDataExecutedErrorMessage
      // ) {
      //   itemsCount = iRunExecutionData.resultData.runData[lastNodeExecuted][0].data!.main[0]!.length
      // }

      // void useExternalHooks().run('pushConnection.executionFinished', {
      //   itemsCount,
      //   nodeName: iRunExecutionData.resultData.lastNodeExecuted,
      //   errorMessage: runDataExecutedErrorMessage,
      //   runDataExecutedStartData: iRunExecutionData.startData,
      //   resultDataError: iRunExecutionData.resultData.error,
      // })
    }
    else if (receivedData.type === 'executionWaiting') {
      // 无需处理
    }
    else if (receivedData.type === 'executionStarted') {
      if (workflowStore.workflowExecutionData?.data && receivedData.data.flattedRunData) {
        workflowStore.workflowExecutionData.data.resultData.runData = parse(
          receivedData.data.flattedRunData,
        )
      }
    }
    else if (receivedData.type === 'nodeExecuteAfter') {
      // 节点执行完成，添加其数据
      const pushData = receivedData.data

      /**
       * 当我们在 `nodeExecuteAfter` 中收到占位符时，我们伪造项目数量
       * 与占位符所代表的数据数量相同。
       * 这防止了当执行完成且完整数据替换占位符时项目数量跳跃。
       */
      if (
        pushData.itemCount
        && pushData.data?.data?.main
        && Array.isArray(pushData.data.data.main[0])
        && pushData.data.data.main[0].length < pushData.itemCount
      ) {
        pushData.data.data.main[0]?.push(...new Array(pushData.itemCount - 1).fill({ json: {} }))
      }

      workflowStore.updateNodeExecutionData(pushData)
      // void assistantStore.onNodeExecution(pushData)
    }
    else if (receivedData.type === 'nodeExecuteBefore') {
      // 节点开始执行，将其设置为执行中
      const pushData = receivedData.data
      workflowStore.addExecutingNode(pushData.nodeName)
    }
    else if (receivedData.type === 'testWebhookDeleted') {
      // 测试 webhook 被删除
      const pushData = receivedData.data

      if (pushData.workflowId === workflowStore.workflowId) {
        workflowStore.executionWaitingForWebhook = false
        uiStore.removeActiveAction('workflowRunning')
      }
    }
    else if (receivedData.type === 'testWebhookReceived') {
      // 测试 webhook 被调用
      const pushData = receivedData.data

      if (pushData.workflowId === workflowStore.workflowId) {
        workflowStore.executionWaitingForWebhook = false
        workflowStore.activeExecutionId = pushData.executionId
      }

      void processWaitingPushMessages()
    }
    else if (receivedData.type === 'reloadNodeType') {
      await nodeTypesStore.getNodeTypes()
      await nodeTypesStore.getFullNodesProperties([receivedData.data])
    }
    else if (receivedData.type === 'removeNodeType') {
      const pushData = receivedData.data

      const nodesToBeRemoved: INodeTypeNameVersion[] = [pushData]

      // 强制重新加载所有凭据类型
      await credentialsStore.fetchCredentialTypes(false).then(() => {
        nodeTypesStore.removeNodeTypes(nodesToBeRemoved as INodeTypeDescription[])
      })
    }
    else if (receivedData.type === 'nodeDescriptionUpdated') {
      await nodeTypesStore.getNodeTypes()
      await credentialsStore.fetchCredentialTypes(true)
    }

    return true
  }

  function initialize() {
    consola.info('pushConnection initialize')

    removeEventListener.value = pushConnectionStore.addEventListener((message) => {
      void pushMessageReceived(message)
    })
  }

  function terminate() {
    consola.info('pushConnection terminate')

    if (typeof removeEventListener.value === 'function') {
      removeEventListener.value()
    }
  }

  return {
    initialize,
    terminate,
    pushMessageReceived,
    queuePushMessage,
    processWaitingPushMessages,
    pushMessageQueue,
    retryTimeout,
  }
}
