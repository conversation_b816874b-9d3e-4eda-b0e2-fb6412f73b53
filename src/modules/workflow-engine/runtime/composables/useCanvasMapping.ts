import DOMPurify from 'dompurify'
import type { ExecutionStatus, ExecutionSummary, IConnections, INodeExecutionData, INodeTypeDescription, ITaskData, Workflow } from 'n8n-workflow'
import { FORM_NODE_TYPE, SEND_AND_WAIT_OPERATION, WAIT_INDEFINITELY } from 'n8n-workflow/dist/Constants.js'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'

import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { CanvasConnectionMode, CanvasNodeRenderType, NodeConnectionType } from '#wf/constants/canvas'
import { CUSTOM_API_CALL_KEY, STICKY_NODE_TYPE, WAIT_NODE_TYPE } from '#wf/constants/common'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { BoundingBox, CanvasConnection, CanvasConnectionData, CanvasConnectionPort, CanvasNode, CanvasNodeAddNodesRender, CanvasNodeData, CanvasNodeDefaultRender, CanvasNodeDefaultRenderLabelSize, CanvasNodeStickyNoteRender, ExecutionOutputMap } from '#wf/types/canvas'
import type { INodeUi } from '#wf/types/interface'
import { checkOverlap, mapLegacyConnectionsToCanvasConnections, mapLegacyEndpointsToCanvasConnectionPort, parseCanvasConnectionHandleString } from '#wf/utils/canvasUtil'
import { getTriggerNodeServiceName } from '#wf/utils/nodeTypesUtils'

interface UseCanvasMappingOptions {
  nodes: Ref<INodeUi[]>
  connections: Ref<IConnections>
  workflowObject: Ref<Workflow>
}

export function useCanvasMapping(options: UseCanvasMappingOptions) {
  const { nodes, connections, workflowObject } = options

  const workflowStore = useWorkflowStore()
  const nodeTypesStore = useNodeTypesStore()

  const nodeHelpers = useNodeHelpers()

  const nodeTypeDescriptionByNodeId = computed(() =>
    nodes.value.reduce<Record<string, INodeTypeDescription | null>>((acc, node) => {
      acc[node.id] = nodeTypesStore.getNodeType(node.type, node.typeVersion)

      return acc
    }, {}),
  )

  const nodeSubtitleById = computed(() => {
    return nodes.value.reduce<Record<string, string>>((acc, node) => {
      try {
        const nodeTypeDescription = nodeTypeDescriptionByNodeId.value[node.id]

        if (!nodeTypeDescription) {
          return acc
        }

        const nodeSubtitle = nodeHelpers.getNodeSubtitle(node, nodeTypeDescription, workflowObject.value) ?? ''

        if (nodeSubtitle.includes(CUSTOM_API_CALL_KEY)) {
          return acc
        }

        acc[node.id] = nodeSubtitle
      }
      catch {
        //
      }

      return acc
    }, {})
  })

  const nodeInputsById = computed(() =>
    nodes.value.reduce<Record<string, CanvasConnectionPort[]>>((acc, node) => {
      const nodeTypeDescription = nodeTypeDescriptionByNodeId.value[node.id]
      const workflowObjectNode = workflowObject.value.getNode(node.name)

      if (workflowObjectNode && nodeTypeDescription) {
        const nodeInputs = NodeHelpers.getNodeInputs(
          workflowObject.value,
          workflowObjectNode,
          nodeTypeDescription,
        )

        acc[node.id] = mapLegacyEndpointsToCanvasConnectionPort(
          nodeInputs,
          nodeTypeDescription.inputNames ?? [],
        )
      }
      else {
        acc[node.id] = []
      }

      return acc
    }, {}),
  )

  const nodeOutputsById = computed(() =>
    nodes.value.reduce<Record<string, CanvasConnectionPort[]>>((acc, node) => {
      const nodeTypeDescription = nodeTypeDescriptionByNodeId.value[node.id]
      const workflowObjectNode = workflowObject.value.getNode(node.name)

      if (nodeTypeDescription && workflowObjectNode) {
        const outputs = NodeHelpers.getNodeOutputs(
          workflowObject.value,
          workflowObjectNode,
          nodeTypeDescription,
        )

        acc[node.id] = mapLegacyEndpointsToCanvasConnectionPort(
          outputs,
          nodeTypeDescription.outputNames ?? [],
        )
      }
      else {
        acc[node.id] = []
      }

      return acc
    }, {}),
  )

  const nodePinnedDataById = computed(() =>
    nodes.value.reduce<Record<string, INodeExecutionData[] | undefined>>((acc, node) => {
      acc[node.id] = workflowStore.pinDataByNodeName(node.name)

      return acc
    }, {}),
  )

  const nodeExecutionRunDataById = computed(() =>
    nodes.value.reduce<Record<string, ITaskData[] | null>>((acc, node) => {
      acc[node.id] = workflowStore.getWorkflowResultDataByNodeName(node.name)

      return acc
    }, {}),
  )

  const nodeExecutionRunningById = computed(() =>
    nodes.value.reduce<Record<string, boolean>>((acc, node) => {
      acc[node.id] = workflowStore.isNodeExecuting(node.name)

      return acc
    }, {}),
  )

  const additionalNodePropertiesById = computed(() => {
    type StickyNoteBoundingBox = BoundingBox & {
      id: string
      area: number
      zIndex: number
    }

    const stickyNodeBaseZIndex = -100

    const stickyNodeBoundingBoxes = nodes.value.reduce<StickyNoteBoundingBox[]>((acc, node) => {
      if (node.type === STICKY_NODE_TYPE) {
        const x = node.position[0]
        const y = node.position[1]
        const width = node.parameters.width as number
        const height = node.parameters.height as number

        acc.push({
          id: node.id,
          x,
          y,
          width,
          height,
          area: width * height,
          zIndex: stickyNodeBaseZIndex,
        })
      }

      return acc
    }, [])

    const sortedStickyNodeBoundingBoxes = stickyNodeBoundingBoxes.sort((a, b) => b.area - a.area)
    sortedStickyNodeBoundingBoxes.forEach((node, index) => {
      node.zIndex = stickyNodeBaseZIndex + index
    })

    for (let i = 0; i < sortedStickyNodeBoundingBoxes.length; i++) {
      const node1 = sortedStickyNodeBoundingBoxes[i]

      for (let j = i + 1; j < sortedStickyNodeBoundingBoxes.length; j++) {
        const node2 = sortedStickyNodeBoundingBoxes[j]

        if (checkOverlap(node1, node2)) {
          if (node1.area < node2.area && node1.zIndex <= node2.zIndex) {
            // Ensure node1 (smaller area) has a higher zIndex than node2 (larger area)
            node1.zIndex = node2.zIndex + 1
          }
          else if (node2.area < node1.area && node2.zIndex <= node1.zIndex) {
            // Ensure node2 (smaller area) has a higher zIndex than node1 (larger area)
            node2.zIndex = node1.zIndex + 1
          }
        }
      }
    }

    return sortedStickyNodeBoundingBoxes.reduce<Record<string, Partial<CanvasNode>>>(
      (acc, node) => {
        acc[node.id] = {
          style: {
            zIndex: node.zIndex,
          },
        }

        return acc
      },
      {},
    )
  })

  const nodeExecutionRunDataOutputMapById = computed(() =>
    Object.keys(nodeExecutionRunDataById.value).reduce<Record<string, ExecutionOutputMap>>(
      (acc, nodeId) => {
        acc[nodeId] = {}

        const outputData = { iterations: 0, total: 0 }

        for (const runIteration of nodeExecutionRunDataById.value[nodeId] ?? []) {
          const data = runIteration.data ?? {}

          for (const connectionType of Object.keys(data)) {
            const connectionTypeData = data[connectionType] ?? {}
            acc[nodeId][connectionType] = acc[nodeId][connectionType] ?? {}

            for (const outputIndex of Object.keys(connectionTypeData)) {
              const parsedOutputIndex = parseInt(outputIndex, 10)
              const connectionTypeOutputIndexData = connectionTypeData[parsedOutputIndex] ?? []

              acc[nodeId][connectionType][outputIndex] = acc[nodeId][connectionType][
                outputIndex
              ] ?? { ...outputData }
              acc[nodeId][connectionType][outputIndex].iterations += 1
              acc[nodeId][connectionType][outputIndex].total += connectionTypeOutputIndexData.length
            }
          }
        }

        return acc
      },
      {},
    ),
  )

  const getConnectionType = (_: CanvasConnection): string => 'canvas-edge'

  const getConnectionLabel = (connection: CanvasConnection): string => {
    const fromNode = nodes.value.find((node) => node.name === connection.data?.source.node)

    if (!fromNode) {
      return ''
    }

    if (nodePinnedDataById.value[fromNode.id]) {
      const pinnedDataCount = nodePinnedDataById.value[fromNode.id]?.length ?? 0

      return pinnedDataCount > 0
        ? `${pinnedDataCount} 项`
        : ''
    }
    else if (nodeExecutionRunDataById.value[fromNode.id]) {
      const { type, index } = parseCanvasConnectionHandleString(connection.sourceHandle)
      const runDataTotal = nodeExecutionRunDataOutputMapById.value[fromNode.id]?.[type]?.[index]?.total ?? 0

      return runDataTotal > 0
        ? `${runDataTotal} 项`
        : ''
    }

    return ''
  }

  const nodeExecutionStatusById = computed(() =>
    nodes.value.reduce<Record<string, ExecutionStatus>>((acc, node) => {
      acc[node.id] = workflowStore.getWorkflowRunData?.[node.name]?.filter(Boolean)[0]?.executionStatus
        ?? 'new'

      return acc
    }, {}),
  )

  const nodeIssuesById = computed(() =>
    nodes.value.reduce<Record<string, string[]>>((acc, node) => {
      const issues: string[] = []
      const nodeExecutionRunData = workflowStore.getWorkflowRunData?.[node.name]

      if (nodeExecutionRunData) {
        nodeExecutionRunData.forEach((executionRunData) => {
          if (executionRunData?.error) {
            const { message, description } = executionRunData.error
            const issue = `${message}${description ? ` (${description})` : ''}`
            issues.push(DOMPurify.sanitize(issue))
          }
        })
      }

      if (node?.issues !== undefined) {
        issues.push(...NodeHelpers.nodeIssuesToString(node.issues, node))
      }

      acc[node.id] = issues

      return acc
    }, {}),
  )

  const nodeHasIssuesById = computed(() =>
    nodes.value.reduce<Record<string, boolean>>((acc, node) => {
      if (['crashed', 'error'].includes(nodeExecutionStatusById.value[node.id])) {
        acc[node.id] = true
      }
      else if (nodePinnedDataById.value[node.id]) {
        acc[node.id] = false
      }
      else {
        acc[node.id] = nodeIssuesById.value[node.id].length > 0
      }

      return acc
    }, {}),
  )

  const nodeExecutionWaitingById = computed(() =>
    nodes.value.reduce<Record<string, string | undefined>>((acc, node) => {
      const isExecutionSummary = (execution: object): execution is ExecutionSummary =>
        'waitTill' in execution

      const workflowExecution = workflowStore.getWorkflowExecution
      const lastNodeExecuted = workflowExecution?.data?.resultData?.lastNodeExecuted

      if (workflowExecution && lastNodeExecuted && isExecutionSummary(workflowExecution)) {
        if (
          node.name === workflowExecution.data?.resultData?.lastNodeExecuted
          && workflowExecution?.waitTill
          && !workflowExecution?.finished
        ) {
          if (
            node
            && node.type === WAIT_NODE_TYPE
            && ['webhook', 'form'].includes(node.parameters.resume as string)
          ) {
            acc[node.id] = node.parameters.resume === 'webhook'
              ? '节点正在等待传入的 Webhook 调用'
              : '节点正在等待表单提交'

            return acc
          }

          if (node?.parameters.operation === SEND_AND_WAIT_OPERATION) {
            acc[node.id] = '节点正在等待用户输入'

            return acc
          }

          if (node?.type === FORM_NODE_TYPE) {
            acc[node.id] = '节点正在等待表单提交'

            return acc
          }

          const waitDate = new Date(workflowExecution.waitTill)

          if (waitDate.getTime() === WAIT_INDEFINITELY.getTime()) {
            acc[node.id] = '节点正在等待传入的 Webhook 调用（无限期）'
          }

          acc[node.id] = `节点正在等待直到 ${waitDate.toLocaleDateString()} ${waitDate.toLocaleTimeString()}`
        }
      }

      return acc
    }, {}),
  )

  const createStickyNoteRenderType = (node: INodeUi): CanvasNodeStickyNoteRender => {
    return {
      type: CanvasNodeRenderType.StickyNote,
      options: {
        width: node.parameters.width as number,
        height: node.parameters.height as number,
        color: node.parameters.color as number,
        content: node.parameters.content as string,
      },
    }
  }

  const createAddNodesRenderType = (): CanvasNodeAddNodesRender => {
    return {
      type: CanvasNodeRenderType.AddNodes,
      options: {},
    }
  }

  const isTriggerNodeById = computed(() =>
    nodes.value.reduce<Record<string, boolean>>((acc, node) => {
      acc[node.id] = nodeTypesStore.isTriggerNode(node.type)

      return acc
    }, {}),
  )

  const getLabelSize = (label: string = ''): number => {
    if (label.length <= 2) {
      return 0
    }
    else if (label.length <= 6) {
      return 1
    }
    else {
      return 2
    }
  }

  const getMaxNodePortsLabelSize = (
    ports: CanvasConnectionPort[],
  ): CanvasNodeDefaultRenderLabelSize => {
    const labelSizes: CanvasNodeDefaultRenderLabelSize[] = ['small', 'medium', 'large']
    const labelSizeIndexes = ports.reduce<number[]>(
      (sizeAcc, input) => {
        if (input.type === NodeConnectionType.Main) {
          sizeAcc.push(getLabelSize(input.label ?? ''))
        }

        return sizeAcc
      },
      [0],
    )

    return labelSizes[Math.max(...labelSizeIndexes)]
  }

  const nodeInputLabelSizeById = computed(() =>
    nodes.value.reduce<Record<string, CanvasNodeDefaultRenderLabelSize>>((acc, node) => {
      acc[node.id] = getMaxNodePortsLabelSize(nodeInputsById.value[node.id])

      return acc
    }, {}),
  )

  const nodeOutputLabelSizeById = computed(() =>
    nodes.value.reduce<Record<string, CanvasNodeDefaultRenderLabelSize>>((acc, node) => {
      acc[node.id] = getMaxNodePortsLabelSize(nodeOutputsById.value[node.id])

      return acc
    }, {}),
  )

  const nodeTooltipById = computed(() => {
    if (!workflowStore.isWorkflowRunning) {
      return {}
    }

    const activeTriggerNodeCount = nodes.value.filter(
      (node) => isTriggerNodeById.value[node.id] && !node.disabled,
    ).length
    const triggerNodeName = workflowStore.getWorkflowExecution?.triggerNode

    // For workflows with multiple active trigger nodes, we show a tooltip only when
    // trigger node name is known
    if (triggerNodeName === undefined && activeTriggerNodeCount !== 1) {
      return {}
    }

    return nodes.value.reduce<Record<string, string | undefined>>((acc, node) => {
      const nodeTypeDescription = nodeTypeDescriptionByNodeId.value[node.id]

      if (nodeTypeDescription && isTriggerNodeById.value[node.id]) {
        if (
          !!node.disabled
          || (triggerNodeName !== undefined && triggerNodeName !== node.name)
          || !['new', 'unknown', 'waiting'].includes(nodeExecutionStatusById.value[node.id])
        ) {
          return acc
        }

        if ('eventTriggerDescription' in nodeTypeDescription) {
          acc[node.id] = `正在等待您在 ${nodeTypeDescription.name} 中创建事件`
        }
        else {
          acc[node.id] = `正在等待您在 ${nodeTypeDescription ? getTriggerNodeServiceName(nodeTypeDescription) : ''} 中创建事件`
        }
      }

      return acc
    }, {})
  })

  const createDefaultNodeRenderType = (node: INodeUi): CanvasNodeDefaultRender => {
    return {
      type: CanvasNodeRenderType.Default,
      options: {
        trigger: isTriggerNodeById.value[node.id],
        configuration: nodeTypesStore.isConfigNode(workflowObject.value, node, node.type),
        configurable: nodeTypesStore.isConfigurableNode(workflowObject.value, node, node.type),
        inputs: {
          labelSize: nodeInputLabelSizeById.value[node.id],
        },
        outputs: {
          labelSize: nodeOutputLabelSizeById.value[node.id],
        },
        tooltip: nodeTooltipById.value[node.id],
      },
    }
  }

  const renderTypeByNodeId = computed(
    () =>
      nodes.value.reduce<Record<string, CanvasNodeData['render']>>((acc, node) => {
        switch (node.type) {
          case `${CanvasNodeRenderType.StickyNote}`:
            acc[node.id] = createStickyNoteRenderType(node)
            break

          case `${CanvasNodeRenderType.AddNodes}`:
            acc[node.id] = createAddNodesRenderType()
            break

          default:
            acc[node.id] = createDefaultNodeRenderType(node)
        }

        return acc
      }, {}) ?? {},
  )

  const getConnectionData = (connection: CanvasConnection): CanvasConnectionData => {
    const { type, index } = parseCanvasConnectionHandleString(connection.sourceHandle)
    const runDataTotal = nodeExecutionRunDataOutputMapById.value[connection.source]?.[type]?.[index]?.total ?? 0

    let status: CanvasConnectionData['status']

    if (nodeExecutionRunningById.value[connection.source]) {
      status = 'running'
    }
    else if (
      nodePinnedDataById.value[connection.source]
      && nodeExecutionRunDataById.value[connection.source]
    ) {
      status = 'pinned'
    }
    else if (nodeHasIssuesById.value[connection.source]) {
      status = 'error'
    }
    else if (runDataTotal > 0) {
      status = 'success'
    }

    const maxConnections = [
      ...nodeInputsById.value[connection.source],
      ...nodeInputsById.value[connection.target],
    ]
      .filter((port) => port.type === type)
      .reduce<number | undefined>((acc, port) => {
        if (port.maxConnections === undefined) {
          return acc
        }

        return Math.min(acc ?? Infinity, port.maxConnections)
      }, undefined)

    return {
      ...(connection.data as CanvasConnectionData),
      ...(maxConnections ? { maxConnections } : {}),
      status,
    }
  }

  // MARK: mappedNodes
  const mappedNodes = computed<CanvasNode[]>(() => {
    return [
      ...nodes.value.map<CanvasNode>((node) => {
        const inputConnections = workflowObject.value.connectionsByDestinationNode[node.name] ?? {}
        const outputConnections = workflowObject.value.connectionsBySourceNode[node.name] ?? {}

        const data: CanvasNodeData = {
          id: node.id,
          name: node.name,
          subtitle: nodeSubtitleById.value[node.id] ?? '',
          type: node.type,
          typeVersion: node.typeVersion,
          disabled: node.disabled,
          inputs: nodeInputsById.value[node.id] ?? [],
          outputs: nodeOutputsById.value[node.id] ?? [],
          connections: {
            [CanvasConnectionMode.Input]: inputConnections,
            [CanvasConnectionMode.Output]: outputConnections,
          },
          issues: {
            items: nodeIssuesById.value[node.id],
            visible: nodeHasIssuesById.value[node.id],
          },
          pinnedData: {
            count: nodePinnedDataById.value[node.id]?.length ?? 0,
            visible: !!nodePinnedDataById.value[node.id],
          },
          execution: {
            status: nodeExecutionStatusById.value[node.id],
            waiting: nodeExecutionWaitingById.value[node.id],
            running: nodeExecutionRunningById.value[node.id],
          },
          runData: {
            outputMap: nodeExecutionRunDataOutputMapById.value[node.id],
            iterations: nodeExecutionRunDataById.value[node.id]?.length ?? 0,
            visible: !!nodeExecutionRunDataById.value[node.id],
          },
          render: renderTypeByNodeId.value[node.id] ?? { type: 'default', options: {} },
        }

        return {
          id: node.id,
          label: node.name,
          type: 'canvas-node',
          position: { x: node.position[0], y: node.position[1] },
          data,
          ...additionalNodePropertiesById.value[node.id],
        }
      }),
    ]
  })

  // MARK: mappedConnections
  const mappedConnections = computed<CanvasConnection[]>(() => {
    return mapLegacyConnectionsToCanvasConnections(connections.value ?? [], nodes.value ?? []).map(
      (connection) => {
        const type = getConnectionType(connection)
        const label = getConnectionLabel(connection)
        const data = getConnectionData(connection)

        return {
          ...connection,
          data,
          type,
          label,
          // markerEnd: {
          //   type: MarkerType.Arrow,
          //   color: 'var(--color-edge-main)',
          // },
        }
      },
    )
  })

  return {
    mappedNodes,
    mappedConnections,
  }
}
