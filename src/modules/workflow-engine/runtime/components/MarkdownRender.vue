<script setup lang="ts">
import DOMPurify from 'dompurify'
import MarkdownIt from 'markdown-it'

const props = defineProps<{
  content?: string
}>()

const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
})

const renderedContent = computed(() => {
  if (props.content) {
    const rawHtml = md.render(props.content)

    return DOMPurify.sanitize(rawHtml)
  }

  return ''
})
</script>

<template>
  <div
    class="prose"
    v-html="renderedContent"
  />
</template>
