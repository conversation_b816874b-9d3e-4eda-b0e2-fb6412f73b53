<script setup lang="ts">
import { AlertTriangleIcon } from 'lucide-vue-next'

const props = defineProps<{
  issues: string[]
}>()

const hasMultipleIssues = computed<boolean>(() => {
  return props.issues.length > 1
})
</script>

<template>
  <div class="pt-2">
    <div class="flex gap-1 overflow-hidden text-sm text-warning-500">
      <AlertTriangleIcon
        class="mt-0.5 shrink-0"
        :size="14"
      />

      <div
        v-if="hasMultipleIssues"
        class="flex-1"
      >
        <ul class="space-y-0.5">
          <li
            v-for="issue of issues"
            :key="issue"
            class="flex"
          >
            <span class="mr-1 font-bold">
              -
            </span>

            <span class="flex-1 break-all">{{ issue }}</span>
          </li>
        </ul>
      </div>

      <div
        v-else
        class="flex-1 break-all"
      >
        {{ issues.at(0) }}
      </div>
    </div>
  </div>
</template>
