<script setup lang="ts">
import { get, set, unset } from 'lodash-es'
import type { INodeParameters, INodeProperties, INodeTypeDescription, NodeParameterValue } from 'n8n-workflow'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'
import { isINodePropertiesList, isINodePropertyCollectionList, isINodePropertyOptionsList } from 'n8n-workflow/dist/type-guards.js'
import { deepCopy } from 'n8n-workflow/dist/utils.js'

import NodeSettingHeader from '#wf/components/node-details/NodeSettingHeader.vue'
import NodeWebhooks from '#wf/components/node-details/NodeWebhooks.vue'
import ParameterInputList from '#wf/components/node-details/param/ParameterInputList.vue'
import NodeCredentials from '#wf/components/NodeCredentials.vue'
import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { RenameNodeCommand } from '#wf/models/history'
import { useHistoryStore } from '#wf/stores/history.store'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { INodeUpdatePropertiesInformation, IUpdateInformation } from '#wf/types/interface'
import { updateDynamicConnections } from '#wf/utils/nodeSettingsUtils'

import { GlobalEvent } from '~/enums/event'

const props = defineProps<{
  nodeType: INodeTypeDescription | null
}>()

const workflowStore = useWorkflowStore()
const { isCanvasReadOnly } = storeToRefs(workflowStore)

const nodeTypesStore = useNodeTypesStore()
const historyStore = useHistoryStore()

const ndvStore = useNDVStore()
const { activeNode } = storeToRefs(ndvStore)

const nodeValuesInitialized = ref(false)
const nodeValid = ref(true)
const nodeSettings = ref<INodeProperties[]>([])
const hiddenIssuesInputs = ref<string[]>([])

const nodeValues = ref<INodeParameters>({
  color: '#ff0000',
  alwaysOutputData: false,
  executeOnce: false,
  notesInFlow: false,
  onError: 'stopWorkflow',
  retryOnFail: false,
  maxTries: 3,
  waitBetweenTries: 1000,
  notes: '',
  parameters: {},
})

const nodeHelpers = useNodeHelpers()

const emit = defineEmits<{
  redrawRequired: []
}>()

const setValue = (name: string, value: NodeParameterValue) => {
  const nameParts = name.split('.')
  let lastNamePart: string | undefined = nameParts.pop()

  let isArray = false

  if (lastNamePart !== undefined && lastNamePart.includes('[')) {
    // It includes an index so we have to extract it
    const lastNameParts = lastNamePart.match(/(.*)\[(\d+)\]$/)

    if (lastNameParts) {
      nameParts.push(lastNameParts[1])
      lastNamePart = lastNameParts[2]
      isArray = true
    }
  }

  // Set the value so that everything updates correctly in the UI
  if (nameParts.length === 0) {
    // Data is on top level
    if (value === null) {
      // Property should be deleted
      if (lastNamePart) {
        const { [lastNamePart]: removedNodeValue, ...remainingNodeValues } = nodeValues.value
        nodeValues.value = remainingNodeValues
      }
    }
    else {
      // Value should be set
      nodeValues.value = {
        ...nodeValues.value,
        [lastNamePart as string]: value,
      }
    }
  }
  else {
    // Data is on lower level
    if (value === null) {
      // Property should be deleted
      let tempValue = get(nodeValues.value, nameParts.join('.')) as
        | INodeParameters
        | INodeParameters[]

      if (lastNamePart && !Array.isArray(tempValue)) {
        const { [lastNamePart]: removedNodeValue, ...remainingNodeValues } = tempValue
        tempValue = remainingNodeValues
      }

      if (isArray && Array.isArray(tempValue) && tempValue.length === 0) {
        // If a value from an array got delete and no values are left
        // delete also the parent
        lastNamePart = nameParts.pop()
        tempValue = get(nodeValues.value, nameParts.join('.')) as INodeParameters

        if (lastNamePart) {
          const { [lastNamePart]: removedArrayNodeValue, ...remainingArrayNodeValues } = tempValue
          tempValue = remainingArrayNodeValues
        }
      }
    }
    else {
      // Value should be set
      if (typeof value === 'object') {
        set(
          get(nodeValues.value, nameParts.join('.')) as Record<string, unknown>,
          lastNamePart as string,
          deepCopy(value),
        )
      }
      else {
        set(
          get(nodeValues.value, nameParts.join('.')) as Record<string, unknown>,
          lastNamePart as string,
          value,
        )
      }
    }
  }

  nodeValues.value = { ...nodeValues.value }
}

const setNodeValues = () => {
  if (activeNode.value) {
    if (props.nodeType !== null) {
      nodeValid.value = true

      const foundNodeSettings = []

      if (activeNode.value.color) {
        foundNodeSettings.push('color')
        nodeValues.value = {
          ...nodeValues.value,
          color: activeNode.value.color,
        }
      }

      if (activeNode.value.notes) {
        foundNodeSettings.push('notes')
        nodeValues.value = {
          ...nodeValues.value,
          notes: activeNode.value.notes,
        }
      }

      if (activeNode.value.alwaysOutputData) {
        foundNodeSettings.push('alwaysOutputData')
        nodeValues.value = {
          ...nodeValues.value,
          alwaysOutputData: activeNode.value.alwaysOutputData,
        }
      }

      if (activeNode.value.executeOnce) {
        foundNodeSettings.push('executeOnce')
        nodeValues.value = {
          ...nodeValues.value,
          executeOnce: activeNode.value.executeOnce,
        }
      }

      if (activeNode.value.continueOnFail) {
        foundNodeSettings.push('onError')
        nodeValues.value = {
          ...nodeValues.value,
          onError: 'continueRegularOutput',
        }
      }

      if (activeNode.value.onError) {
        foundNodeSettings.push('onError')
        nodeValues.value = {
          ...nodeValues.value,
          onError: activeNode.value.onError,
        }
      }

      if (activeNode.value.notesInFlow) {
        foundNodeSettings.push('notesInFlow')
        nodeValues.value = {
          ...nodeValues.value,
          notesInFlow: activeNode.value.notesInFlow,
        }
      }

      if (activeNode.value.retryOnFail) {
        foundNodeSettings.push('retryOnFail')
        nodeValues.value = {
          ...nodeValues.value,
          retryOnFail: activeNode.value.retryOnFail,
        }
      }

      if (activeNode.value.maxTries) {
        foundNodeSettings.push('maxTries')
        nodeValues.value = {
          ...nodeValues.value,
          maxTries: activeNode.value.maxTries,
        }
      }

      if (activeNode.value.waitBetweenTries) {
        foundNodeSettings.push('waitBetweenTries')
        nodeValues.value = {
          ...nodeValues.value,
          waitBetweenTries: activeNode.value.waitBetweenTries,
        }
      }

      // Set default node settings
      for (const nodeSetting of nodeSettings.value) {
        if (!foundNodeSettings.includes(nodeSetting.name)) {
          // Set default value
          nodeValues.value = {
            ...nodeValues.value,
            [nodeSetting.name]: nodeSetting.default,
          }
        }
      }

      nodeValues.value = {
        ...nodeValues.value,
        parameters: deepCopy(activeNode.value.parameters),
      }
    }
    else {
      nodeValid.value = false
    }
  }

  nodeValuesInitialized.value = true
}

// 当节点改变时更新节点设置值
watch(activeNode, () => {
  setNodeValues()
}, { immediate: true })

/**
 * Removes node values that are not valid options for the given parameter.
 * This can happen when there are multiple node parameters with the same name
 * but different options and display conditions
 * @param nodeType The node type description
 * @param nodeParameterValues Current node parameter values
 * @param updatedParameter The parameter that was updated. Will be used to determine which parameters to remove based on their display conditions and option values
 */
const removeMismatchedOptionValues = (
  nodeType: INodeTypeDescription,
  nodeParameterValues: INodeParameters | null,
  updatedParameter: { name: string, value: NodeParameterValue },
) => {
  nodeType.properties.forEach((prop) => {
    const displayOptions = prop.displayOptions

    // Not processing parameters that are not set or don't have options
    if (!nodeParameterValues || !Object.hasOwn(nodeParameterValues, prop.name) || !displayOptions || !prop.options) {
      return
    }

    // Only process the parameters that depend on the updated parameter
    const showCondition = displayOptions.show?.[updatedParameter.name]
    const hideCondition = displayOptions.hide?.[updatedParameter.name]

    if (showCondition === undefined && hideCondition === undefined) {
      return
    }

    let hasValidOptions = true

    // Every value should be a possible option
    if (isINodePropertyCollectionList(prop.options) || isINodePropertiesList(prop.options)) {
      hasValidOptions = Object.keys(nodeParameterValues).every(
        (key) => (prop.options ?? []).find((option) => option.name === key) !== undefined,
      )
    }
    else if (isINodePropertyOptionsList(prop.options)) {
      hasValidOptions = !!prop.options.find(
        (option) => option.value === nodeParameterValues[prop.name],
      )
    }

    if (!hasValidOptions && NodeHelpers.displayParameter(nodeParameterValues, prop, activeNode.value)) {
      unset(nodeParameterValues as object, prop.name)
    }
  })
}

const handleCredentialSelected = (updateInformation: INodeUpdatePropertiesInformation) => {
  // Update the values on the node
  workflowStore.updateNodeProperties(updateInformation)

  const node = workflowStore.getNodeByName(updateInformation.name)

  if (node) {
    // Update the issues
    nodeHelpers.updateNodeCredentialIssues(node)
  }

  // void externalHooks.run('nodeSettings.credentialSelected', { updateInformation })
}

const handleValueChange = (parameterData: IUpdateInformation) => {
  let newValue: NodeParameterValue

  if (Object.hasOwn(parameterData, 'value')) {
    // New value is given
    newValue = parameterData.value as string | number
  }
  else {
    // Get new value from nodeData where it is set already
    newValue = get(nodeValues.value, parameterData.name) as NodeParameterValue
  }

  // Save the node name before we commit the change because
  // we need the old name to rename the node properly
  const nodeNameBefore = parameterData.node || activeNode.value?.name

  if (!nodeNameBefore) {
    return
  }

  const _node = workflowStore.getNodeByName(nodeNameBefore)

  if (_node === null) {
    return
  }

  if (parameterData.name === 'onError') {
    // If that parameter changes, we need to redraw the connections, as the error output may need to be added or removed
    emit('redrawRequired')
  }

  if (parameterData.name === 'name') {
    // Name of node changed so we have to set also the new node name as active

    // Update happens in NodeView so emit event
    const sendData = {
      value: newValue,
      oldValue: nodeNameBefore,
      name: parameterData.name,
    }

    emitter.emit(GlobalEvent.NodeRename, sendData)
  }
  else if (parameterData.name === 'parameters') {
    const nodeType = nodeTypesStore.getNodeType(_node.type, _node.typeVersion)

    if (!nodeType) {
      return
    }

    // Get only the parameters which are different to the defaults
    let nodeParameters = NodeHelpers.getNodeParameters(
      nodeType.properties,
      _node.parameters,
      false,
      false,
      _node,
    )

    // const oldNodeParameters = Object.assign({}, nodeParameters)

    // Copy the data because it is the data of vuex so make sure that
    // we do not edit it directly
    nodeParameters = deepCopy(nodeParameters)

    if (parameterData.value && typeof parameterData.value === 'object') {
      for (const parameterName of Object.keys(parameterData.value)) {
        // @ts-ignore
        newValue = parameterData.value[parameterName]

        // Remove the 'parameters.' from the beginning to just have the
        // actual parameter name
        const parameterPath = parameterName.split('.').slice(1).join('.')

        // Check if the path is supposed to change an array and if so get
        // the needed data like path and index
        const parameterPathArray = parameterPath.match(/(.*)\[(\d+)\]$/)

        // Apply the new value
        // @ts-ignore
        if (parameterData[parameterName] === undefined && parameterPathArray !== null) {
          // Delete array item
          const path = parameterPathArray[1]
          const index = parameterPathArray[2]
          const data = get(nodeParameters, path)

          if (Array.isArray(data)) {
            data.splice(parseInt(index, 10), 1)
            set(nodeParameters as object, path, data)
          }
        }
        else {
          if (newValue === undefined) {
            unset(nodeParameters as object, parameterPath)
          }
          else {
            set(nodeParameters as object, parameterPath, newValue)
          }
        }

        // void externalHooks.run('nodeSettings.valueChanged', {
        //   parameterPath,
        //   newValue,
        //   parameters: parameters.value,
        //   oldNodeParameters,
        // })
      }
    }

    // Get the parameters with the now new defaults according to the
    // from the user actually defined parameters
    nodeParameters = NodeHelpers.getNodeParameters(
      nodeType.properties,
      nodeParameters as INodeParameters,
      true,
      false,
      _node,
    )

    for (const key of Object.keys(nodeParameters as object)) {
      if (nodeParameters && nodeParameters[key] !== null && nodeParameters[key] !== undefined) {
        setValue(`parameters.${key}`, nodeParameters[key] as string)
      }
    }

    if (nodeParameters) {
      const updateInformation: IUpdateInformation = {
        name: _node.name,
        value: nodeParameters,
      }

      workflowStore.setNodeParameters(updateInformation)

      nodeHelpers.updateNodeParameterIssuesByName(_node.name)
      nodeHelpers.updateNodeCredentialIssuesByName(_node.name)
    }
  }
  else if (parameterData.name.startsWith('parameters.')) {
    // A node parameter changed

    const nodeType = nodeTypesStore.getNodeType(_node.type, _node.typeVersion)

    if (!nodeType) {
      return
    }

    // Get only the parameters which are different to the defaults
    let nodeParameters = NodeHelpers.getNodeParameters(
      nodeType.properties,
      _node.parameters,
      false,
      false,
      _node,
    )
    // const oldNodeParameters = Object.assign({}, nodeParameters)

    // Copy the data because it is the data of vuex so make sure that
    // we do not edit it directly
    nodeParameters = deepCopy(nodeParameters)

    // Remove the 'parameters.' from the beginning to just have the
    // actual parameter name
    const parameterPath = parameterData.name.split('.').slice(1).join('.')

    // Check if the path is supposed to change an array and if so get
    // the needed data like path and index
    const parameterPathArray = parameterPath.match(/(.*)\[(\d+)\]$/)

    // Apply the new value
    if (parameterData.value === undefined && parameterPathArray !== null) {
      // Delete array item
      const path = parameterPathArray[1]
      const index = parameterPathArray[2]
      const data = get(nodeParameters, path)

      if (Array.isArray(data)) {
        data.splice(parseInt(index, 10), 1)
        set(nodeParameters as object, path, data)
      }
    }
    else {
      if (newValue === undefined) {
        unset(nodeParameters as object, parameterPath)
      }
      else {
        set(nodeParameters as object, parameterPath, newValue)
      }

      // If value is updated, remove parameter values that have invalid options
      // so getNodeParameters checks don't fail
      removeMismatchedOptionValues(nodeType, nodeParameters, {
        name: parameterPath,
        value: newValue,
      })
    }

    // Get the parameters with the now new defaults according to the
    // from the user actually defined parameters
    nodeParameters = NodeHelpers.getNodeParameters(
      nodeType.properties,
      nodeParameters as INodeParameters,
      true,
      false,
      _node,
    )

    for (const key of Object.keys(nodeParameters as object)) {
      if (nodeParameters && nodeParameters[key] !== null && nodeParameters[key] !== undefined) {
        setValue(`parameters.${key}`, nodeParameters[key] as string)
      }
    }

    // Update the data in vuex
    const updateInformation: IUpdateInformation = {
      name: _node.name,
      value: nodeParameters,
    }

    const connections = workflowStore.workflow.connections

    const updatedConnections = updateDynamicConnections(_node, connections, parameterData)

    if (updatedConnections) {
      workflowStore.setConnections(updatedConnections, true)
    }

    workflowStore.setNodeParameters(updateInformation)

    // void externalHooks.run('nodeSettings.valueChanged', {
    //   parameterPath,
    //   newValue,
    //   parameters: parameters.value,
    //   oldNodeParameters,
    // })

    nodeHelpers.updateNodeParameterIssuesByName(_node.name)
    nodeHelpers.updateNodeCredentialIssuesByName(_node.name)
  }
  else {
    // A property on the node itself changed

    // Update data in settings
    nodeValues.value = {
      ...nodeValues.value,
      [parameterData.name]: newValue,
    }

    // Update data in vuex
    const updateInformation = {
      name: _node.name,
      key: parameterData.name,
      value: newValue,
    }

    workflowStore.setNodeValue(updateInformation)
  }
}

const parameters = computed(() => {
  return props.nodeType?.properties ?? []
})

const parametersSetting = computed(() =>
  parameters.value.filter((it) => it.isNodeSetting),
)

const parametersNoneSetting = computed(() =>
  parameters.value.filter((it) => !it.isNodeSetting),
)

const handleUpdateNodeName = (newName: string) => {
  if (activeNode.value) {
    historyStore.pushCommandToUndo(new RenameNodeCommand(activeNode.value.name, newName))
  }

  handleValueChange({
    value: newName,
    name: 'name',
  })
}
</script>

<template>
  <div class="flex h-full flex-col">
    <NodeSettingHeader
      :nodeType="nodeType"
      @nodeNameChange="handleUpdateNodeName"
    />

    <Divider
      class="z-10 !m-0"
      data-testid="node-settings-divider"
    />

    <div class="flex-1 overflow-y-auto p-card-container">
      <NodeWebhooks :nodeType="nodeType" />

      <ParameterInputList
        v-if="nodeValuesInitialized && activeNode"
        :nodeValues="nodeValues"
        :parameters="parametersNoneSetting"
        path="parameters"
        :readOnly="isCanvasReadOnly"
        @paramValueChange="handleValueChange"
      >
        <NodeCredentials
          :activeNode="activeNode"
          :hideIssues="hiddenIssuesInputs.includes('credentials')"
          :readonly="isCanvasReadOnly"
          showAll
          @credentialSelected="handleCredentialSelected"
          @valueChanged="handleValueChange"
        />
      </ParameterInputList>
    </div>
  </div>
</template>
