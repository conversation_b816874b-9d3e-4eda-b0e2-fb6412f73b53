<script setup lang="ts">
import { computed, reactive, watch } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'

import type {
  AssignmentCollectionValue,
  AssignmentValue,
  INode,
  INodeProperties,
} from 'n8n-workflow'
import { nanoid } from 'nanoid'

import { useDebounce } from '#wf/composables/useDebounce'
import { useNDVStore } from '#wf/stores/ndv.store'

import AssignmentGroup from './AssignmentGroup.vue'

interface Props {
  param: INodeProperties
  value: AssignmentCollectionValue
  path: string
  node: INode | null
  isReadOnly?: boolean
}

const props = withDefaults(defineProps<Props>(), { isReadOnly: false })

const emit = defineEmits<{
  valueChange: [value: { name: string, node: string, value: AssignmentCollectionValue }]
}>()

const state = reactive<{ paramValue: AssignmentCollectionValue }>({
  paramValue: {
    assignments: props.value.assignments?.map((assignment) => {
      if (!assignment.id) {
        assignment.id = crypto.randomUUID()
      }

      return assignment
    }) ?? [],
  },
})

const ndvStore = useNDVStore()

const { callDebounced } = useDebounce()

const issues = computed(() => {
  if (!ndvStore.activeNode) {
    return {}
  }

  return ndvStore.activeNode?.issues?.parameters ?? {}
})

const handlerClass = nanoid(4)
const isDragging = ref(false)

// const inputData = computed(() => ndvStore.ndvInputData?.[0]?.json)

// const activeDragField = computed(() => propertyNameFromExpression(ndvStore.draggableData))

// const actions = computed(() => {
//   return [
//     {
//       label: '添加全部',
//       value: 'addAll',
//       disabled: !inputData.value,
//     },
//     {
//       label: '清空全部',
//       value: 'clearAll',
//       disabled: state.paramValue.assignments.length === 0,
//     },
//   ]
// })

watch(state.paramValue, (value) => {
  void callDebounced(
    () => {
      emit('valueChange', { name: props.path, value, node: props.node?.name as string })
    },
    { debounceTime: 1000 },
  )
})

const addAssignment = () => {
  state.paramValue.assignments.push({
    id: crypto.randomUUID(),
    name: '',
    value: '',
    type: 'string',
  })
}

function onAssignmentUpdate(index: number, value: AssignmentValue): void {
  state.paramValue.assignments[index] = value
}

function onAssignmentRemove(index: number): void {
  state.paramValue.assignments.splice(index, 1)
}

function getIssues(index: number): string[] {
  return issues.value[`${props.param.name}.${index}`] ?? []
}

// function optionSelected(action: string) {
//   if (action === 'clearAll') {
//     state.paramValue.assignments = []
//   }
//   else if (action === 'addAll' && inputData.value) {
//     const newAssignments = inputDataToAssignments(inputData.value)
//     state.paramValue.assignments = state.paramValue.assignments.concat(newAssignments)
//   }
// }
</script>

<template>
  <div :data-test-id="`assignment-collection-${param.name}`">
    <!-- <n8n-input-label
      color="text-dark"
      :label="parameter.displayName"
      :showExpressionSelector="false"
      size="small"
      underline
    >
      <template #options>
        <ParameterOptions
          :customActions="actions"
          :isReadOnly="isReadOnly"
          :parameter="parameter"
          :showExpressionSelector="false"
          :value="value"
          @update:modelValue="optionSelected"
        />
      </template>
    </n8n-input-label> -->

    <div class="flex flex-col gap-2">
      <VueDraggable
        v-model="state.paramValue.assignments"
        :animation="150"
        class="flex flex-col gap-2"
        :handle="`.${handlerClass}`"
        itemid="label"
        @end="isDragging = false"
        @start="isDragging = true"
      >
        <div
          v-for="(item, idx) of state.paramValue.assignments"
          :key="item.id"
        >
          <AssignmentGroup
            :handlerClass="handlerClass"
            :index="idx"
            :isDragging="isDragging"
            :isReadOnly="isReadOnly"
            :issues="getIssues(idx)"
            :modelValue="item"
            :path="`${path}.${idx}`"
            @remove="() => onAssignmentRemove(idx)"
            @update:modelValue="(value: AssignmentValue) => onAssignmentUpdate(idx, value)"
          />
        </div>
      </VueDraggable>

      <Button
        v-if="!isReadOnly"
        data-test-id="assignment-collection-drop-area"
        fluid
        label="添加字段"
        size="small"
        variant="outlined"
        @click="addAssignment()"
      />
    </div>
  </div>
</template>
