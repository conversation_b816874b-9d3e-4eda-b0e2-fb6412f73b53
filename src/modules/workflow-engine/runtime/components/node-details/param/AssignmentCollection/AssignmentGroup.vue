<script setup lang="ts">
import { GripVerticalIcon, TrashIcon } from 'lucide-vue-next'
import type { AssignmentValue, INodeProperties } from 'n8n-workflow'

import ParameterInputFull from '#wf/components/node-details/param/ParameterInputFull.vue'
import ParameterIssues from '#wf/components/ParameterIssues.vue'
import { useResolvedExpression } from '#wf/composables/useResolvedExpression'
import { useEnvironmentsStore } from '#wf/stores/environments.ee.store'
import { useNDVStore } from '#wf/stores/ndv.store'
import type { IUpdateInformation } from '#wf/types/interface'

import TypeSelect from './TypeSelect.vue'

interface Props {
  path: string
  modelValue: AssignmentValue
  issues: string[]
  hideType?: boolean
  isReadOnly?: boolean
  index?: number

  isDragging?: boolean
  handlerClass?: string
}

const props = defineProps<Props>()

const assignment = ref<AssignmentValue>(props.modelValue)

const emit = defineEmits<{
  'update:model-value': [value: AssignmentValue]
  remove: []
}>()

const ndvStore = useNDVStore()
const environmentsStore = useEnvironmentsStore()

const assignmentTypeToNodeProperty = (
  type: string,
): Partial<INodeProperties> & Pick<INodeProperties, 'type'> => {
  switch (type) {
    case 'boolean':
      return {
        type: 'options',
        default: false,
        options: [
          { name: 'false', value: false },
          { name: 'true', value: true },
        ],
      }

    case 'array':
      // fall through

    case 'object':
      // fall through

    case 'any':
      return { type: 'string' }

    default:
      return { type } as INodeProperties
  }
}

const nameParameter = computed<INodeProperties>(() => ({
  name: 'name',
  displayName: 'Name',
  default: '',
  requiresDataPath: 'single',
  placeholder: 'name',
  type: 'string',
}))

const valueParameter = computed<INodeProperties>(() => {
  return {
    name: 'value',
    displayName: 'Value',
    default: '',
    placeholder: 'value',
    ...assignmentTypeToNodeProperty(assignment.value.type ?? 'string'),
  }
})

const value = computed(() => assignment.value.value)

const resolvedAdditionalExpressionData = computed(() => {
  return { $vars: environmentsStore.variablesAsObject }
})

const { resolvedExpressionString, isExpression } = useResolvedExpression({
  expression: value,
  additionalData: resolvedAdditionalExpressionData,
})

const hint = computed(() => resolvedExpressionString.value)

const highlightHint = computed(() => Boolean(hint.value && ndvStore.getHoveringItem))

const onAssignmentNameChange = (update: IUpdateInformation): void => {
  assignment.value.name = update.value as string
}

const onAssignmentTypeChange = (update: string): void => {
  assignment.value.type = update

  if (update === 'boolean' && !isExpression.value) {
    assignment.value.value = false
  }
}

const onAssignmentValueChange = (update: IUpdateInformation): void => {
  assignment.value.value = update.value as string
}

const onRemove = (): void => {
  emit('remove')
}

const onBlur = (): void => {
  emit('update:model-value', assignment.value)
}
</script>

<template>
  <div data-test-id="assignment">
    <div class="flex">
      <div class="flex-wrap gap-1 flex-center">
        <div class="flex-1">
          <ParameterInputFull
            :key="nameParameter.type"
            data-test-id="assignment-name"
            displayOptions
            hideHint
            hideLabel
            :isReadOnly="isReadOnly"
            :parameter="nameParameter"
            :path="`${path}.name`"
            :value="assignment.name"
            @blur="onBlur"
            @update="onAssignmentNameChange"
          />
        </div>

        <TypeSelect
          v-if="!hideType"
          :isReadOnly="isReadOnly"
          :modelValue="assignment.type ?? 'string'"
          @update:modelValue="onAssignmentTypeChange"
        />

        <div class="relative flex-1">
          <ParameterInputFull
            :key="valueParameter.type"
            data-test-id="assignment-value"
            displayOptions
            hideHint
            hideIssues
            hideLabel
            :isReadOnly="isReadOnly"
            isAssignment
            :parameter="valueParameter"
            :path="`${path}.value`"
            :value="assignment.value"
            @blur="onBlur"
            @update="onAssignmentValueChange"
          />
        <!-- <ParameterInputHint
          :class="$style.hint"
          data-test-id="parameter-expression-preview-value"
          :highlight="highlightHint"
          :hint="hint"
          singleLine
        /> -->
        </div>
      </div>

      <div
        v-if="!isReadOnly"
        class="flex shrink-0 flex-col gap-0.5"
      >
        <ProBtn
          :class="[handlerClass, isDragging ? '!cursor-grabbing' : '!cursor-grab']"
          label="拖动排序"
          mini
          onlyIcon
          size="small"
          :tooltip="isDragging ? false : undefined"
        >
          <template #icon="{ size }">
            <GripVerticalIcon :size="size" />
          </template>
        </ProBtn>

        <ProBtn
          label="删除条件"
          mini
          onlyIcon
          size="small"
          @click="onRemove()"
        >
          <template #icon="{ size }">
            <TrashIcon :size="size" />
          </template>
        </ProBtn>
      </div>
    </div>

    <ParameterIssues
      v-if="issues.length > 0"
      :issues="issues"
    />
  </div>
</template>
