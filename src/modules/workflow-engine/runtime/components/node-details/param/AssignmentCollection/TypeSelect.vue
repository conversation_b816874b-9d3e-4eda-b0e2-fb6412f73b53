<script setup lang="ts">
import type { Select } from 'primevue'

import { ASSIGNMENT_TYPES } from './constants'
import type { AssignmentType } from './types'

interface Props {
  isReadOnly?: boolean
}

const modelValue = defineModel<AssignmentType['type']>()

defineProps<Props>()

const types = ASSIGNMENT_TYPES

const selectedType = computed(() =>
  types.find((type: AssignmentType) => type.type === modelValue.value) || types[0],
)

const onTypeChange = (type: string): void => {
  modelValue.value = type
}
</script>

<template>
  <Select
    :disabled="isReadOnly"
    :modelValue="modelValue"
    optionLabel="name"
    :options="types"
    optionValue="type"
    placeholder="选择类型"
    size="small"
    @update:modelValue="onTypeChange"
  >
    <template #value="{ value }">
      <div class="gap-2 text-sm flex-center">
        <Component
          :is="selectedType.icon"
          class="shrink-0"
          :size="15"
        />
        <span>{{ selectedType.name }}</span>
      </div>
    </template>

    <template #option="{ option }: { option: AssignmentType }">
      <div class="gap-2 text-sm flex-center">
        <Component
          :is="option.icon"
          class="shrink-0"
          :size="15"
        />
        <span>{{ option.name }}</span>
      </div>
    </template>
  </Select>
</template>
