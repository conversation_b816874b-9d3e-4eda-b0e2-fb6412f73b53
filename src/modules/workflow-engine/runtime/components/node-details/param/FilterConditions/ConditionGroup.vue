<script setup lang="ts">
import dayjs from 'dayjs'
import { debounce } from 'lodash-es'
import { GripVerticalIcon, TrashIcon } from 'lucide-vue-next'
import type {
  FilterConditionValue,
  FilterOptionsValue,
  INodeProperties,
  NodeParameterValue,
} from 'n8n-workflow'

import ParameterInputFull from '#wf/components/node-details/param/ParameterInputFull.vue'
import type { IUpdateInformation } from '#wf/types/interface'

import type { FilterOperatorId } from './constants'
import OperatorSelect from './OperatorSelect.vue'
import {
  getFilterOperator,
  handleOperatorChange,
  inferOperatorType,
  isEmptyInput,
  operatorTypeToNodeProperty,
  resolveCondition,
} from './utils'

interface Props {
  path: string
  condition: FilterConditionValue
  options: FilterOptionsValue
  issues?: string[]
  fixedLeftValue?: boolean
  canRemove?: boolean
  readOnly?: boolean
  index?: number

  canDrag?: boolean
  isDragging?: boolean
  handlerClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  issues: () => [],
  canRemove: true,
  fixedLeftValue: false,
  readOnly: false,
  index: 0,
  canDrag: true,
})

const emit = defineEmits<{
  update: [value: FilterConditionValue]
  remove: []
}>()

const condition = ref<FilterConditionValue>(props.condition)

const operatorId = computed<FilterOperatorId>(() => {
  const { type, operation } = props.condition.operator

  return `${type}:${operation}` as FilterOperatorId
})
const operator = computed(() => getFilterOperator(operatorId.value))

const isEmpty = computed(() => {
  if (operator.value.singleValue) {
    return isEmptyInput(condition.value.leftValue)
  }

  return isEmptyInput(condition.value.leftValue) && isEmptyInput(condition.value.rightValue)
})

const conditionResult = computed(() =>
  resolveCondition({ condition: condition.value, options: props.options }),
)

const suggestedType = computed(() => {
  if (conditionResult.value.status !== 'resolve_error') {
    return inferOperatorType(conditionResult.value.resolved.leftValue)
  }

  return 'any'
})

const allIssues = computed(() => {
  if (conditionResult.value.status === 'validation_error' && !isEmpty.value) {
    return [conditionResult.value.error]
  }

  return props.issues
})

const now = computed(() => dayjs().toISOString())

const leftParameter = computed<INodeProperties>(() => ({
  name: 'left',
  displayName: 'Left',
  default: '',
  placeholder: operator.value.type === 'dateTime'
    ? now.value
    : '比较值 1',
  ...operatorTypeToNodeProperty(operator.value.type),
}))

const rightParameter = computed<INodeProperties>(() => {
  const type = operator.value.rightType ?? operator.value.type

  return {
    name: 'right',
    displayName: 'Right',
    default: '',
    placeholder: type === 'dateTime' ? now.value : '比较值 2',
    ...operatorTypeToNodeProperty(type),
  }
})

const debouncedEmitUpdate = debounce(() => emit('update', condition.value), 500)

const onLeftValueChange = (update: IUpdateInformation): void => {
  condition.value.leftValue = update.value as NodeParameterValue
  debouncedEmitUpdate()
}

const onRightValueChange = (update: IUpdateInformation): void => {
  condition.value.rightValue = update.value as NodeParameterValue
  debouncedEmitUpdate()
}

const onOperatorChange = (value: string) => {
  const newOperator = getFilterOperator(value)

  condition.value = handleOperatorChange({
    condition: condition.value,
    newOperator,
  })

  debouncedEmitUpdate()
}

const onRemove = (): void => {
  emit('remove')
}

const onBlur = (): void => {
  debouncedEmitUpdate()
}
</script>

<template>
  <div class="flex gap-1">
    <div>
      <div class="gap-2 flex-center">
        <ParameterInputFull
          v-if="!fixedLeftValue"
          :key="leftParameter.type"
          displayOptions
          hideHint
          hideIssues
          hideLabel
          :isReadOnly="readOnly"
          :parameter="leftParameter"
          :path="`${path}.left`"
          :value="condition.leftValue"
          @blur="onBlur"
          @update="onLeftValueChange"
        />

        <div class="shrink-0">
          <OperatorSelect
            :readOnly="readOnly"
            :selected="`${operator.type}:${operator.operation}`"
            :suggestedType="suggestedType"
            @operatorChange="onOperatorChange"
          />
        </div>

        <ParameterInputFull
          :key="rightParameter.type"
          displayOptions
          hideHint
          hideIssues
          hideLabel
          :isReadOnly="readOnly"
          :parameter="rightParameter"
          :path="`${path}.right`"
          :value="condition.rightValue"
          @blur="onBlur"
          @update="onRightValueChange"
        />
      </div>

      <!-- <div :class="$style.status">
        <ParameterIssues
          v-if="allIssues.length > 0"
          :issues="allIssues"
        />

        <n8n-tooltip
          v-else-if="conditionResult.status === 'success' && conditionResult.result === true"
          :showAfter="500"
        >
          <template #content>
            此条件对第一个输入项目为真
          </template>
          <n8n-icon
            :class="$style.statusIcon"
            color="text-light"
            icon="check-circle"
            size="medium"
          />
        </n8n-tooltip>

        <n8n-tooltip
          v-else-if="conditionResult.status === 'success' && conditionResult.result === false"
          :showAfter="500"
        >
          <template #content>
            此条件对第一个输入项目为假
          </template>
          <n8n-icon
            :class="$style.statusIcon"
            color="text-light"
            icon="times-circle"
            size="medium"
          />
        </n8n-tooltip>
      </div> -->
    </div>

    <div
      v-if="!readOnly"
      class="flex shrink-0 flex-col gap-0.5"
    >
      <ProBtn
        v-if="canDrag"
        :class="[handlerClass, isDragging ? '!cursor-grabbing' : '!cursor-grab']"
        label="拖动排序"
        mini
        onlyIcon
        size="small"
        :tooltip="isDragging ? false : undefined"
      >
        <template #icon="{ size }">
          <GripVerticalIcon :size="size" />
        </template>
      </ProBtn>

      <ProBtn
        v-if="canRemove"
        label="删除条件"
        mini
        onlyIcon
        size="small"
        @click="onRemove()"
      >
        <template #icon="{ size }">
          <TrashIcon :size="size - 2" />
        </template>
      </ProBtn>
    </div>
  </div>
</template>
