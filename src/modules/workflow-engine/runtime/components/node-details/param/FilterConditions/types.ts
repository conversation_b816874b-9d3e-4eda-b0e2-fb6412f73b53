import type { FilterConditionValue, FilterOperatorValue } from 'n8n-workflow'

type BaseTextKey = string | number | symbol

export interface FilterOperator extends FilterOperatorValue {
  name: BaseTextKey
}

export interface FilterOperatorGroup {
  id: string
  name: BaseText<PERSON><PERSON>
  icon?: Component
  children: FilterOperator[]
}

export type ConditionResult =
  | { status: 'resolve_error' }
  | { status: 'validation_error', error: string, resolved: FilterConditionValue }
  | {
    status: 'success'
    result: boolean
    resolved: FilterConditionValue
  }
