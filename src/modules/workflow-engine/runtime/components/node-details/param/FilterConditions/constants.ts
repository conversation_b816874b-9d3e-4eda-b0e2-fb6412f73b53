import { BinaryIcon, BoxIcon, BracketsIcon, Calendar1Icon, LetterTextIcon, SquareCheckIcon } from 'lucide-vue-next'
import type { FilterConditionValue, FilterOptionsValue } from 'n8n-workflow'

import type { FilterOperator, FilterOperatorGroup } from './types'

export const DEFAULT_MAX_CONDITIONS = 10

export const DEFAULT_FILTER_OPTIONS: FilterOptionsValue = {
  caseSensitive: true,
  leftValue: '',
  typeValidation: 'strict',
  version: 1,
}

export const OPERATORS_BY_ID = {
  'string:exists': {
    type: 'string',
    operation: 'exists',
    name: '存在',
    singleValue: true,
  },
  'string:notExists': {
    type: 'string',
    operation: 'notExists',
    name: '不存在',
    singleValue: true,
  },
  'string:empty': {
    type: 'string',
    operation: 'empty',
    name: '为空',
    singleValue: true,
  },
  'string:notEmpty': {
    type: 'string',
    operation: 'notEmpty',
    name: '不为空',
    singleValue: true,
  },
  'string:equals': { type: 'string', operation: 'equals', name: '等于' },
  'string:notEquals': { type: 'string', operation: 'notEquals', name: '不等于' },
  'string:contains': { type: 'string', operation: 'contains', name: '包含' },
  'string:notContains': {
    type: 'string',
    operation: 'notContains',
    name: '不包含',
  },
  'string:startsWith': {
    type: 'string',
    operation: 'startsWith',
    name: '以...开始',
  },
  'string:notStartsWith': {
    type: 'string',
    operation: 'notStartsWith',
    name: '不以...开始',
  },
  'string:endsWith': { type: 'string', operation: 'endsWith', name: '以...结束' },
  'string:notEndsWith': {
    type: 'string',
    operation: 'notEndsWith',
    name: '不以...结束',
  },
  'string:regex': { type: 'string', operation: 'regex', name: '正则表达式' },
  'string:notRegex': { type: 'string', operation: 'notRegex', name: '不匹配正则表达式' },
  'number:exists': {
    type: 'number',
    operation: 'exists',
    name: '存在',
    singleValue: true,
  },
  'number:notExists': {
    type: 'number',
    operation: 'notExists',
    name: '不存在',
    singleValue: true,
  },
  'number:empty': {
    type: 'number',
    operation: 'empty',
    name: '为空',
    singleValue: true,
  },
  'number:notEmpty': {
    type: 'number',
    operation: 'notEmpty',
    name: '不为空',
    singleValue: true,
  },
  'number:equals': { type: 'number', operation: 'equals', name: '等于' },
  'number:notEquals': { type: 'number', operation: 'notEquals', name: '不等于' },
  'number:gt': { type: 'number', operation: 'gt', name: '大于' },
  'number:lt': { type: 'number', operation: 'lt', name: '小于' },
  'number:gte': { type: 'number', operation: 'gte', name: '大于等于' },
  'number:lte': { type: 'number', operation: 'lte', name: '小于等于' },
  'dateTime:exists': {
    type: 'dateTime',
    operation: 'exists',
    name: '存在',
    singleValue: true,
  },
  'dateTime:notExists': {
    type: 'dateTime',
    operation: 'notExists',
    name: '不存在',
    singleValue: true,
  },
  'dateTime:empty': {
    type: 'dateTime',
    operation: 'empty',
    name: '为空',
    singleValue: true,
  },
  'dateTime:notEmpty': {
    type: 'dateTime',
    operation: 'notEmpty',
    name: '不为空',
    singleValue: true,
  },
  'dateTime:equals': { type: 'dateTime', operation: 'equals', name: '等于' },
  'dateTime:notEquals': {
    type: 'dateTime',
    operation: 'notEquals',
    name: '不等于',
  },
  'dateTime:after': { type: 'dateTime', operation: 'after', name: '大于' },
  'dateTime:before': { type: 'dateTime', operation: 'before', name: '小于' },
  'dateTime:afterOrEquals': {
    type: 'dateTime',
    operation: 'afterOrEquals',
    name: '大于或等于',
  },
  'dateTime:beforeOrEquals': {
    type: 'dateTime',
    operation: 'beforeOrEquals',
    name: '小于或等于',
  },
  'boolean:exists': {
    type: 'boolean',
    operation: 'exists',
    name: '存在',
    singleValue: true,
  },
  'boolean:notExists': {
    type: 'boolean',
    operation: 'notExists',
    name: '不存在',
    singleValue: true,
  },
  'boolean:empty': {
    type: 'boolean',
    operation: 'empty',
    name: '为空',
    singleValue: true,
  },
  'boolean:notEmpty': {
    type: 'boolean',
    operation: 'notEmpty',
    name: '不为空',
    singleValue: true,
  },
  'boolean:true': {
    type: 'boolean',
    operation: 'true',
    name: '为真',
    singleValue: true,
  },
  'boolean:false': {
    type: 'boolean',
    operation: 'false',
    name: '为假',
    singleValue: true,
  },
  'boolean:equals': { type: 'boolean', operation: 'equals', name: '等于' },
  'boolean:notEquals': {
    type: 'boolean',
    operation: 'notEquals',
    name: '不等于',
  },
  'array:exists': {
    type: 'array',
    operation: 'exists',
    name: '存在',
    singleValue: true,
  },
  'array:notExists': {
    type: 'array',
    operation: 'notExists',
    name: '不存在',
    singleValue: true,
  },
  'array:empty': {
    type: 'array',
    operation: 'empty',
    name: '为空',
    singleValue: true,
  },
  'array:notEmpty': {
    type: 'array',
    operation: 'notEmpty',
    name: '不为空',
    singleValue: true,
  },
  'array:contains': {
    type: 'array',
    operation: 'contains',
    name: '包含',
    rightType: 'any',
  },
  'array:notContains': {
    type: 'array',
    operation: 'notContains',
    name: '不包含',
    rightType: 'any',
  },
  'array:lengthEquals': {
    type: 'array',
    operation: 'lengthEquals',
    name: '长度等于',
    rightType: 'number',
  },
  'array:lengthNotEquals': {
    type: 'array',
    operation: 'lengthNotEquals',
    name: '长度不等于',
    rightType: 'number',
  },
  'array:lengthGt': {
    type: 'array',
    operation: 'lengthGt',
    name: '长度大于',
    rightType: 'number',
  },
  'array:lengthLt': {
    type: 'array',
    operation: 'lengthLt',
    name: '长度小于',
    rightType: 'number',
  },
  'array:lengthGte': {
    type: 'array',
    operation: 'lengthGte',
    name: '长度大于或等于',
    rightType: 'number',
  },
  'array:lengthLte': {
    type: 'array',
    operation: 'lengthLte',
    name: '长度小于或等于',
    rightType: 'number',
  },
  'object:exists': {
    type: 'object',
    operation: 'exists',
    name: '存在',
    singleValue: true,
  },
  'object:notExists': {
    type: 'object',
    operation: 'notExists',
    name: '不存在',
    singleValue: true,
  },
  'object:empty': {
    type: 'object',
    operation: 'empty',
    name: '为空',
    singleValue: true,
  },
  'object:notEmpty': {
    type: 'object',
    operation: 'notEmpty',
    name: '不为空',
    singleValue: true,
  },
} as const satisfies Record<string, FilterOperator>

export const OPERATORS = Object.values(OPERATORS_BY_ID)

export type FilterOperatorId = keyof typeof OPERATORS_BY_ID

export const DEFAULT_OPERATOR_VALUE: FilterConditionValue['operator'] = OPERATORS_BY_ID['string:equals']

export const OPERATOR_GROUPS: FilterOperatorGroup[] = [
  {
    id: 'string',
    name: '字符串',
    icon: LetterTextIcon,
    children: OPERATORS.filter((operator) => operator.type === 'string'),
  },
  {
    id: 'number',
    name: '数字',
    icon: BinaryIcon,
    children: OPERATORS.filter((operator) => operator.type === 'number'),
  },
  {
    id: 'dateTime',
    name: '日期和时间',
    icon: Calendar1Icon,
    children: OPERATORS.filter((operator) => operator.type === 'dateTime'),
  },
  {
    id: 'boolean',
    name: '布尔值',
    icon: SquareCheckIcon,
    children: OPERATORS.filter((operator) => operator.type === 'boolean'),
  },
  {
    id: 'array',
    name: '数组',
    icon: BracketsIcon,
    children: OPERATORS.filter((operator) => operator.type === 'array'),
  },
  {
    id: 'object',
    name: '对象',
    icon: BoxIcon,
    children: OPERATORS.filter((operator) => operator.type === 'object'),
  },
]
