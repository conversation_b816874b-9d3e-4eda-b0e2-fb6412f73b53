<script setup lang="ts">
import type { TieredMenu } from 'primevue'
import type { MenuItem } from 'primevue/menuitem'

import { OPERATOR_GROUPS } from './constants'
import type { FilterOperator, FilterOperatorGroup } from './types'
import { getFilterOperator } from './utils'

interface Props {
  selected: string
  readOnly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readOnly: false,
})

const emit = defineEmits<{
  operatorChange: [value: string]
}>()

const onOperatorChange = (operator: string): void => {
  emit('operatorChange', operator)
}

const getOperatorId = (operator: FilterOperator): string =>
  `${operator.type}:${operator.operation}`

// 将操作符转换为菜单项
function convertOperatorToMenuItem(operator: FilterOperator): MenuItem {
  return {
    label: operator.name as string,
    value: `${operator.type}:${operator.operation}`,
    command: () => {
      onOperatorChange(getOperatorId(operator))
    },
  }
}

// 将操作符组转换为菜单项
function convertGroupToMenuItem(group: FilterOperatorGroup): MenuItem {
  return {
    key: group.id,
    label: group.name as string,
    data: { icon: group.icon },
    items: group.children.map(convertOperatorToMenuItem),
  }
}

// 将操作符组数组转换为菜单项数组
const groups: MenuItem[] = OPERATOR_GROUPS.map(convertGroupToMenuItem)

const selectedOperator = computed(() => getFilterOperator(props.selected))

const selectedGroupIcon = computed(
  () => {
    const targetGroup = OPERATOR_GROUPS.find((group) => group.id === selectedOperator.value.type)

    if (targetGroup) {
      return targetGroup.icon
    }

    return null
  },
)

const { refKey, ref: menuRef } = useRef<InstanceType<typeof TieredMenu>>()

const toggle = (event: Event) => {
  if (!props.readOnly) {
    menuRef.value?.toggle(event)
  }
}
</script>

<template>
  <div>
    <Button
      :disabled="readOnly"
      size="small"
      type="button"
      unstyled
      @click="toggle"
    >
      <span class="gap-2 text-sm flex-center">
        <Component
          :is="selectedGroupIcon"
          class="shrink-0"
          :size="15"
        />

        {{ selectedOperator.name }}
      </span>
    </Button>

    <TieredMenu
      :ref="refKey"
      :model="groups"
      popup
    >
      <template #itemicon="{ item }: { item: MenuItem }">
        <Component
          :is="item?.data?.icon"
          class="shrink-0"
          :size="15"
        />
      </template>
    </TieredMenu>
  </div>
</template>
