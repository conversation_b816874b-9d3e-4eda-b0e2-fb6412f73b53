<script setup lang="ts">
import type { FilterTypeCombinator } from 'n8n-workflow'

interface Props {
  options: FilterTypeCombinator[]
  selected: FilterTypeCombinator
  readOnly: boolean
}

defineProps<Props>()

const emit = defineEmits<{
  combinatorChange: [value: FilterTypeCombinator]
}>()

const handleCombinatorChange = (combinator?: FilterTypeCombinator) => {
  if (combinator) {
    emit('combinatorChange', combinator)
  }
}

const combinatorText = {
  and: '且',
  or: '或',
} satisfies Record<FilterTypeCombinator, string>
</script>

<template>
  <div>
    <div v-if="readOnly || options.length === 1">
      {{ combinatorText[selected] }}
    </div>

    <ProSelect
      v-else
      :modelValue="selected"
      :options="options.map(option => ({ label: combinatorText[option], value: option }))"
      size="small"
      @update:modelValue="handleCombinatorChange"
    />
  </div>
</template>
