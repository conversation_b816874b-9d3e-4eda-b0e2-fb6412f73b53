<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus'

import { isEmpty, isEqual } from 'lodash-es'
import { PlusIcon } from 'lucide-vue-next'
import type {
  FilterConditionValue,
  FilterOptionsValue,
  FilterTypeCombinator,
  FilterValue,
  INode,
  INodeProperties,
  NodeParameterValue,
} from 'n8n-workflow'
import { nanoid } from 'nanoid'

import { useDebounce } from '#wf/composables/useDebounce'
import { useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { useNDVStore } from '#wf/stores/ndv.store'

import CombinatorSelect from './CombinatorSelect.vue'
import ConditionGroup from './ConditionGroup.vue'
import {
  DEFAULT_FILTER_OPTIONS,
  DEFAULT_MAX_CONDITIONS,
  DEFAULT_OPERATOR_VALUE,
} from './constants'

interface Props {
  param: INodeProperties
  value: FilterValue
  path: string
  node: INode | null
  readOnly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readOnly: false,
})

const emit = defineEmits<{
  valueChange: [value: { name: string, node: string, value: FilterValue }]
}>()

const ndvStore = useNDVStore()
const { debounce } = useDebounce()

const workflowHelpers = useWorkflowHelpers()

function createCondition(): FilterConditionValue {
  return {
    id: nanoid(),
    leftValue: '',
    rightValue: '',
    operator: DEFAULT_OPERATOR_VALUE,
  }
}

const allowedCombinators = computed<FilterTypeCombinator[]>(
  () => props.param.typeOptions?.filter?.allowedCombinators ?? ['and', 'or'],
)

const state = reactive<{ paramValue: FilterValue }>({
  paramValue: {
    options: props.value?.options ?? DEFAULT_FILTER_OPTIONS,
    conditions: props.value?.conditions?.map((condition) => {
      if (!condition.id) {
        condition.id = nanoid()
      }

      return condition
    }) ?? [createCondition()],
    combinator: props.value?.combinator ?? allowedCombinators.value[0],
  },
})

function emitChange() {
  emit('valueChange', {
    name: props.path,
    value: state.paramValue,
    node: props.node?.name as string,
  })
}

const debouncedEmitChange = debounce(emitChange, { debounceTime: 1000 })

const maxConditions = computed(
  () => props.param.typeOptions?.filter?.maxConditions ?? DEFAULT_MAX_CONDITIONS,
)

const singleCondition = computed(() => props.param.typeOptions?.multipleValues === false)

const maxConditionsReached = computed(
  () => maxConditions.value <= state.paramValue.conditions.length,
)

const issues = computed(() => {
  if (!ndvStore.activeNode) {
    return {}
  }

  return ndvStore.activeNode?.issues?.parameters ?? {}
})

watch(
  () => props.node?.parameters,
  () => {
    const typeOptions = props.param.typeOptions?.filter

    if (!typeOptions) {
      return
    }

    let newOptions: FilterOptionsValue = DEFAULT_FILTER_OPTIONS

    try {
      newOptions = {
        ...DEFAULT_FILTER_OPTIONS,
        ...workflowHelpers.resolveParameter(typeOptions as unknown as NodeParameterValue),
      }
    }
    catch {
      //
    }

    if (!isEqual(state.paramValue.options, newOptions)) {
      state.paramValue.options = newOptions
      debouncedEmitChange()
    }
  },
  { immediate: true },
)

watch(
  () => props.value,
  (value) => {
    if (isEmpty(value) || isEqual(state.paramValue, value)) {
      return
    }

    state.paramValue.conditions = value.conditions
    state.paramValue.combinator = value.combinator
    state.paramValue.options = value.options
  },
)

function addCondition(): void {
  state.paramValue.conditions.push(createCondition())
  debouncedEmitChange()
}

function onConditionUpdate(index: number, value: FilterConditionValue): void {
  state.paramValue.conditions[index] = value
  debouncedEmitChange()
}

function onCombinatorChange(combinator: FilterTypeCombinator): void {
  state.paramValue.combinator = combinator
  debouncedEmitChange()
}

function onConditionRemove(index: number): void {
  state.paramValue.conditions.splice(index, 1)
  debouncedEmitChange()
}

function getIssues(index: number): string[] {
  return issues.value[`${props.param.name}.${index}`] ?? []
}

const isDragging = ref(false)
const handlerClass = nanoid(4)

const handleSort = () => {
  debouncedEmitChange()
}
</script>

<template>
  <FormItem
    :label="param.displayName"
    labelClass="!font-medium !text-sm"
    :name="param.name"
  >
    <div>
      <div>
        <div>
          <VueDraggable
            v-model="state.paramValue.conditions"
            :animation="150"
            class="flex flex-col gap-2"
            :handle="`.${handlerClass}`"
            itemid="id"
            @end="isDragging = false"
            @sort="handleSort()"
            @start="isDragging = true"
          >
            <div
              v-for="(condition, index) of state.paramValue.conditions"
              :key="condition.id"
            >
              <div class="flex flex-col gap-1">
                <CombinatorSelect
                  v-if="index !== 0"
                  :options="allowedCombinators"
                  :readOnly="index !== 1 || readOnly"
                  :selected="state.paramValue.combinator"
                  @combinatorChange="onCombinatorChange"
                />

                <ConditionGroup
                  :canDrag="index !== 0 || state.paramValue.conditions.length > 1"
                  :canRemove="index !== 0 || state.paramValue.conditions.length > 1"
                  :condition="condition"
                  :fixedLeftValue="!!param.typeOptions?.filter?.leftValue"
                  :handlerClass="handlerClass"
                  :index="index"
                  :issues="getIssues(index)"
                  :options="state.paramValue.options"
                  :path="`${path}.${index}`"
                  :readOnly="readOnly"
                  @remove="() => onConditionRemove(index)"
                  @update="(value: FilterConditionValue) => onConditionUpdate(index, value)"
                />
              </div>
            </div>
          </VueDraggable>
        </div>

        <div
          v-if="!singleCondition && !readOnly"
          class="pt-2"
        >
          <ProBtn
            v-tooltip="maxConditionsReached ? '已达到最大条件限制' : undefined"
            :disabled="maxConditionsReached"
            label="添加条件"
            mini
            size="small"
            variant="outlined"
            @click="addCondition"
          >
            <template #icon="{ size }">
              <PlusIcon :size="size" />
            </template>
          </ProBtn>
        </div>
      </div>
    </div>
  </FormItem>
</template>
