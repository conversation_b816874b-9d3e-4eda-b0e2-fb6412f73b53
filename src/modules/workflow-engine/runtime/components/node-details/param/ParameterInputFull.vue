<script setup lang="ts">
import type { INodeProperties, NodeParameterValueType } from 'n8n-workflow'

import type { IUpdateInformation } from '#wf/types/interface'

defineProps<{
  value: NodeParameterValueType
  parameter: INodeProperties
}>()

const emit = defineEmits<{
  update: [value: IUpdateInformation]
}>()
</script>

<template>
  <InputText
    fluid
    :modelValue="String(value)"
    :placeholder="parameter.placeholder"
    type="text"
    @update:modelValue="emit('update', { name: parameter.name, value: $event })"
  />
</template>
