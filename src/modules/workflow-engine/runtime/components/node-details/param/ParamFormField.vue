<script setup lang="ts">
import { get } from 'lodash-es'
import type { CodeExecutionMode, CodeNodeEditorLanguage, EditorType, ILoadOptions, INodeParameterResourceLocator, INodeParameters, INodeProperties, INodePropertyOptions, NodeParameterValueType } from 'n8n-workflow'
import { CREDENTIAL_EMPTY_VALUE } from 'n8n-workflow/dist/Constants.js'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'
import { nanoid } from 'nanoid'

import JsonEditor from '#wf/components/editor/JsonEditor.vue'
import CodeEditorFullscreenButton from '#wf/components/node-details/CodeEditorFullscreenButton.vue'
import ParameterIssues from '#wf/components/ParameterIssues.vue'
import { useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { CORE_NODES_CATEGORY, CUSTOM_API_CALL_KEY, NODES_USING_CODE_NODE_EDITOR } from '#wf/constants/common'
import { workflowService } from '#wf/services/workflow'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useSettingsStore } from '#wf/stores/setting.store'
import { isValueExpression } from '#wf/utils/nodeTypesUtils'
import { isResourceLocatorValue } from '#wf/utils/typeGuards'

import type { EmptyObject } from '~/types/common'

import CodeNodeEditor from '../../CodeNodeEditor/CodeNodeEditor.vue'

import ResourceLocator from './ResourceLocator/ResourceLocator.vue'

interface Props {
  param: INodeProperties
  path: string
  fieldId?: string
  value?: NodeParameterValueType
  hideIssues?: boolean
  readOnly?: boolean
  expressionEvaluated: unknown
  forceShowExpression?: boolean
}

const props = defineProps<Props>()
const xParam = toRef(props, 'param')

const emit = defineEmits<{
  valueChange: [newValue: Props['value']]
}>()

const ndvStore = useNDVStore()
const { activeNode } = storeToRefs(ndvStore)

const nodeTypesStore = useNodeTypesStore()
const credentialsStore = useCredentialsStore()
const settingsStore = useSettingsStore()

const workflowHelpers = useWorkflowHelpers()

const remoteParameterOptions = ref<INodePropertyOptions[]>([])
const remoteParameterOptionsLoadingIssues = ref<string | null>(null)
const remoteParameterOptionsLoading = ref(false)
const activeCredentialType = ref('')
const nodeName = ref('')
const tempValue = ref('')

const isModelValueExpression = computed(() => isValueExpression(props.param, props.value))

const rgbaToHex = (value: string): string | null => {
  // Convert rgba to hex from: https://stackoverflow.com/questions/5623838/rgb-to-hex-and-hex-to-rgb
  const valueMatch = value.match(/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d+(\.\d+)?)\)$/)

  if (valueMatch === null) {
    // TODO: Display something if value is not valid
    return null
  }

  const [r, g, b, a] = valueMatch.splice(1, 4).map((v) => Number(v))

  return (
    '#'
    + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
    + ((1 << 8) + Math.floor((1 - a) * 255)).toString(16).slice(1)
  )
}

/**
 * 获取参数类型选项中的特定参数值
 *
 * 用于从节点参数的 typeOptions 配置中检索特定选项值，支持类型泛型
 * 在工作流引擎中，该函数用于获取UI渲染、数据验证和行为控制所需的参数配置
 * 例如: 获取 editor 类型、行数设置、加载选项方法等
 */
const getArgument = <T = string | number | boolean | undefined>(argumentName: string): T => {
  return xParam.value.typeOptions?.[argumentName]
}

function handleParamValueChange(value: NodeParameterValueType | EmptyObject | Date) {
  if (remoteParameterOptionsLoading.value) {
    return
  }

  // 只有当值发生变化时才更新
  const oldValue = get(activeNode.value, props.path)

  if (oldValue !== undefined && oldValue === value) {
    return
  }

  if (xParam.value.name === 'nodeCredentialType') {
    activeCredentialType.value = value as string
  }

  if (value instanceof Date) {
    value = value.toISOString()
  }

  if (
    xParam.value.type === 'color'
    && getArgument('showAlpha') === true
    && value !== null
    && value !== undefined
    && (value as string).toString().charAt(0) !== '#'
  ) {
    const newValue = rgbaToHex(value as string)

    if (newValue !== null) {
      tempValue.value = newValue
      value = newValue
    }
  }

  const parameterData = {
    node: activeNode.value ? activeNode.value.name : nodeName.value,
    name: props.path,
    value,
  }

  emit('valueChange', parameterData)
}

const hasRemoteMethod = computed<boolean>(() => {
  return !!getArgument('loadOptionsMethod') || !!getArgument('loadOptions')
})

const loadRemoteParameterOptions = async () => {
  if (
    !activeNode.value
    || !hasRemoteMethod.value
    || remoteParameterOptionsLoading.value
    || !props.param
  ) {
    return
  }

  remoteParameterOptionsLoadingIssues.value = null
  remoteParameterOptionsLoading.value = true
  remoteParameterOptions.value.length = 0

  // Get the resolved parameter values of the current node

  try {
    const currentNodeParameters = activeNode.value.parameters
    const resolvedNodeParameters = workflowHelpers.resolveRequiredParameters(
      props.param,
      currentNodeParameters,
    ) as INodeParameters
    const loadOptionsMethod = getArgument<string | undefined>('loadOptionsMethod')
    const loadOptions = getArgument<ILoadOptions | undefined>('loadOptions')

    const options = await workflowService.getNodeParameterOptions({
      nodeTypeAndVersion: {
        name: activeNode.value.type,
        version: activeNode.value.typeVersion,
      },
      path: props.path,
      methodName: loadOptionsMethod,
      loadOptions,
      currentNodeParameters: resolvedNodeParameters,
      credentials: activeNode.value.credentials,
    })

    remoteParameterOptions.value = remoteParameterOptions.value.concat(options)
  }
  catch (err) {
    if (err instanceof Error) {
      remoteParameterOptionsLoadingIssues.value = err.message
    }
  }
  finally {
    remoteParameterOptionsLoading.value = false
  }
}

// 当节点的凭据发生变化时，重新获取参数选项
watch(
  () => activeNode.value?.credentials,
  () => {
    if (hasRemoteMethod.value && activeNode.value) {
      void loadRemoteParameterOptions()
    }
  },
  { immediate: true },
)

const parameterOptions = computed<INodePropertyOptions[] | undefined>(() => {
  if (!hasRemoteMethod.value) {
    // Options are already given
    return xParam.value.options as INodePropertyOptions[]
  }

  // Options get loaded from server
  return remoteParameterOptions.value
})

const modelValueResourceLocator = computed<INodeParameterResourceLocator>(() => {
  return props.value as INodeParameterResourceLocator
})

const shortPath = computed<string>(() => {
  const short = props.path.split('.')
  short.shift()

  return short.join('.')
})

const nodeType = computed(
  () => activeNode.value && nodeTypesStore.getNodeType(activeNode.value.type, activeNode.value.typeVersion),
)

const codeEditDialogVisible = ref(false)

const editorRows = computed(() => getArgument<number>('rows'))

const isResourceLocatorParameter = computed<boolean>(() => {
  return props.param.type === 'resourceLocator' || props.param.type === 'workflowSelector'
})

const displayValue = computed(() => {
  if (remoteParameterOptionsLoadingIssues.value) {
    if (!nodeType.value || nodeType.value?.codex?.categories?.includes(CORE_NODES_CATEGORY)) {
      return '获取选项时出错'
    }

    if (nodeType.value?.credentials && nodeType.value?.credentials?.length > 0) {
      const credentialsType = nodeType.value?.credentials[0]

      if (credentialsType.required && !activeNode.value?.credentials) {
        return '设置凭据以查看选项'
      }
    }

    return `从 ${nodeType.value.displayName} 获取选项时出错`
  }

  if (remoteParameterOptionsLoading.value) {
    // If it is loading options from server display
    // to user that the data is loading. If not it would
    // display the user the key instead of the value it
    // represents
    return '加载选项中...'
  }

  // if the value is marked as empty return empty string, to prevent displaying the asterisks
  if (props.value === CREDENTIAL_EMPTY_VALUE) {
    return ''
  }

  let returnValue

  if (!isModelValueExpression.value) {
    returnValue = isResourceLocatorParameter.value
      ? isResourceLocatorValue(props.value)
        ? props.value.value
        : ''
      : props.value
  }
  else {
    returnValue = props.expressionEvaluated
  }

  if (props.param.type === 'credentialsSelect' && typeof props.value === 'string') {
    const credType = credentialsStore.getCredentialTypeByName(props.value)

    if (credType) {
      returnValue = credType.displayName
    }
  }

  if (
    Array.isArray(returnValue)
    && props.param.type === 'color'
    && getArgument('showAlpha') === true
    && (returnValue as unknown as string).charAt(0) === '#'
  ) {
    // Convert the value to rgba that el-color-picker can display it correctly
    const bigint = parseInt((returnValue as unknown as string).slice(1), 16)
    const h = []
    h.push((bigint >> 24) & 255)
    h.push((bigint >> 16) & 255)
    h.push((bigint >> 8) & 255)
    h.push(((255 - bigint) & 255) / 255)

    returnValue = 'rgba(' + h.join() + ')'
  }

  if (returnValue !== undefined && returnValue !== null && props.param.type === 'string') {
    const rows = editorRows.value

    if (rows === undefined || rows === 1) {
      returnValue = (returnValue as string).toString().replace(/\n/, '|')
    }
  }

  return returnValue as string
})

/**
 * Check whether a param value must be skipped when collecting node param issues for validation.
 */
function skipCheck(value: string | number | boolean | null) {
  return typeof value === 'string' && value.includes(CUSTOM_API_CALL_KEY)
}

const getIssues = computed<string[]>(() => {
  if (props.hideIssues || !activeNode.value) {
    return []
  }

  const newPath = shortPath.value.split('.')
  newPath.pop()

  const issues = NodeHelpers.getParameterIssues(
    props.param,
    activeNode.value.parameters,
    newPath.join('.'),
    activeNode.value,
  )

  if (props.param.type === 'credentialsSelect' && displayValue.value === '') {
    issues.parameters = issues.parameters ?? {}

    const issue = '从下拉列表中选择凭据类型'

    issues.parameters[props.param.name] = [issue]
  }
  else if (
    ['options', 'multiOptions'].includes(props.param.type)
    && !remoteParameterOptionsLoading.value
    && remoteParameterOptionsLoadingIssues.value === null
    && parameterOptions.value
  ) {
    // Check if the value resolves to a valid option
    // Currently it only displays an error in the node itself in
    // case the value is not valid. The workflow can still be executed
    // and the error is not displayed on the node in the workflow

    const validOptions = parameterOptions.value.map((options) => options.value)

    let checkValues: string[] = []

    if (!skipCheck(displayValue.value)) {
      if (Array.isArray(displayValue.value)) {
        checkValues = checkValues.concat(displayValue.value)
      }
      else {
        checkValues.push(displayValue.value)
      }
    }

    for (const checkValue of checkValues) {
      if (checkValue === null || !validOptions.includes(checkValue)) {
        if (issues.parameters === undefined) {
          issues.parameters = {}
        }

        const issue = `值 "${checkValue}" 不受支持！`

        issues.parameters[props.param.name] = [issue]
      }
    }
  }
  else if (remoteParameterOptionsLoadingIssues.value !== null && !isModelValueExpression.value) {
    if (issues.parameters === undefined) {
      issues.parameters = {}
    }

    issues.parameters[props.param.name] = [
      `从服务器加载参数选项时出现问题："${remoteParameterOptionsLoadingIssues.value}"`,
    ]
  }

  if (issues?.parameters?.[props.param.name] !== undefined) {
    return issues.parameters[props.param.name]
  }

  return []
})

const displayTitle = computed<string>(() => {
  if (getIssues.value.length && isModelValueExpression.value) {
    return `参数："${shortPath.value}" 存在问题和一个表达式`
  }
  else if (getIssues.value.length && !isModelValueExpression.value) {
    return `参数："${shortPath.value}" 存在问题`
  }
  else if (!getIssues.value.length && isModelValueExpression.value) {
    return `参数："${shortPath.value}" 有一个表达式`
  }

  return `参数："${shortPath.value}"`
})

const displayIssues = computed<boolean>(() => {
  return props.param.type !== 'credentialsSelect'
    && !isResourceLocatorParameter.value
    && getIssues.value.length > 0
})

const expressionDisplayValue = computed(() => {
  if (props.forceShowExpression) {
    return ''
  }

  const value = isResourceLocatorValue(props.value)
    ? props.value.value
    : props.value

  if (typeof value === 'string' && value.startsWith('=')) {
    return value.slice(1)
  }

  return `${displayValue.value ?? ''}`
})

const dependentParametersValues = computed<string | null>(() => {
  const loadOptionsDependsOn = getArgument<string[] | undefined>('loadOptionsDependsOn')

  if (loadOptionsDependsOn === undefined) {
    return null
  }

  // Get the resolved parameter values of the current node
  const currentNodeParameters = ndvStore.activeNode?.parameters

  try {
    const resolvedNodeParameters = workflowHelpers.resolveParameter(currentNodeParameters)

    const returnValues: string[] = []

    for (const parameterPath of loadOptionsDependsOn) {
      returnValues.push(get(resolvedNodeParameters, parameterPath) as string)
    }

    return returnValues.join('|')
  }
  catch {
    return null
  }
})

watch(dependentParametersValues, async () => {
  // 当依赖的参数发生变化时，重新获取参数选项
  await loadRemoteParameterOptions()
})

const editorType = computed<EditorType | 'json' | 'code'>(() => {
  return getArgument<EditorType>('editor')
})

const editorIsReadOnly = computed<boolean>(() => {
  return getArgument<boolean>('editorIsReadOnly') ?? false
})

const editorLanguage = computed<CodeNodeEditorLanguage>(() => {
  if (editorType.value === 'json' || props.param.type === 'json') {
    return 'json' as CodeNodeEditorLanguage
  }

  return getArgument<CodeNodeEditorLanguage>('editorLanguage') ?? 'javaScript'
})

const codeEditorMode = computed<CodeExecutionMode>(() => {
  return activeNode.value?.parameters.mode as CodeExecutionMode
})

const parameterId = computed(() => {
  return `${activeNode.value?.id ?? nanoid(4)}${props.path}`
})

const isCodeNode = computed(
  () => !!activeNode.value && NODES_USING_CODE_NODE_EDITOR.includes(activeNode.value.type),
)

const getStringInputType = computed(() => {
  if (getArgument('password') === true) {
    return 'password'
  }

  const rows = editorRows.value

  if (rows !== undefined && rows > 1) {
    return 'textarea'
  }

  if (editorType.value === 'code') {
    return 'textarea'
  }

  return 'text'
})

const commonProps = {
  disabled: props.readOnly,
  readonly: props.readOnly,
}
</script>

<template>
  <div>
    <template v-if="param.type === 'string' || param.type === 'json'">
      <CodeNodeEditor
        v-if="editorType === 'codeNodeEditor' && isCodeNode && !codeEditDialogVisible"
        :id="parameterId"
        :aiButtonEnabled="settingsStore.isCloudDeployment"
        :defaultValue="param.default"
        :isReadOnly="readOnly || editorIsReadOnly"
        :language="editorLanguage"
        :mode="codeEditorMode"
        :modelValue="value as string"
        :rows="editorRows"
        @update:modelValue="handleParamValueChange"
      >
        <template #suffix>
          <CodeEditorFullscreenButton />
        </template>
      </CodeNodeEditor>

      <JsonEditor
        v-else-if="param.type === 'json' && !codeEditDialogVisible"
        fillParent
        :isReadOnly="readOnly"
        :modelValue="value as string"
        :rows="editorRows"
        @update:modelValue="handleParamValueChange"
      >
        <template #suffix>
          <CodeEditorFullscreenButton />
        </template>
      </JsonEditor>

      <InputText
        v-else-if="getStringInputType === 'text'"
        v-bind="commonProps"
        :id="fieldId"
        fluid
        :modelValue="value as string"
        @update:modelValue="handleParamValueChange"
      />

      <Textarea
        v-else-if="getStringInputType === 'textarea'"
        v-bind="commonProps"
        :id="fieldId"
        fluid
        :modelValue="value as string"
        @update:modelValue="handleParamValueChange"
      />

      <Password
        v-else-if="getStringInputType === 'password'"
        v-bind="commonProps"
        :id="fieldId"
        autocomplete="off"
        :feedback="false"
        fluid
        :modelValue="value as string"
        @update:modelValue="handleParamValueChange"
      />
    </template>

    <InputNumber
      v-else-if="param.type === 'number'"
      v-bind="commonProps"
      fluid
      :inputId="fieldId"
      :modelValue="value as number"
      @update:modelValue="handleParamValueChange"
    />

    <Select
      v-else-if="param.type === 'options'"
      v-bind="commonProps"
      :id="fieldId"
      fluid
      :labelId="fieldId"
      :loading="remoteParameterOptionsLoading"
      :modelValue="value as string"
      optionLabel="name"
      :options="parameterOptions"
      optionValue="value"
      :placeholder="remoteParameterOptionsLoading ? '加载选项中...' : undefined"
      :pt="{
        overlay: 'max-w-[330px]',
      }"
      @update:modelValue="handleParamValueChange"
    >
      <template #option="{ option }: { option: INodePropertyOptions }">
        <span class="flex w-full flex-col">
          <span class="text-sm font-medium">{{ option.name }}</span>
          <span
            v-if="option.description"
            class="text-wrap pt-0.5 text-sm opacity-75"
          >
            {{ option.description }}
          </span>
        </span>
      </template>
    </Select>

    <ProDatePicker
      v-else-if="param.type === 'dateTime'"
      v-bind="commonProps"
      fluid
      :modelValue="value as string"
      placeholder="选择日期和时间"
      showTime
      @update:modelValue="handleParamValueChange"
    />

    <ResourceLocator
      v-else-if="param.type === 'resourceLocator'"
      :dependentParametersValues="dependentParametersValues"
      :displayTitle="displayTitle"
      :expressionComputedValue="expressionEvaluated"
      :expressionDisplayValue="expressionDisplayValue"
      :isValueExpression="isModelValueExpression"
      :modelValue="modelValueResourceLocator"
      :node="activeNode ?? undefined"
      :parameter="param"
      :parameterIssues="getIssues"
      :path="path"
      :readOnly="readOnly"
      @update:modelValue="handleParamValueChange"
    />

    <MultiSelect
      v-else-if="param.type === 'multiOptions'"
      v-bind="commonProps"
      :id="fieldId"
      fluid
      :labelId="fieldId"
      :modelValue="value as string"
      optionLabel="name"
      :options="parameterOptions"
      optionValue="value"
      @update:modelValue="handleParamValueChange"
    >
      <template #option="{ option }: { option: INodePropertyOptions }">
        <span class="flex flex-col">
          <span class="text-sm font-medium">{{ option.name }}</span>
          <span
            v-if="option.description"
            class="pt-0.5 text-sm opacity-75"
          >
            {{ option.description }}
          </span>
        </span>
      </template>
    </MultiSelect>

    <ToggleSwitch
      v-else-if="param.type === 'boolean'"
      v-bind="commonProps"
      :inputId="fieldId"
      :modelValue="value as boolean"
      @update:modelValue="handleParamValueChange"
    />

    <Message
      v-else
      severity="warn"
      size="small"
    >
      未找到对应参数类型的组件：{{ param.type }}
    </Message>

    <ParameterIssues
      v-if="displayIssues"
      :issues="getIssues"
    />
  </div>
</template>
