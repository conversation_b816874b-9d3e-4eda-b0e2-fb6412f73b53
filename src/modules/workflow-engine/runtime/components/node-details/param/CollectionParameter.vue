<script setup lang="ts">
import { get } from 'lodash-es'
import type { INodeParameters, INodeProperties, INodePropertyOptions } from 'n8n-workflow'
import { deepCopy } from 'n8n-workflow/dist/utils.js'

import ParameterGroup from '#wf/components/node-details/param/ParameterGroup.vue'
import ParameterInputList from '#wf/components/node-details/param/ParameterInputList.vue'
import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { useNDVStore } from '#wf/stores/ndv.store'
import type { IUpdateInformation } from '#wf/types/interface'

export interface Props {
  nodeValues: INodeParameters
  param: INodeProperties
  path: string
  values: INodeParameters
  readOnly?: boolean
}

const props = defineProps<Props>()
const xParam = toRef(props, 'param')

const ndvStore = useNDVStore()
const { activeNode } = storeToRefs(ndvStore)

const emit = defineEmits<{
  valueChange: [parameterData: IUpdateInformation]
}>()

const nodeHelpers = useNodeHelpers()

const propertyNames = computed<string[]>(() => {
  if (props.values) {
    return Object.keys(props.values)
  }

  return []
})

const getOptionProperties = (optionName: string) => {
  const properties = []

  for (const option of xParam.value.options ?? []) {
    if (option.name === optionName) {
      properties.push(option)
    }
  }

  return properties
}

const getProperties = computed(() => {
  const returnProperties = []
  let tempProperties

  for (const name of propertyNames.value) {
    tempProperties = getOptionProperties(name) as INodeProperties[]

    if (tempProperties !== undefined) {
      returnProperties.push(...tempProperties)
    }
  }

  return returnProperties
})

const displayNodeParameter = (parameter: INodeProperties) => {
  if (parameter.displayOptions === undefined) {
    // If it is not defined no need to do a proper check
    return true
  }

  return nodeHelpers.displayParameter(props.nodeValues, parameter, props.path, activeNode.value)
}

const filteredOptions = computed<Array<INodePropertyOptions | INodeProperties>>(() => {
  return (xParam.value.options as Array<INodePropertyOptions | INodeProperties>).filter(
    (option) => {
      return displayNodeParameter(option as INodeProperties)
    },
  )
})

const parameterOptions = computed(() => {
  return filteredOptions.value.filter((option) => {
    return !propertyNames.value.includes(option.name)
  })
})

const selectedOption = ref<string>()

const handleOptionChange = (optionName: string) => {
  const options = getOptionProperties(optionName)

  if (options.length > 0) {
    const option = options[0]
    const name = `${props.path}.${option.name}`
    let parameterData

    const isMultipleValuesAllowed = 'typeOptions' in option
      && option.typeOptions !== undefined
      && option.typeOptions.multipleValues === true

    if (isMultipleValuesAllowed) {
      // 处理允许多值的情况
      let newValue

      if (option.type === 'fixedCollection') {
        // fixedCollection 类型特殊处理：以对象形式存储值，对象内部再包含数组
        const retrievedObjectValue = get(props.nodeValues, [props.path, optionName], {})
        newValue = retrievedObjectValue
      }
      else {
        // 其他类型直接以数组形式存储多值
        const retrievedArrayValue = get(
          props.nodeValues,
          [props.path, optionName],
          [],
        ) as Array<typeof option.default>

        if (Array.isArray(retrievedArrayValue)) {
          newValue = retrievedArrayValue
          // 添加新选项时，复制默认值并添加到数组中
          newValue.push(deepCopy(option.default))
        }
      }

      parameterData = {
        name,
        value: newValue,
      }
    }
    else {
      // 处理单值情况
      parameterData = {
        name,
        value: 'default' in option ? deepCopy(option.default) : null,
      }
    }

    // 触发值变更事件并重置选择器
    emit('valueChange', parameterData)

    // 选择完后重置选择值
    selectedOption.value = undefined
  }
}

const handleParamValueChange = (parameterData: IUpdateInformation) => {
  emit('valueChange', parameterData)
}
</script>

<template>
  <ParameterGroup :title="param.displayName">
    <ParameterInputList
      v-if="getProperties.length > 0"
      :hideDelete="false"
      :nodeValues="nodeValues"
      :parameters="getProperties"
      :path="path"
      :readOnly="readOnly"
      @paramValueChange="handleParamValueChange"
    />

    <div
      v-else
      class="text-sm text-secondary"
    >
      暂无选项，请在添加后进行配置
    </div>

    <template v-if="parameterOptions.length > 0 && !readOnly">
      <Select
        class="mt-form-field"
        fluid
        :modelValue="selectedOption"
        optionLabel="displayName"
        :options="parameterOptions"
        optionValue="name"
        placeholder="添加选项"
        @update:modelValue="handleOptionChange"
      />
    </template>
  </ParameterGroup>
</template>
