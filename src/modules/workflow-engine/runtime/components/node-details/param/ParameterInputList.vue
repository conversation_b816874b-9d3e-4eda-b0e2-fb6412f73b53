<script setup lang="ts">
import type { WatchSource } from 'vue'

import { computedWithControl } from '@vueuse/core'
import { get, set } from 'lodash-es'
import { TextSelectIcon, TrashIcon } from 'lucide-vue-next'
import type { INodeParameters, INodeProperties, NodeParameterValue, NodeParameterValueType, NodePropertyTypes } from 'n8n-workflow'
import { ADD_FORM_NOTICE } from 'n8n-workflow/dist/Constants.js'
import { deepCopy } from 'n8n-workflow/dist/utils.js'

import CalloutMessage from '#wf/components/ui/CalloutMessage.vue'
import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { getCurrentWorkflow, useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { FORM_NODE_TYPE, FORM_TRIGGER_NODE_TYPE, KEEP_AUTH_IN_NDV_FOR_NODES, WAIT_NODE_TYPE } from '#wf/constants/common'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import type { IUpdateInformation } from '#wf/types/interface'

import AssignmentCollection from './AssignmentCollection/AssignmentCollection.vue'
import FilterConditions from './FilterConditions/FilterConditions.vue'
import CollectionParameter from './CollectionParameter.vue'
import FixedCollectionParameter from './FixedCollectionParameter.vue'
import ImportCurlParameter from './ImportCurlParameter.vue'
import ParamFormItem from './ParamFormItem.vue'

interface Props {
  /** 节点参数值 */
  nodeValues: INodeParameters
  /** 节点参数配置 */
  parameters: INodeProperties[]
  /** 节点参数路径 */
  path?: string
  /** 是否只读 */
  readOnly?: boolean
  /** 是否隐藏删除按钮 */
  hideDelete?: boolean
}

const props = withDefaults(
  defineProps<Props>(),
  {
    path: '',
    readOnly: false,
    hideDelete: true,
  },
)

const emit = defineEmits<{
  paramValueChange: [value: IUpdateInformation]
}>()

const ndvStore = useNDVStore()
const { activeNode } = storeToRefs(ndvStore)

const nodeTypesStore = useNodeTypesStore()

const nodeHelpers = useNodeHelpers()
const workflowHelpers = useWorkflowHelpers()

const mustHideDuringCustomApiCall = (
  parameter: INodeProperties,
  nodeValues: INodeParameters,
): boolean => {
  if (parameter?.displayOptions?.hide) {
    return true
  }

  const MUST_REMAIN_VISIBLE = [
    'authentication',
    'resource',
    'operation',
    ...Object.keys(nodeValues),
  ]

  return !MUST_REMAIN_VISIBLE.includes(parameter.name)
}

const displayNodeParameter = (
  parameter: INodeProperties,
  displayKey: 'displayOptions' | 'disabledOptions' = 'displayOptions',
): boolean => {
  if (parameter.type === 'hidden') {
    return false
  }

  if (
    nodeHelpers.isCustomApiCallSelected(props.nodeValues)
    && mustHideDuringCustomApiCall(parameter, props.nodeValues)
  ) {
    return false
  }

  // Hide authentication related fields since it will now be part of credentials modal
  // if (
  //   !KEEP_AUTH_IN_NDV_FOR_NODES.includes(activeNode.value?.type || '')
  //   && mainNodeAuthField.value
  //   && (parameter.name === mainNodeAuthField.value?.name || shouldHideAuthRelatedParameter(parameter))
  // ) {
  //   return false
  // }

  if (parameter[displayKey] === undefined) {
    // If it is not defined no need to do a proper check
    return true
  }

  const nodeValues: INodeParameters = {}
  let rawValues = props.nodeValues

  if (props.path) {
    rawValues = get(props.nodeValues, props.path) as INodeParameters
  }

  if (!rawValues) {
    return false
  }

  // Resolve expressions
  const resolveKeys = Object.keys(rawValues)
  let key: string
  let i = 0
  let parameterGotResolved = false

  do {
    key = resolveKeys.shift() as string
    const value = rawValues[key]

    if (typeof value === 'string' && value?.charAt(0) === '=') {
      // Contains an expression that
      if (
        value.includes('$parameter')
        && resolveKeys.some((parameterName) => value.includes(parameterName))
      ) {
        // Contains probably an expression of a missing parameter so skip
        resolveKeys.push(key)
        continue
      }
      else {
        // Contains probably no expression with a missing parameter so resolve
        try {
          nodeValues[key] = workflowHelpers.resolveExpression(
            value,
            nodeValues,
          ) as NodeParameterValue
        }
        catch {
          // If expression is invalid ignore
          nodeValues[key] = ''
        }

        parameterGotResolved = true
      }
    }
    else {
      // Does not contain an expression, add directly
      nodeValues[key] = rawValues[key]
    }

    // TODO: Think about how to calculate this best
    if (i++ > 50) {
      // Make sure we do not get caught
      break
    }
  } while (resolveKeys.length !== 0)

  if (parameterGotResolved) {
    if (props.path) {
      rawValues = deepCopy(props.nodeValues)
      set(rawValues, props.path, nodeValues)

      return nodeHelpers.displayParameter(rawValues, parameter, props.path, activeNode.value, displayKey)
    }
    else {
      return nodeHelpers.displayParameter(nodeValues, parameter, '', activeNode.value, displayKey)
    }
  }

  return nodeHelpers.displayParameter(
    props.nodeValues,
    parameter,
    props.path,
    activeNode.value,
    displayKey,
  )
}

const updateFormTriggerParameters = (parameters: INodeProperties[], triggerName: string) => {
  const workflow = getCurrentWorkflow()
  const connectedNodes = workflow.getChildNodes(triggerName)

  const hasFormPage = connectedNodes.some((nodeName) => {
    const node = workflow.getNode(nodeName)

    return node && node.type === FORM_NODE_TYPE
  })

  if (hasFormPage) {
    const triggerParameters: INodeProperties[] = []

    for (const parameter of parameters) {
      if (parameter.name === 'responseMode') {
        triggerParameters.push({
          displayName: 'On submission, the user will be taken to the next form node',
          name: 'formResponseModeNotice',
          type: 'notice',
          default: '',
        })

        continue
      }

      if (parameter.name === ADD_FORM_NOTICE) {
        continue
      }

      if (parameter.name === 'options') {
        const options = (parameter.options as INodeProperties[]).filter(
          (option) => option.name !== 'respondWithOptions',
        )
        triggerParameters.push({
          ...parameter,
          options,
        })
        continue
      }

      triggerParameters.push(parameter)
    }

    return triggerParameters
  }

  return parameters
}

const updateWaitParameters = (parameters: INodeProperties[], nodeName: string) => {
  const workflow = getCurrentWorkflow()
  const parentNodes = workflow.getParentNodes(nodeName)

  const formTriggerName = parentNodes.find(
    (node) => workflow.nodes[node].type === FORM_TRIGGER_NODE_TYPE,
  )

  if (!formTriggerName) {
    return parameters
  }

  const connectedNodes = workflow.getChildNodes(formTriggerName)

  const hasFormPage = connectedNodes.some((nodeName) => {
    const node = workflow.getNode(nodeName)

    return node && node.type === FORM_NODE_TYPE
  })

  if (hasFormPage) {
    const waitNodeParameters: INodeProperties[] = []

    for (const parameter of parameters) {
      if (parameter.name === 'options') {
        const options = (parameter.options as INodeProperties[]).filter(
          (option) => option.name !== 'respondWithOptions' && option.name !== 'webhookSuffix',
        )
        waitNodeParameters.push({
          ...parameter,
          options,
        })
        continue
      }

      waitNodeParameters.push(parameter)
    }

    return waitNodeParameters
  }

  return parameters
}

const filteredParameters = computedWithControl(
  [() => props.parameters, () => props.nodeValues] as WatchSource[],
  () => {
    const parameters = props.parameters.filter((parameter: INodeProperties) =>
      displayNodeParameter(parameter),
    )

    if (activeNode.value && activeNode.value.type === FORM_TRIGGER_NODE_TYPE) {
      return updateFormTriggerParameters(parameters, activeNode.value.name)
    }

    if (
      activeNode.value
      && activeNode.value.type === WAIT_NODE_TYPE
      && activeNode.value.parameters.resume === 'form'
    ) {
      return updateWaitParameters(parameters, activeNode.value.name)
    }

    return parameters
  },
)

const credentialsParameterIndex = computed(() => {
  return filteredParameters.value.findIndex((parameter) => parameter.type === 'credentials')
})

const nodeType = computed(() => {
  if (activeNode.value) {
    return nodeTypesStore.getNodeType(activeNode.value.type, activeNode.value.typeVersion)
  }

  return null
})

/**
 * 获取凭证依赖的字段名集合
 *
 * 通过分析节点类型的凭证配置，找出所有凭证显示条件依赖的字段名
 * 这些字段通常通过 displayOptions.show 属性控制凭证的显示逻辑
 */
const getCredentialsDependencies = () => {
  const dependencies = new Set()

  // Get names of all fields that credentials rendering depends on (using displayOptions > show)
  if (nodeType.value?.credentials) {
    for (const cred of nodeType.value.credentials) {
      if (cred.displayOptions?.show) {
        Object.keys(cred.displayOptions.show).forEach((fieldName) => dependencies.add(fieldName))
      }
    }
  }

  return dependencies
}

/**
 * 计算凭证插槽应该显示的位置索引
 *
 * 根据节点类型和参数配置，确定凭证相关组件应该插入的位置：
 * - 如果存在专门的凭证参数，则使用该参数的位置
 * - 否则，根据凭证依赖的字段位置和节点类型决定插入位置
 * - 对于使用旧凭证 UI 的节点，保持凭证显示在认证字段下方
 */
const indexToShowSlotAt = computed(() => {
  const hasCredentialsParameter = credentialsParameterIndex.value !== -1

  if (hasCredentialsParameter) {
    return credentialsParameterIndex.value
  }

  let index = 0

  // 对于使用旧凭证 UI 的节点，保持凭证显示在认证字段下方
  // 否则凭证将使用认证字段的位置，因为认证字段已移到凭证模态框中
  const fieldOffset = KEEP_AUTH_IN_NDV_FOR_NODES.includes(nodeType.value?.name || '') ? 1 : 0

  const credentialsDependencies = getCredentialsDependencies()

  filteredParameters.value.forEach((prop, propIndex) => {
    if (credentialsDependencies.has(prop.name)) {
      index = propIndex + fieldOffset
    }
  })

  return Math.min(index, filteredParameters.value.length - 1)
})

const getParameterValue = <T extends NodeParameterValueType = NodeParameterValueType>(
  name: string,
): T => {
  return nodeHelpers.getParameterValue(props.nodeValues, name, props.path) as T
}

const getPath = (parameterName: string): string => {
  return (props.path ? `${props.path}.` : '') + parameterName
}

const handleDeleteOption = (optionName: string) => {
  const parameterData = {
    name: getPath(optionName),
    value: undefined,
  }

  emit('paramValueChange', parameterData)
}

const handleValueChange = (value: IUpdateInformation) => {
  console.log('handleValueChange', value)
  emit('paramValueChange', value)
}

const getArgument = (
  argumentName: string,
  parameter: INodeProperties,
): string | string[] | number | boolean | undefined => {
  if (parameter.typeOptions === undefined) {
    return undefined
  }

  if (parameter.typeOptions[argumentName] === undefined) {
    return undefined
  }

  return parameter.typeOptions[argumentName]
}

const isMultipleValues = (parameter: INodeProperties): boolean => {
  return getArgument('multipleValues', parameter) === true
}

const isCollectionParam = (param: INodeProperties): boolean => {
  return param.type.toLowerCase().includes('collection')
}

const isPreviousParamCollection = (currentIndex: number): boolean => {
  return !!filteredParameters.value.at(currentIndex - 1)?.type.toLowerCase().includes('collection')
}

const isBottomDividerNeeded = (param: INodeProperties, index: number): boolean => {
  if (index !== filteredParameters.value.length - 1) {
    // 如果不是最后一个参数，并且是指定的类型，则在后面显示分隔符

    if (isCollectionParam(param)) {
      return true
    }

    const specialTypes: NodePropertyTypes[] = ['filter', 'curlImport']

    return specialTypes.includes(param.type)
  }

  return false
}
</script>

<template>
  <div class="space-y-form-field">
    <template v-if="filteredParameters.length > 0">
      <div
        v-for="(param, idx) of filteredParameters"
        :key="param.name"
        :data-param-type="param.type"
        data-testid="parameter-item"
      >
        <div v-if="indexToShowSlotAt === idx && $slots.default">
          <slot />
        </div>

        <Divider v-if="isCollectionParam(param) && !isPreviousParamCollection(idx)" />

        <CalloutMessage
          v-if="param.type === 'notice'"
          :content="param.displayName"
          severity="secondary"
          size="small"
        />

        <template v-else-if="param.type === 'curlImport'">
          <ImportCurlParameter />
          <Divider>
            <span class="text-[0.85em]">或手动配置下方的 HTTP 请求参数</span>
          </Divider>
        </template>

        <div
          v-else-if="param.type !== 'fixedCollection' && isMultipleValues(param)"
        >
          1111
          <!-- <MultipleParameter
            :isReadOnly="isReadOnly"
            :nodeValues="nodeValues"
            :parameter="parameter"
            :path="getPath(parameter.name)"
            :values="getParameterValue(parameter.name)"
            @value-changed="valueChanged"
          /> -->
        </div>

        <CollectionParameter
          v-else-if="param.type === 'collection'"
          :nodeValues="nodeValues"
          :param="param"
          :path="getPath(param.name)"
          :readOnly="readOnly"
          :values="getParameterValue(param.name)"
          @valueChange="handleValueChange"
        />

        <FixedCollectionParameter
          v-else-if="param.type === 'fixedCollection'"
          :nodeValues="nodeValues"
          :param="param"
          :path="getPath(param.name)"
          :readOnly="readOnly"
          :values="getParameterValue(param.name)"
          @valueChange="handleValueChange"
        />

        <FilterConditions
          v-else-if="param.type === 'filter'"
          :node="activeNode"
          :param="param"
          :path="getPath(param.name)"
          :readOnly="readOnly"
          :value="getParameterValue(param.name)"
          @valueChange="handleValueChange"
        />

        <AssignmentCollection
          v-else-if="param.type === 'assignmentCollection'"
          :node="activeNode"
          :param="param"
          :path="getPath(param.name)"
          :readOnly="readOnly"
          :value="getParameterValue(param.name)"
          @valueChange="handleValueChange"
        />

        <template v-else>
          <div
            v-if="credentialsParameterIndex !== idx"
            class="group/param-item relative"
          >
            <ParamFormItem
              :param="param"
              :path="getPath(param.name)"
              :readOnly="readOnly"
              :value="getParameterValue(param.name)"
              @valueChange="handleValueChange"
            />

            <div
              v-if="!readOnly"
              class="absolute right-0 top-0 shrink-0 opacity-0 group-hover/param-item:opacity-100"
            >
              <ProBtn
                v-if="hideDelete !== true && !param.isNodeSetting"
                label="删除该项"
                mini
                onlyIcon
                size="small"
                @click="handleDeleteOption(param.name)"
              >
                <template #icon="{ size }">
                  <TrashIcon :size="size" />
                </template>
              </ProBtn>
            </div>
          </div>
        </template>

        <Divider
          v-if="isBottomDividerNeeded(param, idx) && credentialsParameterIndex === idx"
        />
      </div>
    </template>

    <div v-else>
      <div class="flex flex-col items-center justify-center gap-3 p-4 text-center text-secondary">
        <TextSelectIcon :size="44" />
        此节点没有任何参数
      </div>
    </div>
  </div>
</template>
