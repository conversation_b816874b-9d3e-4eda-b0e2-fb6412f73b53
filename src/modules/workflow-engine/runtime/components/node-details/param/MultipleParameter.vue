<script setup lang="ts">
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { get } from 'lodash-es'
import { N8nButton, N8nInputLabel, N8nText } from 'n8n-design-system'
import type { INodeParameters, INodeProperties } from 'n8n-workflow'
import { deepCopy } from 'n8n-workflow'

import CollectionParameter from '#wf/components/node-details/param/CollectionParameter.vue'
import ParameterInputFull from '#wf/components/node-details/param/ParameterInputFull.vue'
import type { IUpdateInformation } from '#wf/types/interface'

defineOptions({ name: 'MultipleParameter' })

const props = withDefaults(
  defineProps<{
    nodeValues: INodeParameters
    parameter: INodeProperties
    path: string
    values?: INodeParameters[]
    isReadOnly?: boolean
  }>(),
  {
    values: () => [] as INodeParameters[],
    isReadOnly: false,
  },
)

const emit = defineEmits<{
  valueChanged: [parameterData: IUpdateInformation]
}>()

const i18n = useI18n()

const mutableValues = ref<INodeParameters[]>(deepCopy(props.values))

watch(
  () => props.values,
  (newValues) => {
    mutableValues.value = deepCopy(newValues)
  },
  { deep: true },
)

const addButtonText = computed(() => {
  if (!props.parameter.typeOptions?.multipleValueButtonText) {
    return '添加项目'
  }

  return i18n.nodeText().multipleValueButtonText(props.parameter)
})

const hideDelete = computed(() => props.parameter.options?.length === 1)

const sortable = computed(() => !!props.parameter.typeOptions?.sortable)

const getPath = (index?: number) => {
  return props.path + (index !== undefined ? `[${index}]` : '')
}

const addItem = () => {
  const name = getPath()
  const currentValue = get(props.nodeValues, name, []) as INodeParameters[]

  currentValue.push(deepCopy(props.parameter.default as INodeParameters))

  const parameterData = {
    name,
    value: currentValue,
  }

  emit('valueChanged', parameterData)
}

const deleteItem = (index: number) => {
  const parameterData = {
    name: getPath(index),
    value: undefined,
  }

  emit('valueChanged', parameterData)
}

const moveOptionDown = (index: number) => {
  mutableValues.value.splice(index + 1, 0, mutableValues.value.splice(index, 1)[0])

  emit('valueChanged', {
    name: props.path,
    value: mutableValues.value,
  })
}

const moveOptionUp = (index: number) => {
  mutableValues.value.splice(index - 1, 0, mutableValues.value.splice(index, 1)[0])

  emit('valueChanged', {
    name: props.path,
    value: mutableValues.value,
  })
}

const valueChanged = (parameterData: IUpdateInformation) => {
  emit('valueChanged', parameterData)
}
</script>

<template>
  <div
    class="duplicate-parameter"
    @keydown.stop
  >
    <N8nInputLabel
      color="text-dark"
      :label="i18n.nodeText().inputLabelDisplayName(parameter, path)"
      size="small"
      :tooltipText="i18n.nodeText().inputLabelDescription(parameter, path)"
      :underline="true"
    />

    <div
      v-for="(value, index) of mutableValues"
      :key="index"
      class="duplicate-parameter-item"
      :class="parameter.type"
    >
      <div
        v-if="!isReadOnly"
        class="delete-item clickable"
      >
        <FontAwesomeIcon
          icon="trash"
          :title="i18n.baseText('multipleParameter.deleteItem')"
          @click="deleteItem(index)"
        />
        <div v-if="sortable">
          <FontAwesomeIcon
            v-if="index !== 0"
            class="clickable"
            icon="angle-up"
            :title="i18n.baseText('multipleParameter.moveUp')"
            @click="moveOptionUp(index)"
          />
          <FontAwesomeIcon
            v-if="index !== mutableValues.length - 1"
            class="clickable"
            icon="angle-down"
            :title="i18n.baseText('multipleParameter.moveDown')"
            @click="moveOptionDown(index)"
          />
        </div>
      </div>

      <div v-if="parameter.type === 'collection'">
        <CollectionParameter
          :hideDelete="hideDelete"
          :isReadOnly="isReadOnly"
          :nodeValues="nodeValues"
          :param="parameter"
          :path="getPath(index)"
          :values="value"
          @valueChanged="valueChanged"
        />
      </div>

      <div v-else>
        <ParameterInputFull
          class="duplicate-parameter-input-item"
          :displayOptions="true"
          :hideLabel="true"
          inputSize="small"
          :isReadOnly="isReadOnly"
          :parameter="parameter"
          :path="getPath(index)"
          :value="value"
          @update="valueChanged"
        />
      </div>
    </div>

    <div class="add-item-wrapper">
      <div
        v-if="(mutableValues && mutableValues.length === 0) || isReadOnly"
        class="no-items-exist"
      >
        <span class="text-center text-sm">
          目前没有任何项目
        </span>
      </div>

      <Button
        v-if="!isReadOnly"
        fluid
        :label="addButtonText"
        variant="outlined"
        @click="addItem()"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.duplicate-parameter {
	:deep(.button) {
		--button-background-color: var(--color-background-base);
		--button-border-color: var(--color-foreground-base);
	}

	:deep(.duplicate-parameter-item) {
		position: relative;

		.multi > .delete-item {
			top: 0.1em;
		}
	}

	:deep(.duplicate-parameter-input-item) {
		margin: 0.5em 0 0.25em 2em;
	}

	:deep(.duplicate-parameter-item + .duplicate-parameter-item) {
		.collection-parameter-wrapper {
			border-top: 1px dashed #999;
			margin-top: var(--spacing-xs);
		}
	}
}

.duplicate-parameter-item {
	~ .add-item-wrapper {
		margin-top: var(--spacing-xs);
	}
}

.delete-item {
	display: none;
	position: absolute;
	left: 0.1em;
	top: 0.3em;
	z-index: 999;
	color: #f56c6c;
	width: 15px;
	font-size: var(--font-size-2xs);

	:hover {
		color: #ff0000;
	}
}

.no-items-exist {
	margin: var(--spacing-xs) 0;
}
</style>

<style>
.duplicate-parameter-item:hover > .delete-item {
	display: inline;
}

.duplicate-parameter-item .multi > .delete-item {
	top: 0.1em;
}
</style>
