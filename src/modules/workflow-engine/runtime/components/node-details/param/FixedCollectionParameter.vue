<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus'

import { get } from 'lodash-es'
import { GripVerticalIcon, TrashIcon } from 'lucide-vue-next'
import type { INodeParameters, INodeProperties, NodeParameterValueType } from 'n8n-workflow'
import { isINodePropertyCollectionList } from 'n8n-workflow/dist/type-guards.js'
import { deepCopy } from 'n8n-workflow/dist/utils.js'
import { nanoid } from 'nanoid'

import ParameterInputList from '#wf/components/node-details/param/ParameterInputList.vue'
import type { IUpdateInformation } from '#wf/types/interface'

interface Props {
  param: INodeProperties
  path: string
  values?: Record<string, INodeParameters[]>
  nodeValues: INodeParameters
  readOnly?: boolean
}

const props = withDefaults(
  defineProps<Props>(),
  {
    values: () => ({}),
    readOnly: false,
  },
)

type ValueChangedEvent = {
  name: string
  value: NodeParameterValueType
  type?: 'optionsOrderChanged'
}

const emit = defineEmits<{
  valueChange: [newValue: ValueChangedEvent]
}>()

const handlerClass = nanoid(4)
const isDragging = ref(false)

const xParam = toRef(props, 'param')
const selectedOption = ref<string | null | undefined>(null)

const multipleValues = computed(() => {
  return !!xParam.value.typeOptions?.multipleValues
})

const mutableValues = ref({} as Record<string, INodeParameters[]>)

watch(
  () => props.values,
  (newValues: Record<string, INodeParameters[]>) => {
    mutableValues.value = deepCopy(newValues)
  },
  { deep: true, immediate: true },
)

const propertyNames = computed(() => {
  return new Set(Object.keys(mutableValues.value || {}))
})

const getOptionProperties = (optionName: string) => {
  if (isINodePropertyCollectionList(xParam.value.options)) {
    for (const option of xParam.value.options) {
      if (option.name === optionName) {
        return option
      }
    }
  }

  return undefined
}

const getProperties = computed(() => {
  const returnProperties = []
  let tempProperties

  for (const name of propertyNames.value) {
    tempProperties = getOptionProperties(name)

    if (tempProperties !== undefined) {
      returnProperties.push(tempProperties)
    }
  }

  return returnProperties
})

const getPropertyPath = (name: string, index?: number) => {
  return `${props.path}.${name}` + (index !== undefined ? `[${index}]` : '')
}

const getPlaceholderText = computed(() => {
  const placeholder = props.param.placeholder

  return placeholder ? placeholder : '请选择'
})

const parameterOptions = computed(() => {
  if (!isINodePropertyCollectionList(props.param.options)) {
    return []
  }

  if (multipleValues.value) {
    return props.param.options
  }

  return (props.param.options ?? []).filter((option) => {
    return !propertyNames.value.has(option.name)
  })
})

const optionSelected = (optionName: typeof selectedOption.value) => {
  if (!optionName) {
    return
  }

  const option = getOptionProperties(optionName)

  if (option === undefined) {
    return
  }

  const name = `${props.path}.${option.name}`

  const newParameterValue: INodeParameters = {}

  for (const optionParameter of option.values) {
    if (
      optionParameter.type === 'fixedCollection'
      && optionParameter.typeOptions !== undefined
      && optionParameter.typeOptions.multipleValues === true
    ) {
      newParameterValue[optionParameter.name] = {}
    }
    else if (
      optionParameter.typeOptions !== undefined
      && optionParameter.typeOptions.multipleValues === true
    ) {
      // Multiple values are allowed so append option to array
      const multiValue = get(props.nodeValues, [props.path, optionParameter.name], [])

      if (Array.isArray(optionParameter.default)) {
        multiValue.push(...deepCopy(optionParameter.default))
      }
      else if (optionParameter.default !== '' && typeof optionParameter.default !== 'object') {
        multiValue.push(deepCopy(optionParameter.default))
      }

      newParameterValue[optionParameter.name] = multiValue
    }
    else {
      // Add a new option
      newParameterValue[optionParameter.name] = deepCopy(optionParameter.default)
    }
  }

  let newValue: NodeParameterValueType

  if (multipleValues.value) {
    newValue = get(props.nodeValues, name, []) as INodeParameters[]

    newValue.push(newParameterValue)
  }
  else {
    newValue = newParameterValue
  }

  const parameterData = {
    name,
    value: newValue,
  }

  emit('valueChange', parameterData)

  selectedOption.value = undefined
}

const onAddButtonClick = (optionName: string) => {
  optionSelected(optionName)
}

const handleDeleteOption = (optionName: string, index?: number) => {
  const currentOptionsOfSameType = mutableValues.value[optionName]

  if (!currentOptionsOfSameType || currentOptionsOfSameType.length > 1) {
    // 如果不是唯一选项，则只删除该选项
    emit('valueChange', {
      name: getPropertyPath(optionName, index),
      value: undefined,
    })
  }
  else {
    // 如果是唯一选项，则删除整个类型
    emit('valueChange', {
      name: getPropertyPath(optionName),
      value: undefined,
    })
  }
}

const handleParamValueChange = (parameterData: IUpdateInformation) => {
  emit('valueChange', parameterData)
}
</script>

<template>
  <div :data-testid="`fixed-collection-${param.name}`">
    <FormItem :label="param.displayName">
      <template v-if="getProperties.length > 0">
        <div
          v-for="(property, idx) of getProperties"
          :key="property.name"
        >
          <FormItemLabel
            v-if="property.displayName !== '' && param.options && param.options.length !== 1"
            :label="property.displayName"
          />

          <div v-if="multipleValues">
            <VueDraggable
              v-model="mutableValues[property.name]"
              :animation="150"
              class="flex flex-col gap-2"
              :handle="`.${handlerClass}`"
              itemid="field"
              @end="isDragging = false"
              @start="isDragging = true"
            >
              <div
                v-for="(it, vIdx) of mutableValues[property.name]"
                :key="it.field"
              >
                <Divider
                  v-if="vIdx !== 0"
                  type="dashed"
                />

                <div class="group/param-item relative flex gap-2">
                  <div class="flex-1">
                    <ParameterInputList
                      hideDelete
                      :nodeValues="nodeValues"
                      :parameters="property.values"
                      :path="getPropertyPath(property.name, vIdx)"
                      :readOnly="readOnly"
                      @paramValueChange="handleParamValueChange"
                    />
                  </div>

                  <div
                    v-if="!readOnly"
                    class="absolute right-0 top-0 shrink-0 opacity-0 inline-flex-center group-hover/param-item:opacity-100"
                    :class="handlerClass"
                  >
                    <ProBtn
                      :class="`${handlerClass} ${isDragging ? 'opacity-0' : ''}`"
                      data-testid="fixed-collection-delete"
                      label="移动该项"
                      mini
                      onlyIcon
                      size="small"
                    >
                      <template #icon="{ size }">
                        <GripVerticalIcon :size="size" />
                      </template>
                    </ProBtn>

                    <ProBtn
                      data-testid="fixed-collection-delete"
                      label="删除该项"
                      mini
                      onlyIcon
                      size="small"
                      @click="handleDeleteOption(property.name, idx)"
                    >
                      <template #icon="{ size }">
                        <TrashIcon :size="size" />
                      </template>
                    </ProBtn>
                  </div>
                </div>
              </div>
            </VueDraggable>
          </div>

          <div v-else>
            <div class="group/param-item relative flex gap-2">
              <div class="flex-1">
                <ParameterInputList
                  hideDelete
                  :nodeValues="nodeValues"
                  :parameters="property.values"
                  :path="getPropertyPath(property.name)"
                  :readOnly="readOnly"
                  @paramValueChange="handleParamValueChange"
                />
              </div>

              <div class="absolute right-0 top-0 shrink-0 opacity-0 group-hover/param-item:opacity-100">
                <ProBtn
                  v-if="!readOnly"
                  data-testid="fixed-collection-delete"
                  label="删除该项"
                  mini
                  onlyIcon
                  size="small"
                  @click="handleDeleteOption(property.name)"
                >
                  <template #icon="{ size }">
                    <TrashIcon :size="size" />
                  </template>
                </ProBtn>
              </div>
            </div>
          </div>
        </div>
      </template>

      <div
        v-else
        class="text-sm text-secondary"
      >
        暂无选项，请在添加后进行配置
      </div>
    </FormItem>

    <div
      v-if="parameterOptions.length > 0 && !readOnly"
      :class="{
        'pt-form-field': getProperties.length > 0,
      }"
    >
      <Button
        v-if="param.options && param.options.length === 1"
        class=""
        :class="{
          'mt-form-field': !(getProperties.length > 0),
        }"
        fluid
        :label="getPlaceholderText"
        size="small"
        variant="outlined"
        @click="onAddButtonClick(param.options[0].name)"
      />

      <div v-else>
        <ProSelect
          fluid
          :modelValue="selectedOption"
          :options="parameterOptions.map(option => ({
            label: option.displayName,
            value: option.name,
          }))"
          :placeholder="getPlaceholderText"
          size="small"
          @update:modelValue="optionSelected"
        />
      </div>
    </div>
  </div>
</template>
