<script setup lang="ts">
import type { ResourceLocatorRequestDto } from '@n8n/api-types'
import { onClickOutside, type VueInstance } from '@vueuse/core'
import stringify from 'fast-json-stable-stringify'
import { debounce } from 'lodash-es'
import { ChevronDownIcon, SquareArrowOutUpRightIcon } from 'lucide-vue-next'
import type {
  INode,
  INodeListSearchItems,
  INodeParameterResourceLocator,
  INodeParameters,
  INodeProperties,
  INodePropertyMode,
  INodePropertyModeTypeOptions,
  NodeParameterValue,
} from 'n8n-workflow'
import type { InputText } from 'primevue'

import DraggableTarget from '#wf/components/DraggableTarget.vue'
import ExpressionParameterInput from '#wf/components/ExpressionParameterInput.vue'
import FromAiOverrideButton from '#wf/components/ParameterInputOverrides/FromAiOverrideButton.vue'
import FromAiOverrideField from '#wf/components/ParameterInputOverrides/FromAiOverrideField.vue'
import ParameterIssues from '#wf/components/ParameterIssues.vue'
import { useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useUIStore } from '#wf/stores/ui.store'
import type { EventBus } from '#wf/types/event-bus'
import type { IResourceLocatorResultExpanded } from '#wf/types/interface'
import {
  buildValueFromOverride,
  type FromAIOverride,
  isFromAIOverrideValue,
  makeOverrideValue,
  updateFromAIOverrideValues,
} from '#wf/utils/fromAIOverrideUtils'
import {
  getAppNameFromNodeName,
  hasOnlyListMode as hasOnlyListModeUtil,
} from '#wf/utils/nodeTypesUtils'
import { isResourceLocatorValue } from '#wf/utils/typeGuards'

import ResourceLocatorDropdown from './ResourceLocatorDropdown.vue'

interface IResourceLocatorQuery {
  results: INodeListSearchItems[]
  nextPageToken: unknown
  error: boolean
  loading: boolean
}

type Props = {
  modelValue: INodeParameterResourceLocator
  parameter: INodeProperties
  path: string
  node?: INode
  inputSize?: 'mini' | 'small' | 'medium' | 'large' | 'xlarge'
  parameterIssues?: string[]
  dependentParametersValues?: string | null
  displayTitle?: string
  isReadOnly?: boolean
  expressionComputedValue: unknown
  expressionDisplayValue?: string
  forceShowExpression?: boolean
  isValueExpression?: boolean
  eventBus?: EventBus
}

const props = withDefaults(defineProps<Props>(), {
  node: undefined,
  inputSize: 'small',
  parameterIssues: () => [],
  dependentParametersValues: null,
  displayTitle: '',
  isReadOnly: false,
  expressionDisplayValue: '',
  forceShowExpression: false,
  isValueExpression: false,
  // eventBus: () => createEventBus(),
})
const emit = defineEmits<{
  'update:modelValue': [value: INodeParameterResourceLocator]
  drop: [value: string]
  blur: []
  modalOpenerClick: []
}>()

const workflowHelpers = useWorkflowHelpers()

const resourceDropdownVisible = ref(false)
const resourceDropdownHiding = ref(false)
const searchFilter = ref('')
const cachedResponses = ref<Record<string, IResourceLocatorQuery>>({})
const hasCompletedASearch = ref(false)
const width = ref(0)
const { refKey, ref: inputRef } = useRef<
  InstanceType<typeof InputText> & { $el: HTMLInputElement }
>()
const containerRef = ref<HTMLDivElement>()
const dropdownRef = ref<InstanceType<typeof ResourceLocatorDropdown>>()

const nodeTypesStore = useNodeTypesStore()
const ndvStore = useNDVStore()
const uiStore = useUIStore()

const appName = computed(() => {
  if (!props.node) {
    return ''
  }

  const nodeType = nodeTypesStore.getNodeType(props.node.type)

  return getAppNameFromNodeName(nodeType?.displayName ?? '')
})

const selectedMode = computed(() => {
  if (typeof props.modelValue !== 'object') {
    // legacy mode
    return ''
  }

  if (!props.modelValue) {
    return props.parameter.modes ? props.parameter.modes[0].name : ''
  }

  return props.modelValue.mode
})

const isListMode = computed(() => selectedMode.value === 'list')

const hasCredential = computed(() => {
  const node = ndvStore.activeNode

  if (!node) {
    return false
  }

  return !!(node?.credentials && Object.keys(node.credentials).length === 1)
})

const credentialsNotSet = computed(() => {
  if (!props.node) {
    return false
  }

  const nodeType = nodeTypesStore.getNodeType(props.node.type)

  if (nodeType) {
    const usesCredentials = nodeType.credentials !== undefined && nodeType.credentials.length > 0

    if (usesCredentials && !props.node.credentials) {
      return true
    }
  }

  return false
})

function findModeByName(name: string): INodePropertyMode | null {
  if (props.parameter.modes) {
    return props.parameter.modes.find((mode: INodePropertyMode) => mode.name === name) ?? null
  }

  return null
}

function getPropertyArgument(
  parameter: INodePropertyMode,
  argumentName: keyof INodePropertyModeTypeOptions,
): string | number | boolean | undefined {
  return parameter.typeOptions?.[argumentName]
}

const currentMode = computed<INodePropertyMode>(
  () => findModeByName(selectedMode.value) ?? ({} as INodePropertyMode),
)

const inputPlaceholder = computed(() => {
  if (currentMode.value.placeholder) {
    return currentMode.value.placeholder
  }

  const defaults: { [key: string]: string } = {
    list: '请选择资源名称',
    id: '请输入资源 ID',
    url: '请输入资源 URL',
  }

  return defaults[selectedMode.value] ?? ''
})

const hasMultipleModes = computed(() => {
  return props.parameter.modes && props.parameter.modes.length > 1
})

const hasOnlyListMode = computed(() => hasOnlyListModeUtil(props.parameter))
const valueToDisplay = computed<NodeParameterValue>(() => {
  if (typeof props.modelValue !== 'object') {
    return props.modelValue
  }

  if (isListMode.value) {
    return props.modelValue?.cachedResultName ?? props.modelValue?.value ?? ''
  }

  return props.modelValue?.value ?? ''
})

const urlValue = computed(() => {
  if (isListMode.value && typeof props.modelValue === 'object') {
    return props.modelValue?.cachedResultUrl ?? null
  }

  if (selectedMode.value === 'url') {
    if (
      props.isValueExpression
      && typeof props.expressionComputedValue === 'string'
      && props.expressionComputedValue.startsWith('http')
    ) {
      return props.expressionComputedValue
    }

    if (typeof valueToDisplay.value === 'string' && valueToDisplay.value.startsWith('http')) {
      return valueToDisplay.value
    }
  }

  if (currentMode.value.url) {
    const value = props.isValueExpression ? props.expressionComputedValue : valueToDisplay.value

    if (typeof value === 'string') {
      const expression = currentMode.value.url.replace(/\{\{\$value\}\}/g, value)
      const resolved = workflowHelpers.resolveExpression(expression)

      return typeof resolved === 'string' ? resolved : null
    }
  }

  return null
})

const currentRequestParams = computed(() => {
  return {
    parameters: props.node?.parameters ?? {},
    credentials: props.node?.credentials ?? {},
    filter: searchFilter.value,
  }
})

const currentRequestKey = computed(() => {
  const cacheKeys = { ...currentRequestParams.value }
  cacheKeys.parameters = Object.keys(props.node?.parameters ?? {}).reduce(
    (accu: INodeParameters, param) => {
      if (param !== props.parameter.name && props.node?.parameters) {
        accu[param] = props.node.parameters[param]
      }

      return accu
    },
    {},
  )

  return stringify(cacheKeys)
})

const currentResponse = computed(() => cachedResponses.value[currentRequestKey.value] ?? null)

function getLinkAlt(entity: NodeParameterValue) {
  if (selectedMode.value === 'list' && entity) {
    return `在 ${appName.value} 中打开 ${String(entity)}`
  }

  return `在 ${appName.value} 中打开`
}

const currentQueryResults = computed<IResourceLocatorResultExpanded[]>(() => {
  const results = currentResponse.value?.results ?? []

  return results.map(
    (result: INodeListSearchItems): IResourceLocatorResultExpanded => ({
      ...result,
      ...(result.name && result.url ? { linkAlt: getLinkAlt(result.name) } : {}),
    }),
  )
})

const currentQueryHasMore = computed(() => !!currentResponse.value?.nextPageToken)

const requiresSearchFilter = computed(
  () => !!getPropertyArgument(currentMode.value, 'searchFilterRequired'),
)

const currentQueryLoading = computed(
  () =>
    (requiresSearchFilter.value && searchFilter.value === '')
    || !currentResponse.value
    || !!currentResponse.value?.loading,
)

const currentQueryError = computed(() => {
  return !!(currentResponse.value && currentResponse.value.error)
})

const isSearchable = computed(() => !!getPropertyArgument(currentMode.value, 'searchable'))

const fromAIOverride = ref<FromAIOverride | null>(
  makeOverrideValue(
    {
      value: props.modelValue?.value ?? '',
      ...props,
    },
    props.node && nodeTypesStore.getNodeType(props.node.type, props.node.typeVersion),
  ),
)

const canBeContentOverride = computed(() => {
  if (!props.node) {
    return false
  }

  return fromAIOverride.value !== null
})

const isContentOverride = computed(
  () =>
    canBeContentOverride.value
    && !!isFromAIOverrideValue(props.modelValue?.value?.toString() ?? ''),
)

const showOverrideButton = computed(
  () => canBeContentOverride.value && !isContentOverride.value && !props.isReadOnly,
)

function setWidth() {
  if (containerRef.value) {
    width.value = containerRef.value.offsetWidth
  }
}

function refreshList() {
  cachedResponses.value = {}
}

function onKeyDown(e: KeyboardEvent) {
  if (resourceDropdownVisible.value && !isSearchable.value) {
    props.eventBus?.emit('keyDown', e)
  }
}

function openResource(url: string) {
  window.open(url, '_blank')
}

function openCredential(): void {
  const node = ndvStore.activeNode

  if (!node?.credentials) {
    return
  }

  const credentialKey = Object.keys(node.credentials)[0]

  if (!credentialKey) {
    return
  }

  const id = node.credentials[credentialKey].id

  if (!id) {
    return
  }

  uiStore.openExistingCredential(id)
}

function createNewCredential(): void {
  if (!props.node) {
    return
  }

  const nodeType = nodeTypesStore.getNodeType(props.node.type)

  if (!nodeType) {
    return
  }

  // const defaultCredentialType = nodeType.credentials?.[0].name ?? ''
  // const mainAuthType = getMainAuthField(nodeType)
  // const showAuthOptions = mainAuthType !== null
  //   && Array.isArray(mainAuthType.options)
  //   && mainAuthType.options?.length > 0

  // ndvEventBus.emit('credential.createNew', {
  //   type: defaultCredentialType,
  //   showAuthOptions,
  // })
}

function getModeLabel(mode: INodePropertyMode) {
  if (mode.name === 'id' || mode.name === 'url' || mode.name === 'list') {
    if (mode.name === 'id') {
      return '按 ID'
    }

    if (mode.name === 'url') {
      return '按 URL'
    }

    if (mode.name === 'list') {
      return '从列表中'
    }
  }

  return mode.displayName
}

function onInputChange(value: NodeParameterValue): void {
  const params: INodeParameterResourceLocator = { __rl: true, value, mode: selectedMode.value }

  if (isListMode.value) {
    const resource = currentQueryResults.value.find((result) => result.value === value)

    if (resource?.name) {
      params.cachedResultName = resource.name
    }

    if (resource?.url) {
      params.cachedResultUrl = resource.url
    }
  }

  emit('update:modelValue', params)
}

function onModeSelected(value: string | undefined): void {
  if (!value) {
    return
  }

  if (typeof props.modelValue !== 'object') {
    emit('update:modelValue', { __rl: true, value: props.modelValue, mode: value })
  }
  else if (value === 'url' && props.modelValue?.cachedResultUrl) {
    emit('update:modelValue', {
      __rl: true,
      mode: value,
      value: props.modelValue.cachedResultUrl,
    })
  }
  else if (
    value === 'id'
    && selectedMode.value === 'list'
    && props.modelValue
    && props.modelValue.value
  ) {
    emit('update:modelValue', { __rl: true, mode: value, value: props.modelValue.value })
  }
  else {
    emit('update:modelValue', { __rl: true, mode: value, value: '' })
  }
}

function setResponse(paramsKey: string, response: Partial<IResourceLocatorQuery>) {
  cachedResponses.value = {
    ...cachedResponses.value,
    [paramsKey]: { ...cachedResponses.value[paramsKey], ...response },
  }
}

async function loadResources() {
  const params = currentRequestParams.value
  const paramsKey = currentRequestKey.value
  const cachedResponse = cachedResponses.value[paramsKey]

  if (credentialsNotSet.value) {
    setResponse(paramsKey, { error: true })

    return
  }

  if (requiresSearchFilter.value && !params.filter) {
    return
  }

  if (!props.node) {
    return
  }

  let paginationToken: string | undefined

  try {
    if (cachedResponse) {
      const nextPageToken = cachedResponse.nextPageToken as string

      if (nextPageToken) {
        paginationToken = nextPageToken
        setResponse(paramsKey, { loading: true })
      }
      else if (cachedResponse.error) {
        setResponse(paramsKey, { error: false, loading: true })
      }
      else {
        return // end of results
      }
    }
    else {
      setResponse(paramsKey, {
        loading: true,
        error: false,
        results: [],
        nextPageToken: null,
      })
    }

    const resolvedNodeParameters = workflowHelpers.resolveRequiredParameters(
      props.parameter,
      params.parameters,
    ) as INodeParameters
    const loadOptionsMethod = getPropertyArgument(currentMode.value, 'searchListMethod') as string

    const requestParams: ResourceLocatorRequestDto = {
      nodeTypeAndVersion: {
        name: props.node.type,
        version: props.node.typeVersion,
      },
      path: props.path,
      methodName: loadOptionsMethod,
      currentNodeParameters: resolvedNodeParameters,
      credentials: props.node.credentials,
    }

    if (params.filter) {
      requestParams.filter = params.filter
    }

    if (paginationToken) {
      requestParams.paginationToken = paginationToken
    }

    const response = await nodeTypesStore.getResourceLocatorResults(requestParams)

    setResponse(paramsKey, {
      results: (cachedResponse?.results ?? []).concat(response.results),
      nextPageToken: response.paginationToken ?? null,
      loading: false,
      error: false,
    })

    if (params.filter && !hasCompletedASearch.value) {
      hasCompletedASearch.value = true
    }
  }
  catch {
    setResponse(paramsKey, {
      loading: false,
      error: true,
    })
  }
}

const debouncedLoadResources = debounce(async () => {
  await loadResources()
}, 1000, { trailing: true })

function loadResourcesDebounced() {
  if (currentResponse.value?.error) {
    // Clear error response immediately when retrying to show loading state
    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
    delete cachedResponses.value[currentRequestKey.value]
  }

  void debouncedLoadResources()
}

async function loadInitialResources(): Promise<void> {
  if (!currentResponse.value || currentResponse.value.error) {
    searchFilter.value = ''
    await loadResources()
  }
}

function switchFromListMode(): void {
  if (isListMode.value && props.parameter.modes && props.parameter.modes.length > 1) {
    const mode = findModeByName('id') ?? props.parameter.modes.filter(({ name }) => name !== 'list')[0]

    if (mode) {
      emit('update:modelValue', {
        __rl: true,
        value: props.modelValue && typeof props.modelValue === 'object' ? props.modelValue.value : '',
        mode: mode.name,
      })
    }
  }
}

function onSearchFilter(filter: string) {
  searchFilter.value = filter
  loadResourcesDebounced()
}

function onDrop(data: string) {
  switchFromListMode()
  emit('drop', data)
}

function showResourceDropdown() {
  if (resourceDropdownVisible.value || resourceDropdownHiding.value) {
    return
  }

  resourceDropdownVisible.value = true
}

function onInputFocus(): void {
  if (!isListMode.value || resourceDropdownVisible.value) {
    return
  }

  void loadInitialResources()
  showResourceDropdown()
}

function hideResourceDropdown() {
  if (!resourceDropdownVisible.value) {
    return
  }

  resourceDropdownVisible.value = false
  resourceDropdownHiding.value = true

  void nextTick(() => {
    inputRef.value?.$el.blur()
    resourceDropdownHiding.value = false
  })
}

function onListItemSelected(value: NodeParameterValue) {
  onInputChange(value)
  hideResourceDropdown()
}

function onInputBlur(event: FocusEvent) {
  // Do not blur if focus is within the dropdown
  const newTarget = event.relatedTarget

  if (newTarget instanceof HTMLElement && dropdownRef.value?.isWithinDropdown()) {
    return
  }

  if (!isSearchable.value || currentQueryError.value) {
    hideResourceDropdown()
  }

  emit('blur')
}

function applyOverride() {
  if (!props.node || !fromAIOverride.value) {
    return
  }

  updateFromAIOverrideValues(fromAIOverride.value, props.modelValue.value?.toString() ?? '')

  emit('update:modelValue', {
    ...props.modelValue,
    value: buildValueFromOverride(fromAIOverride.value, props, true),
  })
}

function removeOverride() {
  if (!props.node || !fromAIOverride.value) {
    return
  }

  emit('update:modelValue', {
    ...props.modelValue,
    value: buildValueFromOverride(fromAIOverride.value, props, false),
  })
  void setTimeout(() => {
    inputRef.value?.$el.focus()
    inputRef.value?.$el.select()
  }, 0)
}

watch(currentQueryError, (curr, prev) => {
  if (resourceDropdownVisible.value && curr && !prev) {
    if (inputRef.value) {
      inputRef.value.$el.focus()
    }
  }
})

watch(
  () => props.isValueExpression,
  (newValue) => {
    if (newValue) {
      switchFromListMode()
    }
  },
)

watch(currentMode, (mode) => {
  if (
    mode.extractValue?.regex
    && isResourceLocatorValue(props.modelValue)
    && props.modelValue.__regex !== mode.extractValue.regex
  ) {
    emit('update:modelValue', { ...props.modelValue, __regex: mode.extractValue.regex as string })
  }
})

watch(
  () => props.dependentParametersValues,
  (currentValue, oldValue) => {
    const isUpdated = oldValue !== null && currentValue !== null && oldValue !== currentValue

    // Reset value if dependent parameters change
    if (
      isUpdated
      && props.modelValue
      && isResourceLocatorValue(props.modelValue)
      && props.modelValue.value !== ''
    ) {
      emit('update:modelValue', {
        ...props.modelValue,
        cachedResultName: '',
        cachedResultUrl: '',
        value: '',
      })
    }
  },
)

onMounted(() => {
  props.eventBus?.on('refreshList', refreshList)
  window.addEventListener('resize', setWidth)

  useNDVStore().$subscribe(() => {
    // Update the width when main panel dimension change
    setWidth()
  })

  setTimeout(() => {
    setWidth()
  }, 0)
})

onBeforeUnmount(() => {
  props.eventBus?.off('refreshList', refreshList)
  window.removeEventListener('resize', setWidth)
})

onClickOutside(dropdownRef as Ref<VueInstance>, hideResourceDropdown)
</script>

<template>
  <div
    ref="containerRef"
    :data-testid="`resource-locator-${parameter.name}`"
  >
    <ResourceLocatorDropdown
      ref="dropdownRef"
      :errorView="currentQueryError"
      :eventBus="eventBus"
      :filter="searchFilter"
      :filterRequired="requiresSearchFilter"
      :filterable="isSearchable"
      :hasMore="currentQueryHasMore"
      :loading="currentQueryLoading"
      :modelValue="modelValue ? modelValue.value : ''"
      :resources="currentQueryResults"
      :show="resourceDropdownVisible"
      :width="width"
      @filter="onSearchFilter"
      @loadMore="loadResourcesDebounced"
      @update:modelValue="onListItemSelected"
    >
      <template #error>
        <div data-testid="rlc-error-container">
          <div class="text-center">
            无法加载列表
          </div>

          <div
            v-if="hasCredential || credentialsNotSet"
            class="text-center text-sm"
          >
            请
            <a
              v-if="credentialsNotSet"
              @click="createNewCredential"
            >
              添加您的凭据
            </a>
            <a
              v-else-if="hasCredential"
              @click="openCredential"
            >
              检查您的凭据
            </a>
          </div>
        </div>
      </template>

      <div class="gap-2 flex-center">
        <div v-if="hasMultipleModes">
          <ProSelect
            :disabled="isReadOnly"
            :modelValue="selectedMode"
            :options="parameter.modes?.map<ProSelectOption<string>>(mode => ({
              label: getModeLabel(mode),
              value: mode.name,
              disabled: isValueExpression && mode.name === 'list',
            }))"
            placeholder="请选择资源模式"
            :size="inputSize"
            @click.stop
            @update:modelValue="onModeSelected"
          />
        </div>

        <div
          class="flex-1 gap-0.5 flex-center"
          data-testid="rlc-input-container"
        >
          <div class="flex-1">
            <DraggableTarget
              :disabled="hasOnlyListMode"
              sticky
              :stickyOffset="isValueExpression ? [26, 3] : [3, 3]"
              type="mapping"
              @drop="onDrop"
            >
              <template #default>
                <div @keydown.stop="onKeyDown">
                  <FromAiOverrideField
                    v-if="fromAIOverride && isContentOverride"
                    :isReadOnly="isReadOnly"
                    @close="removeOverride"
                  />

                  <ExpressionParameterInput
                    v-else-if="isValueExpression || forceShowExpression"
                    :ref="refKey"
                    :modelValue="expressionDisplayValue"
                    :path="path"
                    :rows="3"
                    @modalOpenerClick="emit('modalOpenerClick')"
                    @update:modelValue="onInputChange"
                  />

                  <IconField v-else>
                    <InputText
                      :ref="refKey"
                      data-testid="rlc-input"
                      :disabled="isReadOnly"
                      fluid
                      :modelValue="valueToDisplay as string"
                      :placeholder="inputPlaceholder"
                      :readonly="isListMode"
                      :size="inputSize"
                      :title="displayTitle"
                      @blur="onInputBlur"
                      @focus="onInputFocus"
                      @update:modelValue="onInputChange"
                    />

                    <InputIcon class="inline-flex-center">
                      <ChevronDownIcon :size="12" />
                    </InputIcon>
                  </IconField>

                  <div v-if="showOverrideButton">
                    <FromAiOverrideButton @click="applyOverride" />
                  </div>
                </div>
              </template>
            </DraggableTarget>
          </div>

          <div v-if="urlValue">
            <ProBtn
              size="small"
              @click.stop="openResource(urlValue)"
            >
              <template #icon>
                <SquareArrowOutUpRightIcon
                  :size="14"
                  :title="getLinkAlt(valueToDisplay)"
                />
              </template>
            </ProBtn>
          </div>
        </div>
      </div>
    </ResourceLocatorDropdown>

    <ParameterIssues
      v-if="parameterIssues && parameterIssues.length > 0"
      :issues="parameterIssues"
    />

    <!-- <ParameterOverrideSelectableList
      v-if="isContentOverride && fromAIOverride"
      v-model="fromAIOverride"
      :isReadOnly="isReadOnly"
      :parameter="parameter"
      :path="path"
      @update="(x: IUpdateInformation) => onInputChange(x.value?.toString())"
    /> -->
  </div>
</template>
