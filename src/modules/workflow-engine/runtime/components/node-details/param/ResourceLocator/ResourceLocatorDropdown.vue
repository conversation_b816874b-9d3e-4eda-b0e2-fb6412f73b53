<script setup lang="ts">
import { PlusIcon, SquareArrowOutUpRightIcon } from 'lucide-vue-next'
import type { NodeParameterValue } from 'n8n-workflow'

import type { EventBus } from '#wf/types/event-bus'
import type { IResourceLocatorResultExpanded } from '#wf/types/interface'

const SEARCH_BAR_HEIGHT_PX = 40
const SCROLL_MARGIN_PX = 10

interface Props {
  modelValue?: NodeParameterValue
  resources?: IResourceLocatorResultExpanded[]
  show?: boolean
  filterable?: boolean
  loading?: boolean
  filter?: string
  errorView?: boolean
  filterRequired?: boolean
  eventBus?: EventBus
  width?: number
  allowNewResources?: {
    label?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
  resources: () => [],
  show: false,
  filterable: false,
  loading: false,
  filter: '',
  hasMore: false,
  errorView: false,
  filterRequired: false,
  allowNewResources: () => ({}),
})

const emit = defineEmits<{
  'update:modelValue': [value: NodeParameterValue]
  loadMore: []
  filter: [filter: string]
  addResourceClick: []
}>()

const hoverIndex = ref(0)
const searchRef = ref<HTMLInputElement>()
const resultsContainerRef = ref<HTMLDivElement>()
const itemsRef = ref<HTMLDivElement[]>([])

const sortedResources = computed<IResourceLocatorResultExpanded[]>(() => {
  const seen = new Set()
  const { selected, notSelected } = props.resources.reduce(
    (acc, item: IResourceLocatorResultExpanded) => {
      if (seen.has(item.value)) {
        return acc
      }

      seen.add(item.value)

      if (props.modelValue && item.value === props.modelValue) {
        acc.selected = item
      }
      else {
        acc.notSelected.push(item)
      }

      return acc
    },
    {
      selected: null as IResourceLocatorResultExpanded | null,
      notSelected: [] as IResourceLocatorResultExpanded[],
    },
  )

  if (selected) {
    return [selected, ...notSelected]
  }

  return notSelected
})

const onItemClick = (selected: string | number | boolean) => {
  emit('update:modelValue', selected)
}

const resourceMenuItems = computed<ProMenuItem[]>(() => {
  const items: ProMenuItem[] = []

  // 如果允许添加新资源，在顶部添加该选项
  if (props.allowNewResources.label) {
    items.push({
      label: props.allowNewResources.label,
      key: 'addResourceKey',
      command: () => {
        emit('addResourceClick')
      },
      itemIcon: PlusIcon,
    })
  }

  // 添加现有资源选项
  const resourceItems = sortedResources.value.map((result) => ({
    label: result.name,
    key: result.value.toString(),
    command: () => {
      onItemClick(result.value)
    },
    active: props.modelValue === result.value,
    // 如果有 URL，显示外链图标
    itemIcon: result.url ? SquareArrowOutUpRightIcon : undefined,
  }))

  return [...items, ...resourceItems]
})

watch(
  () => props.show,
  (value) => {
    if (value) {
      hoverIndex.value = 0

      setTimeout(() => {
        if (value && props.filterable && searchRef.value) {
          searchRef.value.focus()
        }
      }, 0)
    }
  },
)

function onResultsEnd() {
  if (props.loading || !props.loading) {
    return
  }

  if (resultsContainerRef.value) {
    const diff = resultsContainerRef.value.offsetHeight
      - (resultsContainerRef.value.scrollHeight - resultsContainerRef.value.scrollTop)

    if (diff > -SCROLL_MARGIN_PX && diff < SCROLL_MARGIN_PX) {
      emit('loadMore')
    }
  }
}

watch(
  () => props.loading,
  () => {
    setTimeout(() => onResultsEnd(), 0) // in case of filtering
  },
)

function onKeyDown(e: KeyboardEvent) {
  if (e.key === 'ArrowDown') {
    if (hoverIndex.value < sortedResources.value.length - 1) {
      hoverIndex.value++

      if (resultsContainerRef.value && itemsRef.value.length === 1) {
        const item = itemsRef.value[0]

        if (
          item.offsetTop + item.clientHeight
          > resultsContainerRef.value.scrollTop + resultsContainerRef.value.offsetHeight
        ) {
          const top = item.offsetTop - resultsContainerRef.value.offsetHeight + item.clientHeight
          resultsContainerRef.value.scrollTo({ top })
        }
      }
    }
  }
  else if (e.key === 'ArrowUp') {
    if (hoverIndex.value > 0) {
      hoverIndex.value--

      const searchOffset = props.filterable ? SEARCH_BAR_HEIGHT_PX : 0

      if (resultsContainerRef.value && itemsRef.value.length === 1) {
        const item = itemsRef.value[0]

        if (item.offsetTop <= resultsContainerRef.value.scrollTop + searchOffset) {
          resultsContainerRef.value.scrollTo({ top: item.offsetTop - searchOffset })
        }
      }
    }
  }
  else if (e.key === 'Enter') {
    const selected = sortedResources.value[hoverIndex.value]?.value

    // Selected resource can be empty when loading or empty results
    if (selected) {
      emit('update:modelValue', selected)
    }
  }
}

onMounted(() => {
  props.eventBus?.on('keyDown', onKeyDown)
})

onBeforeUnmount(() => {
  props.eventBus?.off('keyDown', onKeyDown)
})

function onFilterInput(value: string) {
  emit('filter', value)
}

function isWithinDropdown() {
  return false
}

defineExpose({ isWithinDropdown })
</script>

<template>
  <ProPrimePopover
    :popoverProps="{
      pt: {
        content: '!p-0',
      },
    }"
    rootClass="w-full"
    triggerClass="w-full"
  >
    <slot />

    <template #content>
      <div
        class="p-2"
        :style="{ width: `${width}px` }"
      >
        <div v-if="errorView">
          <slot name="error" />
        </div>

        <div
          v-if="filterable && !errorView"
          @keydown="onKeyDown"
        >
          <SearchInput
            ref="searchRef"
            fluid
            :modelValue="filter"
            placeholder="输入资源名称以搜索"
            size="small"
            @update:modelValue="onFilterInput"
          />
        </div>

        <div
          v-if="filterRequired && !filter && !errorView && !loading"
        >
          输入搜索词以显示结果
        </div>

        <div
          v-else-if="!errorView && !allowNewResources.label && sortedResources.length === 0 && !loading"
        >
          没有找到结果
        </div>

        <div
          v-else-if="!errorView"
          ref="resultsContainerRef"
          class="pt-2"
          @scroll="onResultsEnd"
        >
          <ProMenu
            v-if="resourceMenuItems.length > 0"
            data-testid="rlc-menu"
            isEmbed
            :model="resourceMenuItems"
          />

          <div
            v-if="loading && !errorView"
            class="space-y-1"
          >
            <div
              v-for="i of 3"
              :key="i"
            >
              <Skeleton height="1rem" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </ProPrimePopover>
</template>
