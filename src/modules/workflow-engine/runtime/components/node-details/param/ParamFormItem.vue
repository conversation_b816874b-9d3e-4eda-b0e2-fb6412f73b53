<script setup lang="ts">
import type { IDataObject, INodeProperties, NodeParameterValueType } from 'n8n-workflow'

import Param<PERSON>orm<PERSON>ield from '#wf/components/node-details/param/ParamFormField.vue'
import { useResolvedExpression } from '#wf/composables/useResolvedExpression'
import { NodeParamKey } from '#wf/constants/injection'
import { useEnvironmentsStore } from '#wf/stores/environments.ee.store'
import { useExternalSecretsStore } from '#wf/stores/externalSecrets.ee.store'
import { isValueExpression } from '#wf/utils/nodeTypesUtils'
import { isResourceLocatorValue } from '#wf/utils/typeGuards'

interface Props {
  param: INodeProperties
  path: string
  value?: NodeParameterValueType
  readOnly?: boolean
  isForCredential?: boolean
  additionalExpressionData?: IDataObject
}

const props = defineProps<Props>()

const emit = defineEmits<{
  valueChange: [newValue: Props['value']]
}>()

provide(NodeParamKey, props.param)

const environmentsStore = useEnvironmentsStore()
const externalSecretsStore = useExternalSecretsStore()

const isExpression = computed(() => {
  return isValueExpression(props.param, props.value)
})

const expression = computed(() => {
  if (!isExpression.value) {
    return ''
  }

  return isResourceLocatorValue(props.value) ? props.value.value : props.value
})

const resolvedAdditionalExpressionData = computed(() => {
  return {
    $vars: environmentsStore.variablesAsObject,
    ...(externalSecretsStore.isEnterpriseExternalSecretsEnabled && props.isForCredential
      ? { $secrets: externalSecretsStore.secretsAsObject }
      : {}),
    ...props.additionalExpressionData,
  }
})

const { resolvedExpression } = useResolvedExpression({
  expression,
  additionalData: resolvedAdditionalExpressionData,
  isForCredential: props.isForCredential,
  stringifyObject: props.param.type !== 'multiOptions',
})
</script>

<template>
  <FormItem
    v-slot="{ id }: { id: string }"
    :label="param.displayName"
    labelClass="!font-medium !text-sm"
    :name="param.name"
    :tip="param.description"
  >
    <ParamFormField
      :expressionEvaluated="resolvedExpression"
      :fieldId="id"
      :param="param"
      :path="path"
      :readOnly="readOnly"
      :value="value"
      @valueChange="emit('valueChange', $event)"
    />
  </FormItem>
</template>
