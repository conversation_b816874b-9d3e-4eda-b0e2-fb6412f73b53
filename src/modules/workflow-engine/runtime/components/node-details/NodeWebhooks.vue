<script setup lang="ts">
import { CopyIcon } from 'lucide-vue-next'
import type { INodeTypeDescription, IWebhookDescription } from 'n8n-workflow'

import ParameterGroup from '#wf/components/node-details/param/ParameterGroup.vue'
import { useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { CHAT_TRIGGER_NODE_TYPE, FORM_TRIGGER_NODE_TYPE, PRODUCTION_ONLY_TRIGGER_NODE_TYPES } from '#wf/constants/common'
import { useNDVStore } from '#wf/stores/ndv.store'

const props = defineProps<{
  nodeType: INodeTypeDescription | null
}>()

const ndvStore = useNDVStore()
const activeNode = computed(() => ndvStore.activeNode)

const workflowHelpers = useWorkflowHelpers()

const showUrlFor = ref('test')

const baseText = computed(() => {
  const nodeTypeName = props.nodeType?.name

  switch (nodeTypeName) {
    case CHAT_TRIGGER_NODE_TYPE:
      return {
        toggleTitle: '聊天 URL',
        clickToDisplay: '点击显示表单 URL',
        clickToHide: '点击隐藏表单 URL',
        clickToCopy: '点击复制表单 URL',
        testUrl: '测试 URL',
        productionUrl: '生产 URL',
        copyTitle: '聊天 URL 已复制',
        copyMessage: '通过此 URL 发送的聊天将在激活工作流时触发工作流',
      }

    case FORM_TRIGGER_NODE_TYPE:
      return {
        toggleTitle: '表单 URL',
        clickToDisplay: '点击显示表单 URL',
        clickToHide: '点击隐藏表单 URL',
        clickToCopy: '点击复制表单 URL',
        testUrl: '测试 URL',
        productionUrl: '生产 URL',
        copyTitle: '表单 URL 已复制',
        copyMessage: '通过此 URL 提交的表单将在激活工作流时触发工作流',
      }

    default:
      return {
        toggleTitle: 'Webhook URL',
        clickToDisplay: '点击显示 Webhook URL',
        clickToHide: '点击隐藏 Webhook URL',
        clickToCopy: '点击复制 Webhook URL',
        testUrl: '测试 URL',
        productionUrl: '生产 URL',
        copyTitle: '显示消息',
        copyMessage: undefined,
      }
  }
})

const isProductionOnly = computed(() => {
  return (
    props.nodeType
    && PRODUCTION_ONLY_TRIGGER_NODE_TYPES.includes(props.nodeType.name)
  )
})

const isWebhookMethodVisible = (webhook: IWebhookDescription): boolean => {
  try {
    const method = workflowHelpers.getWebhookExpressionValue(webhook, 'httpMethod', false)

    if (Array.isArray(method) && method.length !== 1) {
      return false
    }
  }
  catch {
    //
  }

  if (typeof webhook.ndvHideMethod === 'string') {
    return !workflowHelpers.getWebhookExpressionValue(webhook, 'ndvHideMethod')
  }

  return !webhook.ndvHideMethod
}

const getWebhookHttpMethod = (webhook: IWebhookDescription): string => {
  const method = workflowHelpers.getWebhookExpressionValue(webhook, 'httpMethod', false)

  if (Array.isArray(method)) {
    return method[0]
  }

  return method
}

const getWebhookUrlDisplay = (webhookData: IWebhookDescription): string => {
  if (activeNode.value) {
    return workflowHelpers.getWebhookUrl(
      webhookData,
      activeNode.value,
      isProductionOnly.value ? 'production' : showUrlFor.value,
    )
  }

  return ''
}

const urlOptions = computed(() => [
  ...(isProductionOnly.value ? [] : [{ label: baseText.value.testUrl, value: 'test' }]),
  {
    label: baseText.value.productionUrl,
    value: 'production',
  },
])

const { $toast } = useNuxtApp()

const copyWebhookUrl = async (webhookData: IWebhookDescription) => {
  const webhookUrl = getWebhookUrlDisplay(webhookData)

  await copyToClipboard(webhookUrl)

  $toast.success({
    summary: baseText.value.copyTitle,
    detail: baseText.value.copyMessage,
  })
}

const webhooksNode = computed(() => {
  if (props.nodeType?.webhooks === undefined) {
    return []
  }

  return props.nodeType.webhooks.filter(
    (webhookData) => webhookData.restartWebhook !== true,
  )
})

const visibleWebhookUrls = computed(() => {
  return webhooksNode.value.filter((webhook) => {
    if (typeof webhook.ndvHideUrl === 'string') {
      return !workflowHelpers.getWebhookExpressionValue(webhook, 'ndvHideUrl')
    }

    return !webhook.ndvHideUrl
  })
})
</script>

<template>
  <div v-if="visibleWebhookUrls.length > 0">
    <ParameterGroup :title="baseText.toggleTitle">
      <div>
        <ProSelectButton
          v-model="showUrlFor"
          :options="urlOptions"
          size="small"
        />
      </div>

      <div class="pt-1">
        <div
          v-for="(webhook, idx) of visibleWebhookUrls"
          :key="idx"
          class="space-y-1"
        >
          <div class="gap-2 text-sm flex-center text-secondary">
            <template v-if="isWebhookMethodVisible(webhook)">
              <div class="whitespace-nowrap rounded bg-emphasis px-1 py-0.5 font-medium">
                {{ getWebhookHttpMethod(webhook) }}
              </div>

              <div class="line-clamp-2 break-all">
                {{ getWebhookUrlDisplay(webhook) }}
              </div>

              <ProBtn
                v-tooltip.top="baseText.clickToCopy"
                class="shrink-0"
                mini
                size="small"
                @click.stop="copyWebhookUrl(webhook)"
              >
                <template #icon="{ size }">
                  <CopyIcon :size="size" />
                </template>
              </ProBtn>
            </template>

            <template v-else>
              <div class="line-clamp-2 break-all">
                {{ getWebhookUrlDisplay(webhook) }}<br>
              </div>
              <ProBtn
                v-tooltip.top="baseText.clickToCopy"
                class="shrink-0"
                mini
                size="small"
                @click.stop="copyWebhookUrl(webhook)"
              >
                <template #icon="{ size }">
                  <CopyIcon :size="size" />
                </template>
              </ProBtn>
            </template>
          </div>
        </div>
      </div>
    </ParameterGroup>

    <Divider />
  </div>
</template>
