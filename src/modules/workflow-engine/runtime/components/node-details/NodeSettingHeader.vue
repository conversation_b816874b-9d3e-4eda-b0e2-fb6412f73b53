<script setup lang="ts">
import { EllipsisIcon, PlayIcon, XIcon } from 'lucide-vue-next'
import type { INodeTypeDescription } from 'n8n-workflow'

import NodeIcon from '#wf/components/NodeIcon.vue'
import { useContextMenu } from '#wf/composables/useContextMenu'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'

import ProBtn from '~/components/pro/btn/ProBtn.vue'
import { TooltipShowDelay } from '~/enums/common'
import { GlobalEvent } from '~/enums/event'
import { emitter } from '~/utils/common'

defineProps<{
  nodeType?: INodeTypeDescription | null
}>()

const emit = defineEmits<{
  nodeNameChange: [value?: string]
}>()

const workflowStore = useWorkflowStore()
const { isWorkflowRunning } = storeToRefs(workflowStore)

const ndvStore = useNDVStore()
const { activeNode } = storeToRefs(ndvStore)

const handleRunNode = () => {
  if (activeNode.value) {
    emitter.emit(GlobalEvent.NodeRun, activeNode.value.id)
  }
}

const handleOpenContextMenu = (event: MouseEvent) => {
  if (activeNode.value) {
    emitter.emit(GlobalEvent.OpenContextMenu, {
      event,
      nodeId: activeNode.value.id,
      source: 'node-setting-header',
    })
  }
}

const contextMenu = useContextMenu()

const isContextMenuOpen = computed(() => {
  const target = contextMenu.target.value

  return contextMenu.isOpen
    && target?.source === 'node-setting-header'
    && target.nodeId === activeNode.value?.id
})
</script>

<template>
  <div
    v-if="activeNode"
    class="p-card-container"
  >
    <div class="gap-2 flex-center">
      <NodeIcon
        :nodeType="nodeType"
        :size="18"
      />

      <div class="min-w-0 flex-1">
        <EditableTitle
          class="max-w-full text-lg font-semibold"
          :modelValue="activeNode.name"
          :tooltipConfig="{ value: '点击重命名' }"
          @update:modelValue="emit('nodeNameChange', $event)"
        />
      </div>

      <div class="ml-auto shrink-0 gap-0.5 flex-center">
        <ProBtn
          :disabled="isWorkflowRunning"
          label="调试此步骤"
          onlyIcon
          severity="contrast"
          size="small"
          :tooltip="{
            showDelay: TooltipShowDelay.Slow,
          }"
          variant="text"
          @click.stop="handleRunNode()"
        >
          <template #icon>
            <PlayIcon :size="14" />
          </template>
        </ProBtn>

        <ProBtn
          :highlight="isContextMenuOpen"
          label="更多操作"
          onlyIcon
          severity="contrast"
          size="small"
          :tooltip="{
            showDelay: TooltipShowDelay.Slow,
          }"
          variant="text"
          @click.stop="handleOpenContextMenu($event)"
        >
          <template #icon>
            <EllipsisIcon :size="14" />
          </template>
        </ProBtn>

        <ProBtn
          onlyIcon
          severity="contrast"
          size="small"
          :tooltip="{
            showDelay: TooltipShowDelay.Slow,
          }"
          variant="text"
          @click.stop="emitter.emit(GlobalEvent.CloseNodeDetailsView)"
        >
          <template #icon>
            <XIcon :size="14" />
          </template>
        </ProBtn>
      </div>
    </div>

    <div
      v-if="activeNode.notes"
      class="mt-2 line-clamp-2 text-sm text-secondary"
    >
      {{ activeNode.notes }}
    </div>
  </div>
</template>
