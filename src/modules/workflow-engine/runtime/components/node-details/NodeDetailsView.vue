<script setup lang="ts">
import type { IRunData, Workflow } from 'n8n-workflow'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'

import NodeSettings from '#wf/components/node-details/NodeSettings.vue'
import InputPanel from '#wf/components/node-run/InputPanel.vue'
import OutputPanel from '#wf/components/node-run/OutputPanel.vue'
import TriggerPanel from '#wf/components/node-run/TriggerPanel.vue'
import { NodeConnectionType } from '#wf/constants/canvas'
import { EnterpriseEditionFeature, EXECUTABLE_TRIGGER_NODE_TYPES, START_NODE_TYPE, STICKY_NODE_TYPE } from '#wf/constants/common'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useSettingsStore } from '#wf/stores/setting.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'

import { GlobalEvent } from '~/enums/event'

interface Props {
  workflowObject: Workflow
  readOnly?: boolean
  isProductionExecutionPreview?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readOnly: false,
  isProductionExecutionPreview: false,
})

const workflowStore = useWorkflowStore()
const settingsStore = useSettingsStore()
const uiStore = useUIStore()

const ndvStore = useNDVStore()
const { activeNode } = storeToRefs(ndvStore)

const handleClose = () => {
  ndvStore.setActiveNodeName(null)
}

const { on } = useEmitter()

onMounted(() => {
  on(GlobalEvent.CloseNodeDetailsView, handleClose)
})

const handleModalVisibleChange = (visible: boolean) => {
  if (!visible) {
    handleClose()
  }
}

const nodeTypesStore = useNodeTypesStore()

const activeNodeType = computed(() => {
  if (activeNode.value) {
    return nodeTypesStore.getNodeType(activeNode.value.type, activeNode.value.typeVersion)
  }

  return null
})

const isTriggerNode = computed(
  () =>
    !!activeNodeType.value
    && (activeNodeType.value.group.includes('trigger')
      || activeNodeType.value.name === START_NODE_TYPE),
)

const showTriggerPanel = computed(() => {
  const override = !!activeNodeType.value?.triggerPanel

  if (typeof activeNodeType.value?.triggerPanel === 'boolean') {
    return override
  }

  const isWebhookBasedNode = !!activeNodeType.value?.webhooks?.length
  const isPollingNode = activeNodeType.value?.polling

  return (
    !props.readOnly && isTriggerNode.value && (isWebhookBasedNode || isPollingNode || override)
  )
})

const workflowExecution = computed(() => workflowStore.getWorkflowExecution)

const workflowRunData = computed(() => {
  if (workflowExecution.value === null) {
    return null
  }

  const executionData = workflowExecution.value.data

  if (executionData?.resultData) {
    return executionData.resultData.runData
  }

  return null
})

const selectedInput = ref<string | undefined>()

const parentNodes = computed(() => {
  if (activeNode.value) {
    return (
      props.workflowObject
        .getParentNodesByDepth(activeNode.value.name, 1)
        .map(({ name }) => name) || []
    )
  }
  else {
    return []
  }
})

const parentNode = computed(() => {
  for (const parentNodeName of parentNodes.value) {
    if (workflowStore?.pinnedWorkflowData?.[parentNodeName]) {
      return parentNodeName
    }

    if (workflowRunData.value?.[parentNodeName]) {
      return parentNodeName
    }
  }

  return parentNodes.value[0]
})

const inputNodeName = computed<string | undefined>(() => {
  const nodeOutputs = activeNode.value && activeNodeType.value
    ? NodeHelpers.getNodeOutputs(props.workflowObject, activeNode.value, activeNodeType.value)
    : []

  const nonMainOutputs = nodeOutputs.filter((output) => {
    if (typeof output === 'string') {
      return output !== NodeConnectionType.Main as UnsafeAny
    }

    return output.type !== NodeConnectionType.Main as UnsafeAny
  })

  const isSubNode = nonMainOutputs.length > 0

  if (isSubNode && activeNode.value) {
    // For sub-nodes, we need to get their connected output node to determine the input
    // because sub-nodes use specialized outputs (e.g. NodeConnectionType.AiTool)
    // instead of the standard Main output type
    const connectedOutputNode = props.workflowObject.getChildNodes(
      activeNode.value.name,
      'ALL_NON_MAIN',
    )?.[0]

    return connectedOutputNode
  }

  return selectedInput.value || parentNode.value
})

const inputNode = computed(() => {
  if (inputNodeName.value) {
    return workflowStore.getNodeByName(inputNodeName.value)
  }

  return null
})

const maxInputRun = computed(() => {
  if (inputNode.value === null || activeNode.value === null) {
    return 0
  }

  const workflowNode = props.workflowObject.getNode(activeNode.value.name)

  if (!workflowNode || !activeNodeType.value) {
    return 0
  }

  const outputs = NodeHelpers.getNodeOutputs(
    props.workflowObject,
    workflowNode,
    activeNodeType.value,
  )

  let node = inputNode.value

  const runData: IRunData | null = workflowRunData.value

  if (outputs.some((output) => output !== NodeConnectionType.Main as UnsafeAny)) {
    node = activeNode.value
  }

  if (!node || !runData || !Object.hasOwn(runData, node.name)) {
    return 0
  }

  if (runData[node.name].length) {
    return runData[node.name].length - 1
  }

  return 0
})

const maxOutputRun = computed(() => {
  if (activeNode.value === null) {
    return 0
  }

  const runData = workflowRunData.value

  if (!runData?.[activeNode.value.name]) {
    return 0
  }

  if (runData[activeNode.value.name].length) {
    return runData[activeNode.value.name].length - 1
  }

  return 0
})

const canLinkRuns = computed(
  () => maxOutputRun.value > 0 && maxOutputRun.value === maxInputRun.value,
)

const isLinkingEnabled = ref(true)

const runInputIndex = ref(-1)
const runOutputIndex = ref(-1)

const outputRun = computed(() =>
  runOutputIndex.value === -1
    ? maxOutputRun.value
    : Math.min(runOutputIndex.value, maxOutputRun.value),
)

const inputRun = computed(() => {
  if (isLinkingEnabled.value && maxOutputRun.value === maxInputRun.value) {
    return outputRun.value
  }

  if (runInputIndex.value === -1) {
    return maxInputRun.value
  }

  return Math.min(runInputIndex.value, maxInputRun.value)
})

const isInputPaneActive = ref(false)
const pushRef = computed(() => ndvStore.pushRef)

const foreignCredentials = computed(() => {
  const credentials = activeNode.value?.credentials
  const usedCredentials = workflowStore.usedCredentials

  const foreignCredentialsArray: string[] = []

  if (credentials && settingsStore.isEnterpriseFeatureEnabled[EnterpriseEditionFeature.Sharing]) {
    Object.values(credentials).forEach((credential) => {
      if (
        credential.id
        && usedCredentials[credential.id]
        && !usedCredentials[credential.id].currentUserHasAccess
      ) {
        foreignCredentialsArray.push(credential.id)
      }
    })
  }

  return foreignCredentialsArray
})

const hasForeignCredential = computed(() => foreignCredentials.value.length > 0)

const linked = computed(() => isLinkingEnabled.value && canLinkRuns.value)
const isOutputPaneActive = ref(false)

const isExecutableTriggerNode = computed(() => {
  if (!activeNodeType.value) {
    return false
  }

  return EXECUTABLE_TRIGGER_NODE_TYPES.includes(activeNodeType.value.name)
})

const isWorkflowRunning = computed(() => uiStore.isActionActive.workflowRunning)
const isExecutionWaitingForWebhook = computed(() => workflowStore.executionWaitingForWebhook)

const avgInputRowHeight = ref(0)
const avgOutputRowHeight = ref(0)

const isActiveStickyNode = computed(
  () => !!ndvStore.activeNode && ndvStore.activeNode.type === STICKY_NODE_TYPE,
)

const blockUi = computed(() => isWorkflowRunning.value || isExecutionWaitingForWebhook.value)

const triggerWaitingWarningEnabled = ref(false)

const handleNodeExecute = () => {
  setTimeout(() => {
    if (!activeNode.value || !isWorkflowRunning.value) {
      return
    }

    triggerWaitingWarningEnabled.value = true
  }, 1000)
}

watch(
  activeNode,
  (node, oldNode) => {
    if (node && node.name !== oldNode?.name && !isActiveStickyNode.value) {
      runInputIndex.value = -1
      runOutputIndex.value = -1
      isLinkingEnabled.value = true
      selectedInput.value = undefined
      triggerWaitingWarningEnabled.value = false
      avgOutputRowHeight.value = 0
      avgInputRowHeight.value = 0

      setTimeout(() => ndvStore.setNDVPushRef(), 0)

      if (!activeNodeType.value) {
        return
      }

      // void externalHooks.run('dataDisplay.nodeTypeChanged', {
      //  nodeSubtitle: nodeHelpers.getNodeSubtitle(node, activeNodeType.value, props.workflowObject),
      // });
    }

    if (window.top && !isActiveStickyNode.value) {
      window.top.postMessage(JSON.stringify({ command: node ? 'openNDV' : 'closeNDV' }), '*')
    }
  },
  { immediate: true },
)
watch(maxOutputRun, () => {
  runOutputIndex.value = -1
})

watch(maxInputRun, () => {
  runInputIndex.value = -1
})

watch(inputNodeName, (nodeName) => {
  setTimeout(() => {
    ndvStore.setInputNodeName(nodeName)
  }, 0)
})

watch(inputRun, (inputRun) => {
  setTimeout(() => {
    ndvStore.setInputRunIndex(inputRun)
  }, 0)
})

const handleInputNodeChange = (value: string) => {
  runInputIndex.value = -1
  isLinkingEnabled.value = true
  selectedInput.value = value
}

const onLinkRunToOutput = () => {
  isLinkingEnabled.value = true
}

const onUnlinkRun = () => {
  runInputIndex.value = runOutputIndex.value
  isLinkingEnabled.value = false
}

const isPairedItemHoveringEnabled = ref(true)

const onSearch = (search: string) => {
  isPairedItemHoveringEnabled.value = !search
}

const onRunOutputIndexChange = (run: number) => {
  runOutputIndex.value = run
}

const onRunInputIndexChange = (run: number) => {
  runInputIndex.value = run

  if (linked.value) {
    runOutputIndex.value = run
  }
}
</script>

<template>
  <Dialog
    appendTo="self"
    closable
    :dismissableMask="false"
    :draggable="false"
    modal
    position="right"
    :pt="{
      mask: { class: '!pointer-events-none !absolute !bg-transparent !p-card-container' },
      root: { class: '!pointer-events-auto !m-0 !h-full !max-h-none !transition' },
      content: { class: '!h-full !p-0' },
    }"
    :showHeader="false"
    :visible="!!activeNode"
    @update:visible="handleModalVisibleChange"
  >
    <div
      class="flex h-full"
      data-testid="wf-element-config"
    >
      <div
        v-if="showTriggerPanel || !isTriggerNode"
        class="flex w-[360px]"
      >
        <div class="h-full flex-1 overflow-hidden">
          <TriggerPanel
            v-if="showTriggerPanel"
            :nodeName="activeNode?.name ?? ''"
            @execute="handleNodeExecute()"
          />

          <InputPanel
            v-else-if="!isTriggerNode"
            :canLinkRuns="canLinkRuns"
            :currentNodeName="inputNodeName"
            :isPaneActive="isInputPaneActive"
            :isProductionExecutionPreview="isProductionExecutionPreview"
            :linkedRuns="linked"
            :pushRef="pushRef"
            :readOnly="readOnly || hasForeignCredential"
            :runIndex="inputRun"
            :workflow="workflowObject"
            @changeInputNode="handleInputNodeChange"
            @execute="handleNodeExecute()"
            @linkRun="onLinkRunToOutput"
            @runChange="onRunInputIndexChange"
            @search="onSearch"
            @unlinkRun="onUnlinkRun()"
          />
        </div>

        <Divider
          class="relative z-50 !m-0"
          layout="vertical"
        />
      </div>

      <div class="w-[360px]">
        <NodeSettings
          v-if="activeNodeType"
          :nodeType="activeNodeType"
        />
      </div>

      <div class="flex w-[360px]">
        <Divider
          class="relative z-50 !m-0"
          layout="vertical"
        />

        <div class="h-full flex-1 overflow-hidden">
          <OutputPanel
            :blockUI="blockUi && isTriggerNode && !isExecutableTriggerNode"
            :canLinkRuns="canLinkRuns"
            data-testid="output-panel"
            :isPaneActive="isOutputPaneActive"
            :isProductionExecutionPreview="isProductionExecutionPreview"
            :isReadOnly="readOnly || hasForeignCredential"
            :linkedRuns="linked"
            :pushRef="pushRef"
            :runIndex="outputRun"
            :workflow="workflowObject"
            @linkRun="onLinkRunToOutput"
            @runChange="onRunOutputIndexChange"
            @search="onSearch"
            @unlinkRun="onUnlinkRun()"
          />
        </div>
      </div>
    </div>
  </Dialog>
</template>
