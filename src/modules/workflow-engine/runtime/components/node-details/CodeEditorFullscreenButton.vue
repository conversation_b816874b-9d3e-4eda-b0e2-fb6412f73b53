<script setup lang="ts">
import { SquareArrowUpRightIcon } from 'lucide-vue-next'
</script>

<template>
  <div
    class="absolute bottom-0 right-0 z-10 justify-center inline-flex-center"
    data-testid="code-editor-fullscreen-button"
  >
    <ProBtn
      label="打开编辑窗口"
      mini
      onlyIcon
      size="small"
    >
      <template #icon="{ size }">
        <SquareArrowUpRightIcon :size="size" />
      </template>
    </ProBtn>
  </div>
</template>
