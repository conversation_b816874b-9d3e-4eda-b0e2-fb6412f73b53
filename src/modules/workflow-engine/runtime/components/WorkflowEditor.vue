<script setup lang="ts">
import { useEventListener } from '@vueuse/core'
import { LoaderIcon } from 'lucide-vue-next'
import type { ExecutionSummary, IConnection } from 'n8n-workflow'
import { jsonParse } from 'n8n-workflow/dist/utils.js'
import { safeParse, string } from 'valibot'

import WorkflowCanvas from '#wf/components/canvas/WorkflowCanvas.vue'
import { useCanvasOperations } from '#wf/composables/useCanvasOperations'
import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { usePushConnection } from '#wf/composables/usePushConnection'
import { useRunWorkflow } from '#wf/composables/useRunWorkflow'
import { useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { CanvasConnectionMode, NodeConnectionType } from '#wf/constants/canvas'
import { NODE_CREATOR_OPEN_SOURCES, STICKY_NODE_TYPE } from '#wf/constants/common'
import { historyBus } from '#wf/models/history'
import { postMessageSchema } from '#wf/schemas/post-message.schema'
import { useCanvasStore } from '#wf/stores/canvas.store'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import { useExecutionsStore } from '#wf/stores/executions.store'
import { useNodeCreatorStore } from '#wf/stores/nodeCreator.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useRootStore } from '#wf/stores/root.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { CanvasConnectionCreateData, CanvasNodeMoveEvent } from '#wf/types/canvas'
import type { AddedNodesAndConnections, INodeUi, IUpdateInformation, IWorkflowDataUpdate, XYPosition } from '#wf/types/interface'
import { createCanvasConnectionHandleString } from '#wf/utils/canvasUtil'
import { isValidNodeConnectionType } from '#wf/utils/typeGuards'

import { GlobalEvent } from '~/enums/event'
import { AppService } from '~/features/space/services/app'
import { useSpaceStore } from '~/features/space/stores/space'

const LazyNodeCreator = defineAsyncComponent(
  () => import('#wf/components/node-creator/NodeCreator.vue'),
)

const LazyNodeDetailsView = defineAsyncComponent(
  () => import('#wf/components/node-details/NodeDetailsView.vue'),
)

const spaceStore = useSpaceStore()
const nodeCreatorStore = useNodeCreatorStore()
const uiStore = useUIStore()
const executionsStore = useExecutionsStore()

const pushConnection = usePushConnection()

const {
  resetWorkspace, initWorkspace,
  editableWorkflow, editableWorkflowObject,
  addNodes, addConnections, deleteNodes, renameNode, copyNodes, duplicateNodes,
  createConnection, deleteConnection,
  setNodeActiveByName, setNodeParameters,
  updateNodePosition, updateNodesPosition,
  revertUpdateNodePosition, revertDeleteNode, revertAddNode, revertCreateConnection, revertDeleteConnection, revertRenameNode, revertToggleNodeDisabled,
  importWorkflowData,
  openExecution,
} = useCanvasOperations()

const workflowStore = useWorkflowStore()
const { isCanvasReadOnly } = storeToRefs(workflowStore)

const canvasStore = useCanvasStore()
const { isLoading } = storeToRefs(canvasStore)

const isInited = ref(false)

const errMessage = ref<string>()

const { $toast } = useNuxtApp()

const workflowId = useRouteParam({
  name: 'workflowId',
  schema: string(),
})

const credentialsStore = useCredentialsStore()
const nodeTypesStore = useNodeTypesStore()
const { allNodeTypes } = storeToRefs(nodeTypesStore)

const rootStore = useRootStore()

const nodeHelpers = useNodeHelpers()
const { runWorkflow } = useRunWorkflow()

const emitPostMessageReady = () => {
  window.parent.postMessage(
    { command: 'n8nReady', version: rootStore.versionCli },
    '*',
  )
}

const updateNodesIssues = () => {
  nodeHelpers.updateNodesInputIssues()
  nodeHelpers.updateNodesCredentialsIssues()
  nodeHelpers.updateNodesParameterIssues()
}

const loadCredentials = async () => {
  let options: { workflowId: string, projectId: string }

  if (workflowId.value) {
    const projectId = spaceStore.activeSpace?.id

    if (projectId) {
      options = { workflowId: workflowId.value, projectId }

      await credentialsStore.fetchAllCredentialsForWorkflow(options)
    }
  }
}

const initWorkspaceForExistingWorkflow = async (wfId: string) => {
  resetWorkspace()

  const workflowData = await workflowStore.fetchWorkflow(wfId)
  initWorkspace(workflowData)
}

const initData = async () => {
  canvasStore.setIsLoading(true)

  const loadPromises = [
    // credentialsStore.fetchAllCredentials(),
    credentialsStore.fetchCredentialTypes(true),
    loadCredentials(),
  ]

  if (allNodeTypes.value.length === 0) {
    loadPromises.push(nodeTypesStore.getNodeTypes())
  }

  try {
    await Promise.all(loadPromises)

    if (workflowId.value) {
      await initWorkspaceForExistingWorkflow(workflowId.value)
    }

    await nextTick(() => {
      // 在初始化完画布之后，立即更新节点的 issue，这样便于用户第一时间发现问题
      updateNodesIssues()
    })

    isInited.value = true
  }
  catch (err) {
    if (err instanceof Error) {
      errMessage.value = err.message
    }
    else {
      errMessage.value = '加载工作流失败'
    }
  }
  finally {
    canvasStore.setIsLoading(false)
  }
}

onMounted(() => {
  initData().then(() => {
    emitPostMessageReady()
  })
})

const checkIfEditingIsAllowed = () => {
  // TODO: check if editing is allowed
  return true
}

const workflowHelpers = useWorkflowHelpers()

const handleSetNodeActivate = (nodeId: GlobalEvents[GlobalEvent.NodeActivate]) => {
  const node = workflowStore.getNodeById(nodeId)

  if (node) {
    if (node.type === STICKY_NODE_TYPE) {
      emitter.emit(GlobalEvent.StickyNoteEditModeOn, node.id)
    }
    else {
      setNodeActiveByName(node.name)
    }
  }
}

const handleClickNodeAdd = (data: GlobalEvents[GlobalEvent.NodeActivateByTrigger]) => {
  nodeCreatorStore.openNodeCreatorForConnectingNode({
    connection: {
      source: data.sourceNodeId,
      sourceHandle: data.sourceHandleId,
    },
    eventSource: NODE_CREATOR_OPEN_SOURCES.PLUS_ENDPOINT,
  })
}

const handleRunNode = (nodeId: GlobalEvents[GlobalEvent.NodeRun]) => {
  const node = workflowStore.getNodeById(nodeId)

  if (node) {
    void runWorkflow({ destinationNode: node.name, source: 'Node.executeNode' })
  }
}

const handleToggleNodeCreator = (options: GlobalEvents[GlobalEvent.ToggleNodeCreator]) => {
  nodeCreatorStore.setNodeCreatorState(options)

  if (!options.createNodeActive && !options.hasAddedNodes) {
    uiStore.resetLastInteractedWith()
  }
}

const handleAddNodesAndConnections = async (
  { nodes, connections }: AddedNodesAndConnections,
  dragAndDrop = false,
  position?: XYPosition,
) => {
  if (!checkIfEditingIsAllowed()) {
    return
  }

  const addedNodes = await addNodes(nodes, {
    dragAndDrop,
    position,
    trackHistory: true,
    telemetry: true,
  })

  const offsetIndex = editableWorkflow.value.nodes.length - nodes.length

  const mappedConnections: CanvasConnectionCreateData[] = connections.map(({ from, to }) => {
    const fromNode = editableWorkflow.value.nodes[offsetIndex + from.nodeIndex]
    const toNode = editableWorkflow.value.nodes[offsetIndex + to.nodeIndex]
    const type = from.type ?? to.type ?? NodeConnectionType.Main

    return {
      source: fromNode.id,
      sourceHandle: createCanvasConnectionHandleString({
        mode: CanvasConnectionMode.Output,
        type: isValidNodeConnectionType(type) ? type : NodeConnectionType.Main,
        index: from.outputIndex ?? 0,
      }),
      target: toNode.id,
      targetHandle: createCanvasConnectionHandleString({
        mode: CanvasConnectionMode.Input,
        type: isValidNodeConnectionType(type) ? type : NodeConnectionType.Main,
        index: to.inputIndex ?? 0,
      }),
      data: {
        source: {
          index: from.outputIndex ?? 0,
          type,
        },
        target: {
          index: to.inputIndex ?? 0,
          type,
        },
      },
    }
  })

  await addConnections(mappedConnections)

  if (addedNodes.length > 0) {
    // selectNodes([addedNodes[addedNodes.length - 1].id])
  }
}

const handleDeleteNodes = (ids: string[]) => {
  deleteNodes(ids)
}

const handleUpdateNodePosition = (data: GlobalEvents[GlobalEvent.NodeMove]) => {
  updateNodePosition(data.nodeId, data.position, { trackHistory: true })
}

const handleUpdateNodesPosition = (events: CanvasNodeMoveEvent[]) => {
  updateNodesPosition(events, { trackHistory: true })
}

const handleRenameNode = (parameterData: IUpdateInformation) => {
  if (parameterData.name === 'name' && parameterData.oldValue) {
    void renameNode(parameterData.oldValue as string, parameterData.value as string)
  }
}

const handleUpdateNodeParameters = (data: GlobalEvents[GlobalEvent.NodeUpdate]) => {
  setNodeParameters(data.nodeId, data.parameters)
}

const handleCreateConnection = (connection: GlobalEvents[GlobalEvent.EdgeCreate]) => {
  createConnection(connection, { trackHistory: true })
}

const handleDeleteConnection = (connection: GlobalEvents[GlobalEvent.EdgeDelete]) => {
  deleteConnection(connection, { trackHistory: true })
}

const handleCopyNodes = async (ids: GlobalEvents[GlobalEvent.NodeCopy]) => {
  await copyNodes(ids)

  $toast.success({
    summary: '节点已复制到剪贴板',
  })
}

const handlePasteNodes = async (plainTextData: GlobalEvents[GlobalEvent.NodePaste]) => {
  const workflowData: IWorkflowDataUpdate | null = jsonParse<IWorkflowDataUpdate | null>(plainTextData, { fallbackValue: null })

  if (workflowData) {
    await importWorkflowData(workflowData, 'paste', false)
  }
}

const handleDuplicateNodes = async (ids: string[]) => {
  if (checkIfEditingIsAllowed()) {
    await duplicateNodes(ids)
  }
}

const handleWorkflowSave = async () => {
  const saved = await workflowHelpers.saveCurrentWorkflow()

  if (saved) {
    const workflowData = await AppService.getWorkflow(workflowStore.workflowId)
    workflowStore.setWorkflow(workflowData)

    $toast.success({
      summary: '保存工作流成功',
    })
  }
}

const onOpenExecution = async (executionId: string) => {
  canvasStore.setIsLoading(true)

  resetWorkspace()
  await initData()

  const data = await openExecution(executionId)

  if (!data) {
    return
  }

  // 使用 nextTick 确保执行数据加载后的工作流节点已完全渲染到 DOM
  // 这样 updateNodesIssues 才能正确计算和显示节点的问题状态
  // 在复杂的执行预览场景中尤其重要，确保问题标识正确显示
  await nextTick(() => {
    updateNodesIssues()
  })

  canvasStore.setIsLoading(false)
  // fitView()
}

const isProductionExecutionPreview = ref(false)
const isExecutionPreview = ref(false)

const canOpenNDV = ref(true)
const hideNodeIssues = ref(false)

const onPostMessageReceived = async (messageEvent: MessageEvent) => {
  try {
    const result = safeParse(postMessageSchema, messageEvent.data)

    if (result.success) {
      const json = result.output

      if (json.command === 'openWorkflow') {
        // try {
        //   await importWorkflowExact(json)
        //   canOpenNDV.value = json.canOpenNDV ?? true
        //   hideNodeIssues.value = json.hideNodeIssues ?? false
        //   isExecutionPreview.value = false
        // }
        // catch (e) {
        //   if (window.top) {
        //     window.top.postMessage(
        //       JSON.stringify({
        //         command: 'error',
        //         message: i18n.baseText('openWorkflow.workflowImportError'),
        //       }),
        //       '*',
        //     )
        //   }

        //   toast.showError(e, i18n.baseText('openWorkflow.workflowImportError'))
        // }
      }
      else if (json.command === 'openExecution') {
        try {
          /**
           * 当工作流视图在预览模式下运行（在 iframe 中）时，无法直接访问主应用的状态管理，
           * 因此所有需要的数据必须通过 postMessage 传递并向下分发给子组件
           */
          isProductionExecutionPreview.value = json.executionMode !== 'manual' && json.executionMode !== 'evaluation'

          await onOpenExecution(String(json.executionId))
          canOpenNDV.value = json.canOpenNDV ?? true
          hideNodeIssues.value = json.hideNodeIssues ?? false
          isExecutionPreview.value = true
        }
        catch (err) {
          if (window.top) {
            window.top.postMessage(
              JSON.stringify({
                command: 'error',
                message: '加载执行问题',
              }),
              '*',
            )
          }

          $toast.error({
            summary: '加载执行问题',
            detail: (err as Error).message,
          })
        }
      }
      else if (json.command === 'setActiveExecution') {
        executionsStore.activeExecution = (await executionsStore.fetchExecution(
          String(json.executionId),
        )) as ExecutionSummary
      }
    }
  }
  catch {
    //
  }
}

useEventListener(window, 'message', onPostMessageReceived)

const { on } = useEmitter()

onMounted(() => {
  on(GlobalEvent.NodeActivate, handleSetNodeActivate)
  on(GlobalEvent.NodeActivateByTrigger, handleClickNodeAdd)
  on(GlobalEvent.NodeRun, handleRunNode)
  on(GlobalEvent.ToggleNodeCreator, handleToggleNodeCreator)

  on(GlobalEvent.WorkflowSave, handleWorkflowSave)

  on(GlobalEvent.NodeAdd, handleAddNodesAndConnections)
  on(GlobalEvent.NodeDelete, handleDeleteNodes)
  on(GlobalEvent.NodeUpdate, handleUpdateNodeParameters)
  on(GlobalEvent.NodeCopy, handleCopyNodes)
  on(GlobalEvent.NodePaste, handlePasteNodes)
  on(GlobalEvent.NodeDuplicate, handleDuplicateNodes)
  on(GlobalEvent.NodeMove, handleUpdateNodePosition)
  on(GlobalEvent.NodeRename, handleRenameNode)
  on(GlobalEvent.EdgeCreate, handleCreateConnection)
  on(GlobalEvent.EdgeDelete, handleDeleteConnection)
})

const onRevertNodePosition = ({ nodeName, position }: { nodeName: string, position: XYPosition }) => {
  revertUpdateNodePosition(nodeName, { x: position[0], y: position[1] })
}

const onRevertDeleteNode = ({ node }: { node: INodeUi }) => {
  revertDeleteNode(node)
}

const onRevertAddNode = async ({ node }: { node: INodeUi }) => {
  await revertAddNode(node.name)
}

const onRevertCreateConnection = ({ connection }: { connection: [IConnection, IConnection] }) => {
  revertCreateConnection(connection)
}

const onRevertDeleteConnection = ({ connection }: { connection: [IConnection, IConnection] }) => {
  revertDeleteConnection(connection)
}

const onRevertRenameNode = async ({
  currentName,
  newName,
}: {
  currentName: string
  newName: string
}) => {
  await revertRenameNode(currentName, newName)
}

const onRevertToggleNodeDisabled = ({ nodeName }: { nodeName: string }) => {
  revertToggleNodeDisabled(nodeName)
}

const addUndoRedoEventBindings = () => {
  historyBus.on('nodeMove', onRevertNodePosition)
  historyBus.on('revertAddNode', onRevertAddNode)
  historyBus.on('revertRemoveNode', onRevertDeleteNode)
  historyBus.on('revertAddConnection', onRevertCreateConnection)
  historyBus.on('revertRemoveConnection', onRevertDeleteConnection)
  historyBus.on('revertRenameNode', onRevertRenameNode)
  historyBus.on('enableNodeToggle', onRevertToggleNodeDisabled)
}

const removeUndoRedoEventBindings = () => {
  historyBus.off('nodeMove', onRevertNodePosition)
  historyBus.off('revertAddNode', onRevertAddNode)
  historyBus.off('revertRemoveNode', onRevertDeleteNode)
  historyBus.off('revertAddConnection', onRevertCreateConnection)
  historyBus.off('revertRemoveConnection', onRevertDeleteConnection)
  historyBus.off('revertRenameNode', onRevertRenameNode)
  historyBus.off('enableNodeToggle', onRevertToggleNodeDisabled)
}

onMounted(() => {
  addUndoRedoEventBindings()
  pushConnection.initialize()
})

onBeforeUnmount(() => {
  removeUndoRedoEventBindings()
  pushConnection.terminate()
})
</script>

<template>
  <div
    class="relative size-full"
    data-testid="wf-editor"
  >
    <div
      v-if="isLoading || !isInited"
      class="absolute inset-0 z-10 justify-center bg-canvas flex-center"
    >
      <div class="flex-col flex-center">
        <LoaderIcon
          class="animate-spin"
          :size="24"
        />

        <div class="pt-4 text-sm">
          正在加载工作流
        </div>
      </div>
    </div>

    <div
      v-else-if="errMessage"
      class="absolute inset-0 z-10 justify-center bg-danger-50 flex-center"
    >
      <div class="flex-col gap-2 flex-center">
        <div class="text-xl font-bold text-danger-500">
          加载工作流失败
        </div>

        <div>
          {{ errMessage }}
        </div>
      </div>
    </div>

    <div
      v-else
      class="size-full"
      data-testid="wf-editor-content"
    >
      <div
        class="size-full"
        data-testid="wf-editor-main"
      >
        <WorkflowCanvas
          v-if="editableWorkflow && editableWorkflowObject"
          :readOnly="isCanvasReadOnly"
          :workflow="editableWorkflow"
          :workflowObject="editableWorkflowObject"
          @update:nodes:position="handleUpdateNodesPosition"
        >
          <template #nodeCreator>
            <LazyNodeCreator v-if="!isCanvasReadOnly" />
          </template>

          <template #nodeDetailsView>
            <LazyNodeDetailsView
              :readOnly="isCanvasReadOnly"
              :workflowObject="editableWorkflowObject"
            />
          </template>
        </WorkflowCanvas>
      </div>
    </div>
  </div>
</template>
