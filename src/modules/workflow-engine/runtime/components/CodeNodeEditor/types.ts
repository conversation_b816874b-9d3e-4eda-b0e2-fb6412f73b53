import type { DefineComponent } from 'vue'

import type { EditorView } from '@codemirror/view'
import type { Node } from 'estree'
import type { CodeExecutionMode, CodeNodeEditorLanguage, Workflow } from 'n8n-workflow'

export type CodeNodeEditorMixin = InstanceType<
  DefineComponent & {
    editor: EditorView | null
    mode: CodeExecutionMode
    language: CodeNodeEditorLanguage
    getCurrentWorkflow(): Workflow
  }
>

export type RangeNode = Node & { range: [number, number] }
