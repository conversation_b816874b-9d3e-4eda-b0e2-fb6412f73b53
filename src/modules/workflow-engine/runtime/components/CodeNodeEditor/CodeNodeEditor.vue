<script setup lang="ts">
import type { CodeExecutionMode, CodeNodeEditorLanguage } from 'n8n-workflow'

// import { format } from 'prettier'
// import jsParser from 'prettier/plugins/babel'
// import * as estree from 'prettier/plugins/estree'
import DraggableTarget from '#wf/components/DraggableTarget.vue'
import { useCodeEditor } from '#wf/composables/useCodeEditor'
import { useSettingsStore } from '#wf/stores/setting.store'

// import { dropInCodeEditor } from '@/plugins/codemirror/dragAndDrop'
// import AskAI from './AskAI/AskAI.vue'
import { CODE_PLACEHOLDERS } from './constants'
import { useLinter } from './linter'

type Props = {
  mode: CodeExecutionMode
  modelValue: string
  aiButtonEnabled?: boolean
  fillParent?: boolean
  language?: CodeNodeEditorLanguage
  isReadOnly?: boolean
  rows?: number
  id?: string
}

const props = withDefaults(defineProps<Props>(), {
  aiButtonEnabled: false,
  fillParent: false,
  language: 'javaScript',
  isReadOnly: false,
  rows: 4,
  id: () => crypto.randomUUID(),
})
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const activeTab = ref('code')
const isLoadingAIResponse = ref(false)
const codeNodeEditorRef = ref<HTMLDivElement>()
const codeNodeEditorContainerRef = ref<HTMLDivElement>()
const hasManualChanges = ref(false)

const settingsStore = useSettingsStore()

const linter = useLinter(
  () => props.mode,
  () => props.language,
)

const extensions = computed(() => [linter.value])

const placeholder = computed(() => CODE_PLACEHOLDERS[props.language]?.[props.mode] ?? '')

const dragAndDropEnabled = computed(() => {
  return !props.isReadOnly
})

const { highlightLine, readEditorValue, editor } = useCodeEditor({
  id: props.id,
  editorRef: codeNodeEditorRef,
  language: () => props.language,
  languageParams: () => ({ mode: props.mode }),
  editorValue: () => props.modelValue,
  placeholder,
  extensions,
  isReadOnly: () => props.isReadOnly,
  theme: {
    maxHeight: props.fillParent ? '100%' : '40vh',
    minHeight: '20vh',
    rows: props.rows,
  },
  onChange: onEditorUpdate,
})

function onEditorUpdate() {
  hasManualChanges.value = true
  emit('update:modelValue', readEditorValue())
}

onMounted(() => {
  if (!props.isReadOnly) {
    // codeNodeEditorEventBus.on('highlightLine', highlightLine)
  }

  // codeNodeEditorEventBus.on('codeDiffApplied', diffApplied)

  if (!props.modelValue) {
    emit('update:modelValue', placeholder.value)
  }
})

onBeforeUnmount(() => {
  // codeNodeEditorEventBus.off('codeDiffApplied', diffApplied)

  if (!props.isReadOnly) {
    // codeNodeEditorEventBus.off('highlightLine', highlightLine)
  }
})

const askAiEnabled = computed(() => {
  return settingsStore.isAskAiEnabled && props.language === 'javaScript'
})

watch([() => props.language, () => props.mode], (_, [prevLanguage, prevMode]) => {
  if (readEditorValue().trim() === CODE_PLACEHOLDERS[prevLanguage]?.[prevMode]) {
    emit('update:modelValue', placeholder.value)
  }
})

const { $confirm } = useNuxtApp()

// Handle tab switching with loading state check
watch(activeTab, async (newTab, oldTab) => {
  if (oldTab === 'ask-ai' && isLoadingAIResponse.value) {
    // Prevent switching if AI is loading
    const shouldLeave = await new Promise((resolve) => {
      $confirm.dialog({
        message: '确定要切换到其他标签吗？',
        acceptLabel: '切换选项卡',
        accept: () => resolve(true),
        reject: () => resolve(false),
      })
    })

    if (!shouldLeave) {
      // Revert to previous tab
      nextTick(() => {
        activeTab.value = oldTab
      })
    }
  }
})

async function onAiReplaceCode(code: string) {
  // const formattedCode = await format(code, {
  //   parser: 'babel',
  //   plugins: [jsParser, estree],
  // })

  // emit('update:modelValue', formattedCode)

  // activeTab.value = 'code'
  // hasManualChanges.value = false
}

// function diffApplied() {
//   codeNodeEditorContainerRef.value?.classList.add('flash-editor')
//   codeNodeEditorContainerRef.value?.addEventListener('animationend', () => {
//     codeNodeEditorContainerRef.value?.classList.remove('flash-editor')
//   })
// }

function onAiLoadStart() {
  isLoadingAIResponse.value = true
}

function onAiLoadEnd() {
  isLoadingAIResponse.value = false
}

async function onDrop(value: string, event: MouseEvent) {
  if (!editor.value) {
    return
  }

  const valueToInsert = props.mode === 'runOnceForAllItems'
    ? value.replace('$json', '$input.first().json').replace(/\$\((.*)\)\.item/, '$($1).first()')
    : value

  // await dropInCodeEditor(toRaw(editor.value), event, valueToInsert)
}
</script>

<template>
  <div
    ref="codeNodeEditorContainerRef"
    class="relative overflow-hidden rounded-[var(--p-inputtext-border-radius)] border border-[var(--p-inputtext-border-color)]"
    data-testid="code-node-editor"
  >
    <Tabs
      v-if="askAiEnabled"
      v-model:value="activeTab"
      :class="$style.tabs"
    >
      <TabList>
        <Tab value="code">
          代码
        </Tab>
        <Tab value="ask-ai">
          ✨ 询问 AI
        </Tab>
      </TabList>

      <TabPanels>
        <TabPanel
          :class="$style.fillHeight"
          data-test-id="code-node-tab-code"
          value="code"
        >
          <DraggableTarget
            :class="$style.fillHeight"
            :disabled="!dragAndDropEnabled"
            type="mapping"
            @drop="onDrop"
          >
            <template #default="{ activeDrop, droppable }">
              <div
                ref="codeNodeEditorRef"
                :class="[
                  $style.editorInput,
                  $style.fillHeight,
                  { [$style.activeDrop]: activeDrop, [$style.droppable]: droppable },
                ]"
                data-testid="code-node-editor-tabs"
              />
            </template>
          </DraggableTarget>

          <slot name="suffix" />
        </TabPanel>

        <TabPanel
          data-test-id="code-node-tab-ai"
          value="ask-ai"
        >
          <!-- Key the AskAI tab to make sure it re-mounts when changing tabs -->
          <!-- <AskAI
            :key="activeTab"
            :hasChanges="hasManualChanges"
            @finishedLoading="onAiLoadEnd"
            @replaceCode="onAiReplaceCode"
            @startedLoading="onAiLoadStart"
          /> -->
        </TabPanel>
      </TabPanels>
    </Tabs>

    <!-- If AskAi not enabled, there's no point in rendering tabs -->
    <div
      v-else
      :class="$style.fillHeight"
    >
      <DraggableTarget
        :class="$style.fillHeight"
        :disabled="!dragAndDropEnabled"
        type="mapping"
        @drop="onDrop"
      >
        <template #default="{ activeDrop, droppable }">
          <div
            ref="codeNodeEditorRef"
            :class="[
              $style.fillHeight,
              $style.editorInput,
              { [$style.activeDrop]: activeDrop, [$style.droppable]: droppable },
            ]"
          />
        </template>
      </DraggableTarget>
      <slot name="suffix" />
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.p-tabs) {
  .cm-editor {
    border: 0;
  }
}

@keyframes backgroundAnimation {
  0% {
    background-color: none;
  }
  30% {
    background-color: rgba(41, 163, 102, 0.1);
  }
  100% {
    background-color: none;
  }
}

.flash-editor {
  :deep(.cm-editor),
  :deep(.cm-gutter) {
    animation: backgroundAnimation 1.5s ease-in-out;
  }
}
</style>

<style lang="scss" module>
.tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.fillHeight {
  height: 100%;
}

.editorInput.droppable {
  :global(.cm-editor) {
    border-color: var(--color-ndv-droppable-parameter);
    border-style: dashed;
    border-width: 1.5px;
  }
}

.editorInput.activeDrop {
  :global(.cm-editor) {
    border-color: var(--color-success);
    border-style: solid;
    cursor: grabbing;
    border-width: 1px;
  }
}
</style>
