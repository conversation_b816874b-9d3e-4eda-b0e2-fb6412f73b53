import type { Completion, CompletionContext, CompletionResult } from '@codemirror/autocomplete'

import { addInfoRenderer, addVarType, escape } from '../utils'

export function useExecutionCompletions() {
  /**
   * Complete `$execution.` to `.id .mode .resumeUrl .resumeFormUrl`
   */
  const executionCompletions = (
    context: CompletionContext,
    matcher = '$execution',
  ): CompletionResult | null => {
    const pattern = new RegExp(`${escape(matcher)}\..*`)

    const preCursor = context.matchBefore(pattern)

    if (!preCursor || (preCursor.from === preCursor.to && !context.explicit)) {
      return null
    }

    const options: Completion[] = [
      {
        label: `${matcher}.id`,
        info: '当前执行的 ID',
      },
      {
        label: `${matcher}.mode`,
        info: '执行是如何触发的：\'test\' 或 \'production\'',
      },
      {
        label: `${matcher}.resumeUrl`,
        info: '当使用 \'wait\' 节点等待 Webhook 时使用。恢复执行的 Webhook 调用',
      },
      {
        label: `${matcher}.resumeFormUrl`,
        info: '当使用 \'wait\' 节点等待表单提交时使用。用于恢复执行的表单提交的 URL',
      },
      {
        label: `${matcher}.customData.set("key", "value")`,
        info: '在指定的键下存储自定义执行数据。使用此数据轻松过滤执行。',
      },
      {
        label: `${matcher}.customData.get("key")`,
        info: '返回存储在给定键下的自定义执行数据。',
      },
      {
        label: `${matcher}.customData.setAll({})`,
        info: '设置多个键值对的自定义数据。使用此数据轻松过滤执行。',
      },
      {
        label: `${matcher}.customData.getAll()`,
        info: '返回当前执行中设置的所有键值对的自定义数据。',
      },
    ]

    return {
      from: preCursor.from,
      options: options.map(addVarType).map(addInfoRenderer),
    }
  }

  return { executionCompletions }
}
