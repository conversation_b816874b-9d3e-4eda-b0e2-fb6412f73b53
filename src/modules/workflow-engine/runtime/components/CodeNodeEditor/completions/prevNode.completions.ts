import type { Completion, CompletionContext, CompletionResult } from '@codemirror/autocomplete'

import { addVarType } from '../utils'

const DEFAULT_MATCHER = '$prevNode'

const escape = (str: string) => str.replace('$', '\\$')

export function usePrevNodeCompletions(matcher = DEFAULT_MATCHER) {
  /**
   * Complete `$prevNode.` to `.name .outputIndex .runIndex`.
   */
  const prevNodeCompletions = (context: CompletionContext): CompletionResult | null => {
    const pattern = new RegExp(`${escape(matcher)}..*`)

    const preCursor = context.matchBefore(pattern)

    if (!preCursor || (preCursor.from === preCursor.to && !context.explicit)) {
      return null
    }

    const options: Completion[] = [
      {
        label: `${matcher}.name`,
        info: '为此运行提供输入数据的节点的名称',
      },
      {
        label: `${matcher}.outputIndex`,
        info: '为此运行提供输入数据的节点的输出连接器',
      },
      {
        label: `${matcher}.runIndex`,
        info: '为当前节点提供输入数据的节点的运行',
      },
    ]

    return {
      from: preCursor.from,
      options: options.map(addVarType),
    }
  }

  return { prevNodeCompletions }
}
