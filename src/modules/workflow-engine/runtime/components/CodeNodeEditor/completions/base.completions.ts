import type { Completion, CompletionContext, CompletionResult } from '@codemirror/autocomplete'

import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { INodeUi } from '#wf/types/interface'
import { escapeMappingString } from '#wf/utils/mappingUtils'

import { NODE_TYPES_EXCLUDED_FROM_AUTOCOMPLETION } from '../constants'
import { addInfoRenderer, addVarType } from '../utils'

function getAutoCompletableNodeNames(nodes: INodeUi[]) {
  return nodes
    .filter((node: INodeUi) => !NODE_TYPES_EXCLUDED_FROM_AUTOCOMPLETION.includes(node.type))
    .map((node: INodeUi) => node.name)
}

export function useBaseCompletions(
  mode: 'runOnceForEachItem' | 'runOnceForAllItems',
  language: string,
) {
  const workflowsStore = useWorkflowStore()

  const itemCompletions = (context: CompletionContext): CompletionResult | null => {
    const preCursor = context.matchBefore(/i\w*/)

    if (!preCursor || (preCursor.from === preCursor.to && !context.explicit)) {
      return null
    }

    const options: Completion[] = []

    if (mode === 'runOnceForEachItem') {
      options.push({
        label: 'item',
        info: '生成当前项的项',
      })
    }
    else if (mode === 'runOnceForAllItems') {
      options.push({
        label: 'items',
        info: '@:_reusableBaseText.codeNodeEditor.completer.all',
      })
    }

    return {
      from: preCursor.from,
      options,
    }
  }

  /**
   * - Complete `$` to `$execution $input $prevNode $runIndex $workflow $now $today
   * $jmespath $ifEmpt $('nodeName')` in both modes.
   * - Complete `$` to `$json $binary $itemIndex` in single-item mode.
   */
  const baseCompletions = (context: CompletionContext): CompletionResult | null => {
    const prefix = language === 'python' ? '_' : '$'
    const preCursor = context.matchBefore(new RegExp(`\\${prefix}\\w*`))

    if (!preCursor || (preCursor.from === preCursor.to && !context.explicit)) {
      return null
    }

    const TOP_LEVEL_COMPLETIONS_IN_BOTH_MODES: Completion[] = [
      {
        label: `${prefix}execution`,
        info: '关于当前执行的信息',
      },
      {
        label: `${prefix}ifEmpty()`,
        info: '检查第一个参数是否为空，如果是，则返回第二个参数。否则返回第一个参数。以下情况被视为空：null/undefined 值、空字符串、空数组、没有键的对象。',
      },
      { label: `${prefix}input`, info: '此节点的输入数据' },
      {
        label: `${prefix}prevNode`,
        info: '为此运行提供输入数据的节点',
      },
      {
        label: `${prefix}workflow`,
        info: '关于工作流的信息',
      },
      {
        label: `${prefix}vars`,
        info: '在您的实例中定义的变量',
      },
      {
        label: `${prefix}now`,
        info: '当前时间戳（作为 Luxon 对象）',
      },
      {
        label: `${prefix}today`,
        info: '表示当天的时间戳（午夜时分，作为 Luxon 对象）',
      },
      {
        label: `${prefix}jmespath()`,
        info: '评估 JMESPath 表达式',
      },
      {
        label: `${prefix}runIndex`,
        info: '此节点的当前运行的索引',
      },
      {
        label: `${prefix}nodeVersion`,
        info: '当前节点的版本（显示在节点设置面板底部）',
      },
    ]

    const options: Completion[] = TOP_LEVEL_COMPLETIONS_IN_BOTH_MODES.map(addVarType)

    options.push(
      ...getAutoCompletableNodeNames(workflowsStore.workflow.nodes).map((nodeName) => {
        return {
          label: `${prefix}('${escapeMappingString(nodeName)}')`,
          type: 'variable',
          info: `${nodeName} 节点的输出数据`,
        }
      }),
    )

    if (mode === 'runOnceForEachItem') {
      const TOP_LEVEL_COMPLETIONS_IN_SINGLE_ITEM_MODE = [
        { label: `${prefix}json` },
        { label: `${prefix}binary` },
        {
          label: `${prefix}itemIndex`,
          info: '当前项在项目列表中的位置',
        },
      ]

      options.push(...TOP_LEVEL_COMPLETIONS_IN_SINGLE_ITEM_MODE.map(addVarType))
    }

    return {
      from: preCursor.from,
      options: options.map(addInfoRenderer),
    }
  }

  /**
   * Complete `$(` to `$('nodeName')`.
   */
  const nodeSelectorCompletions = (context: CompletionContext): CompletionResult | null => {
    const prefix = language === 'python' ? '_' : '$'
    const preCursor = context.matchBefore(new RegExp(`\\${prefix}\\(.*`))

    if (!preCursor || (preCursor.from === preCursor.to && !context.explicit)) {
      return null
    }

    const options: Completion[] = getAutoCompletableNodeNames(workflowsStore.workflow.nodes).map(
      (nodeName) => {
        return {
          label: `${prefix}('${escapeMappingString(nodeName)}')`,
          type: 'variable',
          info: `${nodeName} 节点的输出数据`,
        }
      },
    )

    return {
      from: preCursor.from,
      options,
    }
  }

  return {
    itemCompletions,
    baseCompletions,
    nodeSelectorCompletions,
  }
}
