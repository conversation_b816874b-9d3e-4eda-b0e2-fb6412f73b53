import type { Completion, CompletionContext, CompletionResult } from '@codemirror/autocomplete'

import { addVarType } from '../utils'

const escape = (str: string) => str.replace('$', '\\$')

export function useWorkflowCompletions() {
  /**
   * Complete `$workflow.` to `.id .name .active`.
   */
  const workflowCompletions = (
    context: CompletionContext,
    matcher = '$workflow',
  ): CompletionResult | null => {
    const pattern = new RegExp(`${escape(matcher)}\..*`)

    const preCursor = context.matchBefore(pattern)

    if (!preCursor || (preCursor.from === preCursor.to && !context.explicit)) {
      return null
    }

    const options: Completion[] = [
      {
        label: `${matcher}.id`,
        info: '工作流的 ID',
      },
      {
        label: `${matcher}.name`,
        info: '工作流的名称',
      },
      {
        label: `${matcher}.active`,
        info: '工作流是否处于活动状态（布尔值）',
      },
    ]

    return {
      from: preCursor.from,
      options: options.map(addVarType),
    }
  }

  return { workflowCompletions }
}
