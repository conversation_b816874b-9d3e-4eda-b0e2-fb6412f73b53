import { type MaybeRefOrGetter, toValue } from 'vue'

import type { Completion, CompletionContext, CompletionResult } from '@codemirror/autocomplete'
import type { CodeExecutionMode } from 'n8n-workflow'

import { escape } from '../utils'

export function useItemIndexCompletions(mode: MaybeRefOrGetter<CodeExecutionMode>) {
  /**
   * - Complete `$input.` to `.first() .last() .all() .itemMatching()` in all-items mode.
   * - Complete `$input.` to `.item` in single-item mode.
   */
  const inputCompletions = (
    context: CompletionContext,
    matcher = '$input',
  ): CompletionResult | null => {
    const pattern = new RegExp(`${escape(matcher)}..*`)

    const preCursor = context.matchBefore(pattern)

    if (!preCursor || (preCursor.from === preCursor.to && !context.explicit)) {
      return null
    }

    const options: Completion[] = []

    if (toValue(mode) === 'runOnceForAllItems') {
      options.push(
        {
          label: `${matcher}.first()`,
          type: 'function',
          info: '@:_reusableBaseText.codeNodeEditor.completer.first',
        },
        {
          label: `${matcher}.last()`,
          type: 'function',
          info: '@:_reusableBaseText.codeNodeEditor.completer.last',
        },
        {
          label: `${matcher}.all()`,
          type: 'function',
          info: '@:_reusableBaseText.codeNodeEditor.completer.all',
        },
        {
          label: `${matcher}.itemMatching()`,
          type: 'function',
          info: '@:_reusableBaseText.codeNodeEditor.completer.itemMatching',
        },
      )
    }

    if (toValue(mode) === 'runOnceForEachItem') {
      options.push({
        label: `${matcher}.item`,
        type: 'variable',
        info: '生成当前项的项',
      })
    }

    return {
      from: preCursor.from,
      options,
    }
  }

  /**
   * - Complete `$('nodeName').` to `.params .context` in both modes.
   * - Complete `$('nodeName').` to `.first() .last() .all() .itemMatching()` in all-items mode.
   * - Complete `$('nodeName').` to `.item` in single-item mode.
   */
  const selectorCompletions = (context: CompletionContext, matcher: string | null = null) => {
    const pattern = matcher === null
      ? /\$\((?<quotedNodeName>['"][\S\s]+['"])\)\..*/ // $('nodeName').
      : new RegExp(`${matcher}..*`)

    const preCursor = context.matchBefore(pattern)

    if (!preCursor || (preCursor.from === preCursor.to && !context.explicit)) {
      return null
    }

    const match = preCursor.text.match(pattern)

    let replacementBase = ''

    if (matcher === null && match?.groups?.quotedNodeName) {
      replacementBase = `$(${match.groups.quotedNodeName})`
    }
    else if (matcher) {
      replacementBase = matcher
    }

    const options: Completion[] = [
      {
        label: `${replacementBase}.params`,
        type: 'variable',
        info: '节点的参数',
      },
      {
        label: `${replacementBase}.context`,
        type: 'variable',
        info: '有关节点的额外数据',
      },
    ]

    if (toValue(mode) === 'runOnceForAllItems') {
      options.push(
        {
          label: `${replacementBase}.first()`,
          type: 'function',
          info: '@:_reusableBaseText.codeNodeEditor.completer.first',
        },
        {
          label: `${replacementBase}.last()`,
          type: 'function',
          info: '@:_reusableBaseText.codeNodeEditor.completer.last',
        },
        {
          label: `${replacementBase}.all()`,
          type: 'function',
          info: '@:_reusableBaseText.codeNodeEditor.completer.all',
        },
        {
          label: `${replacementBase}.itemMatching()`,
          type: 'function',
          info: '@:_reusableBaseText.codeNodeEditor.completer.itemMatching',
        },
      )
    }

    if (toValue(mode) === 'runOnceForEachItem') {
      options.push({
        label: `${replacementBase}.item`,
        type: 'variable',
        info: '生成当前项目的项目',
      })
    }

    return {
      from: preCursor.from,
      options,
    }
  }

  return {
    inputCompletions,
    selectorCompletions,
  }
}
