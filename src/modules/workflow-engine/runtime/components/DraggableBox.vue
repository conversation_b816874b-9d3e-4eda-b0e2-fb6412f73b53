<script setup lang="ts">
import type { StyleValue } from 'vue'

import { useNDVStore } from '#wf/stores/ndv.store'
import type { XYPosition } from '#wf/types/interface'
import { isPresent } from '#wf/utils/typesUtils'

interface Props {
  type: string
  data?: string
  tag?: string
  targetDataKey?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), { tag: 'div', disabled: false })

const emit = defineEmits<{
  drag: [value: XYPosition]
  dragstart: [value: HTMLElement]
  dragend: [value: HTMLElement]
}>()

const isDragging = ref(false)
const draggingElement = ref<HTMLElement>()
const draggablePosition = ref<XYPosition>([0, 0])
const animationFrameId = ref<number>()
const ndvStore = useNDVStore()

const draggableStyle = computed<StyleValue>(() => ({
  transform: `translate(${draggablePosition.value[0]}px, ${draggablePosition.value[1]}px)`,
}))

const canDrop = computed(() => ndvStore.canDraggableDrop)

const stickyPosition = computed(() => ndvStore.draggableStickyPos)

const onDrag = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()

  if (props.disabled) {
    return
  }

  if (!isDragging.value && draggingElement.value) {
    isDragging.value = true

    const data = props.targetDataKey ? draggingElement.value.dataset.value : (props.data ?? '')

    ndvStore.draggableStartDragging({
      type: props.type,
      data: data ?? '',
      dimensions: draggingElement.value?.getBoundingClientRect() ?? null,
    })

    emit('dragstart', draggingElement.value)
    document.body.style.cursor = 'grabbing'
  }

  animationFrameId.value = window.requestAnimationFrame(() => {
    if (canDrop.value && stickyPosition.value) {
      draggablePosition.value = stickyPosition.value
    }
    else {
      draggablePosition.value = [event.pageX, event.pageY]
    }

    emit('drag', draggablePosition.value)
  })
}

const onDragEnd = () => {
  if (props.disabled) {
    return
  }

  document.body.style.cursor = 'unset'
  window.removeEventListener('mousemove', onDrag)
  window.removeEventListener('mouseup', onDragEnd)

  if (isPresent(animationFrameId.value)) {
    window.cancelAnimationFrame(animationFrameId.value)
  }

  setTimeout(() => {
    if (draggingElement.value) {
      emit('dragend', draggingElement.value)
    }

    isDragging.value = false
    draggingElement.value = undefined
    ndvStore.draggableStopDragging()
  }, 0)
}

const onDragStart = (event: MouseEvent) => {
  if (props.disabled) {
    return
  }

  draggingElement.value = event.target as HTMLElement

  if (props.targetDataKey && draggingElement.value.dataset?.target !== props.targetDataKey) {
    draggingElement.value = draggingElement.value.closest(
      `[data-target="${props.targetDataKey}"]`,
    ) as HTMLElement
  }

  if (props.targetDataKey && draggingElement.value?.dataset?.target !== props.targetDataKey) {
    return
  }

  event.preventDefault()
  event.stopPropagation()

  isDragging.value = false
  draggablePosition.value = [event.pageX, event.pageY]

  window.addEventListener('mousemove', onDrag)
  window.addEventListener('mouseup', onDragEnd)

  // blur so that any focused inputs update value
  const activeElement = document.activeElement as HTMLElement

  if (activeElement) {
    activeElement.blur()
  }
}
</script>

<template>
  <Component
    :is="tag"
    ref="wrapper"
    :class="{ 'visible cursor-grabbing': isDragging }"
    @mousedown="onDragStart"
  >
    <slot :isDragging="isDragging" />

    <Teleport to="body">
      <div
        v-show="isDragging"
        ref="draggable"
        class="pointer-events-none fixed left-0 top-0 z-[9999999]"
        :style="draggableStyle"
      >
        <slot
          :canDrop="canDrop"
          :el="draggingElement"
          name="preview"
        />
      </div>
    </Teleport>
  </Component>
</template>
