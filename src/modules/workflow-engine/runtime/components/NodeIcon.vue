<script setup lang="ts">
import type { INodeTypeDescription } from 'n8n-workflow'

import BaseNodeIcon, { type NodeIconType } from '#wf/components/ui/BaseNodeIcon.vue'
import type { IVersionNode, SimplifiedNodeType } from '#wf/types/interface'
import { getNodeIcon, getNodeIconColor, getNodeIconUrl } from '#wf/utils/nodeTypesUtils'

interface NodeIconSource {
  path?: string
  fileBuffer?: string
  icon?: string
}

interface Props {
  nodeType?: INodeTypeDescription | SimplifiedNodeType | IVersionNode | null
  nodeName?: string
  showTooltip?: boolean
  size?: number
  colorDefault?: string
  disabled?: boolean
}

const props = defineProps<Props>()

const xNodeType = toRef(props, 'nodeType')
const xNodeName = toRef(props, 'nodeName')
const xColorDefault = toRef(props, 'colorDefault')

const { themeMode } = useTheme()

const color = computed(() => getNodeIconColor(xNodeType.value) ?? xColorDefault.value ?? '')

const iconType = computed<NodeIconType>(() => {
  if (xNodeType.value) {
    if (xNodeType.value.iconUrl) {
      return 'file'
    }

    if ('iconData' in xNodeType.value && xNodeType.value.iconData) {
      return xNodeType.value.iconData.type as NodeIconType
    }

    if (xNodeType.value.icon) {
      const icon = getNodeIcon(xNodeType.value, themeMode.value)

      return icon && icon.split(':')[0] === 'file' ? 'file' : 'icon'
    }
  }

  return 'unknown'
})

const iconSource = computed<NodeIconSource>(() => {
  if (xNodeType.value) {
    // If node type has icon data, use it
    if ('iconData' in xNodeType.value && xNodeType.value.iconData) {
      return {
        icon: xNodeType.value.iconData.icon,
        fileBuffer: xNodeType.value.iconData.fileBuffer,
      }
    }

    const iconUrl = getNodeIconUrl(xNodeType.value, themeMode.value)

    if (iconUrl) {
      return { path: `https://workflow.waner.cn/${iconUrl}` }
    }

    // Otherwise, extract it from icon prop
    if (xNodeType.value.icon) {
      const icon = getNodeIcon(xNodeType.value, themeMode.value)

      if (icon) {
        const [type, path] = icon.split(':')

        if (type === 'file') {
          throw new Error(`Unexpected icon: ${icon}`)
        }

        return { icon: path }
      }
    }
  }

  return {}
})

const nodeTypeName = computed(() => {
  return xNodeName.value ? xNodeName.value : xNodeType.value?.displayName
})
</script>

<template>
  <BaseNodeIcon
    :color="color"
    :disabled="disabled"
    :name="iconSource.icon"
    :nodeTypeName="nodeTypeName"
    :showTooltip="showTooltip"
    :size="size"
    :src="iconSource.path || iconSource.fileBuffer"
    :type="iconType"
  />
</template>
