<script setup lang="ts">
import { ChevronLeftIcon, WifiOffIcon } from 'lucide-vue-next'

import WorkflowActivator from '#wf/components/WorkflowActivator.vue'
import { useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { useCanvasStore } from '#wf/stores/canvas.store'
import { usePushConnectionStore } from '#wf/stores/pushConnection.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'

import { DateFormat } from '~/enums/common'
import { RouteKey } from '~/enums/route'
import AppIcon from '~/features/space/components/app/AppIcon.vue'

const workflowStore = useWorkflowStore()
const { workflow } = storeToRefs(workflowStore)

const canvasStore = useCanvasStore()
const { isLoading } = storeToRefs(canvasStore)

const workflowHelpers = useWorkflowHelpers()

const pushConnectionStore = usePushConnectionStore()
const { isConnected } = storeToRefs(pushConnectionStore)

const handleUpdateWorkflowName = async (newName: string) => {
  const oldName = workflow.value.name

  workflowStore.setWorkflowName({
    newName,
    setStateDirty: false,
  })

  try {
    await workflowHelpers.saveCurrentWorkflow()
  }
  catch {
    workflowStore.setWorkflowName({
      newName: oldName,
      setStateDirty: false,
    })
  }
}
</script>

<template>
  <div class="flex-center">
    <div class="gap-2 flex-center">
      <ProBtn
        class="!p-1.5"
        severity="contrast"
        title="返回工作空间"
        variant="text"
        @click="navigateTo(getRoutePath(RouteKey.TW_工作空间))"
      >
        <template #icon="{ size }">
          <ChevronLeftIcon
            :size="size"
            :strokeWidth="3"
          />
        </template>
      </ProBtn>

      <AppIcon />

      <div>
        <div
          v-if="isLoading"
          class="space-y-1"
        >
          <Skeleton width="8rem" />
          <Skeleton
            height="0.8rem"
            width="15rem"
          />
        </div>

        <template v-else>
          <EditableTitle
            :disabled="isLoading"
            :modelValue="workflow.name"
            :tooltipConfig="{ value: '点击重命名' }"
            @update:modelValue="handleUpdateWorkflowName"
          />

          <div class="text-xs text-secondary">
            最近保存 {{ formatDate(workflow.updatedAt, DateFormat.MM_DD_HH_MM) }}
          </div>
        </template>
      </div>
    </div>

    <div class="ml-auto gap-2 inline-flex-center">
      <template v-if="isConnected">
        <WorkflowActivator
          :workflowActive="workflow.active"
          :workflowId="workflow.id"
        />
      </template>

      <Message
        v-else
        severity="error"
        size="small"
      >
        <template #icon>
          <WifiOffIcon
            class="shrink-0"
            :size="16"
          />
        </template>
        服务连接断开，请检查网络连接并刷新页面以恢复工作流功能
      </Message>

      <AdminTopUser />
    </div>
  </div>
</template>
