<script setup lang="ts">
import type { MenuItem as PrimeMenuItem } from 'primevue/menuitem'

import { useWorkflowStore } from '#wf/stores/workflow.store'

import { RouteKey } from '~/enums/route'
import { useSpaceStore } from '~/features/space/stores/space'

const spaceStore = useSpaceStore()
const { activeSpaceId } = storeToRefs(spaceStore)

const workflowStore = useWorkflowStore()
const { workflowId } = storeToRefs(workflowStore)

const menuItems: PrimeMenuItem[] = [
  {
    key: RouteKey.TW_应用_工作流,
    label: '工作流编排',
    data: { icon: getRouteIcon(RouteKey.TW_应用_工作流) },
  },
  {
    key: RouteKey.TW_应用_工作流执行记录,
    label: '执行记录',
    data: { icon: getRouteIcon(RouteKey.TW_应用_工作流执行记录) },
  },
]

const { activeRouteKey: selectedMenuKey } = useNavMenu()

const handleMenuSelect = (menuItem: PrimeMenuItem) => {
  if (menuItem.key) {
    if (activeSpaceId.value && workflowId.value) {
      navigateTo(getRoutePath(menuItem.key as RouteKey, {
        spaceId: activeSpaceId.value,
        workflowId: workflowId.value,
      }))
    }
  }
}
</script>

<template>
  <div>
    <SideMenuPanel
      :menuItems="menuItems"
      :selectedKey="selectedMenuKey"
      @select="handleMenuSelect"
    />
  </div>
</template>
