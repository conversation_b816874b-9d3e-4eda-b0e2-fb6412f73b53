<script setup lang="ts">
import { LoaderCircleIcon, TriangleAlertIcon } from 'lucide-vue-next'
import { OPEN_AI_API_CREDENTIAL_TYPE } from 'n8n-workflow/dist/Constants.js'

import { useWorkflowActivate } from '#wf/composables/useWorkflowActivate'
import { EXECUTE_WORKFLOW_TRIGGER_NODE_TYPE } from '#wf/constants/common'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { INodeUi, IUsedCredential } from '#wf/types/interface'
import { getActivatableTriggerNodes } from '#wf/utils/nodeTypesUtils'

const { $toast } = useNuxtApp()

const workflowActivate = useWorkflowActivate()

const workflowsStore = useWorkflowStore()
const { workflow, workflowId } = storeToRefs(workflowsStore)
const workflowActive = computed(() => workflow.value?.active)

const credentialsStore = useCredentialsStore()

const isWorkflowActive = computed((): boolean => {
  const activeWorkflows = workflowsStore.activeWorkflows

  return activeWorkflows.includes(workflowId.value)
})

const couldNotBeStarted = computed((): boolean => {
  return workflowActive.value && isWorkflowActive.value !== workflowActive.value
})

const isCurrentWorkflow = computed((): boolean => {
  return workflowsStore.workflowId === workflowId.value
})

const containsTrigger = computed((): boolean => {
  const foundTriggers = getActivatableTriggerNodes(workflowsStore.workflowTriggerNodes)

  return foundTriggers.length > 0
})

const containsOnlyExecuteWorkflowTrigger = computed((): boolean => {
  const foundActiveTriggers = workflowsStore.workflowTriggerNodes.filter(
    (trigger) => !trigger.disabled,
  )
  const foundTriggers = foundActiveTriggers.filter(
    (trigger) => trigger.type === EXECUTE_WORKFLOW_TRIGGER_NODE_TYPE,
  )

  return foundTriggers.length > 0 && foundTriggers.length === foundActiveTriggers.length
})

const disabled = computed((): boolean => {
  if (isCurrentWorkflow.value) {
    return !workflowActive.value && !containsTrigger.value
  }

  return false
})

function findManagedOpenAiCredentialId(
  usedCredentials: Record<string, IUsedCredential>,
): string | undefined {
  return Object.keys(usedCredentials).find((credentialId) => {
    const credential = credentialsStore.state.credentials[credentialId]

    return credential.isManaged && credential.type === OPEN_AI_API_CREDENTIAL_TYPE
  })
}

function hasActiveNodeUsingCredential(nodes: INodeUi[], credentialId: string): boolean {
  return nodes.some(
    (node) =>
      node?.credentials?.[OPEN_AI_API_CREDENTIAL_TYPE]?.id === credentialId && !node.disabled,
  )
}

/**
 * Determines if the warning for free AI credits should be shown in the workflow.
 *
 * This computed property evaluates whether to display a warning about free AI credits
 * in the workflow. The warning is shown when both conditions are met:
 * 1. The workflow uses managed OpenAI API credentials
 * 2. Those credentials are associated with at least one enabled node
 *
 */
const shouldShowFreeAiCreditsWarning = computed((): boolean => {
  const usedCredentials = workflowsStore?.usedCredentials

  if (!usedCredentials) {
    return false
  }

  const managedOpenAiCredentialId = findManagedOpenAiCredentialId(usedCredentials)

  if (!managedOpenAiCredentialId) {
    return false
  }

  return hasActiveNodeUsingCredential(workflowsStore.workflow.nodes, managedOpenAiCredentialId)
})

async function handleActiveChanged(newActiveState: boolean) {
  await workflowActivate.updateWorkflowActivation(
    workflowId.value,
    newActiveState,
  )
}

async function displayActivationError() {
  let errorMessage: string | VNode

  try {
    const errorData = await workflowsStore.getActivationError(workflowId.value)

    if (errorData === undefined) {
      errorMessage = '未知错误'
    }
    else {
      errorMessage = `激活工作流时发生以下错误：${errorData}`
    }
  }
  catch {
    errorMessage = '抱歉，请求错误'
  }

  $toast.warn({
    summary: '激活工作流时出现问题',
    detail: errorMessage,
    keepAlive: true,
  })
}

watch(
  workflowActive,
  (newActiveState) => {
    if (newActiveState && shouldShowFreeAiCreditsWarning.value) {
      $toast.warn({
        summary: '你正在使用免费的 OpenAI API 积分',
        detail: '为了确保你的工作流在未来运行顺利，请将免费的 OpenAI API 积分替换为你的 API 密钥。',
        keepAlive: true,
      })
    }
  },
)
</script>

<template>
  <div class="gap-1 flex-center">
    <div
      class="text-sm"
      :class="{
        'opacity-70': !workflowActive,
      }"
    >
      {{ workflowActive ? '已启用' : '已停用' }}
    </div>

    <div
      v-tooltip.bottom="containsOnlyExecuteWorkflowTrigger
        ? '“执行工作流触发器”无需激活，因为它是由其他工作流触发的'
        : '此工作流没有需要激活的触发器节点'"
      class="inline-flex-center"
    >
      <ToggleSwitch
        data-testid="workflow-activate-switch"
        :disabled="disabled"
        :modelValue="workflowActive"
        :title="
          workflowActive
            ? '点击停用工作流'
            : '点击启用工作流'
        "
        @update:modelValue="handleActiveChanged"
      >
        <template #handle>
          <LoaderCircleIcon
            v-if="workflowActivate.updatingWorkflowActivation.value"
            class="animate-spin"
            :size="12"
          />
        </template>
      </ToggleSwitch>
    </div>

    <div
      v-if="couldNotBeStarted"
      v-tooltip.bottom="'工作流已激活但无法启动。\n 单击以显示错误消息。'"
      class="cursor-pointer text-danger-500"
      @click="displayActivationError()"
    >
      <TriangleAlertIcon :size="14" />
    </div>
  </div>
</template>
