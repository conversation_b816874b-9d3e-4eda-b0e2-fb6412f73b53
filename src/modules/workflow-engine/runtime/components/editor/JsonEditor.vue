<script setup lang="ts">
import { history } from '@codemirror/commands'
import { json, jsonParseLinter } from '@codemirror/lang-json'
import { bracketMatching, foldGutter, indentOnInput } from '@codemirror/language'
import { linter as createLinter, lintGutter } from '@codemirror/lint'
import type { Extension } from '@codemirror/state'
import { EditorState, Prec } from '@codemirror/state'
import type { ViewUpdate } from '@codemirror/view'
import {
  dropCursor,
  EditorView,
  highlightActiveLine,
  highlightActiveLineGutter,
  keymap,
  lineNumbers,
} from '@codemirror/view'
import { twMerge } from 'tailwind-merge'

// import { mappingDropCursor } from '@/plugins/codemirror/dragAndDrop'
import { editorKeymap } from '#wf/lib/codemirror/keymap'
// import { n8nAutocompletion } from '#wf/lib/codemirror/n8nLang'

// import { codeEditorTheme } from '../CodeNodeEditor/theme'

interface Props {
  modelValue: string
  isReadOnly?: boolean
  fillParent?: boolean
  rows?: number
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  fillParent: false,
  isReadOnly: false,
  rows: 4,
})
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const { refKey: jsonEditorRefKey, ref: jsonEditorRef } = useRef<HTMLDivElement>()
const editor = ref<EditorView | null>(null)
const editorState = ref<EditorState | null>(null)

const extensions = computed(() => {
  const extensionsToApply: Extension[] = [
    json(),
    lineNumbers(),
    EditorView.lineWrapping,
    EditorState.readOnly.of(props.isReadOnly),
    // codeEditorTheme({
    //   isReadOnly: props.isReadOnly,
    //   maxHeight: props.fillParent ? '100%' : '40vh',
    //   minHeight: '20vh',
    //   rows: props.rows,
    // }),
  ]

  if (!props.isReadOnly) {
    extensionsToApply.push(
      history(),
      Prec.highest(keymap.of(editorKeymap)),
      createLinter(jsonParseLinter()),
      lintGutter(),
      // n8nAutocompletion(),
      indentOnInput(),
      highlightActiveLine(),
      highlightActiveLineGutter(),
      foldGutter(),
      dropCursor(),
      bracketMatching(),
      // mappingDropCursor(),
      EditorView.updateListener.of((viewUpdate: ViewUpdate) => {
        if (!viewUpdate.docChanged || !editor.value) {
          return
        }

        emit('update:modelValue', editor.value?.state.doc.toString())
      }),
    )
  }

  return extensionsToApply
})

function createEditor() {
  const state = EditorState.create({ doc: props.modelValue, extensions: extensions.value })
  const parent = jsonEditorRef.value

  if (!parent) {
    return
  }

  editor.value = new EditorView({ parent, state })
  editorState.value = editor.value.state
}

function destroyEditor() {
  editor.value?.destroy()
}

onMounted(() => {
  createEditor()
})

onBeforeUnmount(() => {
  if (!editor.value) {
    return
  }

  editor.value.destroy()
})

watch(
  () => props.modelValue,
  (newValue: string) => {
    const editorValue = editor.value?.state?.doc.toString()

    // If model value changes from outside the component
    if (editorValue && editorValue.length !== newValue.length && editorValue !== newValue) {
      destroyEditor()
      createEditor()
    }
  },
)
</script>

<template>
  <div
    :class="twMerge('relative h-full rounded-lg border border-divider overflow-hidden', props.class)"
  >
    <div
      :ref="jsonEditorRefKey"
      class="h-full [&_.cm-editor]:h-full"
    />

    <slot name="suffix" />
  </div>
</template>
