<script setup lang="ts">
import { LoaderIcon } from 'lucide-vue-next'
import type { ExecutionSummary } from 'n8n-workflow'

import { type IExecutionUIData, useExecutionHelpers } from '#wf/composables/useExecutionHelpers'

const props = defineProps<{
  execution: ExecutionSummary
  highlight?: boolean
}>()

const executionHelpers = useExecutionHelpers()

const executionUIDetails = computed<IExecutionUIData>(() =>
  executionHelpers.getUIDetails(props.execution),
)
</script>

<template>
  <div
    class="cursor-pointer rounded-md p-2 hover:bg-emphasis"
    :class="{ '!bg-emphasis': highlight }"
  >
    <div class="text-[13px] font-medium tabular-nums">
      <template v-if="executionUIDetails.name === 'new'">
        {{ executionUIDetails.createdAt }} -
        即将开始
      </template>

      <template v-else>
        {{ executionUIDetails.startTime }}
      </template>
    </div>

    <div class="gap-0.5 text-sm flex-center text-secondary">
      <LoaderIcon
        v-if="executionUIDetails.name === 'running'"
        class="animate-spin"
        :size="14"
      />

      <span
        :class="{
          'text-success-500': executionUIDetails.name === 'success',
          'text-danger-500': executionUIDetails.name === 'error',
          'text-secondary': executionUIDetails.name === 'waiting' || executionUIDetails.name === 'canceled',
          'text-warning-500': executionUIDetails.name === 'running',
        }"
      >
        {{ executionUIDetails.label }}
      </span>

      <span v-if="executionUIDetails.name === 'running'">
        持续时间：
        <!-- <ExecutionsTime :startTime="execution.startedAt" /> -->
      </span>

      <span v-if="executionUIDetails.name === 'new' && execution.createdAt">
        {{ execution.createdAt }}
      </span>

      <span
        v-else-if="executionUIDetails.runningTime !== ''"
      >
        在 {{ executionUIDetails.runningTime }} 内完成
      </span>
    </div>
  </div>
</template>
