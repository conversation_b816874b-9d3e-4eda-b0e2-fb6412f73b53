<script setup lang="ts">
import WorkflowPreview from '#wf/components/execution/WorkflowPreview.vue'
import { useExecutionsStore } from '#wf/stores/executions.store'

const executionsStore = useExecutionsStore()
const { activeExecution } = storeToRefs(executionsStore)
</script>

<template>
  <WorkflowPreview
    v-if="activeExecution"
    :executionId="Number(activeExecution.id)"
    :executionMode="activeExecution.mode"
    mode="execution"
  />
</template>
