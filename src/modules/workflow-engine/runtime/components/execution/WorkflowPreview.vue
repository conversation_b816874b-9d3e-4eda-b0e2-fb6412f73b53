<script setup lang="ts">
import { useEventListener } from '@vueuse/core'
import { safeParse } from 'valibot'

import { postMessageSchema } from '#wf/schemas/post-message.schema'
import { useExecutionsStore } from '#wf/stores/executions.store'
import type { IWorkflowDb, IWorkflowTemplate } from '#wf/types/interface'

import { RouteKey } from '~/enums/route'
import { useSpaceStore } from '~/features/space/stores/space'

const props = withDefaults(
  defineProps<{
    loading?: boolean
    mode?: 'workflow' | 'execution'
    workflow?: IWorkflowDb | IWorkflowTemplate['workflow']
    executionId?: number
    executionMode?: string
    canOpenNDV?: boolean
    hideNodeIssues?: boolean
    focusOnLoad?: boolean
  }>(),
  {
    loading: false,
    mode: 'workflow',
    canOpenNDV: true,
    hideNodeIssues: false,
    focusOnLoad: true,
  },
)

const spaceStore = useSpaceStore()
const { activeSpaceId } = storeToRefs(spaceStore)

const emit = defineEmits<{
  close: []
}>()

const { $toast } = useNuxtApp()

const executionsStore = useExecutionsStore()

const { refKey: iframeRefKey, ref: iframeRef } = useRef<HTMLIFrameElement>()
const nodeViewDetailsOpened = ref(false)
const isReady = ref(false)

const iframeSrc = computed(() => {
  if (activeSpaceId.value) {
    return getRoutePath(RouteKey.TW_应用_工作流执行记录画布, {
      spaceId: activeSpaceId.value,
    })
  }

  return null
})

const showPreview = computed(() => {
  return (
    !props.loading
    && ((props.mode === 'workflow' && !!props.workflow)
      || (props.mode === 'execution' && !!props.executionId))
    && isReady.value
  )
})

const loadWorkflow = () => {
  try {
    if (!props.workflow) {
      throw new Error('缺失工作流')
    }

    if (!props.workflow.nodes || !Array.isArray(props.workflow.nodes)) {
      throw new Error('工作流节点为空')
    }

    if (iframeRef.value?.contentWindow) {
      iframeRef.value.contentWindow.postMessage?.(
        {
          command: 'openWorkflow',
          workflow: props.workflow,
          canOpenNDV: props.canOpenNDV,
          hideNodeIssues: props.hideNodeIssues,
        },
        '*',
      )
    }
  }
  catch {
    $toast.error({
      summary: '工作流预览错误',
      detail: '无法预览工作流执行',
    })
  }
}

const loadExecution = () => {
  try {
    if (!props.executionId) {
      throw new Error('缺失工作流执行')
    }

    if (iframeRef.value?.contentWindow) {
      iframeRef.value.contentWindow.postMessage?.(
        {
          command: 'openExecution',
          executionId: props.executionId,
          executionMode: props.executionMode ?? '',
          canOpenNDV: props.canOpenNDV,
        },
        '*',
      )

      if (executionsStore.activeExecution) {
        iframeRef.value.contentWindow.postMessage?.(
          {
            command: 'setActiveExecution',
            executionId: executionsStore.activeExecution.id,
          },
          '*',
        )
      }
    }
  }
  catch (error) {
    $toast.error({
      summary: '执行预览错误',
      detail: error,
    })
  }
}

const onReady = () => {
  isReady.value = true

  if (props.focusOnLoad) {
    setTimeout(() => {
      iframeRef.value?.contentWindow?.focus()
    })
  }
}

const onOpenNDV = () => {
  nodeViewDetailsOpened.value = true
}

const onCloseNDV = () => {
  nodeViewDetailsOpened.value = false
}

const onError = () => {
  emit('close')
}

const receiveMessage = ({ data }: MessageEvent) => {
  const result = safeParse(postMessageSchema, data)

  if (result.success) {
    const json = result.output

    if (json.command === 'n8nReady') {
      onReady()
    }
    else if (json.command === 'openNDV') {
      onOpenNDV()
    }
    else if (json.command === 'closeNDV') {
      onCloseNDV()
    }
    else if (json.command === 'error') {
      onError()
    }
  }
}

useEventListener(window, 'message', receiveMessage)

watch(
  showPreview,
  () => {
    if (showPreview.value) {
      if (props.mode === 'workflow') {
        loadWorkflow()
      }
      else if (props.mode === 'execution') {
        loadExecution()
      }
    }
  },
)

watch(
  () => props.executionId,
  () => {
    if (props.mode === 'execution' && props.executionId) {
      loadExecution()
    }
  },
)

watch(
  () => props.workflow,
  () => {
    if (props.mode === 'workflow' && props.workflow) {
      loadWorkflow()
    }
  },
)
</script>

<template>
  <iframe
    :ref="iframeRefKey"
    class="size-full"
    data-testid="workflow-preview-iframe"
    :src="iframeSrc"
  />
</template>
