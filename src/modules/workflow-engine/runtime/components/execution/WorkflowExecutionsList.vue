<script setup lang="ts">
import type { ExecutionSummary } from 'n8n-workflow'
import { string } from 'valibot'

import { useExecutionsStore } from '#wf/stores/executions.store'

import WorkflowExecutionsListItem from './WorkflowExecutionsListItem.vue'

const workflowId = useRouteParam({
  name: 'workflowId',
  schema: string(),
})

const executionsStore = useExecutionsStore()
const { activeExecution } = storeToRefs(executionsStore)

const executions = computed(() =>
  workflowId.value
    ? [
        ...(executionsStore.currentExecutionsByWorkflowId[workflowId.value] ?? []),
        ...(executionsStore.executionsByWorkflowId[workflowId.value] ?? []),
      ]
    : [],
)

function onDocumentVisibilityChange() {
  if (document.visibilityState === 'hidden') {
    executionsStore.stopAutoRefreshInterval()
  }
  else {
    void executionsStore.startAutoRefreshInterval(workflowId.value)
  }
}

onMounted(async () => {
  if (workflowId.value) {
    await executionsStore.initialize(workflowId.value)

    if (executions.value.length > 0) {
      const firstExecution = executions.value.at(0)

      if (firstExecution) {
        executionsStore.setActiveExecution(firstExecution)
      }
    }
  }

  document.addEventListener('visibilitychange', onDocumentVisibilityChange)
})

watch(activeExecution, async (newVal) => {
  if (newVal) {
    await executionsStore.fetchExecution(newVal.id)
  }
}, { immediate: true })

onBeforeUnmount(() => {
  executionsStore.reset()
  document.removeEventListener('visibilitychange', onDocumentVisibilityChange)
})

const handleActiveExecutionChange = (executionId: ExecutionSummary['id']) => {
  const targetExecution = executions.value.find((e) => e.id === executionId)

  if (targetExecution) {
    executionsStore.setActiveExecution(targetExecution)
  }
}
</script>

<template>
  <div class="h-full">
    <div class="flex flex-col gap-1">
      <WorkflowExecutionsListItem
        v-for="execution of executions"
        :key="execution.id"
        :execution="execution"
        :highlight="execution.id === activeExecution?.id"
        @click="handleActiveExecutionChange(execution.id)"
      />
    </div>
  </div>
</template>
