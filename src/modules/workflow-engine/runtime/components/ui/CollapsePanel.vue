<script setup lang="ts">
import { ChevronRightIcon } from 'lucide-vue-next'

interface Props {
  /** 标题 */
  title?: string
  expanded?: boolean
  /** 是否默认展开, 默认值为 true */
  defaultExpanded?: boolean
  /** 自定义标题类名 */
  headerClass?: string
  /** 自定义内容类名 */
  contentClass?: string
  /** 是否禁用折叠功能 */
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  expanded: undefined,
  defaultExpanded: true,
  headerClass: '',
  contentClass: '',
  disabled: false,
})

const isExpanded = ref(props.expanded ?? props.defaultExpanded)

watch(
  () => props.expanded, (val) => {
    if (typeof val === 'boolean') {
      isExpanded.value = val
    }
  },
)

const toggle = () => {
  if (!props.disabled) {
    isExpanded.value = !isExpanded.value
  }
}

const slots = defineSlots<{
  header: VNode[]
  content: VNode[]
}>()
</script>

<template>
  <div>
    <div
      class="flex-center"
      :class="[headerClass, { 'cursor-not-allowed opacity-60': disabled }]"
    >
      <ProBtn
        class="!p-0"
        highlight
        mini
        severity="contrast"
        size="small"
        @click="toggle()"
      >
        <template #icon>
          <ChevronRightIcon
            :class="[
              { 'rotate-90': isExpanded },
            ]"
            :size="16"
          />
        </template>
      </ProBtn>

      <slot name="header">
        <div class="pl-1 font-medium">
          {{ title }}
        </div>
      </slot>
    </div>

    <div
      v-if="slots.content"
      v-show="isExpanded"
      class="pt-2"
      :class="contentClass"
    >
      <slot name="content" />
    </div>
  </div>
</template>
