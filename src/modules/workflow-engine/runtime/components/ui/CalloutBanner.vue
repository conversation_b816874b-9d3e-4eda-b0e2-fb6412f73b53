<script setup lang="ts">
import DOMPurify from 'dompurify'
import { BadgeInfoIcon } from 'lucide-vue-next'
import type { MessageProps } from 'primevue'

interface CalloutMessageProps extends MessageProps {
  /** 消息内容 */
  content?: string
  /** 是否隐藏图标 */
  hideIcon?: boolean
}

const props = withDefaults(defineProps<CalloutMessageProps>(), {
  hideIcon: false,
})

const slots = defineSlots<{
  icon: VNode
  content: VNode
}>()
</script>

<template>
  <div class="p-px">
    <Message
      v-bind="props"
      :pt="{
        content: 'prose prose-sm font-normal leading-normal dark:prose-invert',
      }"
    >
      <template
        v-if="!hideIcon"
        #icon
      >
        <slot name="icon">
          <BadgeInfoIcon
            class="shrink-0"
            :size="16"
          />
        </slot>
      </template>

      <slot name="content">
        <span
          v-if="!slots.content && content"
          v-html="DOMPurify.sanitize(content)"
        />
      </slot>
    </Message>
  </div>
</template>
