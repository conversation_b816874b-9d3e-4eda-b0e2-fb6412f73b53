<script setup lang="ts">
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

export type NodeIconType = 'file' | 'icon' | 'unknown'

interface NodeIconProps {
  type: NodeIconType
  src?: string
  name?: string
  nodeTypeName?: string
  size?: number
  disabled?: boolean
  color?: string
  showTooltip?: boolean
  tooltipPosition?: 'top' | 'bottom' | 'left' | 'right'
}

const props = withDefaults(defineProps<NodeIconProps>(), {
  tooltipPosition: 'top',
  showTooltip: false,
})

const iconStyleData = computed(() => {
  if (props.size) {
    return {
      color: props.color,
      width: `${props.size}px`,
      height: `${props.size}px`,
      'font-size': `${props.size}px`,
      'line-height': `${props.size}px`,
    }
  }

  return {
    color: props.color,
    width: '16px',
    height: '16px',
    'line-height': '26px',
  }
})

const fontStyleData = computed(() => {
  if (props.size) {
    return {
      'max-width': `${props.size}px`,
    }
  }

  return {
    width: '100%',
    height: '100%',
  }
})
</script>

<template>
  <div
    class="justify-center rounded-sm font-bold inline-flex-center"
    :class="{
      'cursor-not-allowed': disabled,
    }"
    :style="iconStyleData"
  >
    <div
      v-tooltip:[tooltipPosition]="
        showTooltip
          ? { value: nodeTypeName, autoHide: false }
          : null
      "
      class="size-full"
    >
      <div
        v-if="type !== 'unknown'"
        class="size-full justify-center flex-center"
      >
        <img
          v-if="type === 'file'"
          class="size-full object-contain"
          :src="src"
        >

        <FontAwesomeIcon
          v-else
          :icon="name ?? ''"
          :style="fontStyleData"
        />
      </div>

      <div
        v-else
        class="text-center"
      >
        {{ nodeTypeName ? nodeTypeName.charAt(0) : '?' }}
      </div>
    </div>
  </div>
</template>
