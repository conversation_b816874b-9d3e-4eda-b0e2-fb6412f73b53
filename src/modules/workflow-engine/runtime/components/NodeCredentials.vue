<script setup lang="ts">
import { PlusIcon } from 'lucide-vue-next'
import type {
  ICredentialType,
  INodeCredentialDescription,
  INodeCredentialsDetails,
  NodeParameterValueType,
} from 'n8n-workflow'

import ParameterIssues from '#wf/components/ParameterIssues.vue'
import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { CREDENTIAL_ONLY_NODE_PREFIX, HTTP_REQUEST_NODE_TYPE, KEEP_AUTH_IN_NDV_FOR_NODES } from '#wf/constants/common'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { ICredentialsResponse, INodeUi, INodeUpdatePropertiesInformation } from '#wf/types/interface'
import { assert } from '#wf/utils/assert'
import {
  getAllNodeCredentialForAuthType,
  getAuthTypeForNodeCredential,
  getMainAuthField,
  getNodeCredentialForSelectedAuthType,
  isRequiredCredential,
  updateNodeAuthType,
} from '#wf/utils/nodeTypesUtils'
import { isEmpty } from '#wf/utils/typesUtils'

interface CredentialDropdownOption extends ICredentialsResponse {
  typeDisplayName: string
}

interface Props {
  /** 当前 active 的节点 */
  activeNode: INodeUi
  overrideCredType?: NodeParameterValueType
  readOnly?: boolean
  /** 是否列出所有凭据 */
  showAll?: boolean
  /** 是否隐藏校验问题 */
  hideIssues?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readOnly: false,
  overrideCredType: '',
  showAll: false,
  hideIssues: false,
})

const emit = defineEmits<{
  credentialSelected: [credential: INodeUpdatePropertiesInformation]
  valueChanged: [value: { name: string, value: string }]
}>()

const credentialsStore = useCredentialsStore()
const nodeTypesStore = useNodeTypesStore()
const uiStore = useUIStore()
const workflowStore = useWorkflowStore()
const ndvStore = useNDVStore()

const nodeHelpers = useNodeHelpers()
const { $toast } = useNuxtApp()

const subscribedToCredentialType = ref('')
const filter = ref('')
const listeningForAuthChange = ref(false)

const nodeType = computed(() =>
  nodeTypesStore.getNodeType(props.activeNode.type, props.activeNode.typeVersion),
)

const credentialTypesNodeDescription = computed(() => {
  if (typeof props.overrideCredType !== 'string') {
    return []
  }

  const credType = credentialsStore.getCredentialTypeByName(props.overrideCredType)

  if (credType) {
    return [credType]
  }

  const activeNodeType = nodeType.value

  if (activeNodeType?.credentials) {
    return activeNodeType.credentials
  }

  return []
})

const credentialTypesNode = computed(() =>
  credentialTypesNodeDescription.value.map(
    (credentialTypeDescription) => credentialTypeDescription.name,
  ),
)

const credentialTypeNames = computed(() => {
  const returnData: Record<string, string> = {}

  for (const credentialTypeName of credentialTypesNode.value) {
    const credentialType = credentialsStore.getCredentialTypeByName(credentialTypeName)
    returnData[credentialTypeName] = credentialType
      ? credentialType.displayName
      : credentialTypeName
  }

  return returnData
})

const selected = computed<Record<string, INodeCredentialsDetails>>(
  () => props.activeNode.credentials ?? {},
)

const mainNodeAuthField = computed(() => getMainAuthField(nodeType.value))

watch(
  () => props.activeNode.parameters,
  (newValue, oldValue) => {
    // When active node parameters change, check if authentication type has been changed
    // and set `subscribedToCredentialType` to corresponding credential type
    const isActive = props.activeNode.name === ndvStore.activeNode?.name

    // Only do this for active node and if it's listening for auth change
    if (isActive && nodeType.value && listeningForAuthChange.value) {
      if (mainNodeAuthField.value && oldValue && newValue) {
        const newAuth = newValue[mainNodeAuthField.value.name]

        if (newAuth) {
          const authType = typeof newAuth === 'object' ? JSON.stringify(newAuth) : newAuth.toString()
          const credentialType = getNodeCredentialForSelectedAuthType(nodeType.value, authType)

          if (credentialType) {
            subscribedToCredentialType.value = credentialType.name
          }
        }
      }
    }
  },
  { immediate: true, deep: true },
)

function createNewCredential(
  credentialType: string,
  listenForAuthChange: boolean = false,
  showAuthOptions = false,
) {
  if (listenForAuthChange) {
    // If new credential dialog is open, start listening for auth type change which should happen in the modal
    // this will be handled in this component's watcher which will set subscribed credential accordingly
    listeningForAuthChange.value = true
    subscribedToCredentialType.value = credentialType
  }

  uiStore.openNewCredential(credentialType, showAuthOptions)
}

function onCredentialSelected(
  credentialType: string,
  credentialId: string | null | undefined,
  showAuthOptions = false,
) {
  if (!credentialId) {
    createNewCredential(credentialType, false, showAuthOptions)

    return
  }

  const selectedCredentials = credentialsStore.getCredentialById(credentialId)
  const selectedCredentialsType = props.showAll ? selectedCredentials.type : credentialType
  const oldCredentials = props.activeNode.credentials?.[selectedCredentialsType] ?? null

  const newSelectedCredentials: INodeCredentialsDetails = {
    id: selectedCredentials.id,
    name: selectedCredentials.name,
  }

  // if credentials has been string or neither id matched nor name matched uniquely
  if (
    oldCredentials?.id === null
    || (oldCredentials?.id
      && !credentialsStore.getCredentialByIdAndType(oldCredentials.id, selectedCredentialsType))
  ) {
    // update all nodes in the workflow with the same old/invalid credentials
    workflowStore.replaceInvalidWorkflowCredentials({
      credentials: newSelectedCredentials,
      invalid: oldCredentials,
      type: selectedCredentialsType,
    })
    nodeHelpers.updateNodesCredentialsIssues()

    $toast.success({
      summary: '节点凭据已更新',
      detail: `使用凭据“${oldCredentials.name}”的节点已更新为使用“${newSelectedCredentials.name}”`,
    })
  }

  // If credential is selected from mixed credential dropdown, update node's auth filed based on selected credential
  if (props.showAll && mainNodeAuthField.value) {
    const nodeCredentialDescription = nodeType.value?.credentials?.find(
      (cred) => cred.name === selectedCredentialsType,
    )
    const authOption = getAuthTypeForNodeCredential(nodeType.value, nodeCredentialDescription)

    if (authOption) {
      updateNodeAuthType(props.activeNode, authOption.value)
      const parameterData = {
        name: `parameters.${mainNodeAuthField.value.name}`,
        value: authOption.value,
      }

      emit('valueChanged', parameterData)
    }
  }

  const node = props.activeNode

  const credentials = {
    ...(node.credentials ?? {}),
    [selectedCredentialsType]: newSelectedCredentials,
  }

  const updateInformation: INodeUpdatePropertiesInformation = {
    name: props.activeNode.name,
    properties: {
      credentials,
      position: props.activeNode.position,
    },
  }

  emit('credentialSelected', updateInformation)
}

function clearSelectedCredential(credentialType: string) {
  const node = props.activeNode

  const credentials = {
    ...(node.credentials ?? {}),
  }

  // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
  delete credentials[credentialType]

  const updateInformation: INodeUpdatePropertiesInformation = {
    name: props.activeNode.name,
    properties: {
      credentials,
      position: props.activeNode.position,
    },
  }

  emit('credentialSelected', updateInformation)
}

onMounted(() => {
  credentialsStore.$onAction(({ name, after, args }) => {
    const listeningForActions = ['createNewCredential', 'updateCredential', 'deleteCredential']
    const credentialType = subscribedToCredentialType.value

    if (!credentialType) {
      return
    }

    after(async (result) => {
      if (!listeningForActions.includes(name)) {
        return
      }

      const current = selected.value[credentialType]
      let credentialsOfType: ICredentialsResponse[] = []

      if (props.showAll) {
        if (props.activeNode) {
          credentialsOfType = [...(credentialsStore.allUsableCredentialsForNode(props.activeNode) || [])]
        }
      }
      else {
        credentialsOfType = [
          ...(credentialsStore.allUsableCredentialsByType[credentialType] || []),
        ]
      }

      switch (name) {
        // new credential was added
        case 'createNewCredential':
          if (result) {
            onCredentialSelected(credentialType, (result as ICredentialsResponse).id)
          }

          break

        case 'updateCredential': {
          const updatedCredential = result as ICredentialsResponse

          // credential name was changed, update it
          if (updatedCredential.name !== current.name) {
            onCredentialSelected(credentialType, current.id)
          }

          break
        }

        case 'deleteCredential':
          // all credentials were deleted
          if (credentialsOfType.length === 0) {
            clearSelectedCredential(credentialType)
          }
          else {
            const id = args[0].id

            // credential was deleted, select last one added to replace with
            if (current.id === id) {
              onCredentialSelected(
                credentialType,
                credentialsOfType[credentialsOfType.length - 1].id,
              )
            }
          }

          break
      }
    })
  })

  // ndvEventBus.on('credential.createNew', onCreateAndAssignNewCredential)
})

// onBeforeUnmount(() => {
//   ndvEventBus.off('credential.createNew', onCreateAndAssignNewCredential)
// })

const showMixedCredentials = (credentialType: INodeCredentialDescription): boolean => {
  const isRequired = isRequiredCredential(nodeType.value, credentialType)

  return !KEEP_AUTH_IN_NDV_FOR_NODES.includes(props.activeNode.type ?? '') && isRequired
}

function getAllRelatedCredentialTypes(credentialType: INodeCredentialDescription): string[] {
  const credentialIsRequired = showMixedCredentials(credentialType)

  if (credentialIsRequired) {
    if (mainNodeAuthField.value) {
      const credentials = getAllNodeCredentialForAuthType(
        nodeType.value,
        mainNodeAuthField.value.name,
      )

      return credentials.map((cred) => cred.name)
    }
  }

  return [credentialType.name]
}

const getCredentialOptions = (types: string[]): CredentialDropdownOption[] => {
  let options: CredentialDropdownOption[] = []

  types.forEach((type) => {
    options = options.concat(
      credentialsStore.allUsableCredentialsByType[type].map(
        (option: ICredentialsResponse) =>
          ({
            ...option,
            typeDisplayName: credentialsStore.getCredentialTypeByName(type)?.displayName,
          }) as CredentialDropdownOption,
      ),
    )
  })

  if (ndvStore.activeNode?.type === HTTP_REQUEST_NODE_TYPE) {
    options = options.filter((option) => !option.isManaged)
  }

  return options
}

function displayCredentials(credentialTypeDescription: INodeCredentialDescription): boolean {
  if (credentialTypeDescription.displayOptions === undefined) {
    // If it is not defined no need to do a proper check
    return true
  }

  return nodeHelpers.displayParameter(
    props.activeNode.parameters,
    credentialTypeDescription,
    '',
    props.activeNode,
  )
}

/** 显示的凭据类型 */
const credentialTypesNodeDescriptionDisplayed = computed(() => {
  return credentialTypesNodeDescription.value
    .filter((credentialTypeDescription) => displayCredentials(credentialTypeDescription))
    .map((type) => {
      return {
        type,
        options: getCredentialOptions(getAllRelatedCredentialTypes(type)),
      }
    })
})

// Select most recent credential by default
watch(
  credentialTypesNodeDescriptionDisplayed,
  (types) => {
    if (types.length === 0 || !isEmpty(selected.value)) {
      return
    }

    const allOptions = types.map((type) => type.options).flat()

    if (allOptions.length === 0) {
      return
    }

    const mostRecentCredential = allOptions.reduce(
      (mostRecent, current) =>
        mostRecent && mostRecent.updatedAt > current.updatedAt ? mostRecent : current,
      allOptions[0],
    )

    onCredentialSelected(mostRecentCredential.type, mostRecentCredential.id)
  },
  { immediate: true },
)

const isCredentialExisting = (credentialType: string): boolean => {
  if (!props.activeNode.credentials?.[credentialType]?.id) {
    return false
  }

  const { id } = props.activeNode.credentials[credentialType]
  const options = getCredentialOptions([credentialType])

  return !!options.find((option: ICredentialsResponse) => option.id === id)
}

function getSelectedId(type: string) {
  if (isCredentialExisting(type)) {
    return selected.value[type].id
  }

  return undefined
}

function getSelectedName(type: string) {
  return selected.value?.[type]?.name
}

function getSelectPlaceholder(type: string, issues: string[]) {
  return issues.length && getSelectedName(type)
    ? `${getSelectedName(type)}（不可用）`
    : '选择凭据'
}

function getIssues(credentialTypeName: string): string[] {
  const node = props.activeNode

  if (node.issues?.credentials === undefined) {
    return []
  }

  if (!Object.hasOwn(node.issues.credentials, credentialTypeName)) {
    return []
  }

  return node.issues.credentials[credentialTypeName]
}

const handleEditCredential = (credentialType: string) => {
  const credential = props.activeNode.credentials?.[credentialType]
  assert(credential?.id)

  uiStore.openExistingCredential(credential.id)

  subscribedToCredentialType.value = credentialType
}

const getCredentialsFieldLabel = (credentialType: INodeCredentialDescription): string => {
  if (credentialType.displayName) {
    return credentialType.displayName
  }

  const credentialTypeName = credentialTypeNames.value[credentialType.name]
  const isCredentialOnlyNode = props.activeNode.type.startsWith(CREDENTIAL_ONLY_NODE_PREFIX)

  if (isCredentialOnlyNode) {
    return `${nodeType.value?.displayName ?? credentialTypeName} 的凭据`
  }

  if (!showMixedCredentials(credentialType)) {
    return `${credentialTypeName} 的凭据`
  }

  return '用于连接的凭据'
}

const setFilter = (newFilter = '') => {
  filter.value = newFilter
}

const matches = (needle: string, haystack: string) => {
  return haystack.toLocaleLowerCase().includes(needle)
}

const getOptions = (options: CredentialDropdownOption[]) => {
  return options.filter((o) => matches(filter.value, o.name)).map((o) => ({
    ...o,
    label: o.name,
    value: o.id,
    description: o.typeDisplayName,
  }))
}

const onClickCreateCredential = async (type: ICredentialType | INodeCredentialDescription) => {
  await nextTick()
  createNewCredential(type.name, true, showMixedCredentials(type))
}
</script>

<template>
  <div
    v-if="credentialTypesNodeDescriptionDisplayed.length"
    class="pb-form-field"
  >
    <div
      v-for="{ type, options } of credentialTypesNodeDescriptionDisplayed"
      :key="type.name"
    >
      <FormItem
        v-slot="{ id }: { id: string }"
        :label="getCredentialsFieldLabel(type)"
        labelClass="!font-medium !text-sm"
      >
        <div v-if="readOnly">
          <InputText
            data-testid="node-credentials-select"
            disabled
            :modelValue="getSelectedName(type.name)"
            size="small"
          />
        </div>

        <div
          v-else
          data-testid="node-credentials-select"
        >
          <div class="gap-2 flex-center">
            <ProSelect
              fluid
              :labelId="id"
              :modelValue="getSelectedId(type.name)"
              :options="getOptions(options)"
              :placeholder="getSelectPlaceholder(type.name, getIssues(type.name))"
              @update:modelValue="
                (value) => onCredentialSelected(type.name, value as string, showMixedCredentials(type))
              "
            />

            <!-- <div
              v-if="selected[type.name] && isCredentialExisting(type.name)"
              data-testid="credential-edit-button"
            >
              <ProBtnEdit
                label="更新此凭据"
                onlyIcon
                size="small"
                @click="handleEditCredential(type.name)"
              />
            </div> -->
          </div>

          <div v-if="getIssues(type.name).length && !hideIssues">
            <ParameterIssues :issues="getIssues(type.name)" />
          </div>
        </div>
      </FormItem>
    </div>
  </div>
</template>
