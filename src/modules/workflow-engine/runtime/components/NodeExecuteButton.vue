<script setup lang="ts">
import { consola } from 'consola'
import type { INodeTypeDescription } from 'n8n-workflow'
import {
  AI_TRANSFORM_CODE_GENERATED_FOR_PROMPT,
  AI_TRANSFORM_JS_CODE,
  AI_TRANSFORM_NODE_TYPE,
} from 'n8n-workflow/dist/Constants.js'

import { usePinnedData } from '#wf/composables/usePinnedData'
import { useRunWorkflow } from '#wf/composables/useRunWorkflow'
import {
  CHAT_TRIGGER_NODE_TYPE,
  FORM_TRIGGER_NODE_TYPE,
  MANUAL_TRIGGER_NODE_TYPE,
  WEBHOOK_NODE_TYPE,
} from '#wf/constants/common'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { IUpdateInformation } from '#wf/types/interface'

const NODE_TEST_STEP_POPUP_COUNT_KEY = 'N8N_NODE_TEST_STEP_POPUP_COUNT'
const MAX_POPUP_COUNT = 10
const POPUP_UPDATE_DELAY = 3000

const props = withDefaults(
  defineProps<{
    nodeName: string
    disabled?: boolean
    label?: string
    tooltip?: string
  }>(),
  {
    disabled: false,
    transparent: false,
  },
)

const emit = defineEmits<{
  stopExecution: []
  execute: []
  valueChanged: [value: IUpdateInformation]
}>()

defineOptions({
  inheritAttrs: false,
})

const generateCodeForAiTransform = async (prompt: string, code: string, maxTokens: number) => {
  consola.log('generateCodeForAiTransform', prompt, code, maxTokens)
  throw new Error('Not implemented')
}

const lastPopupCountUpdate = ref(0)
const codeGenerationInProgress = ref(false)

const { runWorkflow, stopCurrentExecution } = useRunWorkflow()

const workflowStore = useWorkflowStore()
const ndvStore = useNDVStore()
const nodeTypesStore = useNodeTypesStore()
const uiStore = useUIStore()

const { $toast } = useNuxtApp()

const node = computed(() => workflowStore.getNodeByName(props.nodeName))
const pinnedData = usePinnedData(node)

const nodeType = computed((): INodeTypeDescription | null => {
  return node.value ? nodeTypesStore.getNodeType(node.value.type, node.value.typeVersion) : null
})

const isNodeRunning = computed(() => {
  if (!uiStore.isActionActive['workflowRunning'] || codeGenerationInProgress.value) {
    return false
  }

  const triggeredNode = workflowStore.executedNode

  return (
    workflowStore.isNodeExecuting(node.value?.name ?? '') || triggeredNode === node.value?.name
  )
})

const isTriggerNode = computed(() => {
  return node.value ? nodeTypesStore.isTriggerNode(node.value.type) : false
})

const isWorkflowRunning = computed(() => uiStore.isActionActive.workflowRunning)

const isManualTriggerNode = computed(() =>
  nodeType.value ? nodeType.value.name === MANUAL_TRIGGER_NODE_TYPE : false,
)

const isChatNode = computed(() =>
  nodeType.value ? nodeType.value.name === CHAT_TRIGGER_NODE_TYPE : false,
)

const isChatChild = computed(() => workflowStore.checkIfNodeHasChatParent(props.nodeName))

const isFormTriggerNode = computed(() =>
  nodeType.value ? nodeType.value.name === FORM_TRIGGER_NODE_TYPE : false,
)

const isPollingTypeNode = computed(() => !!nodeType.value?.polling)

const isScheduleTrigger = computed(() => !!nodeType.value?.group.includes('schedule'))

const isWebhookNode = computed(() =>
  nodeType.value ? nodeType.value.name === WEBHOOK_NODE_TYPE : false,
)

const isListeningForEvents = computed(() => {
  const waitingOnWebhook = workflowStore.executionWaitingForWebhook
  const executedNode = workflowStore.executedNode

  return (
    !!node.value
    && !node.value.disabled
    && isTriggerNode.value
    && waitingOnWebhook
    && (!executedNode || executedNode === props.nodeName)
  )
})

const isListeningForWorkflowEvents = computed(() => {
  return (
    isNodeRunning.value
    && isTriggerNode.value
    && !isScheduleTrigger.value
    && !isManualTriggerNode.value
  )
})

const hasIssues = computed(() =>
  Boolean(node.value?.issues && (node.value.issues.parameters || node.value.issues.credentials)),
)

const disabledHint = computed(() => {
  if (isListeningForEvents.value) {
    return ''
  }

  if (codeGenerationInProgress.value) {
    return '从指令生成代码'
  }

  if (isTriggerNode.value && node?.value?.disabled) {
    return '启用节点以执行'
  }

  if (isTriggerNode.value && hasIssues.value) {
    const activeNode = ndvStore.activeNode

    if (activeNode && activeNode.name !== props.nodeName) {
      return '首先修复之前的节点'
    }

    return '先完成必填字段'
  }

  if (isWorkflowRunning.value && !isNodeRunning.value) {
    return '工作流已在运行'
  }

  return ''
})

const shouldGenerateCode = computed(() => {
  if (node.value?.type !== AI_TRANSFORM_NODE_TYPE) {
    return false
  }

  if (!node.value?.parameters?.instructions) {
    return false
  }

  if (!node.value?.parameters?.jsCode) {
    return true
  }

  if (
    node.value?.parameters[AI_TRANSFORM_CODE_GENERATED_FOR_PROMPT]
    && (node.value?.parameters?.instructions as string).trim()
    !== (node.value?.parameters?.[AI_TRANSFORM_CODE_GENERATED_FOR_PROMPT] as string).trim()
  ) {
    return true
  }

  return false
})

const isLoading = computed(
  () =>
    codeGenerationInProgress.value
    || (isNodeRunning.value && !isListeningForEvents.value && !isListeningForWorkflowEvents.value),
)

const testStepButtonPopupCount = () => {
  return Number(localStorage.getItem(NODE_TEST_STEP_POPUP_COUNT_KEY))
}

const tooltipText = computed(() => {
  if (shouldGenerateCode.value) {
    return '生成代码并运行当前节点'
  }

  if (disabledHint.value) {
    return disabledHint.value
  }

  if (props.tooltip && !isLoading.value && testStepButtonPopupCount() < MAX_POPUP_COUNT) {
    return props.tooltip
  }

  return ''
})

const buttonLabel = computed(() => {
  if (isListeningForEvents.value || isListeningForWorkflowEvents.value) {
    if (isWebhookNode.value) {
      return '停止监听 Webhook'
    }

    return '停止监听'
  }

  if (props.label) {
    return props.label
  }

  if (isChatNode.value) {
    return '测试聊天节点'
  }

  if (isWebhookNode.value) {
    return '监听 Webhook 事件'
  }

  if (isFormTriggerNode.value) {
    return '测试表单触发器'
  }

  if (isPollingTypeNode.value || nodeType.value?.mockManualExecution) {
    return '获取测试事件'
  }

  return '测试该节点'
})

async function stopWaitingForWebhook() {
  try {
    await workflowStore.removeTestWebhook(workflowStore.workflowId)
  }
  catch (error) {
    if (error instanceof Error) {
      $toast.error({
        summary: '停止 Webhook 时出错',
        detail: error.message,
      })
    }
  }
}

const handleMouseOver = () => {
  const count = testStepButtonPopupCount()

  if (count < MAX_POPUP_COUNT && !disabledHint.value && tooltipText.value) {
    const now = Date.now()

    if (!lastPopupCountUpdate.value || now - lastPopupCountUpdate.value >= POPUP_UPDATE_DELAY) {
      localStorage.setItem(NODE_TEST_STEP_POPUP_COUNT_KEY, `${count + 1}`)
      lastPopupCountUpdate.value = now
    }
  }
}

const handleExecuteNode = async () => {
  if (shouldGenerateCode.value) {
    // Generate code if user hasn't clicked 'Generate Code' button
    // and update parameters
    codeGenerationInProgress.value = true

    try {
      $toast.success({
        summary: '代码生成成功',
        detail: `“${node.value?.name}”中的指令已更改`,
      })
      const prompt = node.value?.parameters?.instructions as string
      const updateInformation = await generateCodeForAiTransform(
        prompt,
        `parameters.${AI_TRANSFORM_JS_CODE}`,
        5,
      )

      if (!updateInformation) {
        return
      }

      emit('valueChanged', updateInformation)

      emit('valueChanged', {
        name: `parameters.${AI_TRANSFORM_CODE_GENERATED_FOR_PROMPT}`,
        value: prompt,
      })
    }
    catch (err) {
      if (err instanceof Error) {
        $toast.error({
          summary: '代码生成失败',
          detail: err.message,
        })
      }
    }

    codeGenerationInProgress.value = false
  }

  if (isChatNode.value || (isChatChild.value && ndvStore.isInputPanelEmpty)) {
    ndvStore.setActiveNodeName(null)
    workflowStore.chatPartialExecutionDestinationNode = props.nodeName
    // nodeViewEventBus.emit('openChat')
  }
  else if (isListeningForEvents.value) {
    await stopWaitingForWebhook()
  }
  else if (isListeningForWorkflowEvents.value) {
    await stopCurrentExecution()
    emit('stopExecution')
  }
  else {
    const shouldUnpinAndExecute = false

    if (pinnedData.hasData.value) {
      // const confirmResult = await message.confirm(
      //   i18n.baseText('ndv.pinData.unpinAndExecute.description'),
      //   i18n.baseText('ndv.pinData.unpinAndExecute.title'),
      //   {
      //     confirmButtonText: i18n.baseText('ndv.pinData.unpinAndExecute.confirm'),
      //     cancelButtonText: i18n.baseText('ndv.pinData.unpinAndExecute.cancel'),
      //   },
      // )
      // shouldUnpinAndExecute = confirmResult === MODAL_CONFIRM

      // if (shouldUnpinAndExecute && node.value) {
      //   pinnedData.unsetData('unpin-and-execute-modal')
      // }
    }

    if (!pinnedData.hasData.value || shouldUnpinAndExecute) {
      // await externalHooks.run('nodeExecuteButton.onClick', telemetryPayload)

      await runWorkflow({
        destinationNode: props.nodeName,
        source: 'RunData.ExecuteNodeButton',
      })

      emit('execute')
    }
  }
}
</script>

<template>
  <Button
    v-tooltip.right="{
      value: tooltipText,
    }"
    :disabled="disabled || !!disabledHint"
    :label="buttonLabel"
    :loading="isLoading"
    :title="
      !isTriggerNode && !tooltipText
        ? '运行当前节点。如果之前的节点尚未运行，则也会运行他们'
        : ''
    "
    @click="handleExecuteNode()"
    @mouseover="handleMouseOver()"
  />
</template>
