<script setup lang="ts">
import { camelCase } from 'lodash-es'

import PanelNodeBox from '#wf/components/node-creator/panel/PanelNodeBox.vue'
import BaseNodeIcon from '#wf/components/ui/BaseNodeIcon.vue'
import { subcategoryNodeDescriptions, subcategoryNodeTitles } from '#wf/constants/wf'
import type { SubcategoryItemProps } from '#wf/types/interface'

interface Props {
  item: SubcategoryItemProps
}

const props = defineProps<Props>()

const subcategoryName = computed(() => camelCase(props.item.subcategory || props.item.title))
</script>

<template>
  <PanelNodeBox
    :description="subcategoryNodeDescriptions[subcategoryName as keyof typeof subcategoryNodeDescriptions]"
    hasNextStep
    :isTrigger="false"
    :title="subcategoryNodeTitles[subcategoryName as keyof typeof subcategoryNodeTitles]"
  >
    <template #icon>
      <BaseNodeIcon
        :name="item.icon"
        :size="16"
        type="icon"
        v-bind="item.iconProps"
      />
    </template>
  </PanelNodeBox>
</template>
