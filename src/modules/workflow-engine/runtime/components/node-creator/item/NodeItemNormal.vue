<script setup lang="ts">
import NodeIcon from '#wf/components/NodeIcon.vue'
import { useNodeType } from '#wf/composables/useNodeType'
import {
  COMMUNITY_NODES_INSTALLATION_DOCS_URL,
  CREDENTIAL_ONLY_NODE_PREFIX,
  DEFAULT_SUBCATEGORY,
  HITL_SUBCATEGORY,
} from '#wf/constants/common'
import { useNodeCreatorStore } from '#wf/stores/nodeCreator.store'
import { useViewStacksStore } from '#wf/stores/viewStacks.store'
import type { SimplifiedNodeType } from '#wf/types/interface'
import type { NodeCreatorTag } from '#wf/types/node-create'
import { isCommunityPackageName } from '#wf/utils/nodeTypesUtils'

import PanelNodeBox from '../panel/PanelNodeBox.vue'

interface Props {
  nodeType: SimplifiedNodeType
  subcategory?: string
}

const props = withDefaults(defineProps<Props>(), {
  subcategory: undefined,
})

const { actions } = useNodeCreatorStore()

const viewStacksStore = useViewStacksStore()
const { activeViewStack } = storeToRefs(viewStacksStore)

const { isSubNodeType } = useNodeType({
  nodeType: props.nodeType,
})

const isSendAndWaitCategory = computed(() => activeViewStack.value.subcategory === HITL_SUBCATEGORY)

const nodeActions = computed(() => {
  return actions[props.nodeType.name] || []
})

const hasActions = computed(() => {
  return nodeActions.value.length > 1 && !activeViewStack.value.hideActions
})

const description = computed<string>(() => {
  if (isSendAndWaitCategory.value) {
    return ''
  }

  if (
    props.subcategory === DEFAULT_SUBCATEGORY
    && !props.nodeType.name.startsWith(CREDENTIAL_ONLY_NODE_PREFIX)
  ) {
    return ''
  }

  return props.nodeType.description
})

const hasNextStep = computed(() => hasActions.value && !isSendAndWaitCategory.value)

const isCommunityNode = computed<boolean>(() => isCommunityPackageName(props.nodeType.name))

const displayName = computed<string>(() => {
  const trimmedDisplayName = props.nodeType.displayName.trimEnd()

  return hasActions.value ? trimmedDisplayName.replace('Trigger', '') : trimmedDisplayName
})

const isTrigger = computed<boolean>(() => {
  return props.nodeType.group.includes('trigger') && !hasActions.value
})
</script>

<template>
  <PanelNodeBox
    :description="description"
    :hasNextStep="hasNextStep"
    :isTrigger="isTrigger"
    :tag="nodeType.tag as unknown as NodeCreatorTag"
    :title="displayName"
  >
    <template #icon>
      <div
        v-if="isSubNodeType"
      />
      <NodeIcon :nodeType="nodeType" />
    </template>

    <template
      v-if="isCommunityNode"
      #tooltip
    >
      <p
        v-html="`
          这是我们社区的一个节点。它是 ${nodeType.name.split('.')[0]} 包的一部分。<a href='${COMMUNITY_NODES_INSTALLATION_DOCS_URL}' target='_blank' title='阅读系统文档'>了解更多</a>
        `"
      />
    </template>
  </PanelNodeBox>
</template>
