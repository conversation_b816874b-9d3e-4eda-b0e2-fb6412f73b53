<script setup lang="ts">
import { LinkIcon } from 'lucide-vue-next'

import PanelNodeBox from '#wf/components/node-creator/panel/PanelNodeBox.vue'
import BaseNodeIcon from '#wf/components/ui/BaseNodeIcon.vue'
import type { LinkItemProps } from '#wf/types/interface'

interface Props {
  link: LinkItemProps
}

defineProps<Props>()
</script>

<template>
  <PanelNodeBox
    :description="link.description"
    :isTrigger="false"
    :tag="link.tag"
    :title="link.title"
  >
    <template #icon>
      <BaseNodeIcon
        :circle="false"
        :name="link.icon"
        :showTooltip="false"
        type="icon"
      />
    </template>

    <template #boxRightIcon="{ size }">
      <LinkIcon :size="size" />
    </template>
  </PanelNodeBox>
</template>
