<script setup lang="ts">
import { CircleHelpIcon, PowerIcon } from 'lucide-vue-next'

import NodeItemsRender from '#wf/components/node-creator/panel/NodeItemsRender.vue'
import CollapsePanel from '#wf/components/ui/CollapsePanel.vue'
import type { INodeCreateElement } from '#wf/types/node-create'

interface Props {
  elements?: INodeCreateElement[]
  /** 分组标题 */
  title?: string
  expanded?: boolean
  isTriggerCategory?: boolean
  helpTip?: string
  startDivider?: boolean
  endDivider?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  expanded: true,
  startDivider: false,
  endDivider: true,
})

const emit = defineEmits<{
  selected: [element: INodeCreateElement]
}>()

const actionCount = computed(() => {
  if (Array.isArray(props.elements)) {
    return props.elements.filter(({ type }) => type === 'action').length
  }

  return 0
})

const slots = defineSlots<{
  default: VNode[]
}>()
</script>

<template>
  <div>
    <Divider v-if="startDivider" />

    <CollapsePanel
      :expanded="expanded"
      :title="title"
    >
      <template #header>
        <div class="w-full gap-2 pl-1 flex-center">
          <span class="font-medium">
            {{ title }}
          </span>

          <Badge
            v-if="actionCount > 0"
            class="shrink-0 !font-bold"
            severity="contrast"
            size="small"
            :value="actionCount"
          />

          <PowerIcon
            v-if="isTriggerCategory"
            class="shrink-0"
            :size="14"
          />

          <span class="ml-auto shrink-0">
            <CircleHelpIcon
              v-if="helpTip"
              v-tooltip.top="{ value: helpTip, autoHide: false }"
              :size="14"
            />
          </span>
        </div>
      </template>

      <template #content>
        <div
          v-if="expanded && actionCount > 0 && slots.default"
          class="pb-node-items"
        >
          <slot name="default" />
        </div>

        <NodeItemsRender
          :elements="elements"
          @nodeItemSelect="emit('selected', $event)"
        />
      </template>
    </CollapsePanel>

    <Divider v-if="endDivider" />
  </div>
</template>
