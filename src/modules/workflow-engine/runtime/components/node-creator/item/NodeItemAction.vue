<script setup lang="ts">
import NodeIcon from '#wf/components/NodeIcon.vue'
import { WEBHOOK_NODE_TYPE } from '#wf/constants/common'
import type { ActionTypeDescription } from '#wf/types/node-create'

import PanelNodeBox from '../panel/PanelNodeBox.vue'

interface Props {
  action: ActionTypeDescription
}

defineProps<Props>()

const isTriggerAction = (action: ActionTypeDescription) => {
  return action.name?.toLowerCase().includes('trigger') || action.name === WEBHOOK_NODE_TYPE
}
</script>

<template>
  <PanelNodeBox
    draggable
    :isTrigger="isTriggerAction(action)"
    :title="action.displayName"
  >
    <template #icon>
      <NodeIcon :nodeType="action" />
    </template>
  </PanelNodeBox>
</template>
