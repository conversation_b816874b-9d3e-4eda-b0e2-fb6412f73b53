<script setup lang="ts">
import PanelNodeBox from '#wf/components/node-creator/panel/PanelNodeBox.vue'
import BaseNodeIcon from '#wf/components/ui/BaseNodeIcon.vue'
import type { ViewItemProps } from '#wf/types/interface'

interface Props {
  view: ViewItemProps
}

defineProps<Props>()
</script>

<template>
  <PanelNodeBox
    :description="view.description"
    hasNextStep
    :isTrigger="false"
    :tag="view.tag"
    :title="view.title"
  >
    <template #icon>
      <BaseNodeIcon
        :name="view.icon"
        :size="16"
        type="icon"
      />
    </template>
  </PanelNodeBox>
</template>
