<script setup lang="ts">
import NodeItemAction from '#wf/components/node-creator/item/NodeItemAction.vue'
import NodeItemCategorized from '#wf/components/node-creator/item/NodeItemCategorized.vue'
import NodeItemLabel from '#wf/components/node-creator/item/NodeItemLabel.vue'
import NodeItemLink from '#wf/components/node-creator/item/NodeItemLink.vue'
import NodeItemNormal from '#wf/components/node-creator/item/NodeItemNormal.vue'
import NodeItemSubcategory from '#wf/components/node-creator/item/NodeItemSubcategory.vue'
import NodeItemView from '#wf/components/node-creator/item/NodeItemView.vue'
import type { INodeCreateElement } from '#wf/types/node-create'

withDefaults(defineProps<{
  elements?: INodeCreateElement[]
}>(), {
  elements: () => [],
})

const emit = defineEmits<{
  nodeItemSelect: [element: INodeCreateElement]
}>()

const handleSelect = (element: INodeCreateElement) => {
  emit('nodeItemSelect', element)
}
</script>

<template>
  <div class="flex flex-col gap-node-items">
    <div
      v-for="(ele, idx) of elements"
      :key="ele.key"
    >
      <NodeItemCategorized
        v-if="ele.type === 'section'"
        :elements="ele.children"
        :endDivider="idx !== elements.length - 1"
        :startDivider="!elements.at(idx - 1)?.type.toLowerCase().includes('section')"
        :title="ele.title"
        @selected="handleSelect($event)"
      />

      <div
        v-else
        @click="handleSelect(ele)"
      >
        <NodeItemLabel
          v-if="ele.type === 'label'"
          :item="ele"
        />

        <NodeItemNormal
          v-else-if="ele.type === 'node'"
          :nodeType="ele.properties"
          :subcategory="ele.subcategory"
        />

        <NodeItemAction
          v-else-if="ele.type === 'action'"
          :action="ele.properties"
        />

        <NodeItemSubcategory
          v-else-if="ele.type === 'subcategory'"
          :item="ele.properties"
        />

        <template v-else-if="ele.type === 'view'">
          <div
            v-if="!ele.properties.borderless"
            class="pt-node-items"
          >
            <Divider class="!mt-0" />
          </div>

          <NodeItemView :view="ele.properties" />
        </template>

        <NodeItemLink
          v-else-if="ele.type === 'link'"
          :link="ele.properties"
        />

        <Message
          v-else
          severity="warn"
          size="small"
          @click.stop
        >
          未找到匹配的节点类型：{{ ele.type }}
        </Message>
      </div>
    </div>
  </div>
</template>
