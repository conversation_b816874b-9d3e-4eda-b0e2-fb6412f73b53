<script setup lang="ts">
import { consola } from 'consola'
import type { IDataObject } from 'n8n-workflow'

import OrderSwitcher from '#wf/components/node-creator/panel/OrderSwitcher.vue'
import CalloutMessage from '#wf/components/ui/CalloutMessage.vue'
import { CUSTOM_API_CALL_KEY, OPEN_AI_NODE_MESSAGE_ASSISTANT_TYPE, OPEN_AI_NODE_TYPE, TRIGGER_NODE_CREATOR_VIEW } from '#wf/constants/common'
import { useViewStacksStore } from '#wf/stores/viewStacks.store'
import type { ActionCreateElement, INodeCreateElement } from '#wf/types/node-create'

import { GlobalEvent } from '~/enums/event'

import { useActions } from '../../node-creator/composables/useActions'
import NodeItemCategorized from '../item/NodeItemCategorized.vue'

const viewStacksStore = useViewStacksStore()
const { activeViewStack } = storeToRefs(viewStacksStore)

const {
  getActionData,
  getPlaceholderTriggerActions,
  parseCategoryActions,
  actionsCategoryLocales,
} = useActions()

const rootView = computed(() => activeViewStack.value.rootView)
const isTriggerRootView = computed(() => rootView.value === TRIGGER_NODE_CREATOR_VIEW)

const actions = computed(() => {
  return (activeViewStack.value.items || []).filter(
    (p) => (p as ActionCreateElement).properties.actionKey !== CUSTOM_API_CALL_KEY,
  )
})

function parseActions(base: INodeCreateElement[], locale: string, withLabels = false) {
  return parseCategoryActions(base, locale, withLabels)
}

const search = computed(() => activeViewStack.value.search)

const parsedTriggerActions = computed(() =>
  parseActions(actions.value, actionsCategoryLocales.value.triggers, false),
)
const parsedActionActions = computed(() =>
  parseActions(actions.value, actionsCategoryLocales.value.actions, !search.value),
)

const parsedTriggerActionsBaseline = computed(() =>
  parseActions(
    activeViewStack.value.baselineItems || [],
    actionsCategoryLocales.value.triggers,
    false,
  ),
)

const parsedActionActionsBaseline = computed(() =>
  parseActions(
    activeViewStack.value.baselineItems || [],
    actionsCategoryLocales.value.actions,
    !search.value,
  ),
)

const subcategory = computed(() => activeViewStack.value.subcategory)

const placeholderTriggerActions = getPlaceholderTriggerActions(subcategory.value || '')

const triggerCategoryName = computed(() =>
  parsedTriggerActions.value.length || search.value
    ? actionsCategoryLocales.value.triggers
    : `${actionsCategoryLocales.value.triggers} (${placeholderTriggerActions.length})`,
)

const handleActionNodeSelect = (actionCreateElement: INodeCreateElement) => {
  consola.info('选中的动作：', actionCreateElement.type)
  consola.info(actionCreateElement)

  if (actionCreateElement.type === 'action') {
    const actionData = getActionData(actionCreateElement.properties)
    const isPlaceholderTriggerAction = placeholderTriggerActions.some(
      (p) => p.key === actionCreateElement.key,
    )

    if (isPlaceholderTriggerAction && isTriggerRootView.value) {
      const actionNode = actions.value[0]?.key

      if (actionData.key && actionNode) {
        emitter.emit(GlobalEvent.NodeTypeSelected, [actionData.key, actionNode])
      }
    }
    else if (
      actionData?.key === OPEN_AI_NODE_TYPE
      && (actionData?.value as IDataObject)?.resource === 'assistant'
      && (actionData?.value as IDataObject)?.operation === 'message'
    ) {
      emitter.emit(GlobalEvent.NodeTypeSelected, [OPEN_AI_NODE_MESSAGE_ASSISTANT_TYPE])
    }
    else {
      if (actionData.key) {
        emitter.emit(GlobalEvent.NodeTypeSelected, [actionData.key])
      }
    }
  }
}

const msg = '动作需要由另一个节点触发，例如使用 <strong>定时触发</strong> 节点定期触发。 <a target="_blank" href="https://docs.n8n.io/integrations/builtin/">了解更多</a>'
const triggerHelpTip = '触发器是启动工作流的步骤'
const actionHelpTip = '动作在工作流启动后执行步骤'
</script>

<template>
  <OrderSwitcher
    v-if="rootView"
    :rootView="rootView"
  >
    <template
      v-if="isTriggerRootView || parsedTriggerActionsBaseline.length !== 0"
      #triggers
    >
      <NodeItemCategorized
        :elements="parsedTriggerActions"
        :expanded="isTriggerRootView || parsedActionActions.length === 0"
        :helpTip="triggerHelpTip"
        isTriggerCategory
        :title="triggerCategoryName"
        @selected="handleActionNodeSelect($event)"
      />
    </template>

    <template
      v-if="!isTriggerRootView || parsedActionActionsBaseline.length !== 0"
      #actions
    >
      <NodeItemCategorized
        :elements="parsedActionActions"
        :expanded="!isTriggerRootView || parsedTriggerActions.length === 0"
        :helpTip="actionHelpTip"
        :title="actionsCategoryLocales.actions"
        @selected="handleActionNodeSelect($event)"
      >
        <CalloutMessage
          v-if="!isTriggerRootView"
          :content="msg"
          hideIcon
          severity="info"
          size="small"
        />
      </NodeItemCategorized>
    </template>
  </OrderSwitcher>
</template>
