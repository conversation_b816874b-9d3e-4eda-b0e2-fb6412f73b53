<script setup lang="ts">
import { ChevronRightIcon, PlusIcon, PowerIcon } from 'lucide-vue-next'

import type { NodeCreatorTag } from '#wf/types/node-create'

export interface Props {
  isTrigger?: boolean
  description?: string
  tag?: NodeCreatorTag
  title: string
  /** 是否显示右侧箭头，该箭头指示还有下一级操作 */
  hasNextStep?: boolean
}

defineProps<Props>()

const iconSize = 16

const slots = defineSlots<{
  icon?: VNode
  boxRightIcon?: VNode
}>()
</script>

<template>
  <div class="group/node-item cursor-pointer gap-4 rounded-lg py-2 pl-3 pr-1 flex-center hover:bg-emphasis">
    <div
      v-if="slots.icon"
      class="shrink-0 inline-flex-center"
    >
      <slot name="icon" />
    </div>

    <div class="flex-1">
      <div class="gap-2 flex-center">
        <div class="font-semibold">
          {{ title }}
        </div>

        <Tag
          v-if="tag"
          class="shrink-0 !text-sm !font-medium"
          :severity="tag.type ?? 'secondary'"
          :value="tag.text"
        />

        <PowerIcon
          v-if="isTrigger"
          class="shrink-0"
          :size="14"
        />
      </div>

      <div
        v-if="description"
        class="text-sm text-secondary"
      >
        {{ description }}
      </div>
    </div>

    <span class="ml-auto shrink-0 p-2 opacity-0 group-hover/node-item:opacity-100">
      <slot
        name="boxRightIcon"
        :size="iconSize"
      >
        <ChevronRightIcon
          v-if="hasNextStep"
          :size="iconSize"
        />

        <PlusIcon
          v-else
          class="text-[var(--color-canvas-accent)]"
          :size="iconSize"
        />
      </slot>
    </span>
  </div>
</template>
