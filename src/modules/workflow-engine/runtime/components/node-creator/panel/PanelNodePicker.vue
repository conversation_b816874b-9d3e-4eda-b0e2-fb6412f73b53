<script setup lang="ts">
import NodePickListAction from '#wf/components/node-creator/panel/NodePickListAction.vue'
import NodePickListNormal from '#wf/components/node-creator/panel/NodePickListNormal.vue'
import PanelHeader from '#wf/components/node-creator/panel/PanelHeader.vue'
import { AINodesView, AIView, RegularView, TriggerView } from '#wf/components/node-creator/viewsData'
import { AI_NODE_CREATOR_VIEW, AI_OTHERS_NODE_CREATOR_VIEW, AI_UNCATEGORIZED_CATEGORY, REGULAR_NODE_CREATOR_VIEW, TRIGGER_NODE_CREATOR_VIEW } from '#wf/constants/common'
import { useNodeCreatorStore } from '#wf/stores/nodeCreator.store'
import { useViewStacksStore } from '#wf/stores/viewStacks.store'
import type { INodeCreateElement } from '#wf/types/node-create'

import type SearchInput from '~/components/search/SearchInput.vue'

const nodeCreatorStore = useNodeCreatorStore()
const { selectedView, mergedNodes } = storeToRefs(nodeCreatorStore)

const viewStacksStore = useViewStacksStore()
const { viewStacks, activeViewStack, activeViewStackMode } = storeToRefs(viewStacksStore)

const { refKey: inputRefKey, ref: inputRef } = useRef<
  InstanceType<typeof SearchInput>
>()

watch(
  activeViewStack,
  () => {
    if (activeViewStack.value.hasSearch) {
      nextTick(() => {
        // 在首次打开时，需要添加延迟聚焦，否则会破坏 dialog 弹出动画（未知原因）。
        const hackDelay = viewStacks.value.length > 1 ? 0 : 200

        setTimeout(() => {
          inputRef.value?.focus()
        }, hackDelay)
      })
    }
  },
)

watch(
  selectedView,
  (selectedView) => {
    const views = {
      [TRIGGER_NODE_CREATOR_VIEW]: TriggerView,
      [REGULAR_NODE_CREATOR_VIEW]: RegularView,
      [AI_NODE_CREATOR_VIEW]: AIView,
      [AI_OTHERS_NODE_CREATOR_VIEW]: AINodesView,
      [AI_UNCATEGORIZED_CATEGORY]: AINodesView,
    }

    const itemKey = selectedView
    const matchedView = views[itemKey]

    if (matchedView) {
      const view = matchedView(mergedNodes.value)

      viewStacksStore.pushViewStack({
        title: view.title,
        subtitle: view?.subtitle ?? '',
        items: view.items as INodeCreateElement[],
        info: view.info,
        hasSearch: true,
        mode: 'nodes',
        rootView: selectedView,
        // Root search should include all nodes
        searchItems: mergedNodes.value,
      })
    }
  },
  { immediate: true },
)

const isActionsMode = computed(() => activeViewStackMode.value === 'actions')

const searchPlaceholder = computed(() =>
  isActionsMode.value
    ? `搜索 ${activeViewStack.value.title} 动作...`
    : '搜索节点...',
)

const handleSearch = (value: string) => {
  if (activeViewStack.value.uuid) {
    viewStacksStore.updateCurrentViewStack({ search: value })
  }
}
</script>

<template>
  <div
    class="flex h-full flex-col"
    data-testid="wf-node-creator"
  >
    <div class="p-card-container">
      <PanelHeader />
    </div>

    <Divider class="!m-0" />

    <div class="p-card-container pb-0">
      <SearchInput
        v-if="activeViewStack.hasSearch"
        :ref="inputRefKey"
        fluid
        :modelValue="activeViewStack.search"
        :placeholder="searchPlaceholder"
        showClear
        @update:debounceChange="handleSearch"
      />
    </div>

    <div class="flex-1 overflow-y-auto p-card-container">
      <NodePickListAction
        v-if="isActionsMode && activeViewStack.subcategory"
      />

      <NodePickListNormal
        v-else
        :elements="activeViewStack.items"
      />
    </div>
  </div>
</template>
