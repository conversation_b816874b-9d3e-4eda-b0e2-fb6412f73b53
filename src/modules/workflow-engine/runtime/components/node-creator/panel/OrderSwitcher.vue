<script setup lang="ts">
import { REGULAR_NODE_CREATOR_VIEW } from '#wf/constants/common'
import type { NodeFilterType } from '#wf/types/interface'

defineProps<{
  rootView: NodeFilterType
}>()
</script>

<template>
  <template v-if="rootView === REGULAR_NODE_CREATOR_VIEW">
    <slot name="actions" />
    <slot name="triggers" />
  </template>

  <template v-else>
    <slot name="triggers" />
    <slot name="actions" />
  </template>
</template>
