<script setup lang="ts">
import { ChevronLeftIcon, XIcon } from 'lucide-vue-next'

import BaseNodeIcon, { type NodeIconType } from '#wf/components/ui/BaseNodeIcon.vue'
import { useNodeCreatorStore } from '#wf/stores/nodeCreator.store'
import { useViewStacksStore } from '#wf/stores/viewStacks.store'

const viewStacksStore = useViewStacksStore()
const { viewStacks, activeViewStack } = storeToRefs(viewStacksStore)

const nodeCreatorStore = useNodeCreatorStore()

const handleBack = () => {
  viewStacksStore.popViewStack()
}
</script>

<template>
  <div class="flex gap-2">
    <div
      v-if="viewStacks.length > 1 && !activeViewStack.preventBack"
      class="inline shrink-0"
    >
      <ProBtn
        severity="contrast"
        size="small"
        @click="handleBack()"
      >
        <template #icon>
          <ChevronLeftIcon
            :size="14"
            :strokeWidth="2.5"
          />
        </template>
      </ProBtn>
    </div>

    <div class="flex-1 overflow-hidden">
      <div class="gap-2 pt-1 inline-flex-center">
        <BaseNodeIcon
          v-if="activeViewStack.nodeIcon"
          :color="activeViewStack.nodeIcon.color"
          :name="activeViewStack.nodeIcon.icon as string"
          :size="14"
          :src="activeViewStack.nodeIcon.icon as string"
          :type="(activeViewStack.nodeIcon.iconType || 'unknown') as NodeIconType"
        />

        <div
          v-if="activeViewStack.title"
          class="truncate font-semibold"
        >
          {{ activeViewStack.title }}
        </div>
      </div>

      <div
        v-if="activeViewStack.subtitle"
        class="pt-0.5 text-sm text-secondary"
      >
        {{ activeViewStack.subtitle }}
      </div>
    </div>

    <div class="ml-auto inline shrink-0">
      <ProBtn
        severity="contrast"
        size="small"
        variant="text"
        @click="nodeCreatorStore.closeNodeCreator()"
      >
        <template #icon>
          <XIcon :size="14" />
        </template>
      </ProBtn>
    </div>
  </div>
</template>
