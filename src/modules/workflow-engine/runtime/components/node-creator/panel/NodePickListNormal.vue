<script setup lang="ts">
import { consola } from 'consola'
import { camelCase } from 'lodash-es'
import type { INodeParameters } from 'n8n-workflow'
import { SEND_AND_WAIT_OPERATION } from 'n8n-workflow/dist/Constants.js'

import NodeItemsRender from '#wf/components/node-creator/panel/NodeItemsRender.vue'
import { AINodesView, AIView, RegularView, TriggerView } from '#wf/components/node-creator/viewsData'
import { AI_NODE_CREATOR_VIEW, AI_OTHERS_NODE_CREATOR_VIEW, HITL_SUBCATEGORY, REGULAR_NODE_CREATOR_VIEW, TRIGGER_NODE_CREATOR_VIEW } from '#wf/constants/common'
import { subcategoryNodeInfos, subcategoryNodeTitles } from '#wf/constants/wf'
import { useNodeCreatorStore } from '#wf/stores/nodeCreator.store'
import { useRootStore } from '#wf/stores/root.store'
import { useViewStacksStore } from '#wf/stores/viewStacks.store'
import type { NodeFilterType } from '#wf/types/interface'
import type { ActionTypeDescription, INodeCreateElement, NodeCreateElement } from '#wf/types/node-create'
import { getNodeIcon, getNodeIconColor, getNodeIconUrl } from '#wf/utils/nodeTypesUtils'

import { GlobalEvent } from '~/enums/event'

import { useActions } from '../composables/useActions'
import { transformNodeType } from '../utils'

defineProps<{
  elements?: INodeCreateElement[]
}>()

const viewStackStore = useViewStacksStore()
const { activeViewStack } = storeToRefs(viewStackStore)

const nodeCreatorStore = useNodeCreatorStore()
const { mergedNodes, actions } = storeToRefs(nodeCreatorStore)

const rootStore = useRootStore()
const { themeMode } = useTheme()

const selectNodeType = (nodeTypes: string[]) => {
  emitter.emit(GlobalEvent.NodeTypeSelected, nodeTypes)
}

const getHumanInTheLoopActions = (nodeActions: ActionTypeDescription[]) => {
  return nodeActions.filter((action) => action.actionKey === SEND_AND_WAIT_OPERATION)
}

const getFilteredActions = (node: NodeCreateElement) => {
  const nodeActions = actions.value?.[node.key] || []

  if (activeViewStack.value.subcategory === HITL_SUBCATEGORY) {
    return getHumanInTheLoopActions(nodeActions)
  }

  if (activeViewStack.value.actionsFilter) {
    return activeViewStack.value.actionsFilter(nodeActions)
  }

  return nodeActions
}

const baseSubcategoriesFilter = (item: INodeCreateElement): boolean => {
  if (item.type === 'section') {
    return true
  }

  if (item.type !== 'node') {
    return false
  }

  const hasTriggerGroup = item.properties.group.includes('trigger')
  const nodeActions = getFilteredActions(item)
  const hasActions = nodeActions.length > 0

  const isTriggerRootView = activeViewStack.value.rootView === TRIGGER_NODE_CREATOR_VIEW

  if (isTriggerRootView) {
    return hasActions || hasTriggerGroup
  }

  return hasActions || !hasTriggerGroup
}

const subcategoriesMapper = (item: INodeCreateElement) => {
  if (item.type !== 'node') {
    return item
  }

  const hasTriggerGroup = item.properties.group.includes('trigger')
  const nodeActions = getFilteredActions(item)
  const hasActions = nodeActions.length > 0

  if (hasTriggerGroup && hasActions) {
    if (item.properties?.codex) {
      // Store the original name in the alias so we can search for it
      item.properties.codex.alias = [
        ...(item.properties.codex?.alias || []),
        item.properties.displayName,
      ]
    }

    item.properties.displayName = item.properties.displayName.replace(' Trigger', '')
  }

  return item
}

const { setAddedNodeActionParameters } = useActions()

const handleNodeItemSelect = (item: INodeCreateElement) => {
  consola.info('选中的节点：', item.type)
  consola.info(item)

  switch (item.type) {
    case 'subcategory': {
      const subcategoryKey = camelCase(item.properties.title)
      const title = subcategoryNodeTitles[subcategoryKey as keyof typeof subcategoryNodeTitles]

      const info = subcategoryNodeInfos[subcategoryKey as keyof typeof subcategoryNodeInfos]

      viewStackStore.pushViewStack({
        subcategory: item.key,
        mode: 'nodes',
        title,
        info,
        ...(item.properties.icon
          ? {
              nodeIcon: {
                icon: item.properties.icon,
                iconType: 'icon',
              },
            }
          : {}),
        ...(item.properties.panelClass ? { panelClass: item.properties.panelClass } : {}),
        rootView: activeViewStack.value.rootView,
        forceIncludeNodes: item.properties.forceIncludeNodes,
        baseFilter: baseSubcategoriesFilter,
        itemsMapper: subcategoriesMapper,
        sections: item.properties.sections,
      })

      break
    }

    case 'node': {
      const nodeActions = getFilteredActions(item)

      // 如果只有一个动作，使用它
      if (nodeActions.length === 1) {
        selectNodeType([item.key])
        setAddedNodeActionParameters({
          name: nodeActions[0].defaults.name ?? item.properties.displayName,
          key: item.key,
          value: nodeActions[0].values as INodeParameters,
        })
      }
      // 如果没有任何动作，或者当前视图没有动作，则只显示节点
      else if (nodeActions.length === 0 || activeViewStack.value.hideActions) {
        selectNodeType([item.key])
      }
      // 其他情况，显示动作列表
      else {
        const iconUrl = getNodeIconUrl(item.properties, themeMode.value)
        const icon = iconUrl
          ? rootStore.baseUrl + iconUrl
          : getNodeIcon(item.properties, themeMode.value)?.split(':')[1]

        const transformedActions = nodeActions?.map((a) =>
          transformNodeType(a, item.properties.displayName, 'action'),
        )

        viewStackStore.pushViewStack({
          subcategory: item.properties.displayName,
          title: item.properties.displayName,
          nodeIcon: {
            color: getNodeIconColor(item.properties),
            icon,
            iconType: iconUrl ? 'file' : 'icon',
          },

          rootView: activeViewStack.value.rootView,
          hasSearch: true,
          mode: 'actions',
          items: transformedActions,
        })
      }

      break
    }

    case 'view': {
      const views = {
        [TRIGGER_NODE_CREATOR_VIEW]: TriggerView,
        [REGULAR_NODE_CREATOR_VIEW]: RegularView,
        [AI_NODE_CREATOR_VIEW]: AIView,
        [AI_OTHERS_NODE_CREATOR_VIEW]: AINodesView,
      }

      const itemKey = item.key as keyof typeof views
      const matchedView = views[itemKey]

      if (matchedView) {
        const view = matchedView(mergedNodes.value)

        viewStackStore.pushViewStack({
          title: view.title,
          subtitle: view?.subtitle ?? '',
          info: view?.info ?? '',
          items: view.items as INodeCreateElement[],
          hasSearch: true,
          rootView: view.value as NodeFilterType,
          mode: 'nodes',
          // Root search should include all nodes
          searchItems: mergedNodes.value,
        })
      }

      break
    }

    case 'link': {
      window.open(item.properties.url, '_blank')
      break
    }
  }
}
</script>

<template>
  <NodeItemsRender
    :elements="elements"
    @nodeItemSelect="handleNodeItemSelect($event)"
  />
</template>
