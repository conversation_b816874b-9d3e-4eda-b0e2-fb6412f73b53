<script setup lang="ts">
import PanelNodePicker from '#wf/components/node-creator/panel/PanelNodePicker.vue'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import { useNodeCreatorStore } from '#wf/stores/nodeCreator.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'

import { GlobalEvent } from '~/enums/event'

import { useActions } from './composables/useActions'
import { useActionsGenerator } from './composables/useActionsGeneration'

const nodeCreatorStore = useNodeCreatorStore()
const { isCreateNodeActive } = storeToRefs(nodeCreatorStore)

const nodeTypesStore = useNodeTypesStore()
const { visibleNodeTypes } = storeToRefs(nodeTypesStore)

const credentialsStore = useCredentialsStore()
const { httpOnlyCredentialTypes } = storeToRefs(credentialsStore)

const { generateMergedNodesAndActions } = useActionsGenerator()

const handleCloseNodeCreator = () => {
  nodeCreatorStore.closeNodeCreator()
}

const handleModalVisibleChange = (visible: boolean) => {
  if (!visible) {
    handleCloseNodeCreator()
  }
}

watch(
  [visibleNodeTypes, httpOnlyCredentialTypes],
  () => {
    const { actions, mergedNodes } = generateMergedNodesAndActions(visibleNodeTypes.value, httpOnlyCredentialTypes.value)

    nodeCreatorStore.setActions(actions)
    nodeCreatorStore.setMergeNodes(mergedNodes)
  },
  { immediate: true },
)

const { getAddedNodesAndConnections } = useActions()

const handleNodeTypeSelected = (nodeTypes: string[]) => {
  const addedNodesAndConnections = getAddedNodesAndConnections(
    nodeTypes.map((type) => ({ type })),
  )
  emitter.emit(GlobalEvent.NodeAdd, addedNodesAndConnections)

  handleCloseNodeCreator()
}

const { on } = useEmitter()

onMounted(() => {
  on(GlobalEvent.NodeTypeSelected, handleNodeTypeSelected)
})
</script>

<template>
  <Dialog
    appendTo="self"
    closable
    :dismissableMask="false"
    :draggable="false"
    modal
    position="right"
    :pt="{
      mask: { class: '!pointer-events-none !absolute !bg-transparent !p-card-container' },
      root: { class: '!pointer-events-auto !m-0 !h-full !max-h-none !w-[360px] !transition' },
      content: { class: '!h-full !p-0' },
    }"
    :showHeader="false"
    :visible="!!isCreateNodeActive"
    @update:visible="handleModalVisibleChange"
  >
    <PanelNodePicker />
  </Dialog>
</template>
