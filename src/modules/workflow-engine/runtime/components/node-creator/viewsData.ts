import { camelCase } from 'lodash-es'
import type { INodeTypeDescription, Themed } from 'n8n-workflow'

import { NodeConnectionType } from '#wf/constants/canvas'
import {
  AGGREGATE_NODE_TYPE,
  AI_CATEGORY_AGENTS,
  AI_CATEGORY_CHAINS,
  AI_CATEGORY_DOCUMENT_LOADERS,
  AI_CATEGORY_EMBEDDING,
  AI_CATEGORY_LANGUAGE_MODELS,
  AI_CATEGORY_MEMORY,
  AI_CATEGORY_OUTPUTPARSER,
  AI_CATEGORY_RETRIEVERS,
  AI_CATEGORY_TEXT_SPLITTERS,
  AI_CATEGORY_TOOLS,
  AI_CATEGORY_VECTOR_STORES,
  AI_CODE_TOOL_LANGCHAIN_NODE_TYPE,
  AI_NODE_CREATOR_VIEW,
  AI_OTHERS_NODE_CREATOR_VIEW,
  AI_S<PERSON><PERSON>ATEGORY,
  AI_TRANSFORM_NODE_TYPE,
  AI_UNCATEGORIZED_CATEGORY,
  AI_WORKFLOW_TOOL_LANGCHAIN_NODE_TYPE,
  CHAT_TRIGGER_NODE_TYPE,
  CODE_NODE_TYPE,
  COMPRESSION_NODE_TYPE,
  CONVERT_TO_FILE_NODE_TYPE,
  CORE_NODES_CATEGORY,
  CRYPTO_NODE_TYPE,
  DATETIME_NODE_TYPE,
  DEFAULT_SUBCATEGORY,
  EDIT_IMAGE_NODE_TYPE,
  EMAIL_IMAP_NODE_TYPE,
  EMAIL_SEND_NODE_TYPE,
  EXECUTE_WORKFLOW_TRIGGER_NODE_TYPE,
  EXTRACT_FROM_FILE_NODE_TYPE,
  FILTER_NODE_TYPE,
  FLOWS_CONTROL_SUBCATEGORY,
  FORM_TRIGGER_NODE_TYPE,
  HELPERS_SUBCATEGORY,
  HITL_SUBCATEGORY,
  HTML_NODE_TYPE,
  HTTP_REQUEST_NODE_TYPE,
  HUMAN_IN_THE_LOOP_CATEGORY,
  IF_NODE_TYPE,
  LIMIT_NODE_TYPE,
  MANUAL_TRIGGER_NODE_TYPE,
  MARKDOWN_NODE_TYPE,
  MERGE_NODE_TYPE,
  OTHER_TRIGGER_NODES_SUBCATEGORY,
  REGULAR_NODE_CREATOR_VIEW,
  REMOVE_DUPLICATES_NODE_TYPE,
  RSS_READ_NODE_TYPE,
  SCHEDULE_TRIGGER_NODE_TYPE,
  SET_NODE_TYPE,
  SPLIT_IN_BATCHES_NODE_TYPE,
  SPLIT_OUT_NODE_TYPE,
  SUMMARIZE_NODE_TYPE,
  TRANSFORM_DATA_SUBCATEGORY,
  TRIGGER_NODE_CREATOR_VIEW,
  WEBHOOK_NODE_TYPE,
  XML_NODE_TYPE,
} from '#wf/constants/common'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useTemplatesStore } from '#wf/stores/templates.store'
import type { SimplifiedNodeType } from '#wf/types/interface'

export interface NodeViewItemSection {
  key: string
  title: string
  items: string[]
}

export interface NodeViewItem {
  key: string
  type: string
  properties: {
    name?: string
    title?: string
    icon?: Themed<string>
    iconProps?: {
      color?: string
    }
    info?: string
    url?: string
    connectionType?: NodeConnectionType
    panelClass?: string
    group?: string[]
    sections?: NodeViewItemSection[]
    description?: string
    displayName?: string
    tag?: {
      type: string
      text: string
    }
    forceIncludeNodes?: string[]
    iconData?: {
      type: string
      icon?: string
      fileBuffer?: string
    }
  }
  category?: string | string[]
}

interface NodeView {
  value: string
  title: string
  info?: string
  subtitle?: string
  items: NodeViewItem[]
}

function getAiNodesBySubcategory(nodes: INodeTypeDescription[], subcategory: string) {
  return nodes
    .filter(
      (node) => !node.hidden && node.codex?.subcategories?.[AI_SUBCATEGORY]?.includes(subcategory),
    )
    .map((node) => ({
      key: node.name,
      type: 'node',
      properties: {
        group: [],
        name: node.name,
        displayName: node.displayName,
        title: node.displayName,
        description: node.description,

        icon: node.icon!,
        iconUrl: node.iconUrl,
      },
    }))
    .sort((a, b) => a.properties.displayName.localeCompare(b.properties.displayName))
}

export function AIView(_nodes: SimplifiedNodeType[]): NodeView {
  const nodeTypesStore = useNodeTypesStore()
  const templatesStore = useTemplatesStore()

  const chainNodes = getAiNodesBySubcategory(nodeTypesStore.allLatestNodeTypes, AI_CATEGORY_CHAINS)
  const agentNodes = getAiNodesBySubcategory(nodeTypesStore.allLatestNodeTypes, AI_CATEGORY_AGENTS)

  const websiteCategoryURLParams = templatesStore.websiteTemplateRepositoryParameters
  websiteCategoryURLParams.append('utm_user_role', 'AdvancedAI')

  return {
    value: AI_NODE_CREATOR_VIEW,
    title: 'AI 节点',
    subtitle: '选择要添加到工作流的 AI 节点',
    items: [
      ...agentNodes,
      ...chainNodes,
      {
        key: AI_OTHERS_NODE_CREATOR_VIEW,
        type: 'view',
        properties: {
          title: '其他 AI 节点',
          icon: 'robot',
          description: '包含嵌入、向量存储、大语言模型等丰富 AI 节点',
        },
      },
    ],
  }
}

export function AINodesView(_nodes: SimplifiedNodeType[]): NodeView {
  function getAISubcategoryProperties(nodeConnectionType: NodeConnectionType) {
    return {
      connectionType: nodeConnectionType,
      iconProps: {
        color: `var(--node-type-${nodeConnectionType}-color)`,
      },
      panelClass: `nodes-list-panel-${nodeConnectionType}`,
    }
  }

  function getSubcategoryInfo(subcategory: string) {
    const infoText = {
      languageModels: 'Chat 模型设计用于交互式对话，并遵循指令良好，而文本完成模型专注于生成给定文本输入的延续',
      memory: 'Memory 允许 AI 模型记住并引用它过去的交互',
      vectorStores: '向量存储允许 AI 模型引用文档的相关部分，这对于问答和文档搜索非常有用',
    }

    const info = infoText[camelCase(subcategory) as keyof typeof infoText]

    if (!info) {
      return undefined
    }

    return info
  }

  return {
    value: AI_OTHERS_NODE_CREATOR_VIEW,
    title: '其他 AI 节点',
    subtitle: '选择要添加到工作流的 AI 节点',
    items: [
      {
        key: AI_CATEGORY_DOCUMENT_LOADERS,
        type: 'subcategory',
        properties: {
          title: AI_CATEGORY_DOCUMENT_LOADERS,
          info: getSubcategoryInfo(AI_CATEGORY_DOCUMENT_LOADERS),
          icon: 'file-import',
          ...getAISubcategoryProperties(NodeConnectionType.AiDocument),
        },
      },
      {
        key: AI_CATEGORY_LANGUAGE_MODELS,
        type: 'subcategory',
        properties: {
          title: AI_CATEGORY_LANGUAGE_MODELS,
          info: getSubcategoryInfo(AI_CATEGORY_LANGUAGE_MODELS),
          icon: 'language',
          ...getAISubcategoryProperties(NodeConnectionType.AiLanguageModel),
        },
      },
      {
        key: AI_CATEGORY_MEMORY,
        type: 'subcategory',
        properties: {
          title: AI_CATEGORY_MEMORY,
          info: getSubcategoryInfo(AI_CATEGORY_MEMORY),
          icon: 'brain',
          ...getAISubcategoryProperties(NodeConnectionType.AiMemory),
        },
      },
      {
        key: AI_CATEGORY_OUTPUTPARSER,
        type: 'subcategory',
        properties: {
          title: AI_CATEGORY_OUTPUTPARSER,
          info: getSubcategoryInfo(AI_CATEGORY_OUTPUTPARSER),
          icon: 'list',
          ...getAISubcategoryProperties(NodeConnectionType.AiOutputParser),
        },
      },
      {
        key: AI_CATEGORY_RETRIEVERS,
        type: 'subcategory',
        properties: {
          title: AI_CATEGORY_RETRIEVERS,
          info: getSubcategoryInfo(AI_CATEGORY_RETRIEVERS),
          icon: 'search',
          ...getAISubcategoryProperties(NodeConnectionType.AiRetriever),
        },
      },
      {
        key: AI_CATEGORY_TEXT_SPLITTERS,
        type: 'subcategory',
        properties: {
          title: AI_CATEGORY_TEXT_SPLITTERS,
          info: getSubcategoryInfo(AI_CATEGORY_TEXT_SPLITTERS),
          icon: 'grip-lines-vertical',
          ...getAISubcategoryProperties(NodeConnectionType.AiTextSplitter),
        },
      },
      {
        type: 'subcategory',
        key: AI_CATEGORY_TOOLS,
        category: CORE_NODES_CATEGORY,
        properties: {
          title: AI_CATEGORY_TOOLS,
          info: getSubcategoryInfo(AI_CATEGORY_TOOLS),
          icon: 'tools',
          ...getAISubcategoryProperties(NodeConnectionType.AiTool),
          sections: [
            {
              key: 'popular',
              title: '常用',
              items: [AI_WORKFLOW_TOOL_LANGCHAIN_NODE_TYPE, AI_CODE_TOOL_LANGCHAIN_NODE_TYPE],
            },
          ],
        },
      },
      {
        key: AI_CATEGORY_EMBEDDING,
        type: 'subcategory',
        properties: {
          title: AI_CATEGORY_EMBEDDING,
          info: getSubcategoryInfo(AI_CATEGORY_EMBEDDING),
          icon: 'vector-square',
          ...getAISubcategoryProperties(NodeConnectionType.AiEmbedding),
        },
      },
      {
        key: AI_CATEGORY_VECTOR_STORES,
        type: 'subcategory',
        properties: {
          title: AI_CATEGORY_VECTOR_STORES,
          info: getSubcategoryInfo(AI_CATEGORY_VECTOR_STORES),
          icon: 'project-diagram',
          ...getAISubcategoryProperties(NodeConnectionType.AiVectorStore),
        },
      },
      {
        key: AI_UNCATEGORIZED_CATEGORY,
        type: 'subcategory',
        properties: {
          title: AI_UNCATEGORIZED_CATEGORY,
          icon: 'code',
        },
      },
    ],
  }
}

export function TriggerView() {
  const view: NodeView = {
    value: TRIGGER_NODE_CREATOR_VIEW,
    title: '什么触发了此工作流？',
    subtitle: '触发器是启动工作流的步骤',
    items: [
      {
        key: MANUAL_TRIGGER_NODE_TYPE,
        type: 'node',
        category: [CORE_NODES_CATEGORY],
        properties: {
          group: [],
          name: MANUAL_TRIGGER_NODE_TYPE,
          displayName: '手动触发',
          description: '点击按钮即可立即启动工作流',
          icon: 'fa:mouse-pointer',
        },
      },
      {
        key: DEFAULT_SUBCATEGORY,
        type: 'subcategory',
        properties: {
          forceIncludeNodes: [WEBHOOK_NODE_TYPE, EMAIL_IMAP_NODE_TYPE],
          title: 'App Trigger Nodes',
          icon: 'satellite-dish',
        },
      },
      {
        key: SCHEDULE_TRIGGER_NODE_TYPE,
        type: 'node',
        category: [CORE_NODES_CATEGORY],
        properties: {
          group: [],
          name: SCHEDULE_TRIGGER_NODE_TYPE,
          displayName: '定时触发',
          description: '按设定的时间间隔自动运行工作流',
          icon: 'fa:clock',
        },
      },
      {
        key: WEBHOOK_NODE_TYPE,
        type: 'node',
        category: [CORE_NODES_CATEGORY],
        properties: {
          group: [],
          name: WEBHOOK_NODE_TYPE,
          displayName: 'Webhook 触发',
          description: '收到外部 Webhook 请求时自动启动工作流',
          iconData: {
            type: 'file',
            icon: 'webhook',
            fileBuffer: '/static/webhook-icon.svg',
          },
        },
      },
      {
        key: FORM_TRIGGER_NODE_TYPE,
        type: 'node',
        category: [CORE_NODES_CATEGORY],
        properties: {
          group: [],
          name: FORM_TRIGGER_NODE_TYPE,
          displayName: '表单提交触发',
          description: '用户提交 Web 表单后自动触发工作流',
          iconData: {
            type: 'file',
            icon: 'form',
            fileBuffer: '/static/form-grey.svg',
          },
        },
      },
      {
        key: EXECUTE_WORKFLOW_TRIGGER_NODE_TYPE,
        type: 'node',
        category: [CORE_NODES_CATEGORY],
        properties: {
          group: [],
          name: EXECUTE_WORKFLOW_TRIGGER_NODE_TYPE,
          displayName: '工作流调用触发',
          description: '被其他工作流通过"执行工作流"节点调用时触发',
          icon: 'fa:sign-out-alt',
        },
      },
      {
        key: CHAT_TRIGGER_NODE_TYPE,
        type: 'node',
        category: [CORE_NODES_CATEGORY],
        properties: {
          group: [],
          name: CHAT_TRIGGER_NODE_TYPE,
          displayName: '聊天消息触发',
          description: '用户发送聊天消息时自动启动 AI 工作流',
          icon: 'fa:comments',
        },
      },
      {
        type: 'subcategory',
        key: OTHER_TRIGGER_NODES_SUBCATEGORY,
        category: CORE_NODES_CATEGORY,
        properties: {
          title: OTHER_TRIGGER_NODES_SUBCATEGORY,
          icon: 'folder-open',
        },
      },
    ],
  }

  return view
}

export function RegularView(nodes: SimplifiedNodeType[]) {
  const popularItemsSubcategory = [
    SET_NODE_TYPE,
    CODE_NODE_TYPE,
    DATETIME_NODE_TYPE,
    AI_TRANSFORM_NODE_TYPE,
  ]

  const getSendAndWaitNodes = (nodes: SimplifiedNodeType[]) => {
    return (nodes ?? [])
      .filter((node) => node.codex?.categories?.includes(HUMAN_IN_THE_LOOP_CATEGORY))
      .map((node) => node.name)
  }

  const view: NodeView = {
    value: REGULAR_NODE_CREATOR_VIEW,
    title: '选择要添加的节点',
    items: [
      {
        key: DEFAULT_SUBCATEGORY,
        type: 'subcategory',
        properties: {
          title: 'App Regular Nodes',
          icon: 'globe',
          forceIncludeNodes: [RSS_READ_NODE_TYPE, EMAIL_SEND_NODE_TYPE],
        },
      },
      {
        type: 'subcategory',
        key: TRANSFORM_DATA_SUBCATEGORY,
        category: CORE_NODES_CATEGORY,
        properties: {
          title: TRANSFORM_DATA_SUBCATEGORY,
          icon: 'pen',
          sections: [
            {
              key: 'popular',
              title: '常用',
              items: popularItemsSubcategory,
            },
            {
              key: 'addOrRemove',
              title: '添加或移除项',
              items: [
                FILTER_NODE_TYPE,
                REMOVE_DUPLICATES_NODE_TYPE,
                SPLIT_OUT_NODE_TYPE,
                LIMIT_NODE_TYPE,
              ],
            },
            {
              key: 'combine',
              title: '合并项',
              items: [SUMMARIZE_NODE_TYPE, AGGREGATE_NODE_TYPE, MERGE_NODE_TYPE],
            },
            {
              key: 'convert',
              title: '转换数据',
              items: [
                HTML_NODE_TYPE,
                MARKDOWN_NODE_TYPE,
                XML_NODE_TYPE,
                CRYPTO_NODE_TYPE,
                EXTRACT_FROM_FILE_NODE_TYPE,
                CONVERT_TO_FILE_NODE_TYPE,
                COMPRESSION_NODE_TYPE,
                EDIT_IMAGE_NODE_TYPE,
              ],
            },
          ],
        },
      },
      {
        type: 'subcategory',
        key: FLOWS_CONTROL_SUBCATEGORY,
        category: CORE_NODES_CATEGORY,
        properties: {
          title: FLOWS_CONTROL_SUBCATEGORY,
          icon: 'code-branch',
          sections: [
            {
              key: 'popular',
              title: '常用',
              items: [FILTER_NODE_TYPE, IF_NODE_TYPE, SPLIT_IN_BATCHES_NODE_TYPE, MERGE_NODE_TYPE],
            },
          ],
        },
      },
      {
        type: 'subcategory',
        key: HELPERS_SUBCATEGORY,
        category: CORE_NODES_CATEGORY,
        properties: {
          title: HELPERS_SUBCATEGORY,
          icon: 'toolbox',
          sections: [
            {
              key: 'popular',
              title: '常用',
              items: [HTTP_REQUEST_NODE_TYPE, WEBHOOK_NODE_TYPE, CODE_NODE_TYPE],
            },
          ],
        },
      },
      // To add node to this subcategory:
      // - add "HITL" to the "categories" property of the node's codex
      // - add "HITL": ["Human in the Loop"] to the "subcategories" property of the node's codex
      // node has to have the "sendAndWait" operation, if a new operation needs to be included here:
      // - update getHumanInTheLoopActions in packages/editor-ui/src/components/Node/NodeCreator/Modes/NodesMode.vue
      {
        type: 'subcategory',
        key: HITL_SUBCATEGORY,
        category: HUMAN_IN_THE_LOOP_CATEGORY,
        properties: {
          title: HITL_SUBCATEGORY,
          icon: 'user-check',
          sections: [
            {
              key: 'sendAndWait',
              title: '发送并等待响应',
              items: getSendAndWaitNodes(nodes),
            },
          ],
        },
      },
    ],
  }

  const hasAINodes = (nodes ?? []).some((node) => node.codex?.categories?.includes(AI_SUBCATEGORY))

  if (hasAINodes) {
    view.items.unshift({
      key: AI_NODE_CREATOR_VIEW,
      type: 'view',
      properties: {
        title: '智能 AI 节点',
        icon: 'robot',
        description: '构建智能代理、文档摘要、知识问答等 AI 功能',
        borderless: true,
      },
    } as NodeViewItem)
  }

  view.items.push({
    key: TRIGGER_NODE_CREATOR_VIEW,
    type: 'view',
    properties: {
      title: '添加触发器',
      icon: 'bolt',
      description: '为工作流添加更多触发方式，支持多种启动条件',
    },
  })

  return view
}
