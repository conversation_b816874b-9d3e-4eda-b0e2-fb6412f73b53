<script setup lang="ts">
import { ChevronDownIcon, ChevronRightIcon, PowerIcon } from 'lucide-vue-next'
import type { INodeTypeDescription } from 'n8n-workflow'

import NodeIcon from '#wf/components/NodeIcon.vue'
import CalloutMessage from '#wf/components/ui/CalloutMessage.vue'
import type { RenderHeader, Renders } from '#wf/composables/useDataSchema'
import { DATA_EDITING_DOCS_URL } from '#wf/constants/common'

interface Props
  extends Required<Pick<Renders, 'title' | 'id'>>,
  Pick<RenderHeader, | 'collapsable' | 'itemCount' | 'preview'> {
  /** 节点运行的附加上下文信息 */
  info?: string
  collapsed: boolean
  nodeType: INodeTypeDescription
}

const props = defineProps<Props>()

const isTrigger = computed(() => props.nodeType.group.includes('trigger'))
const emit = defineEmits<{
  'click:toggle': []
}>()
</script>

<template>
  <div
    class="pb-[var(--virtual-item-gap)]"
    :data-testid="id"
  >
    <div
      class="text-sm flex-center"
      data-testid="run-data-schema-header"
    >
      <div
        v-if="collapsable"
        class="shrink-0 justify-center inline-flex-center"
      >
        <ProBtn
          mini
          size="small"
          @click.capture.stop="emit('click:toggle')"
        >
          <template #icon="{ size }">
            <ChevronRightIcon
              v-if="collapsed"
              :size="size"
              :strokeWidth="3"
            />
            <ChevronDownIcon
              v-else
              :size="size"
              :strokeWidth="3"
            />
          </template>
        </ProBtn>
      </div>

      <div class="min-w-0 flex-1 px-0.5">
        <div class="inline-block max-w-full rounded-md border border-divider px-1.5 py-0.5">
          <div class="gap-1 flex-center">
            <div class="shrink-0 justify-center flex-center">
              <NodeIcon
                :nodeType="nodeType"
                :size="12"
              />
            </div>

            <div class="truncate font-medium">
              {{ title }}
            </div>

            <div class="shrink-0 pl-1">
              <PowerIcon
                v-if="isTrigger"
                :size="12"
              />
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="itemCount"
        class="ml-auto shrink-0 whitespace-nowrap pl-2 tabular-nums text-secondary"
        data-testid="run-data-schema-node-item-count"
      >
        {{ `${itemCount} 项` }}
      </div>
    </div>

    <div
      v-if="info"
      class="pl-[var(--level-indent)] pt-1 text-sm text-secondary"
    >
      {{ info }}
    </div>

    <div
      v-if="preview && !collapsed"
      class="pl-[var(--level-indent)] pt-[var(--virtual-item-gap)]"
    >
      <CalloutMessage
        data-testid="schema-preview-warning"
        hideIcon
        severity="warn"
        size="small"
      >
        <template #content>
          这是一个预览模式的结构，请执行节点以查看准确的结构和数据。
          <NuxtLink :to="DATA_EDITING_DOCS_URL">
            了解更多
          </NuxtLink>
        </template>
      </CalloutMessage>
    </div>
  </div>
</template>
