<script setup lang="ts">
import { asyncComputed } from '@vueuse/core'
import type {
  IConnectedNode,
  IDataObject,
} from 'n8n-workflow'
import { createResultError } from 'n8n-workflow/dist/result.js'

import { type SchemaNode, useDataSchema, useFlattenSchema } from '#wf/composables/useDataSchema'
import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { NodeConnectionType } from '#wf/constants/canvas'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useSchemaPreviewStore } from '#wf/stores/schemaPreview.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { INodeUi } from '#wf/types/interface'
import { executionDataToJson } from '#wf/utils/nodeTypesUtils'

import VirtualSchemaHeader from './VirtualSchemaHeader.vue'
import VirtualSchemaItem from './VirtualSchemaItem.vue'

type Props = {
  nodes?: IConnectedNode[]
  node?: INodeUi | null
  data?: IDataObject[]
  mappingEnabled?: boolean
  runIndex?: number
  outputIndex?: number
  totalRuns?: number
  paneType: 'input' | 'output'
  connectionType?: NodeConnectionType
  search?: string
}

const props = withDefaults(defineProps<Props>(), {
  nodes: () => [],
  distanceFromActive: 1,
  node: null,
  data: () => [],
  runIndex: 0,
  outputIndex: 0,
  totalRuns: 1,
  connectionType: NodeConnectionType.Main,
  search: '',
  mappingEnabled: false,
})

const ndvStore = useNDVStore()
const nodeTypesStore = useNodeTypesStore()
const workflowsStore = useWorkflowStore()
const schemaPreviewStore = useSchemaPreviewStore()

const { getSchemaForExecutionData, getSchemaForJsonSchema, filterSchema } = useDataSchema()

const { closedNodes, flattenSchema, flattenMultipleSchemas, toggleLeaf, toggleNode } = useFlattenSchema()

const { getNodeInputData } = useNodeHelpers()

const emit = defineEmits<{
  'clear:search': []
}>()

const toggleNodeAndScrollTop = (id: string) => {
  toggleNode(id)
  // scroller.value?.scrollToItem(0)
}

watch(
  () => props.search,
  (newSearch) => {
    if (!newSearch) {
      return
    }

    closedNodes.value.clear()
  },
)

const getSchemaPreview = async (node: INodeUi | null) => {
  if (!node) {
    return createResultError(new Error())
  }

  const {
    type,
    typeVersion,
    parameters: { resource, operation },
  } = node

  return await schemaPreviewStore.getSchemaPreview({
    nodeType: type,
    version: typeVersion,
    resource: resource as string,
    operation: operation as string,
  })
}

const isSchemaPreviewEnabled = computed(() => {
  return true
  // return posthogStore.isFeatureEnabled(SCHEMA_PREVIEW_EXPERIMENT)
})

const getNodeSchema = async (fullNode: INodeUi, connectedNode: IConnectedNode) => {
  const pinData = workflowsStore.pinDataByNodeName(connectedNode.name)
  const connectedOutputIndexes = connectedNode.indicies.length > 0 ? connectedNode.indicies : [0]
  const data = pinData
    ?? connectedOutputIndexes
      .map((outputIndex) =>
        executionDataToJson(
          getNodeInputData(
            fullNode,
            props.runIndex,
            outputIndex,
            props.paneType,
            props.connectionType,
          ),
        ),
      )
      .flat()

  let schema = getSchemaForExecutionData(data)
  let preview = false

  if (data.length === 0 && isSchemaPreviewEnabled.value) {
    const previewSchema = await getSchemaPreview(fullNode)

    if (previewSchema.ok) {
      schema = getSchemaForJsonSchema(previewSchema.result)
      preview = true
    }
  }

  return {
    schema,
    connectedOutputIndexes,
    itemsCount: data.length,
    preview,
  }
}

const nodeSchema = asyncComputed(async () => {
  if (props.data.length === 0 && isSchemaPreviewEnabled.value) {
    const previewSchema = await getSchemaPreview(props.node)

    if (previewSchema.ok) {
      return filterSchema(getSchemaForJsonSchema(previewSchema.result), props.search)
    }
  }

  return filterSchema(getSchemaForExecutionData(props.data), props.search)
})

const nodesSchemas = asyncComputed<SchemaNode[]>(async () => {
  const result: SchemaNode[] = []

  for (const node of props.nodes) {
    const fullNode = workflowsStore.getNodeByName(node.name)

    if (!fullNode) {
      continue
    }

    const nodeType = nodeTypesStore.getNodeType(fullNode.type, fullNode.typeVersion)

    if (!nodeType) {
      continue
    }

    const { schema, connectedOutputIndexes, itemsCount, preview } = await getNodeSchema(
      fullNode,
      node,
    )

    const filteredSchema = filterSchema(schema, props.search)

    if (!filteredSchema) {
      continue
    }

    result.push({
      node: fullNode,
      connectedOutputIndexes,
      depth: node.depth,
      itemsCount,
      nodeType,
      schema: filteredSchema,
      preview,
    })
  }

  return result
}, [])

const nodeAdditionalInfo = (node: INodeUi) => {
  const returnData: string[] = []

  if (node.disabled) {
    returnData.push('已停用')
  }

  const connections = ndvStore.ndvNodeInputNumber[node.name]

  if (connections) {
    if (connections.length === 1) {
      returnData.push(`Input ${connections}`)
    }
    else {
      returnData.push(`Inputs ${connections.join(', ')}`)
    }
  }

  return returnData.length ? `(${returnData.join(' | ')})` : ''
}

const flattenedNodes = computed(() =>
  flattenMultipleSchemas(nodesSchemas.value, nodeAdditionalInfo),
)

const flattenNodeSchema = computed(() =>
  nodeSchema.value ? flattenSchema({ schema: nodeSchema.value, depth: 0, level: -1 }) : [],
)

/**
 * In debug mode nodes are empty
 */
const isDebugging = computed(() => !props.nodes.length)

const items = computed(() => {
  if (isDebugging.value || props.paneType === 'output') {
    return flattenNodeSchema.value
  }

  return flattenedNodes.value
})

const noSearchResults = computed(() => {
  return Boolean(props.search.trim()) && !items.value.length
})

// const onDragStart = () => {
//   ndvStore.resetMappingTelemetry()
// }

// const onDragEnd = (el: HTMLElement) => {
//   setTimeout(() => {
//     const mappingTelemetry = ndvStore.mappingTelemetry
//     const telemetryPayload = {
//       src_node_type: el.dataset.nodeType,
//       src_field_name: el.dataset.name ?? '',
//       src_nodes_back: el.dataset.depth,
//       src_run_index: props.runIndex,
//       src_runs_total: props.totalRuns,
//       src_field_nest_level: el.dataset.level ?? 0,
//       src_view: 'schema',
//       src_element: el,
//       success: false,
//       ...mappingTelemetry,
//     }

//     void useExternalHooks().run('runDataJson.onDragEnd', telemetryPayload)

//     telemetry.track('User dragged data for mapping', telemetryPayload, { withPostHog: true })
//   }, 250) // ensure dest data gets set if drop
// }
</script>

<template>
  <div
    :style="{
      '--level-indent': '1.5rem',
      '--virtual-item-gap': '0.5rem',
    }"
  >
    <div
      v-if="noSearchResults"
      class="flex-col py-8 flex-center"
    >
      <div class="mb-2 text-lg font-bold">
        未找到匹配结果
      </div>

      <p class="text-sm text-secondary">
        没有找到与 "{{ search }}" 相关的字段或节点。请尝试修改关键词，或
        <ProBtn
          label="清空搜索"
          mini
          size="small"
          variant="text"
          @click="emit('clear:search')"
        />
      </p>

      <p
        v-if="paneType === 'output'"
        class="mt-4 text-sm text-secondary"
      >
        如需搜索字段内容，请切换到表格或 JSON 视图。
      </p>
    </div>

    <template
      v-for="item of items"
      :key="item.id"
    >
      <VirtualSchemaHeader
        v-if="item.type === 'header'"
        :id="item.id"
        :collapsable="item.collapsable"
        :collapsed="closedNodes.has(item.id)"
        :itemCount="item.itemCount"
        :nodeType="item.nodeType"
        :title="item.title"
        @click:toggle="toggleLeaf(item.id)"
      />

      <VirtualSchemaItem
        v-else
        v-bind="item"
        :collapsed="closedNodes.has(item.id)"
        :draggable="mappingEnabled"
        :highlight="ndvStore.highlightDraggables"
        :search="search"
        @click:toggle="toggleLeaf(item.id)"
      />
    </template>
  </div>
</template>
