<script setup lang="ts">
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { ChevronDownIcon, ChevronRightIcon } from 'lucide-vue-next'

import type { RenderItem } from '#wf/composables/useDataSchema'

import { TooltipShowDelay } from '~/enums/common'

import TextWithHighlight from './TextWithHighlight.vue'

interface Props extends Pick<RenderItem, 'title' | 'nodeType' | 'collapsable' | 'preview'>, Partial<Pick<RenderItem, 'icon'>> {
  path?: string
  level?: number
  depth?: number
  expression?: string
  value?: string
  highlight?: boolean
  draggable?: boolean
  collapsed?: boolean
  search?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'click:toggle': []
}>()

const { $toast } = useNuxtApp()

const handleCopyExpression = async () => {
  if (props.expression) {
    await copyToClipboard(props.expression)

    $toast.success({
      summary: '已复制表达式',
      detail: '您可以将其粘贴到配置中',
    })
  }
}
</script>

<template>
  <div
    class="pb-[var(--virtual-item-gap)] pl-[var(--level-indent)] flex-center"
    :class="{
      'text-app-accent-500': draggable,
    }"
    data-testid="run-data-schema-item"
  >
    <div
      v-if="collapsable"
      class="shrink-0 justify-center inline-flex-center"
    >
      <ProBtn
        mini
        size="small"
        @click="emit('click:toggle')"
      >
        <template #icon="{ size }">
          <ChevronRightIcon
            v-if="collapsed"
            :size="size"
            :strokeWidth="3"
          />
          <ChevronDownIcon
            v-else
            :size="size"
            :strokeWidth="3"
          />
        </template>
      </ProBtn>
    </div>

    <div
      v-if="title"
      class="flex-1 whitespace-nowrap px-0.5"
    >
      <div
        v-tooltip.top="{ value: '点击复制表达式', showDelay: TooltipShowDelay.Slow }"
        class="cursor-pointer rounded-md border border-dashed border-divider px-1.5 py-0.5 text-sm inline-flex-center"
        :class="{
          'text-app-accent-500': highlight,
        }"
        :data-depth="depth"
        :data-name="title"
        :data-nest-level="level"
        :data-node-type="nodeType"
        :data-path="path"
        data-target="mappable"
        data-testid="run-data-schema-node-name"
        :data-value="expression"
        @click.stop="handleCopyExpression()"
      >
        <div
          v-if="icon"
          class="shrink-0 inline-flex-center"
        >
          <span class="flex-1">
            <FontAwesomeIcon
              :icon="icon"
              size="sm"
            />
          </span>

          <Divider
            class="!mx-1"
            layout="vertical"
          />
        </div>

        <div
          class="flex-1 truncate font-medium"
          :title="title"
        >
          <TextWithHighlight
            :content="title"
            :search="search"
          />
        </div>
      </div>
    </div>

    <div class="pl-2 text-sm text-secondary">
      <TextWithHighlight
        :content="value"
        data-testid="run-data-schema-item-value"
        :search="search"
      />
    </div>
  </div>
</template>
