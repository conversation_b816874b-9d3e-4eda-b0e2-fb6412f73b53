<script setup lang="ts">
import type { GenericValue } from 'n8n-workflow'

const props = defineProps<{
  content: GenericValue
  search?: string
}>()

const splitTextBySearch = (
  text = '',
  search = '',
): Array<{ tag: 'span' | 'mark', content: string }> => {
  if (!search) {
    return [
      {
        tag: 'span',
        content: text,
      },
    ]
  }

  const escapeRegExp = (s: string) => s.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // $& 表示整个匹配的字符串
  const pattern = new RegExp(`(${escapeRegExp(search)})`, 'i')
  const splitText = text.split(pattern)

  return splitText.map((t) => ({ tag: pattern.test(t) ? 'mark' : 'span', content: t }))
}

const parts = computed(() => {
  return props.search && typeof props.content === 'string'
    ? splitTextBySearch(props.content, props.search)
    : []
})
</script>

<template>
  <span class="font-mono">
    <span v-if="parts.length && typeof props.content === 'string'">
      <template v-for="(part, index) of parts">
        <mark
          v-if="part.tag === 'mark' && part.content"
          :key="`mark-${index}`"
          class="rounded-sm bg-app-accent-200 text-primary"
        >
          {{ part.content }}
        </mark>

        <span
          v-else-if="part.content"
          :key="`span-${index}`"
        >
          {{ part.content }}
        </span>
      </template>
    </span>

    <span v-else>
      <template v-if="typeof props.content === 'string'">
        <span
          v-for="(line, index) of props.content.split('\n')"
          :key="`line-${index}`"
        >
          <span
            v-if="index > 0"
          >
            \n
          </span>
          {{ line }}
        </span>
      </template>

      <span v-else>
        {{ props.content }}
      </span>
    </span>
  </span>
</template>
