<script setup lang="ts">
import type { ICredentialType } from 'n8n-workflow'

import BaseNodeIcon from '#wf/components/ui/BaseNodeIcon.vue'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useUIStore } from '#wf/stores/ui.store'
import { getThemedValue } from '#wf/utils/nodeTypesUtils'

const props = defineProps<{
  credentialTypeName: string | null
}>()

const credentialsStore = useCredentialsStore()
const uiStore = useUIStore()
const nodeTypesStore = useNodeTypesStore()

const getCredentialWithIcon = (name: string | null): ICredentialType | null => {
  if (!name) {
    return null
  }

  const type = credentialsStore.getCredentialTypeByName(name)

  if (!type) {
    return null
  }

  if (type.icon ?? type.iconUrl) {
    return type
  }

  if (type.extends) {
    let parentCred = null
    type.extends.forEach((credType) => {
      parentCred = getCredentialWithIcon(credType)

      if (parentCred !== null) {
        return
      }
    })

    return parentCred
  }

  return null
}

const credentialWithIcon = computed(() => getCredentialWithIcon(props.credentialTypeName))

const nodeBasedIconUrl = computed(() => {
  const icon = getThemedValue(credentialWithIcon.value?.icon)

  if (!icon?.startsWith('node:')) {
    return null
  }

  return nodeTypesStore.getNodeType(icon.replace('node:', ''))?.iconUrl
})

const iconSource = computed(() => {
  const themeIconUrl = getThemedValue(
    nodeBasedIconUrl.value ?? credentialWithIcon.value?.iconUrl,
    uiStore.appliedTheme,
  )

  if (!themeIconUrl) {
    return undefined
  }

  return `/${themeIconUrl}`
})

const iconName = computed(() => {
  const icon = getThemedValue(credentialWithIcon.value?.icon, uiStore.appliedTheme)

  if (!icon || !icon?.startsWith('fa:')) {
    return undefined
  }

  return icon.replace('fa:', '')
})

const iconType = computed(() => {
  if (iconSource.value) {
    return 'file'
  }
  else if (iconName.value) {
    return 'icon'
  }

  return 'unknown'
})

const iconColor = computed(() => {
  const { iconColor: color } = credentialWithIcon.value ?? {}

  if (!color) {
    return undefined
  }

  return `var(--color-node-icon-${color})`
})
</script>

<template>
  <BaseNodeIcon
    :color="iconColor"
    :name="iconName"
    :size="26"
    :src="iconSource"
    :type="iconType"
  />
</template>
