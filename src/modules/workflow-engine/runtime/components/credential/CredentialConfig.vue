<script setup lang="ts">
import { CircleXIcon } from 'lucide-vue-next'
import type { ICredentialDataDecryptedObject, ICredentialType, INodeProperties } from 'n8n-workflow'

import CalloutBanner from '#wf/components/ui/CalloutBanner.vue'
import CalloutMessage from '#wf/components/ui/CalloutMessage.vue'
import type { IUpdateInformation } from '#wf/types/interface'

import CredentialInputs from './CredentialInputs.vue'

interface Props {
  credentialType?: ICredentialType | null
  credentialData: ICredentialDataDecryptedObject
  credentialProperties?: INodeProperties[]

  isManage?: boolean
  authError?: string
}

defineProps<Props>()

const emit = defineEmits<{
  valueChange: [parameterData: IUpdateInformation]
}>()

const handleValueChange = (event: IUpdateInformation) => {
  emit('valueChange', event)
}
</script>

<template>
  <div class="flex flex-col gap-form-field">
    <CalloutBanner
      v-if="authError"
      severity="error"
    >
      <template #icon>
        <CircleXIcon
          class="shrink-0"
          :size="16"
        />
      </template>

      <template #content>
        无法使用这些设置连接
      </template>
    </CalloutBanner>

    <CalloutMessage
      v-if="isManage"
      content="这是一个受管理的凭据，无法编辑。"
    />

    <template v-else>
      <CredentialInputs
        v-if="credentialType"
        :credentialData="credentialData"
        :credentialProperties="credentialProperties"
        @valueChange="handleValueChange"
      />
    </template>
  </div>
</template>
