<script setup lang="ts">
import type { ICredentialDataDecryptedObject, INodeProperties, NodeParameterValueType } from 'n8n-workflow'

import ParamFormField from '#wf/components/node-details/param/ParamFormField.vue'
import CalloutMessage from '#wf/components/ui/CalloutMessage.vue'
import type { IUpdateInformation } from '#wf/types/interface'

interface Props {
  credentialData: ICredentialDataDecryptedObject
  credentialProperties?: INodeProperties[]
}

const props = defineProps<Props>()

const credentialDataValues = computed(
  () => props.credentialData as Record<string, NodeParameterValueType>,
)

const emit = defineEmits<{
  valueChange: [parameterData: IUpdateInformation]
}>()

const handleValueChange = (parameterData: IUpdateInformation) => {
  // 取参数名的最后一段（如 a.b.c => c），用于简化参数名传递；若无分隔符则用原名
  const name = parameterData.name.split('.').pop() ?? parameterData.name

  emit('valueChange', {
    name,
    value: parameterData.value,
  })
}
</script>

<template>
  <!--
    这里为每个参数单独使用 form 容器，主要原因是：
    - 拆分输入项，便于管理每个参数的输入逻辑。
    - 防止 Chrome 浏览器的自动填充（autofill）影响到敏感凭证输入，提高安全性和用户体验。
  -->
  <form
    v-for="param of credentialProperties"
    :key="param.name"
    autocomplete="off"
    data-testid="credential-connection-parameter"
    @submit.prevent
  >
    <CalloutMessage
      v-if="param.type === 'notice'"
      :content="param.displayName"
    />

    <FormItem
      v-else
      :label="param.displayName"
      :required="param.required"
      :tip="param.description"
    >
      <ParamFormField
        :expressionEvaluated="null"
        :param="param"
        :path="param.name"
        :value="credentialDataValues[param.name]"
        @valueChange="handleValueChange"
      />
    </FormItem>
  </form>
</template>
