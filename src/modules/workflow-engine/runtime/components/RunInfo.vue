<script setup lang="ts">
import { CheckIcon, InfoIcon, XIcon } from 'lucide-vue-next'
import type { ITaskData } from 'n8n-workflow'

import BaseTooltip from '~/components/BaseTooltip.vue'
import { DateFormat } from '~/enums/common'

const props = defineProps<{
  taskData: ITaskData | null
  hasStaleData?: boolean
  hasPinData?: boolean
}>()

const runTaskData = computed(() => {
  return props.taskData
})

const runMetadata = computed(() => {
  if (!runTaskData.value) {
    return null
  }

  return {
    executionTime: runTaskData.value.executionTime,
    startTime: formatDate(runTaskData.value.startTime, DateFormat.YYYY年MM月DD日HH时MM分),
  }
})
</script>

<template>
  <ProBtn
    v-if="hasStaleData"
    v-tooltip="{ value: hasPinData
      ? '固定的输出数据不受节点参数更改的影响。'
      : '节点参数已更改。\n重新测试节点以刷新输出。' }"
    data-testid="node-run-info-stale"
    mini
    size="small"
  >
    <template #icon="{ size }">
      <InfoIcon
        class="text-warning-500"
        :size="size"
      />
    </template>
  </ProBtn>

  <BaseTooltip
    v-else-if="runMetadata && runTaskData"
    maxWidth="300px"
    placement="top"
    trigger="hover"
  >
    <ProBtn
      class="!cursor-default"
      mini
      size="small"
    >
      <template #icon="{ size }">
        <XIcon
          v-if="runTaskData.error"
          class="text-danger-500"
          :size="size"
          :strokeWidth="3"
        />
        <CheckIcon
          v-else
          class="text-success-500"
          :size="size"
          :strokeWidth="3"
        />
      </template>
    </ProBtn>

    <template #content>
      <div
        class="text-sm"
        data-testid="node-run-info"
      >
        <div
          class="pb-1 font-bold"
          :class="[
            runTaskData.error ? 'text-danger-500' : 'text-success-500',
          ]"
        >
          {{
            runTaskData.error
              ? '执行失败'
              : '执行成功'
          }}
        </div>

        <div>
          <span class="font-semibold">开始时间：</span>
          <span class="tabular-nums">{{ runMetadata.startTime }}</span>
        </div>

        <div>
          <span class="font-semibold">执行耗时：</span>
          <span class="tabular-nums">{{ runMetadata.executionTime }} ms</span>
        </div>
      </div>
    </template>
  </BaseTooltip>
</template>
