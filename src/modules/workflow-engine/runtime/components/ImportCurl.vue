<script setup lang="ts">
import { useImportCurlCommand } from '#wf/composables/useImportCurlCommand'

import { InjectKey } from '~/constants-tw/common'

interface DialogRef {
  close: () => void
}

const dialogRef = inject<Ref<DialogRef | undefined>>(InjectKey.PrimeVueDialog, ref())

const curlCommand = ref('')

const handleImportCurl = () => {
  const { importCurlCommand } = useImportCurlCommand({
    onImportSuccess: () => {
      dialogRef.value?.close()
    },
  })

  importCurlCommand(curlCommand)
}
</script>

<template>
  <div class="space-y-form-field">
    <FormItem label="cURL 命令">
      <Textarea
        v-model="curlCommand"
        placeholder="在此处粘贴 cURL 命令"
        rows="8"
      />

      <template #helpTip>
        点击导入按钮后，将 cURL 解析为 n8n 节点参数，并自动填充到节点参数中
      </template>
    </FormItem>

    <div class="justify-between gap-5 flex-center">
      <Message
        class="pb-1"
        severity="warn"
        size="small"
        variant="simple"
      >
        注意：这会覆盖当前节点的配置
      </Message>

      <Button
        class="ml-auto"
        label="导入"
        @click="handleImportCurl()"
      />
    </div>
  </div>
</template>
