<script setup lang="ts">
import VueJsonPretty from 'vue-json-pretty'

import { useElementSize } from '@vueuse/core'
import type { INodeExecutionData } from 'n8n-workflow'

import DraggableBox from '#wf/components/DraggableBox.vue'
import MappingPill from '#wf/components/MappingPill.vue'
import TextWithHighlight from '#wf/components/VirtualSchema/TextWithHighlight.vue'
import { nonExistingJsonPath } from '#wf/constants/common'
import { useNDVStore } from '#wf/stores/ndv.store'
import type { INodeUi } from '#wf/types/interface'
import { getMappedExpression } from '#wf/utils/mappingUtils'
import { executionDataToJson } from '#wf/utils/nodeTypesUtils'
import { isString } from '#wf/utils/typeGuards'
import { shorten } from '#wf/utils/typesUtils'

// const LazyRunDataJsonActions = defineAsyncComponent(
//   async () => await import('@/components/RunDataJsonActions.vue'),
// )

const props = withDefaults(
  defineProps<{
    editMode: { enabled?: boolean, value?: string }
    pushRef: string
    paneType: string
    node: INodeUi
    inputData: INodeExecutionData[]
    mappingEnabled?: boolean
    distanceFromActive: number
    runIndex: number | undefined
    totalRuns: number | undefined
    search: string | undefined
  }>(),
  {
    editMode: () => ({}),
  },
)

const ndvStore = useNDVStore()

const selectedJsonPath = ref(nonExistingJsonPath)
const draggingPath = ref<null | string>(null)
const displayMode = ref('json')

const { refKey: jsonDataContainerRefKey, ref: jsonDataContainer } = useRef<HTMLDivElement>()

const { height } = useElementSize(jsonDataContainer)

const jsonData = computed(() => executionDataToJson(props.inputData))

const highlight = computed(() => ndvStore.highlightDraggables)

const getShortKey = (el: HTMLElement) => {
  if (!el) {
    return ''
  }

  return shorten(el.dataset.name ?? '', 16, 2)
}

const getJsonParameterPath = (path: string) => {
  const subPath = path.replace(/^(\["?\d"?])/, '') // remove item position

  return getMappedExpression({
    nodeName: props.node.name,
    distanceFromActive: props.distanceFromActive,
    path: subPath,
  })
}

const onDragStart = (el: HTMLElement) => {
  if (el?.dataset.path) {
    draggingPath.value = el.dataset.path
  }

  // ndvStore.resetMappingTelemetry()
}

const onDragEnd = (el: HTMLElement) => {
  draggingPath.value = null

  // setTimeout(() => {
  //   void externalHooks.run('runDataJson.onDragEnd', telemetryPayload)
  //   telemetry.track('User dragged data for mapping', telemetryPayload, {
  //     withPostHog: true,
  //   })
  // }, 1000) // ensure dest data gets set if drop
}

const getContent = (value: unknown) => {
  return isString(value) ? `"${value}"` : JSON.stringify(value)
}

const getListItemName = (path: string) => {
  return path.replace(/^(\["?\d"?]\.?)/g, '')
}
</script>

<template>
  <div
    :ref="jsonDataContainerRefKey"
    data-component-name="JSON 视图"
  >
    <!-- <Suspense>
      <LazyRunDataJsonActions
        v-if="!editMode.enabled"
        :displayMode="displayMode"
        :distanceFromActive="distanceFromActive"
        :jsonData="jsonData"
        :node="node"
        :paneType="paneType"
        :pushRef="pushRef"
        :selectedJsonPath="selectedJsonPath"
      />
    </Suspense> -->

    <DraggableBox
      :disabled="!mappingEnabled"
      target-data-key="mappable"
      type="mapping"
      @dragend="onDragEnd"
      @dragstart="onDragStart"
    >
      <template #preview="{ canDrop, el }">
        <MappingPill
          v-if="el"
          :canDrop="canDrop"
          :html="getShortKey(el)"
        />
      </template>

      <VueJsonPretty
        :data="jsonData"
        :deep="10"
        :height="height"
        rootPath=""
        selectableType="single"
        :selectedValue="selectedJsonPath"
        showLength
        @update:selectedValue="selectedJsonPath = $event"
      >
        <template #renderNodeKey="{ node: n }">
          <TextWithHighlight
            :content="getContent(n.key)"
            :data-depth="n.level"
            :data-name="n.key"
            :data-path="n.path"
            data-target="mappable"
            :data-value="getJsonParameterPath(n.path)"
            :search="search"
          />
        </template>

        <template #renderNodeValue="{ node: n }">
          <TextWithHighlight
            v-if="isNaN(n.index)"
            :content="getContent(n.content)"
            :search="search"
          />

          <TextWithHighlight
            v-else
            :content="getContent(n.content)"
            :data-depth="n.level"
            :data-name="getListItemName(n.path)"
            :data-path="n.path"
            data-target="mappable"
            :data-value="getJsonParameterPath(n.path)"
            :search="search"
          />
        </template>
      </VueJsonPretty>
    </DraggableBox>
  </div>
</template>
