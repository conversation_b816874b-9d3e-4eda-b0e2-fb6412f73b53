<script setup lang="ts">
import type { INodeTypeDescription } from 'n8n-workflow'

import NodeExecuteButton from '#wf/components/NodeExecuteButton.vue'
import NodeIcon from '#wf/components/NodeIcon.vue'
import CollapsePanel from '#wf/components/ui/CollapsePanel.vue'
import { getCurrentWorkflow, useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import {
  CHAT_TRIGGER_NODE_TYPE,
  FORM_TRIGGER_NODE_TYPE,
  WEBHOOK_NODE_TYPE,
  WORKFLOW_SETTINGS_MODAL_KEY,
} from '#wf/constants/common'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { INodeUi } from '#wf/types/interface'
import { getTriggerNodeServiceName } from '#wf/utils/nodeTypesUtils'
import { isTriggerPanelObject } from '#wf/utils/typeGuards'

const props = defineProps<{
  nodeName: string
}>()

const emit = defineEmits<{
  activate: []
  execute: []
}>()

const nodesTypeStore = useNodeTypesStore()
const uiStore = useUIStore()
const workflowStore = useWorkflowStore()
const ndvStore = useNDVStore()

const workflowHelpers = useWorkflowHelpers()

const node = computed<INodeUi | null>(() => workflowStore.getNodeByName(props.nodeName))

const nodeType = computed<INodeTypeDescription | null>(() => {
  if (node.value) {
    return nodesTypeStore.getNodeType(node.value.type, node.value.typeVersion)
  }

  return null
})

const triggerPanel = computed(() => {
  const panel = nodeType.value?.triggerPanel

  if (isTriggerPanelObject(panel)) {
    return panel
  }

  return undefined
})

const hideContent = computed(() => {
  const hideContent = triggerPanel.value?.hideContent

  if (typeof hideContent === 'boolean') {
    return hideContent
  }

  if (node.value) {
    const hideContentValue = getCurrentWorkflow()
      .expression.getSimpleParameterValue(node.value, hideContent, 'internal', {})

    if (typeof hideContentValue === 'boolean') {
      return hideContentValue
    }
  }

  return false
})

const hasIssues = computed(() => {
  return Boolean(
    node.value?.issues && (node.value.issues.parameters ?? node.value.issues.credentials),
  )
})

const serviceName = computed(() => {
  if (nodeType.value) {
    return getTriggerNodeServiceName(nodeType.value)
  }

  return ''
})

const displayChatButton = computed(() => {
  return Boolean(
    node.value
    && node.value.type === CHAT_TRIGGER_NODE_TYPE
    && node.value.parameters.mode !== 'webhook',
  )
})

const isWebhookNode = computed(() => {
  return Boolean(node.value && node.value.type === WEBHOOK_NODE_TYPE)
})

const webhookHttpMethod = computed(() => {
  if (!node.value || !nodeType.value?.webhooks?.length) {
    return undefined
  }

  const httpMethod = workflowHelpers.getWebhookExpressionValue(
    nodeType.value.webhooks[0],
    'httpMethod',
    false,
  )

  if (Array.isArray(httpMethod)) {
    return httpMethod.join(', ')
  }

  return httpMethod
})

const webhookTestUrl = computed(() => {
  if (!node.value || !nodeType.value?.webhooks?.length) {
    return undefined
  }

  return workflowHelpers.getWebhookUrl(nodeType.value.webhooks[0], node.value, 'test')
})

const isWebhookBasedNode = computed(() => {
  return Boolean(nodeType.value?.webhooks?.length)
})

const isPollingNode = computed(() => {
  return Boolean(nodeType.value?.polling)
})

const isListeningForEvents = computed(() => {
  const waitingOnWebhook = workflowStore.executionWaitingForWebhook
  const executedNode = workflowStore.executedNode

  return (
    !!node.value
    && !node.value.disabled
    && isWebhookBasedNode.value
    && waitingOnWebhook
    && (!executedNode || executedNode === props.nodeName)
  )
})

const workflowRunning = computed(() => {
  return uiStore.isActionActive['workflowRunning']
})

const isActivelyPolling = computed(() => {
  const triggeredNode = workflowStore.executedNode

  return workflowRunning.value && isPollingNode.value && props.nodeName === triggeredNode
})

const isWorkflowActive = computed(() => {
  return workflowStore.isWorkflowActive
})

const listeningTitle = computed(() => {
  return nodeType.value?.name === FORM_TRIGGER_NODE_TYPE
    ? '等待表单提交中...'
    : '等待触发事件中...'
})

const listeningHint = computed(() => {
  switch (nodeType.value?.name) {
    case CHAT_TRIGGER_NODE_TYPE:
      return '请在聊天窗口中发送一条消息来测试触发器'

    case FORM_TRIGGER_NODE_TYPE:
      return '请在新打开的表单页面中填写并提交表单'

    default:
      return `请前往 ${serviceName.value} 执行相应操作来触发事件`
  }
})

const header = computed(() => {
  if (isActivelyPolling.value) {
    return '正在检测新事件'
  }

  if (triggerPanel.value?.header) {
    return triggerPanel.value.header
  }

  if (isWebhookBasedNode.value) {
    return `从 ${serviceName.value} 获取数据`
  }

  return ''
})

const subheader = computed(() => {
  if (isActivelyPolling.value) {
    return `正在 ${serviceName.value} 中查找符合条件的新事件...`
  }

  return ''
})

const executionsHelp = computed(() => {
  if (triggerPanel.value?.executionsHelp) {
    if (typeof triggerPanel.value.executionsHelp === 'string') {
      return triggerPanel.value.executionsHelp
    }

    if (!isWorkflowActive.value && triggerPanel.value.executionsHelp.inactive) {
      return triggerPanel.value.executionsHelp.inactive
    }

    if (isWorkflowActive.value && triggerPanel.value.executionsHelp.active) {
      return triggerPanel.value.executionsHelp.active
    }
  }

  if (isWebhookBasedNode.value) {
    if (isWorkflowActive.value) {
      return `
        <div class="space-y-3">
          <div>
            <strong>测试阶段：</strong>点击「监听」按钮，然后在 ${serviceName.value} 中触发一次事件，你将在此处看到对应的测试结果。
          </div>
          <div>
            <strong>正式运行：</strong>工作流已激活，每当 ${serviceName.value} 发生匹配事件时，系统将自动执行对应流程。执行记录可在 <strong>执行记录</strong> 中查看。
          </div>
        </div>
      `
    }
    else {
      return `
        <div class="space-y-3">
          <div>
            <strong>测试阶段：</strong>点击「监听」按钮，然后在 ${serviceName.value} 中尝试触发一次事件，系统将抓取该事件用于测试。
          </div>
          <div>
            <strong>下一步：</strong>测试无误后，请<strong>激活工作流</strong>，系统将开始实时监听 ${serviceName.value} 的事件并自动执行流程。
          </div>
        </div>
      `
    }
  }

  if (isPollingNode.value) {
    if (isWorkflowActive.value) {
      return `
        <div class="space-y-3">
          <div>
            <strong>测试阶段：</strong>点击「获取事件」按钮，系统将立即拉取一个示例事件以供测试。
          </div>
          <div>
            <strong>正式运行：</strong>工作流已激活，系统会定时轮询 ${serviceName.value} 是否有新事件并自动执行。历史执行记录可在<strong>执行记录</strong>中查看。
          </div>
        </div>
      `
    }
    else {
      return `
        <div class="space-y-3">
          <div>
            <strong>测试阶段：</strong>点击「获取事件」按钮，系统将拉取一个示例事件用于测试。
          </div>
          <div>
            <strong>下一步：</strong>确认测试无误后，请 <a data-key="activate">激活工作流</a>，系统会定期检查 ${serviceName.value} 是否有新事件并自动执行。
          </div>
        </div>
      `
    }
  }

  return ''
})

const activationHint = computed(() => {
  if (isActivelyPolling.value || !triggerPanel.value) {
    return ''
  }

  if (triggerPanel.value.activationHint) {
    if (typeof triggerPanel.value.activationHint === 'string') {
      return triggerPanel.value.activationHint
    }

    if (!isWorkflowActive.value && typeof triggerPanel.value.activationHint.inactive === 'string') {
      return triggerPanel.value.activationHint.inactive
    }

    if (isWorkflowActive.value && typeof triggerPanel.value.activationHint.active === 'string') {
      return triggerPanel.value.activationHint.active
    }
  }

  if (isWebhookBasedNode.value) {
    if (isWorkflowActive.value) {
      return `工作流已激活，${serviceName.value} 的新事件将自动触发执行`
    }
    else {
      return `工作流构建完成后，请 <a data-key="activate">激活工作流</a> 以开始自动监听 ${serviceName.value} 事件`
    }
  }

  if (isPollingNode.value) {
    if (isWorkflowActive.value) {
      return `工作流已激活，系统正在定期检查 ${serviceName.value} 的新事件`
    }
    else {
      return `工作流构建完成后，请激活它以开始定期检查 ${serviceName.value} 的新事件`
    }
  }

  return ''
})

const openWebhookUrl = () => {
  window.open(webhookTestUrl.value, '_blank', 'noreferrer')
}

const onLinkClick = (e: MouseEvent) => {
  if (!e.target) {
    return
  }

  const target = e.target as HTMLElement

  if (target.localName !== 'a') {
    return
  }

  if (target.dataset?.key) {
    e.stopPropagation()
    e.preventDefault()

    if (target.dataset.key === 'activate') {
      emit('activate')
    }
    else if (target.dataset.key === 'executions') {
      ndvStore.activeNodeName = null
      // void router.push({
      //   name: VIEWS.EXECUTIONS,
      // })
    }
    else if (target.dataset.key === 'settings') {
      uiStore.openModal(WORKFLOW_SETTINGS_MODAL_KEY)
    }
  }
}

const onNodeExecute = () => {
  emit('execute')
}
</script>

<template>
  <div class="flex size-full flex-col">
    <div
      v-if="hasIssues || hideContent"
      data-component-name="空内容"
    />

    <div
      v-else-if="isListeningForEvents"
      class="flex flex-1 flex-col justify-center p-card-container"
      data-component-name="监听"
    >
      <div class="flex justify-center p-5">
        <div class="animate-bounce justify-center rounded-xl border border-divider p-2 shadow inline-flex-center">
          <NodeIcon
            :nodeType="nodeType"
            :size="40"
          />
        </div>
      </div>

      <div
        v-if="isWebhookNode"
        class="flex flex-col items-center justify-center gap-2"
      >
        <div class="text-lg font-bold text-secondary">
          等待 Webhook 事件
        </div>

        <div class="text-sm text-secondary">
          {{ `请发送 ${webhookHttpMethod} 请求到以下地址：` }}
          <div :value="webhookTestUrl" />
        </div>

        <NodeExecuteButton
          data-testid="trigger-execute-button"
          :nodeName="nodeName"
          @execute="onNodeExecute"
        />
      </div>

      <div
        v-else
        class="flex flex-col items-center justify-center gap-2"
      >
        <div class="text-lg font-bold text-secondary">
          {{ listeningTitle }}
        </div>

        <div class="text-sm text-secondary">
          {{ listeningHint }}
        </div>

        <div v-if="displayChatButton">
          <Button
            class="mb-2"
            label="打开聊天测试"
            @click="openWebhookUrl()"
          />
        </div>

        <NodeExecuteButton
          data-testid="trigger-execute-button"
          :nodeName="nodeName"
          @execute="onNodeExecute"
        />
      </div>
    </div>

    <div
      v-else
      class="flex flex-1 flex-col overflow-auto p-card-container"
      data-component-name="默认展示"
    >
      <div class="flex-1 flex-col justify-center flex-center">
        <div
          v-if="isActivelyPolling"
          class="p-5"
        >
          <ProgressSpinner
            animationDuration=".5s"
            fill="transparent"
            strokeWidth="8"
            style="width: 50px; height: 50px"
          />
        </div>

        <div class="py-8 text-center">
          <div class="pb-4">
            <h1
              v-if="header"
              class="text-lg font-bold"
            >
              {{ header }}
            </h1>

            <span v-if="subheader">
              <span>{{ subheader }}</span>
            </span>
          </div>

          <NodeExecuteButton
            data-testid="trigger-execute-button"
            :nodeName="nodeName"
            @execute="onNodeExecute"
          />
        </div>

        <div
          v-if="activationHint"
          class="text-sm text-secondary"
          @click="onLinkClick"
        >
          <div v-html="activationHint" />
        </div>
      </div>

      <div
        v-if="executionsHelp"
        class="mt-auto"
      >
        <CollapsePanel
          :defaultExpanded="false"
          title="这个触发器是如何工作的？"
        >
          <template #content>
            <div
              class="rounded-md border border-divider bg-emphasis px-2.5 py-2 text-sm"
              v-html="executionsHelp"
            />
          </template>
        </CollapsePanel>
      </div>
    </div>
  </div>
</template>
