<script setup lang="ts">
import VueJsonPretty from 'vue-json-pretty'

import type { IBinaryData } from 'n8n-workflow'
import { jsonParse } from 'n8n-workflow/dist/utils.js'

import RunDataHtml from '#wf/components/node-run/RunDataHtml.vue'
import { useWorkflowStore } from '#wf/stores/workflow.store'

const props = defineProps<{
  binaryData: IBinaryData
}>()

const isLoading = ref(true)

const embedSource = ref('')
const error = ref(false)
const data = ref('')

const workflowsStore = useWorkflowStore()

const embedClass = computed(() => {
  const fileType = props.binaryData.fileType ?? 'other'
  const classes = [fileType]

  if (fileType === 'other' || fileType === 'pdf') {
    classes.push('h-full', 'w-full')
  }

  return classes
})

onMounted(async () => {
  const { id, data: binaryData, fileName, fileType, mimeType } = props.binaryData

  const isJSONData = fileType === 'json'
  const isHTMLData = fileType === 'html'

  if (!id) {
    if (isJSONData || isHTMLData) {
      try {
        // 使用TextDecoder处理UTF-8编码
        const bytes = window.atob(binaryData)
        const bytes_arr = new Uint8Array(bytes.length)

        for (let i = 0; i < bytes.length; i++) {
          bytes_arr[i] = bytes.charCodeAt(i)
        }

        data.value = jsonParse(new TextDecoder('utf-8').decode(bytes_arr))
      }
      catch {
        // 如果上面的方法失败，尝试直接解析
        data.value = jsonParse(atob(binaryData))
      }
    }
    else {
      embedSource.value = 'data:' + mimeType + ';base64,' + binaryData
    }
  }
  else {
    try {
      const binaryUrl = workflowsStore.getBinaryUrl(id, 'view', fileName ?? '', mimeType)

      if (isJSONData || isHTMLData) {
        const fetchedData = await fetch(binaryUrl, { credentials: 'include' })

        data.value = await (isJSONData ? fetchedData.json() : fetchedData.text())
      }
      else {
        embedSource.value = binaryUrl
      }
    }
    catch {
      error.value = true
    }
  }

  isLoading.value = false
})
</script>

<template>
  <div>
    <div
      v-if="isLoading"
      class="flex items-center justify-center p-5 text-secondary"
    >
      正在加载二进制数据
    </div>

    <div
      v-else-if="error"
      class="flex items-center justify-center p-5 text-secondary"
    >
      加载二进制数据时出错
    </div>

    <div v-else>
      <video
        v-if="binaryData.fileType === 'video'"
        autoplay
        class="max-h-full max-w-full"
        controls
      >
        <source
          :src="embedSource"
          :type="binaryData.mimeType"
        >
        您的浏览器不支持视频元素。请更新至最新版本。
      </video>

      <audio
        v-else-if="binaryData.fileType === 'audio'"
        autoplay
        controls
      >
        <source
          :src="embedSource"
          :type="binaryData.mimeType"
        >
        您的浏览器不支持音频元素。请更新至最新版本。
      </audio>

      <img
        v-else-if="binaryData.fileType === 'image'"
        class="max-h-full max-w-full"
        :src="embedSource"
      >

      <VueJsonPretty
        v-else-if="binaryData.fileType === 'json'"
        :data="data"
        :deep="3"
        showLength
      />

      <RunDataHtml
        v-else-if="binaryData.fileType === 'html'"
        :inputHtml="data"
      />

      <embed
        v-else
        :class="embedClass"
        :src="embedSource"
      >
    </div>
  </div>
</template>
