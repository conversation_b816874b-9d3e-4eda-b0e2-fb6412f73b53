<!-- eslint-disable @typescript-eslint/no-use-before-define -->
<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<script setup lang="ts">
import { asyncComputed } from '@vueuse/core'
import { isEqual, isObject } from 'lodash-es'
import { ArrowDownToLineIcon, EyeIcon, Loader2Icon, LoaderIcon, PencilIcon, SearchIcon, SquareArrowUpRightIcon } from 'lucide-vue-next'
import type {
  IBinaryData,
  IConnectedNode,
  IDataObject,
  INodeExecutionData,
  INodeOutputConfiguration,
  IRunData,
  IRunExecutionData,
  ITaskMetadata,
  NodeError,
  NodeHint,
  Workflow,
} from 'n8n-workflow'
import { TRIMMED_TASK_DATA_CONNECTIONS_KEY } from 'n8n-workflow/dist/Constants.js'
import { parseErrorMetadata } from 'n8n-workflow/dist/MetadataUtils.js'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'

import JsonEditor from '#wf/components/editor/JsonEditor.vue'
import NodeErrorView from '#wf/components/error/NodeError.vue'
import BinaryDataDisplay from '#wf/components/node-run/BinaryDataDisplay.vue'
import RunDataPinButton from '#wf/components/RunDataPinButton.vue'
import CalloutMessage from '#wf/components/ui/CalloutMessage.vue'
import { useExecutionHelpers } from '#wf/composables/useExecutionHelpers'
import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { useNodeType } from '#wf/composables/useNodeType'
import { type PinDataSource, type UnpinDataSource, usePinnedData } from '#wf/composables/usePinnedData'
import { NodeConnectionType } from '#wf/constants/canvas'
import {
  CORE_NODES_CATEGORY,
  DATA_EDITING_DOCS_URL,
  DATA_PINNING_DOCS_URL,
  HTML_NODE_TYPE,
  MAX_DISPLAY_DATA_SIZE,
  MAX_DISPLAY_DATA_SIZE_SCHEMA_VIEW,
  NODE_TYPES_EXCLUDED_FROM_OUTPUT_NAME_APPEND,
  TEST_PIN_DATA,
} from '#wf/constants/common'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useSchemaPreviewStore } from '#wf/stores/schemaPreview.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type {
  INodeUi,
  INodeUpdatePropertiesInformation,
  IRunDataDisplayMode,
  ITab,
  NodePanelType,
} from '#wf/types/interface'
import { executionDataToJson } from '#wf/utils/nodeTypesUtils'
import { getGenericHints } from '#wf/utils/nodeViewUtils'
import { searchInObject } from '#wf/utils/objectUtils'
import { clearJsonKey, isEmpty, isPresent } from '#wf/utils/typesUtils'

import { saveAs } from '~/utils/file'

import RunDataBox from './RunDataBox.vue'

const LazyRunDataTable = defineAsyncComponent(
  async () => await import('#wf/components/node-run/RunDataTable.vue'),
)

const LazyRunDataJson = defineAsyncComponent(
  async () => await import('#wf/components/node-run/RunDataJson.vue'),
)

const LazyRunDataSchema = defineAsyncComponent(
  async () => await import('#wf/components/VirtualSchema/VirtualSchema.vue'),
)

const LazyRunDataHtml = defineAsyncComponent(
  async () => await import('#wf/components/node-run/RunDataHtml.vue'),
)

const LazyRunDataSearch = defineAsyncComponent(
  async () => await import('#wf/components/node-run/RunDataSearch.vue'),
)

export type EnterEditModeArgs = {
  origin: 'editIconButton' | 'insertTestDataLink'
}

interface Props {
  workflow: Workflow
  runIndex: number
  tooMuchDataTitle: string
  executingMessage: string
  pushRef: string
  paneType: NodePanelType
  noDataInBranchMessage: string
  node?: INodeUi | null
  nodes?: IConnectedNode[]
  linkedRuns?: boolean
  canLinkRuns?: boolean
  isExecuting?: boolean
  overrideOutputs?: number[]
  /** 是否开启数据映射 */
  mappingEnabled?: boolean
  distanceFromActive?: number
  /** 是否显示加载中 */
  blockUI?: boolean
  isProductionExecutionPreview?: boolean
  isPaneActive?: boolean
  hidePagination?: boolean
  calloutMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  node: null,
  nodes: () => [],
  overrideOutputs: undefined,
  distanceFromActive: 0,
  blockUI: false,
  isPaneActive: false,
  isProductionExecutionPreview: false,
  mappingEnabled: false,
  isExecuting: false,
  hidePagination: false,
  calloutMessage: undefined,
})
const emit = defineEmits<{
  search: [search: string]
  runChange: [runIndex: number]
  itemHover: [item: {
    outputIndex: number
    itemIndex: number
  } | null,
  ]
  linkRun: []
  unlinkRun: []
  activatePane: []
  tableMounted: [
    {
      avgRowHeight: number
    },
  ]
}>()

const connectionType = ref<NodeConnectionType>(NodeConnectionType.Main)
const dataSize = ref(0)
const showData = ref(false)
const userEnabledShowData = ref(false)
const outputIndex = ref(0)
const binaryDataDisplayVisible = ref(false)
const binaryDataDisplayData = ref<IBinaryData | null>(null)
const currentPage = ref(1)
const pageSize = ref(10)
const pageSizes = [1, 10, 25, 50, 100]

const pinDataDiscoveryTooltipVisible = ref(false)
const isControlledPinDataTooltip = ref(false)
const search = ref('')

const nodeTypesStore = useNodeTypesStore()

const ndvStore = useNDVStore()
const { activeNode } = storeToRefs(ndvStore)

const workflowsStore = useWorkflowStore()
const uiStore = useUIStore()
const schemaPreviewStore = useSchemaPreviewStore()

const { $toast } = useNuxtApp()

const nodeHelpers = useNodeHelpers()
const { resolveRelatedExecutionUrl } = useExecutionHelpers()

const node = toRef(props, 'node')

const pinnedData = usePinnedData(node, {
  runIndex: props.runIndex,
  displayMode: props.paneType === 'input' ? ndvStore.inputPanelDisplayMode : ndvStore.outputPanelDisplayMode,
})
const { isSubNodeType } = useNodeType({
  node,
})

const displayMode = computed(() =>
  props.paneType === 'input' ? ndvStore.inputPanelDisplayMode : ndvStore.outputPanelDisplayMode,
)

const isReadOnlyRoute = ref(false)

const workflowExecution = computed(() => workflowsStore.getWorkflowExecution)

const isWaitNodeWaiting = computed(() => {
  return (
    node.value?.name
    && workflowExecution.value?.data?.resultData?.runData?.[node.value?.name]?.[props.runIndex]
      ?.executionStatus === 'waiting'
  )
})

const nodeType = computed(() => {
  if (!node.value) {
    return null
  }

  return nodeTypesStore.getNodeType(node.value.type, node.value.typeVersion)
})

const isSchemaView = computed(() => displayMode.value === 'schema')
const isSearchInSchemaView = computed(() => isSchemaView.value && !!search.value)
const displaysMultipleNodes = computed(
  () => isSchemaView.value && props.paneType === 'input' && props.nodes.length > 0,
)

const isTriggerNode = computed(() => !!node.value && nodeTypesStore.isTriggerNode(node.value.type))

const workflowRunData = computed(() => {
  if (workflowExecution.value === null) {
    return null
  }

  const executionData: IRunExecutionData | undefined = workflowExecution.value.data

  if (executionData?.resultData) {
    return executionData.resultData.runData
  }

  return null
})

const binaryData = computed(() => {
  if (!node.value) {
    return []
  }

  return nodeHelpers
    .getBinaryData(workflowRunData.value, node.value.name, props.runIndex, currentOutputIndex.value)
    .filter((data) => Boolean(data && Object.keys(data).length))
})

const canPinData = computed(
  () =>
    !!node.value
    && pinnedData.canPinNode(false, currentOutputIndex.value)
    && !isPaneTypeInput.value
    && pinnedData.isValidNodeType.value
    && !(binaryData.value && binaryData.value.length > 0),
)
const displayModes = computed(() => {
  const defaults: { label: string, value: IRunDataDisplayMode }[] = [
    { label: '表格', value: 'table' },
    { label: 'JSON', value: 'json' },
  ]

  if (binaryData.value.length) {
    defaults.push({ label: '二进制', value: 'binary' })
  }

  const schemaView = { label: 'Schema', value: 'schema' } as const

  if (isPaneTypeInput.value) {
    defaults.unshift(schemaView)
  }
  else {
    defaults.push(schemaView)
  }

  if (
    isPaneTypeOutput.value
    && activeNode.value?.type === HTML_NODE_TYPE
    && activeNode.value.parameters.operation === 'generateHtmlTemplate'
  ) {
    defaults.unshift({ label: 'HTML', value: 'html' })
  }

  return defaults
})

/** 节点是否已执行 */
const hasNodeRun = computed(() =>
  Boolean(
    !props.isExecuting
    && node.value
    && ((workflowRunData.value && Object.hasOwn(workflowRunData.value, node.value.name))
      || pinnedData.hasData.value),
  ),
)

const isArtificialRecoveredEventItem = computed(
  () => rawInputData.value?.[0]?.json?.isArtificialRecoveredEventItem,
)

const isTrimmedManualExecutionDataItem = computed(
  () => rawInputData.value?.[0]?.json?.[TRIMMED_TASK_DATA_CONNECTIONS_KEY],
)

const subworkflowExecutionError = computed(() => {
  if (!node.value) {
    return null
  }

  return {
    node: node.value,
    messages: [workflowsStore.subWorkflowExecutionError?.message ?? ''],
  } as NodeError
})

const hasSubworkflowExecutionError = computed(() =>
  Boolean(workflowsStore.subWorkflowExecutionError),
)

// Sub-nodes may wish to display the parent node error as it can contain additional metadata
const parentNodeError = computed(() => {
  const parentNode = props.workflow.getChildNodes(node.value?.name ?? '', 'ALL_NON_MAIN')[0]

  return workflowRunData.value?.[parentNode]?.[props.runIndex]?.error as NodeError
})
const workflowRunErrorAsNodeError = computed(() => {
  if (!node.value) {
    return null
  }

  // If the node is a sub-node, we need to get the parent node error to check for input errors
  if (isSubNodeType.value && props.paneType === 'input') {
    return parentNodeError.value
  }

  return workflowRunData.value?.[node.value?.name]?.[props.runIndex]?.error as NodeError
})

const hasRunError = computed(() => Boolean(node.value && workflowRunErrorAsNodeError.value))

const executionHints = computed(() => {
  if (hasNodeRun.value) {
    const hints = node.value && workflowRunData.value?.[node.value.name]?.[props.runIndex]?.hints

    if (hints) {
      return hints
    }
  }

  return []
})

function getDataCount(
  runIndex: number,
  outputIndex: number,
  connectionType: NodeConnectionType = NodeConnectionType.Main,
) {
  if (!node.value) {
    return 0
  }

  const runData = workflowRunData.value?.[node.value.name]?.[runIndex]

  if (runData && Object.hasOwn(runData, 'error')) {
    return 1
  }

  const rawInputData = getRawInputData(runIndex, outputIndex, connectionType)
  const pinOrLiveData = getPinDataOrLiveData(rawInputData)

  return getFilteredData(pinOrLiveData).length
}

const dataCount = computed(() =>
  getDataCount(props.runIndex, currentOutputIndex.value, connectionType.value),
)

const unfilteredDataCount = computed(() =>
  pinnedData.data.value ? pinnedData.data.value.length : rawInputData.value.length,
)
const dataSizeInMB = computed(() => (dataSize.value / (1024 * 1024)).toFixed(1))
const maxOutputIndex = computed(() => {
  if (node.value === null || props.runIndex === undefined) {
    return 0
  }

  const runData: IRunData | null = workflowRunData.value

  if (runData === null || !Object.hasOwn(runData, node.value.name)) {
    return 0
  }

  if (runData[node.value.name].length < props.runIndex) {
    return 0
  }

  if (runData[node.value.name][props.runIndex]) {
    const taskData = runData[node.value.name][props.runIndex].data

    if (taskData?.main) {
      return taskData.main.length - 1
    }
  }

  return 0
})
const currentPageOffset = computed(() => pageSize.value * (currentPage.value - 1))
const maxRunIndex = computed(() => {
  if (!node.value) {
    return 0
  }

  const runData: IRunData | null = workflowRunData.value

  if (runData === null || !Object.hasOwn(runData, node.value.name)) {
    return 0
  }

  if (runData[node.value.name].length) {
    return runData[node.value.name].length - 1
  }

  return 0
})

function getRawInputData(
  runIndex: number,
  outputIndex: number,
  connectionType: NodeConnectionType = NodeConnectionType.Main,
): INodeExecutionData[] {
  let inputData: INodeExecutionData[] = []

  if (node.value) {
    inputData = nodeHelpers.getNodeInputData(
      node.value,
      runIndex,
      outputIndex,
      props.paneType,
      connectionType,
    )
  }

  if (inputData.length === 0 || !Array.isArray(inputData)) {
    return []
  }

  return inputData
}

const rawInputData = computed(() =>
  getRawInputData(props.runIndex, currentOutputIndex.value, connectionType.value),
)

const unfilteredInputData = computed(() => getPinDataOrLiveData(rawInputData.value))
const inputData = computed(() => getFilteredData(unfilteredInputData.value))
const inputDataPage = computed(() => {
  const offset = pageSize.value * (currentPage.value - 1)

  return inputData.value.slice(offset, offset + pageSize.value)
})
const jsonData = computed(() => executionDataToJson(inputData.value))

const inputHtml = computed(() => String(inputData.value[0]?.json?.html ?? ''))
const currentOutputIndex = computed(() => {
  if (props.overrideOutputs?.length && !props.overrideOutputs.includes(outputIndex.value)) {
    return props.overrideOutputs[0]
  }

  // In some cases nodes may switch their outputCount while the user still
  // has a higher outputIndex selected. We could adjust outputIndex directly,
  // but that loses data as we can keep the user selection if the branch reappears.
  return Math.min(outputIndex.value, maxOutputIndex.value)
})

const branches = computed(() => {
  const capitalize = (name: string) => name.charAt(0).toLocaleUpperCase() + name.slice(1)

  const result: Array<ITab<number>> = []

  for (let i = 0; i <= maxOutputIndex.value; i++) {
    if (props.overrideOutputs && !props.overrideOutputs.includes(i)) {
      continue
    }

    const totalItemsCount = getRawInputData(props.runIndex, i).length
    const itemsCount = getDataCount(props.runIndex, i)
    const items = search.value
      ? `${itemsCount} 个结果，共 ${totalItemsCount} 项`
      : `${itemsCount} 项`
    let outputName = getOutputName(i)

    if (`${outputName}` === `${i}`) {
      outputName = `输出 ${outputName}`
    }
    else {
      const appendBranchWord = NODE_TYPES_EXCLUDED_FROM_OUTPUT_NAME_APPEND.includes(
        node.value?.type ?? '',
      )
        ? ''
        : ` 分支`
      outputName = capitalize(`${getOutputName(i)}${appendBranchWord}`)
    }

    result.push({
      label: (search.value && itemsCount) || totalItemsCount ? `${outputName} (${items})` : outputName,
      value: i,
    })
  }

  return result
})

const editMode = computed(() => {
  return isPaneTypeInput.value ? { enabled: false, value: '' } : ndvStore.outputPanelEditMode
})

const isPaneTypeInput = computed(() => props.paneType === 'input')
const isPaneTypeOutput = computed(() => props.paneType === 'output')

const readOnlyEnv = ref(false)
// const readOnlyEnv = computed(() => sourceControlStore.preferences.branchReadOnly)
const showIOSearch = computed(
  () => hasNodeRun.value && !hasRunError.value && unfilteredInputData.value.length > 0,
)

/**
 * 计算输入选择器的显示位置
 *
 * 这个计算属性决定了输入选择器组件（input-select slot）应该在界面的哪个位置显示。
 * 根据不同的状态和条件，选择器会出现在不同的位置：
 *
 * - 'none': 不显示选择器（Schema 视图模式下）
 * - 'header': 显示在头部区域（节点未运行时）
 * - 'runs': 显示在运行选择器区域（有多个运行记录时）
 * - 'outputs': 显示在输出分支选择器区域（有多个输出分支时）
 * - 'items': 显示在数据项计数区域（默认位置）
 */
const inputSelectLocation = computed(() => {
  // Schema 视图模式下不显示输入选择器
  if (isSchemaView.value) {
    return 'none'
  }

  // 节点未运行时，选择器显示在头部区域
  if (!hasNodeRun.value) {
    return 'header'
  }

  // 有多个运行记录时，选择器显示在运行选择器区域
  if (maxRunIndex.value > 0) {
    return 'runs'
  }

  // 有多个输出分支时，选择器显示在输出分支选择器区域
  if (maxOutputIndex.value > 0 && branches.value.length > 1) {
    return 'outputs'
  }

  // 默认情况下，选择器显示在数据项计数区域
  return 'items'
})

const showIoSearchNoMatchContent = computed(
  () => hasNodeRun.value && !inputData.value.length && !!search.value,
)

const parentNodeOutputData = computed(() => {
  const parentNode = props.workflow.getParentNodesByDepth(node.value?.name ?? '')[0]
  let parentNodeData: INodeExecutionData[] = []

  if (parentNode?.name) {
    parentNodeData = nodeHelpers.getNodeInputData(
      props.workflow.getNode(parentNode?.name),
      props.runIndex,
      outputIndex.value,
      'input',
      connectionType.value,
    )
  }

  return parentNodeData
})

const parentNodePinnedData = computed(() => {
  const parentNode = props.workflow.getParentNodesByDepth(node.value?.name ?? '')[0]

  return props.workflow.pinData?.[parentNode?.name || ''] ?? []
})

const showPinButton = computed(() => {
  if (!rawInputData.value.length && !pinnedData.hasData.value) {
    return false
  }

  if (editMode.value.enabled) {
    return false
  }

  if (binaryData.value?.length) {
    return isPaneTypeOutput.value
  }

  return canPinData.value
})

const pinButtonDisabled = computed(
  () =>
    pinnedData.hasData.value
    || !rawInputData.value.length
    || !!binaryData.value?.length
    || isReadOnlyRoute.value
    || readOnlyEnv.value,
)

const activeTaskMetadata = computed((): ITaskMetadata | null => {
  if (!node.value) {
    return null
  }

  const errorMetadata = parseErrorMetadata(workflowRunErrorAsNodeError.value)

  if (errorMetadata !== undefined) {
    return errorMetadata
  }

  // This is needed for the WorkflowRetriever to display the associated execution
  if (parentNodeError.value) {
    const subNodeMetadata = parseErrorMetadata(parentNodeError.value)

    if (subNodeMetadata !== undefined) {
      return subNodeMetadata
    }
  }

  return workflowRunData.value?.[node.value.name]?.[props.runIndex]?.metadata ?? null
})

const hasRelatedExecution = computed(() => {
  return Boolean(
    activeTaskMetadata.value?.subExecution ?? activeTaskMetadata.value?.parentExecution,
  )
})

const hasInputOverwrite = computed((): boolean => {
  if (!node.value) {
    return false
  }

  const taskData = nodeHelpers.getNodeTaskData(node.value, props.runIndex)

  return Boolean(taskData?.inputOverride)
})

const isSchemaPreviewEnabled = computed(
  () =>
    props.paneType === 'input'
    && !(nodeType.value?.codex?.categories ?? []).some(
      (category) => category === CORE_NODES_CATEGORY,
    ),
)

const hasPreviewSchema = asyncComputed(async () => {
  if (!isSchemaPreviewEnabled.value || props.nodes.length === 0) {
    return false
  }

  const nodes = props.nodes
    .filter((n) => n.depth === 1)
    .map((n) => workflowsStore.getNodeByName(n.name))
    .filter(isPresent)

  for (const connectedNode of nodes) {
    const { type, typeVersion, parameters } = connectedNode
    const hasPreview = await schemaPreviewStore.getSchemaPreview({
      nodeType: type,
      version: typeVersion,
      resource: parameters.resource as string,
      operation: parameters.operation as string,
    })

    if (hasPreview.ok) {
      return true
    }
  }

  return false
}, false)

watch(node, (newNode, prevNode) => {
  if (newNode?.id === prevNode?.id) {
    return
  }

  init()
})

watch(hasNodeRun, () => {
  if (props.paneType === 'output') {
    setDisplayMode()
  }
  else {
    // InputPanel relies on the outputIndex to check if we have data
    outputIndex.value = determineInitialOutputIndex()
  }
})

watch(
  inputDataPage,
  (data: INodeExecutionData[]) => {
    if (props.paneType && data) {
      ndvStore.setNDVPanelDataIsEmpty({
        panel: props.paneType,
        isEmpty: data.every((item) => isEmpty(item.json)),
      })
    }
  },
  { immediate: true, deep: true },
)

watch(jsonData, (data: IDataObject[], prevData: IDataObject[]) => {
  if (isEqual(data, prevData)) {
    return
  }

  refreshDataSize()

  if (dataCount.value) {
    resetCurrentPageIfTooFar()
  }

  showPinDataDiscoveryTooltip(data)
})

const switchToBinary = () => {
  onDisplayModeChange('binary')
}

watch(binaryData, (newData, prevData) => {
  if (newData.length && !prevData.length && displayMode.value !== 'binary') {
    switchToBinary()
  }
  else if (!newData.length && displayMode.value === 'binary') {
    onDisplayModeChange('table')
  }
})

watch(currentOutputIndex, (branchIndex: number) => {
  ndvStore.setNDVBranchIndex({
    pane: props.paneType,
    branchIndex,
  })
})

watch(search, (newSearch) => {
  emit('search', newSearch)
})

onMounted(() => {
  init()

  if (!isPaneTypeInput.value) {
    showPinDataDiscoveryTooltip(jsonData.value)
  }

  ndvStore.setNDVBranchIndex({
    pane: props.paneType,
    branchIndex: currentOutputIndex.value,
  })

  if (props.paneType === 'output') {
    setDisplayMode()
    activatePane()
  }

  if (hasRunError.value && node.value) {
    const error = workflowRunData.value?.[node.value.name]?.[props.runIndex]?.error
    const errorsToTrack = ['unknown error']
  }
})

onBeforeUnmount(() => {
  hidePinDataDiscoveryTooltip()
})

function getResolvedNodeOutputs() {
  if (node.value && nodeType.value) {
    const workflowNode = props.workflow.getNode(node.value.name)

    if (workflowNode) {
      const outputs = NodeHelpers.getNodeOutputs(props.workflow, workflowNode, nodeType.value)

      return outputs
    }
  }

  return []
}

function shouldHintBeDisplayed(hint: NodeHint): boolean {
  const { location, whenToDisplay } = hint

  if (location) {
    if (location === 'ndv' && !['input', 'output'].includes(props.paneType)) {
      return false
    }

    if (location === 'inputPane' && props.paneType !== 'input') {
      return false
    }

    if (location === 'outputPane' && props.paneType !== 'output') {
      return false
    }
  }

  if (whenToDisplay === 'afterExecution' && !hasNodeRun.value) {
    return false
  }

  if (whenToDisplay === 'beforeExecution' && hasNodeRun.value) {
    return false
  }

  return true
}

const getNodeHints = (): NodeHint[] => {
  try {
    if (node.value && nodeType.value) {
      const workflowNode = props.workflow.getNode(node.value.name)

      if (workflowNode) {
        const nodeHints = NodeHelpers.getNodeHints(props.workflow, workflowNode, nodeType.value, {
          runExecutionData: workflowExecution.value?.data ?? null,
          runIndex: props.runIndex,
          connectionInputData: parentNodeOutputData.value,
        })

        const hasMultipleInputItems = parentNodeOutputData.value.length > 1 || parentNodePinnedData.value.length > 1

        const nodeOutputData = workflowRunData.value?.[node.value.name]?.[props.runIndex]?.data?.main?.[0] ?? []

        const genericHints = getGenericHints({
          workflowNode,
          node: node.value,
          nodeType: nodeType.value,
          nodeOutputData,
          workflow: props.workflow,
          hasNodeRun: hasNodeRun.value,
          hasMultipleInputItems,
        })

        return executionHints.value.concat(nodeHints, genericHints).filter(shouldHintBeDisplayed)
      }
    }
  }
  catch (error) {
    console.error('Error while getting node hints', error)
  }

  return []
}

const nodeHints = computed(() => {
  return getNodeHints()
})

function onItemHover(itemIndex: number | null) {
  if (itemIndex === null) {
    emit('itemHover', null)

    return
  }

  emit('itemHover', {
    outputIndex: currentOutputIndex.value,
    itemIndex,
  })
}

function showPinDataDiscoveryTooltip(value: IDataObject[]) {
  if (!isTriggerNode.value) {
    return
  }

  const pinDataDiscoveryFlag = false
  // const pinDataDiscoveryFlag = useStorage(LOCAL_STORAGE_PIN_DATA_DISCOVERY_NDV_FLAG).value

  if (value && value.length > 0 && !isReadOnlyRoute.value && !pinDataDiscoveryFlag) {
    pinDataDiscoveryComplete()

    setTimeout(() => {
      isControlledPinDataTooltip.value = true
      pinDataDiscoveryTooltipVisible.value = true
      // dataPinningEventBus.emit('data-pinning-discovery', { isTooltipVisible: true })
    }, 500) // Wait for NDV to open
  }
}

function hidePinDataDiscoveryTooltip() {
  if (pinDataDiscoveryTooltipVisible.value) {
    isControlledPinDataTooltip.value = false
    pinDataDiscoveryTooltipVisible.value = false
    // dataPinningEventBus.emit('data-pinning-discovery', { isTooltipVisible: false })
  }
}

function pinDataDiscoveryComplete() {
  // useStorage(LOCAL_STORAGE_PIN_DATA_DISCOVERY_NDV_FLAG).value = 'true'
  // useStorage(LOCAL_STORAGE_PIN_DATA_DISCOVERY_CANVAS_FLAG).value = 'true'
}

function enterEditMode({ origin }: EnterEditModeArgs) {
  const inputData = pinnedData.data.value
    ? clearJsonKey(pinnedData.data.value)
    : executionDataToJson(rawInputData.value)

  const inputDataLength = Array.isArray(inputData)
    ? inputData.length
    : Object.keys(inputData ?? {}).length

  const data = inputDataLength > 0 ? inputData : TEST_PIN_DATA

  ndvStore.setOutputPanelEditModeEnabled(true)
  ndvStore.setOutputPanelEditModeValue(JSON.stringify(data, null, 2))
}

function onClickCancelEdit() {
  ndvStore.setOutputPanelEditModeEnabled(false)
  ndvStore.setOutputPanelEditModeValue('')
}

function onClickSaveEdit() {
  if (!node.value) {
    return
  }

  const { value } = editMode.value

  $toast.removeAllGroups()

  try {
    const clearedValue = clearJsonKey(value) as INodeExecutionData[]

    try {
      pinnedData.setData(clearedValue, 'save-edit')
    }
    catch (error) {
      // setData function already shows toasts on error, so just return here
      return
    }
  }
  catch (error) {
    $toast.error({ summary: '无法保存无效的 JSON' })

    return
  }

  ndvStore.setOutputPanelEditModeEnabled(false)
}

async function onTogglePinData({ source }: { source: PinDataSource | UnpinDataSource }) {
  if (!node.value) {
    return
  }

  if (source === 'pin-icon-click') {
    // void externalHooks.run('runData.onTogglePinData', telemetryPayload)
  }

  nodeHelpers.updateNodeParameterIssues(node.value)

  if (pinnedData.hasData.value) {
    pinnedData.unsetData(source)

    return
  }

  try {
    pinnedData.setData(rawInputData.value, 'pin-icon-click')
  }
  catch (error) {
    console.error(error)

    return
  }

  if (maxRunIndex.value > 0) {
    $toast.success({
      summary: `运行 #${props.runIndex} 已固定`,
      detail: '每次运行节点时，将输出此运行。',
    })
  }

  hidePinDataDiscoveryTooltip()
  pinDataDiscoveryComplete()
}

function onBranchChange(value: number) {
  outputIndex.value = value
}

function showTooMuchData() {
  showData.value = true
  userEnabledShowData.value = true
}

function toggleLinkRuns() {
  if (props.linkedRuns) {
    unlinkRun()
  }
  else {
    linkRun()
  }
}

function linkRun() {
  emit('linkRun')
}

function unlinkRun() {
  emit('unlinkRun')
}

const onCurrentPageChange = (value: PrimeVuePageInfo) => {
  currentPage.value = value.page + 1
  pageSize.value = value.rows
}

function resetCurrentPageIfTooFar() {
  const maxPage = Math.ceil(dataCount.value / pageSize.value)

  if (maxPage < currentPage.value) {
    currentPage.value = maxPage
  }
}

function onPageSizeChange(newPageSize: number) {
  pageSize.value = newPageSize

  resetCurrentPageIfTooFar()
}

function updateShowData() {
  // Display data if it is reasonably small (< 1MB)
  showData.value = (isSchemaView.value && dataSize.value < MAX_DISPLAY_DATA_SIZE_SCHEMA_VIEW)
    || dataSize.value < MAX_DISPLAY_DATA_SIZE
}

const closeBinaryDataDisplay = () => {
  binaryDataDisplayVisible.value = false
  binaryDataDisplayData.value = null
}

const { refKey: dataContainerRefKey, ref: dataContainerRef } = useRef<InstanceType<typeof RunDataBox>>()

function onDisplayModeChange(newDisplayMode: IRunDataDisplayMode) {
  ndvStore.setPanelDisplayMode({ pane: props.paneType, mode: newDisplayMode })

  if (!userEnabledShowData.value) {
    updateShowData()
  }

  if (dataContainerRef.value) {
    const containerElement = dataContainerRef.value.rootRef

    if (containerElement) {
      const dataDisplay = containerElement.children[0]

      if (dataDisplay) {
        /**
         * 在显示模式切换后，重置数据容器的滚动位置到顶部，
         * 这样可以确保用户在切换视图模式时能看到新视图的开始内容
         */
        dataDisplay.scrollTo(0, 0)
      }
    }
  }

  closeBinaryDataDisplay()
  // void externalHooks.run('runData.displayModeChanged', {
  //   newValue: newDisplayMode,
  //   oldValue: previous,
  // })
}

function getRunLabel(option: number) {
  if (!node.value) {
    return
  }

  let itemsCount = 0

  for (let i = 0; i <= maxOutputIndex.value; i++) {
    itemsCount += getPinDataOrLiveData(getRawInputData(option - 1, i)).length
  }

  const items = `${itemsCount} 项`

  const metadata = workflowRunData.value?.[node.value.name]?.[option - 1]?.metadata ?? null
  const subexecutions = metadata?.subExecutionsCount
    ? `${metadata.subExecutionsCount} 个子执行`
    : ''

  const itemsLabel = itemsCount > 0 ? ` (${items}${subexecutions})` : ''

  return `${option} / ${maxRunIndex.value + 1}${itemsLabel}`
}

function getPinDataOrLiveData(data: INodeExecutionData[]): INodeExecutionData[] {
  if (pinnedData.data.value && !props.isProductionExecutionPreview) {
    return Array.isArray(pinnedData.data.value)
      ? pinnedData.data.value.map((value) => ({
          json: value,
        }))
      : [
          {
            json: pinnedData.data.value,
          },
        ]
  }

  return data
}

function getFilteredData(data: INodeExecutionData[]): INodeExecutionData[] {
  if (!search.value || isSchemaView.value) {
    return data
  }

  currentPage.value = 1

  return data.filter(({ json }) => searchInObject(json, search.value))
}

function determineInitialOutputIndex() {
  for (let i = 0; i <= maxOutputIndex.value; i++) {
    if (getRawInputData(props.runIndex, i).length) {
      return i
    }
  }

  return 0
}

function init() {
  // Reset the selected output index every time another node gets selected
  outputIndex.value = determineInitialOutputIndex()
  refreshDataSize()
  closeBinaryDataDisplay()
  let outputTypes: NodeConnectionType[] = []

  if (node.value && nodeType.value) {
    const outputs = getResolvedNodeOutputs()
    outputTypes = NodeHelpers.getConnectionTypes(outputs) as unknown as NodeConnectionType[]
  }

  connectionType.value = outputTypes.length === 0 ? NodeConnectionType.Main : outputTypes[0]

  if (binaryData.value.length > 0) {
    ndvStore.setPanelDisplayMode({
      pane: props.paneType,
      mode: 'binary',
    })
  }
  else if (displayMode.value === 'binary') {
    ndvStore.setPanelDisplayMode({
      pane: props.paneType,
      mode: 'table',
    })
  }
}

function isViewable(index: number, key: string | number): boolean {
  const { fileType } = binaryData.value[index][key]

  return (
    !!fileType && ['image', 'audio', 'video', 'text', 'json', 'pdf', 'html'].includes(fileType)
  )
}

function isDownloadable(index: number, key: string | number): boolean {
  const { mimeType, fileName } = binaryData.value[index][key]

  return !!(mimeType && fileName)
}

async function downloadBinaryData(index: number, key: string | number) {
  const { id, data, fileName, fileExtension, mimeType } = binaryData.value[index][key]

  if (id) {
    const url = workflowsStore.getBinaryUrl(id, 'download', fileName ?? '', mimeType)
    saveAs(url, [fileName, fileExtension].join('.'))

    return
  }
  else {
    const bufferString = 'data:' + mimeType + ';base64,' + data
    const blob = await fetch(bufferString).then(async (d) => await d.blob())
    saveAs(blob, fileName ?? '')
  }
}

const downloadJsonData = async () => {
  const fileName = (node.value?.name ?? '').replace(/[^\w\d]/g, '_')
  const blob = new Blob([JSON.stringify(rawInputData.value, null, 2)], {
    type: 'application/json',
  })

  saveAs(blob, `${fileName}.json`)
}

const displayBinaryData = (index: number, key: string | number) => {
  const { data, mimeType } = binaryData.value[index][key]
  binaryDataDisplayVisible.value = true

  binaryDataDisplayData.value = {
    node: node.value?.name,
    runIndex: props.runIndex,
    outputIndex: currentOutputIndex.value,
    index,
    key,
    data,
    mimeType,
  }
}

function getOutputName(outputIndex: number) {
  if (node.value === null) {
    return outputIndex + 1
  }

  const outputs = getResolvedNodeOutputs()
  const outputConfiguration = outputs?.[outputIndex] as INodeOutputConfiguration

  if (outputConfiguration && isObject(outputConfiguration)) {
    return outputConfiguration?.displayName
  }

  if (!nodeType.value?.outputNames || nodeType.value.outputNames.length <= outputIndex) {
    return outputIndex + 1
  }

  return nodeType.value.outputNames[outputIndex]
}

function refreshDataSize() {
  // Hide by default the data from being displayed
  showData.value = false
  const jsonItems = inputDataPage.value.map((item) => item.json)
  const byteSize = new Blob([JSON.stringify(jsonItems)]).size
  dataSize.value = byteSize
  updateShowData()
}

function onRunIndexChange(run: number | undefined) {
  if (typeof run === 'number') {
    emit('runChange', run)
  }
}

function enableNode() {
  if (node.value) {
    const updateInformation = {
      name: node.value.name,
      properties: {
        disabled: !node.value.disabled,
      } as IDataObject,
    } as INodeUpdatePropertiesInformation

    workflowsStore.updateNodeProperties(updateInformation)
  }
}

function setDisplayMode() {
  if (!activeNode.value) {
    return
  }

  const shouldDisplayHtml = activeNode.value.type === HTML_NODE_TYPE
    && activeNode.value.parameters.operation === 'generateHtmlTemplate'

  if (shouldDisplayHtml) {
    ndvStore.setPanelDisplayMode({
      pane: 'output',
      mode: 'html',
    })
  }
}

function activatePane() {
  emit('activatePane')
}

function onSearchClear() {
  search.value = ''
  document.dispatchEvent(new KeyboardEvent('keyup', { key: '/' }))
}

function getExecutionLinkLabel(task: ITaskMetadata): string | undefined {
  if (task.parentExecution) {
    return `查看父执行 ${task.parentExecution.executionId}`
  }

  if (task.subExecution) {
    if (activeTaskMetadata.value?.subExecutionsCount === 1) {
      return '查看子执行'
    }
    else {
      return `查看子执行 ${task.subExecution.executionId}`
    }
  }

  return
}

defineExpose({ enterEditMode })
</script>

<template>
  <div
    class="relative flex size-full flex-col"
    :style="{
      '--p-togglebutton-content-sm-padding': '0.25rem 0.5rem',
      '--p-togglebutton-content-border-radius': '4px',
    }"
    @mouseover="activatePane"
  >
    <!-- 固定数据提示横幅 -->
    <RunDataBox
      v-if="
        !isPaneTypeInput
          && pinnedData.hasData.value
          && !editMode.enabled
          && !isProductionExecutionPreview
      "
    >
      <CalloutMessage
        data-testid="ndv-pinned-data-callout"
        size="small"
      >
        <template #content>
          <div class="flex-wrap gap-1 flex-center">
            此数据已固定。
            <span
              v-if="!isReadOnlyRoute && !readOnlyEnv"
            >
              <ProBtn
                data-testid="ndv-unpin-data"
                label="取消固定"
                mini
                size="small"
                variant="text"
                @click.stop="onTogglePinData({ source: 'banner-link' })"
              />
            </span>

            <Button
              class="ml-auto"
              label="了解更多"
              size="small"
              :to="DATA_PINNING_DOCS_URL"
              variant="link"
            />
          </div>
        </template>
      </CalloutMessage>
    </RunDataBox>

    <!-- 二进制数据显示弹窗 -->
    <template v-if="binaryDataDisplayData">
      <div
        v-if="binaryDataDisplayVisible"
        class="absolute inset-0 z-10"
      >
        <BinaryDataDisplay
          :displayData="binaryDataDisplayData"
          @close="closeBinaryDataDisplay"
        />
      </div>
    </template>

    <!-- 顶部工具栏 -->
    <RunDataBox class="pt-card-container">
      <div class="gap-1 flex-center">
        <!-- 头部插槽 -->
        <div
          v-if="$slots.header"
          class="flex-1"
        >
          <slot name="header" />
        </div>

        <!-- 工具按钮组 -->
        <div
          v-show="!hasRunError && !isTrimmedManualExecutionDataItem"
          class="flex-wrap gap-1 flex-center"
          data-testid="run-data-pane-header"
        >
          <LazyRunDataSearch
            v-if="showIOSearch"
            v-model="search"
            class="shrink-0"
            :displayMode="displayMode"
            :isAreaActive="isPaneActive"
            :paneType="paneType"
            @focus="activatePane"
          />

          <SelectButton
            v-show="
              hasPreviewSchema
                || (hasNodeRun && (inputData.length || binaryData.length || search) && !editMode.enabled)
            "
            data-testid="ndv-run-data-display-mode"
            :modelValue="displayMode"
            optionLabel="label"
            :options="displayModes"
            optionValue="value"
            size="small"
            @update:modelValue="onDisplayModeChange"
          />

          <ProBtn
            v-if="canPinData && !isReadOnlyRoute && !readOnlyEnv"
            v-show="!editMode.enabled"
            data-testid="ndv-edit-pinned-data"
            :disabled="node?.disabled"
            label="编辑输出"
            mini
            onlyIcon
            size="small"
            @click="enterEditMode({ origin: 'editIconButton' })"
          >
            <template #icon="{ size }">
              <PencilIcon :size="size" />
            </template>
          </ProBtn>

          <RunDataPinButton
            v-if="showPinButton"
            :data-pinning-docs-url="DATA_PINNING_DOCS_URL"
            :disabled="pinButtonDisabled"
            :pinnedData="pinnedData"
            :tooltipContentsVisibility="{
              binaryDataTooltipContent: !!binaryData?.length,
              pinDataDiscoveryTooltipContent:
                isControlledPinDataTooltip && pinDataDiscoveryTooltipVisible,
            }"
            @togglePinData="onTogglePinData({ source: 'pin-icon-click' })"
          />

          <!-- 编辑模式操作按钮 -->
          <div
            v-show="editMode.enabled"
            class="gap-2 flex-center"
          >
            <ProBtn
              label="取消"
              mini
              severity="secondary"
              size="small"
              @click="onClickCancelEdit()"
            />
            <ProBtn
              label="保存"
              mini
              severity="primary"
              size="small"
              variant="default"
              @click="onClickSaveEdit()"
            />
          </div>
        </div>
      </div>
    </RunDataBox>

    <RunDataBox v-if="inputSelectLocation === 'header' && $slots['input-select']">
      <slot name="input-select" />
    </RunDataBox>

    <!-- 运行选择器区域 -->
    <RunDataBox
      v-if="maxRunIndex > 0 && !displaysMultipleNodes"
      v-show="!editMode.enabled"
    >
      <div class="flex-wrap gap-1 flex-center">
        <slot
          v-if="inputSelectLocation === 'runs'"
          name="input-select"
        />

        <!-- 运行选择下拉框 -->
        <ProSelect
          data-testid="run-selector"
          :modelValue="runIndex"
          :options="Array.from({ length: maxRunIndex + 1 }, (_, i) => ({
            label: getRunLabel(i + 1) ?? '',
            value: i,
          }))"
          :pt="{
            option: '!tabular-nums',
          }"
          size="small"
          @update:modelValue="onRunIndexChange"
        />

        <span
          v-if="canLinkRuns"
          v-tooltip.top="linkedRuns
            ? '取消显示的输入和输出运行的链接'
            : '将显示的输入和输出运行链接起来'"
        >
          <Button
            data-testid="link-run"
            mini
            size="small"
            text
            variant="link"
            @click="toggleLinkRuns"
          >
            <template #icon>
              <SquareArrowUpRightIcon :size="14" />
            </template>
          </Button>
        </span>

        <slot name="run-info" />
      </div>

      <a
        v-if="
          activeTaskMetadata && hasRelatedExecution && !(paneType === 'input' && hasInputOverwrite)
        "
        data-testid="related-execution-link"
        :href="resolveRelatedExecutionUrl(activeTaskMetadata)"
        target="_blank"
      >
        <SquareArrowUpRightIcon :size="14" />
        {{ getExecutionLinkLabel(activeTaskMetadata) }}
      </a>
    </RunDataBox>

    <!-- 数据前置插槽 -->
    <RunDataBox v-if="!displaysMultipleNodes && $slots['before-data']">
      <slot name="before-data" />
    </RunDataBox>

    <!-- 提示消息 -->
    <RunDataBox v-if="props.calloutMessage">
      <CalloutMessage
        :content="props.calloutMessage"
        data-testid="run-data-callout"
        severity="info"
        size="small"
      />
    </RunDataBox>

    <!-- MARK: 节点提示信息 -->
    <RunDataBox v-if="nodeHints.length > 0">
      <div class="space-y-1">
        <CalloutMessage
          v-for="hint of nodeHints"
          :key="hint.message"
          :content="hint.message"
          data-testid="node-hint"
          severity="secondary"
          size="small"
        />
      </div>
    </RunDataBox>

    <RunDataBox
      v-if="maxOutputIndex > 0 && branches.length > 1 && !displaysMultipleNodes"
      data-testid="branches"
    >
      <slot
        v-if="inputSelectLocation === 'outputs'"
        name="input-select"
      />

      <!-- 分支标签页 -->
      <Tabs
        :value="currentOutputIndex"
        @update:value="onBranchChange"
      >
        <TabList>
          <Tab
            v-for="branch of branches"
            :key="branch.value"
            :value="branch.value"
          >
            {{ branch.label }}
          </Tab>
        </TabList>
      </Tabs>
    </RunDataBox>

    <!-- 数据项计数显示 -->
    <RunDataBox
      v-else-if="
        hasNodeRun
          && !isSearchInSchemaView
          && ((dataCount > 0 && maxRunIndex === 0) || search)
          && !isArtificialRecoveredEventItem
          && !displaysMultipleNodes
      "
      v-show="!editMode.enabled"
      class="gap-2 flex-center"
      data-testid="ndv-items-count"
    >
      <div
        v-if="inputSelectLocation === 'items' && $slots['input-select']"
        class="flex-1"
      >
        <slot name="input-select" />
      </div>

      <div class="ml-auto text-sm flex-center text-secondary">
        <span v-if="search">
          {{ dataCount }} 个结果，共 {{ unfilteredDataCount }} 项数据可供搜索
        </span>

        <span v-else>
          <span>{{ dataCount }} 项</span>
          <span v-if="activeTaskMetadata?.subExecutionsCount">
            {{ activeTaskMetadata.subExecutionsCount }} 子执行
          </span>
        </span>

        <a
          v-if="
            activeTaskMetadata && hasRelatedExecution && !(paneType === 'input' && hasInputOverwrite)
          "
          data-testid="related-execution-link"
          :href="resolveRelatedExecutionUrl(activeTaskMetadata)"
          target="_blank"
        >
          <SquareArrowUpRightIcon :size="14" />
          {{ getExecutionLinkLabel(activeTaskMetadata) }}
        </a>
      </div>
    </RunDataBox>

    <!-- MARK: 数据展示容器 -->
    <RunDataBox
      :ref="dataContainerRefKey"
      class="relative flex flex-1 flex-col overflow-auto"
      data-testid="ndv-data-container"
    >
      <!-- 执行中状态 -->
      <div
        v-if="isExecuting && !isWaitNodeWaiting"
        class="flex flex-col items-center justify-center gap-5 px-5 py-10 text-secondary"
        data-testid="ndv-executing"
      >
        <Loader2Icon
          class="animate-spin"
          :size="40"
        />

        <span>{{ executingMessage }}</span>
      </div>

      <!-- 编辑模式 -->
      <div
        v-else-if="editMode.enabled"
        class="flex flex-1 flex-col gap-2"
      >
        <div class="flex-1">
          <JsonEditor
            fillParent
            :modelValue="editMode.value"
            @update:modelValue="ndvStore.setOutputPanelEditModeValue($event)"
          />
        </div>

        <div class="">
          <Message
            severity="secondary"
            size="small"
            variant="simple"
          >
            <span>您可以从先前的执行中复制数据，并将其粘贴到上方。</span>
            <ProBtn
              :href="DATA_EDITING_DOCS_URL"
              label="了解更多"
              size="small"
              variant="link"
            />
          </Message>
        </div>
      </div>

      <!-- 子工作流执行错误 -->
      <div
        v-else-if="
          paneType === 'output' && hasSubworkflowExecutionError && subworkflowExecutionError
        "
      >
        <NodeErrorView
          :error="subworkflowExecutionError"
        />
      </div>

      <!-- 等待节点状态 -->
      <div
        v-else-if="isWaitNodeWaiting"
      >
        <slot name="node-waiting">
          xxx
        </slot>
      </div>

      <!-- 节点未运行状态 -->
      <div
        v-else-if="!hasNodeRun && !(displaysMultipleNodes && (node?.disabled || hasPreviewSchema)) && $slots['node-not-run']"
      >
        <slot name="node-not-run" />
      </div>

      <!-- 节点已禁用状态 -->
      <div
        v-else-if="paneType === 'input' && !displaysMultipleNodes && node?.disabled"
      >
        <span>
          「{{ node.name }}」 节点已禁用，将不会执行。
          <Button
            label="启用它"
            @click="enableNode"
          />
        </span>
      </div>

      <!-- 数据加载中状态 -->
      <div
        v-else-if="isTrimmedManualExecutionDataItem && uiStore.isProcessingExecutionResults"
      >
        <div>
          <LoaderIcon
            class="animate-spin"
            :size="14"
          />
        </div>
        <span class="text-lg">
          加载数据
        </span>
      </div>

      <!-- 数据不可见状态 -->
      <div
        v-else-if="isTrimmedManualExecutionDataItem"
      >
        <div
          class="text-lg font-semibold"
        >
          数据不可见
        </div>
        <div>
          一旦执行完成，它将在此处可用。
        </div>
      </div>

      <!-- 人工恢复数据状态 -->
      <div
        v-else-if="hasNodeRun && isArtificialRecoveredEventItem"
      >
        <slot name="recovered-artificial-output-data" />
      </div>

      <!-- 运行错误状态 -->
      <div
        v-else-if="hasNodeRun && hasRunError"
      >
        <p
          v-if="isPaneTypeInput"
          class="text-lg font-semibold"
        >
          运行节点 「{{ node?.name ?? '' }}」 时出错
        </p>
        <div v-else-if="$slots['content']">
          <NodeErrorView
            v-if="workflowRunErrorAsNodeError"
            compact
            :error="workflowRunErrorAsNodeError"
          />
          <slot name="content" />
        </div>
        <NodeErrorView
          v-else-if="workflowRunErrorAsNodeError"
          :error="workflowRunErrorAsNodeError"
        />
      </div>

      <!-- 无匹配项目或分支无数据 -->
      <div
        v-else-if="
          hasNodeRun && (!unfilteredDataCount || (search && !dataCount)) && branches.length > 1
        "
      >
        <template v-if="search">
          <!-- 空态图标 -->
          <SearchIcon :size="32" />

          <!-- 主标题 -->
          <div class="py-4 text-lg font-semibold">
            没有匹配的项目
          </div>

          <!-- 当前搜索词 -->
          <div class="pb-3">
            <span class="text-secondary">当前搜索：</span><span class="font-medium">{{ search }}</span>
          </div>

          <!-- 操作建议 -->
          <div class="flex flex-wrap items-center justify-center gap-0.5 text-secondary">
            <span>尝试更改关键词或</span>
            <ProBtn
              label="清除过滤器"
              mini
              size="small"
              @click="onSearchClear"
            />
            <span>以查看更多数据</span>
          </div>
        </template>

        <div v-else>
          {{ noDataInBranchMessage }}
        </div>
      </div>

      <!-- 无输出数据 -->
      <div
        v-else-if="hasNodeRun && !inputData.length && !search"
      >
        <slot name="no-output-data">
          xxx
        </slot>
      </div>

      <!-- 数据过大警告 -->
      <div
        v-else-if="hasNodeRun && !showData"
        data-testid="ndv-data-size-warning"
      >
        <span class="text-lg font-semibold">
          {{ tooMuchDataTitle }}
        </span>

        <div class="text-center">
          <div>
            该节点包含 {{ dataSizeInMB }} MB 的数据。显示它可能会引起问题。
          </div>
          <div>
            如果决定显示它，请避免使用 JSON 视图。
          </div>
        </div>

        <Button
          label="无论如何显示数据"
          outline
          @click="showTooMuchData"
        />

        <Button
          label="下载"
          size="small"
          @click="downloadJsonData()"
        />
      </div>

      <!-- 自定义内容插槽 -->
      <slot
        v-else-if="hasNodeRun && $slots['content']"
        name="content"
      />

      <!-- 仅二进制数据提示 -->
      <div
        v-else-if="
          hasNodeRun
            && displayMode === 'table'
            && binaryData.length > 0
            && inputData.length === 1
            && Object.keys(jsonData[0] || {}).length === 0
        "
      >
        <div class="flex flex-col items-center py-4 text-center">
          <div class="mb-3 text-sm text-secondary">
            当前项目不包含普通 JSON 数据，仅包含二进制文件数据
          </div>

          <ProBtn
            label="查看二进制数据"
            size="small"
            variant="outlined"
            @click="switchToBinary()"
          >
            <template #icon="{ size }">
              <EyeIcon :size="size" />
            </template>
          </ProBtn>
        </div>
      </div>

      <!-- 搜索无匹配结果 -->
      <div
        v-else-if="showIoSearchNoMatchContent"
        class="flex-col justify-center py-12 text-center text-sm flex-center"
      >
        <!-- 空态图标 -->
        <SearchIcon :size="32" />

        <!-- 主标题 -->
        <div class="py-4 text-lg font-semibold">
          没有匹配的项目
        </div>

        <!-- 当前搜索词 -->
        <div class="pb-3">
          <span class="text-secondary">当前搜索：</span><span class="font-medium">{{ search }}</span>
        </div>

        <!-- 操作建议 -->
        <div class="flex flex-wrap items-center justify-center gap-0.5 text-secondary">
          <span>尝试更改关键词或</span>
          <ProBtn
            label="清除过滤器"
            mini
            size="small"
            @click="onSearchClear"
          />
          <span>以查看更多数据</span>
        </div>
      </div>

      <template v-else-if="hasNodeRun && displayMode === 'table' && node">
        <LazyRunDataTable
          :distanceFromActive="distanceFromActive"
          :hasDefaultHoverState="paneType === 'input' && !search"
          :inputData="inputDataPage"
          :mappingEnabled="mappingEnabled"
          :node="node"
          :pageOffset="currentPageOffset"
          :runIndex="runIndex"
          :search="search"
          :totalRuns="maxRunIndex"
          @activeRowChanged="onItemHover"
          @displayModeChange="onDisplayModeChange"
          @mounted="emit('tableMounted', $event)"
        />
      </template>

      <template v-else-if="hasNodeRun && displayMode === 'json' && node">
        <LazyRunDataJson
          :distanceFromActive="distanceFromActive"
          :editMode="editMode"
          :inputData="inputDataPage"
          :mappingEnabled="mappingEnabled"
          :node="node"
          :paneType="paneType"
          :pushRef="pushRef"
          :runIndex="runIndex"
          :search="search"
          :totalRuns="maxRunIndex"
        />
      </template>

      <!-- MARK: HTML 视图 -->
      <template v-else-if="hasNodeRun && isPaneTypeOutput && displayMode === 'html'">
        <LazyRunDataHtml :inputHtml="inputHtml" />
      </template>

      <!-- MARK: Schema 视图 -->
      <template v-else-if="(hasNodeRun || hasPreviewSchema) && isSchemaView">
        <LazyRunDataSchema
          :connectionType="connectionType"
          :data="jsonData"
          :mappingEnabled="mappingEnabled"
          :node="node"
          :nodes="nodes"
          :outputIndex="currentOutputIndex"
          :paneType="paneType"
          :runIndex="runIndex"
          :search="search"
          :totalRuns="maxRunIndex"
          @clear:search="onSearchClear"
        />
      </template>

      <!-- MARK: 二进制数据视图 -->
      <div v-else-if="displayMode === 'binary'">
        <div
          v-if="binaryData.length === 0"
          class="flex flex-col items-center justify-center py-16 text-center"
        >
          <h3 class="mb-2 font-semibold">
            未找到二进制数据
          </h3>
          <div class="text-sm text-secondary">
            此节点没有生成任何二进制数据
          </div>
        </div>

        <div
          v-else
          class="grid grid-cols-1 gap-card-container"
        >
          <div
            v-for="(binaryDataEntry, index) of binaryData"
            :key="index"
            class="rounded-lg border px-2.5 py-2 shadow-sm"
          >
            <!-- 二进制数据详情 -->
            <div>
              <div
                v-for="(binaryDataX, key) of binaryDataEntry"
                :key="index + '_' + key"
              >
                <div :data-testid="'ndv-binary-data_' + index">
                  <!-- 数据键名 -->
                  <div class="gap-2 flex-center">
                    <div
                      v-if="binaryData.length > 1"
                      class="flex size-6 items-center justify-center rounded-lg bg-emphasis text-sm font-medium text-primary"
                    >
                      {{ index + 1 }}
                    </div>

                    <div class="flex-1 truncate font-semibold">
                      {{ key }}
                    </div>
                  </div>

                  <Divider class="!my-2" />

                  <!-- 文件信息 -->
                  <div class="space-y-2.5 text-sm">
                    <!-- 文件名 -->
                    <div v-if="binaryDataX.fileName">
                      <div class="font-medium">
                        文件名
                      </div>
                      <div
                        class="line-clamp-2 text-secondary"
                        :title="binaryDataX.fileName"
                      >
                        {{ binaryDataX.fileName }}
                      </div>
                    </div>

                    <!-- 目录 -->
                    <div v-if="binaryDataX.directory">
                      <div class="font-medium">
                        目录
                      </div>
                      <div
                        class="line-clamp-2 text-secondary"
                        :title="binaryDataX.directory"
                      >
                        {{ binaryDataX.directory }}
                      </div>
                    </div>

                    <!-- 文件扩展名 -->
                    <div v-if="binaryDataX.fileExtension">
                      <div class="font-medium">
                        文件扩展名
                      </div>
                      <div class="line-clamp-2 text-secondary">
                        {{ binaryDataX.fileExtension }}
                      </div>
                    </div>

                    <!-- 文件类型 -->
                    <div v-if="binaryDataX.mimeType">
                      <div class="min-w-0 flex-1">
                        <div class="font-medium">
                          MIME 类型
                        </div>
                        <div class="line-clamp-2 text-secondary">
                          {{ binaryDataX.mimeType }}
                        </div>
                      </div>
                    </div>

                    <!-- 文件大小 -->
                    <div v-if="binaryDataX.fileSize">
                      <div class="font-medium">
                        文件大小
                      </div>
                      <div class="line-clamp-2 text-secondary">
                        {{ binaryDataX.fileSize }}
                      </div>
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="flex justify-center gap-2">
                    <ProBtn
                      v-if="isViewable(index, key)"
                      class="shrink-0"
                      data-testid="ndv-view-binary-data"
                      label="查看"
                      mini
                      size="small"
                      variant="outlined"
                      @click="displayBinaryData(index, key)"
                    >
                      <template #icon="{ size }">
                        <EyeIcon :size="size" />
                      </template>
                    </ProBtn>
                    <ProBtn
                      v-if="isDownloadable(index, key)"
                      class="shrink-0"
                      data-testid="ndv-download-binary-data"
                      label="下载"
                      mini
                      size="small"
                      variant="outlined"
                      @click="downloadBinaryData(index, key)"
                    >
                      <template #icon="{ size }">
                        <ArrowDownToLineIcon :size="size" />
                      </template>
                    </ProBtn>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </RunDataBox>

    <!-- 分页器 -->
    <RunDataBox
      v-if="
        hidePagination === false
          && hasNodeRun
          && !hasRunError
          && displayMode !== 'binary'
          && dataCount > pageSize
          && !isSchemaView
          && !isArtificialRecoveredEventItem
      "
      v-show="!editMode.enabled"
      data-testid="ndv-data-pagination"
    >
      <div class="flex justify-center">
        <ProPaginator
          hideStart
          :pageInfo="{
            page: currentPage,
            page_size: pageSize,
            page_sum: Math.ceil(dataCount / pageSize),
            total: dataCount,
          }"
          :pageSizeOptions="pageSizes"
          @pageChange="onCurrentPageChange($event)"
        />
      </div>
    </RunDataBox>

    <div
      v-if="blockUI"
      class="absolute inset-0 z-10 bg-content opacity-60"
    />
  </div>
</template>
