<script setup lang="ts">
import { ChevronLeftIcon } from 'lucide-vue-next'
import type { IBinaryData, IRunData } from 'n8n-workflow'

import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { useWorkflowStore } from '#wf/stores/workflow.store'

import BinaryDataDisplayEmbed from './BinaryDataDisplayEmbed.vue'
import RunDataBox from './RunDataBox.vue'

const props = defineProps<{
  displayData: IBinaryData
}>()

const emit = defineEmits<{
  close: []
}>()

const nodeHelpers = useNodeHelpers()
const workflowsStore = useWorkflowStore()

const workflowRunData = computed<IRunData | null>(() => {
  const workflowExecution = workflowsStore.getWorkflowExecution

  if (workflowExecution === null) {
    return null
  }

  const executionData = workflowExecution.data

  return executionData ? executionData.resultData.runData : null
})

const binaryData = computed<IBinaryData | null>(() => {
  if (
    typeof props.displayData.node !== 'string'
    || typeof props.displayData.key !== 'string'
    || typeof props.displayData.runIndex !== 'number'
    || typeof props.displayData.index !== 'number'
    || typeof props.displayData.outputIndex !== 'number'
  ) {
    return null
  }

  const binaryDataLocal = nodeHelpers.getBinaryData(
    workflowRunData.value,
    props.displayData.node,
    props.displayData.runIndex,
    props.displayData.outputIndex,
  )

  if (binaryDataLocal.length === 0) {
    return null
  }

  if (
    props.displayData.index >= binaryDataLocal.length
    || binaryDataLocal[props.displayData.index][props.displayData.key] === undefined
  ) {
    return null
  }

  const binaryDataItem: IBinaryData = binaryDataLocal[props.displayData.index][props.displayData.key]

  return binaryDataItem
})

const closeWindow = () => {
  // Handle the close externally as the visible parameter is an external prop
  // and is so not allowed to be changed here.
  emit('close')
}
</script>

<template>
  <div class="flex size-full flex-col bg-content">
    <div class="p-2 pb-0">
      <ProBtn
        label="返回"
        mini
        size="small"
        variant="text"
        @click.stop="closeWindow()"
      >
        <template #icon="{ size }">
          <ChevronLeftIcon :size="size" />
        </template>
      </ProBtn>
    </div>

    <Divider class="!my-2" />

    <div class="flex-1 overflow-y-auto">
      <RunDataBox>
        <div v-if="!binaryData">
          没有找到要显示的数据
        </div>

        <BinaryDataDisplayEmbed
          v-else
          :binaryData="binaryData"
        />
      </RunDataBox>
    </div>
  </div>
</template>
