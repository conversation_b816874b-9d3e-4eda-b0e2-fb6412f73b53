<script setup lang="ts">
import type { StyleValue } from 'vue'

import { debounce } from 'lodash-es'
import { SearchIcon } from 'lucide-vue-next'

import type { IRunDataDisplayMode, NodePanelType } from '#wf/types/interface'

type Props = {
  modelValue: string
  paneType?: NodePanelType
  displayMode?: IRunDataDisplayMode
}

const COLLAPSED_WIDTH = '30px'
const OPEN_WIDTH = '110px'
const OPEN_MIN_WIDTH = '110px'

const emit = defineEmits<{
  'update:modelValue': [value: Props['modelValue']]
  focus: []
}>()

const props = withDefaults(defineProps<Props>(), {
  paneType: 'output',
  displayMode: 'schema',
})

const { refKey, ref: inputRef } = useRef<{ $el: HTMLInputElement }>()

const search = ref(props.modelValue ?? '')
const opened = ref(!!search.value)

const placeholder = computed(() => {
  if (props.paneType === 'output') {
    return '过滤输出'
  }

  if (props.displayMode === 'schema') {
    return '搜索前置节点的字段'
  }

  return '过滤输入'
})

const style = computed<StyleValue>(() =>
  opened.value
    ? { maxWidth: OPEN_WIDTH, minWidth: OPEN_MIN_WIDTH }
    : { maxWidth: COLLAPSED_WIDTH },
)

const debouncedEmitUpdate = debounce((value: string) => emit('update:modelValue', value), 300, {
  trailing: true,
})

const handleSearchUpdate = (value: string) => {
  search.value = value
  void debouncedEmitUpdate(value)
}

const handleSearchOpen = () => {
  opened.value = true
  inputRef.value?.$el.select()
  emit('focus')
}

const handleSearchClose = () => {
  if (!props.modelValue) {
    opened.value = false
  }
}

watch(
  () => props.modelValue,
  (value) => {
    search.value = value
  },
)
</script>

<template>
  <div
    class="relative"
    data-testid="ndv-search"
    :style="style"
  >
    <div
      v-show="!opened"
      class="absolute inset-0 cursor-pointer justify-center flex-center"
      @click="handleSearchOpen()"
    >
      <SearchIcon :size="14" />
    </div>

    <InputText
      :ref="refKey"
      :dt="{
        sm: {
          padding: {
            x: '0.25rem',
          },
        },
      }"
      fluid
      :modelValue="search"
      :placeholder="opened ? placeholder : ''"
      size="small"
      @blur="handleSearchClose()"
      @update:modelValue="handleSearchUpdate"
    />
  </div>
</template>
