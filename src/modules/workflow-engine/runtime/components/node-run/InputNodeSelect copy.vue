<script setup lang="ts">
import type { IConnectedNode, Workflow } from 'n8n-workflow'

import NodeIcon from '#wf/components/NodeIcon.vue'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import { isPresent } from '#wf/utils/typesUtils'

interface Props {
  nodes: IConnectedNode[]
  workflow: Workflow
  modelValue: string | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:model-value': [value: string]
}>()

const workflowsStore = useWorkflowStore()
const nodeTypesStore = useNodeTypesStore()
const ndvStore = useNDVStore()

const selectedInputNode = computed(() => workflowsStore.getNodeByName(props.modelValue ?? ''))

const selectedInputNodeType = computed(() => {
  const node = selectedInputNode.value

  if (!node) {
    return null
  }

  return nodeTypesStore.getNodeType(node.type, node.typeVersion)
})

const inputNodes = computed(() =>
  props.nodes
    .map((node) => {
      const fullNode = workflowsStore.getNodeByName(node.name)

      if (!fullNode) {
        return null
      }

      return {
        node: fullNode,
        type: nodeTypesStore.getNodeType(fullNode.type, fullNode.typeVersion),
        depth: node.depth,
      }
    })
    .filter(isPresent),
)

const activeNode = computed(() => ndvStore.activeNode)

const activeNodeType = computed(() => {
  const node = activeNode.value

  if (!node) {
    return null
  }

  return nodeTypesStore.getNodeType(node.type, node.typeVersion)
})

const isMultiInputNode = computed(() => {
  const nodeType = activeNodeType.value

  return nodeType !== null && nodeType.inputs.length > 1
})

const connectedTo = (nodeName: string) => {
  const connections = ndvStore.ndvNodeInputNumber[nodeName]

  if (!connections) {
    return ''
  }

  if (connections.length === 1) {
    return `Input ${ndvStore.ndvNodeInputNumber[nodeName]}`
  }

  return `Inputs ${ndvStore.ndvNodeInputNumber[nodeName].join(', ')}`
}

function getMultipleNodesText(nodeName: string): string {
  if (
    !nodeName
    || !isMultiInputNode.value
    || !activeNode.value
    || !activeNodeType.value?.inputNames
  ) { return '' }

  const activeNodeConnections = props.workflow.connectionsByDestinationNode[activeNode.value.name].main || []
  // Collect indexes of connected nodes
  const connectedInputIndexes = activeNodeConnections.reduce((acc: number[], node, index) => {
    if (node?.[0] && node[0].node === nodeName) {
      return [...acc, index]
    }

    return acc
  }, [])

  // Match connected input indexes to their names specified by active node
  const connectedInputs = connectedInputIndexes.map(
    (inputIndex) => activeNodeType.value?.inputNames?.[inputIndex],
  )

  if (connectedInputs.length === 0) {
    return ''
  }

  return `(${connectedInputs.join(' & ')})`
}

function subtitle(nodeName: string, depth: number) {
  const multipleNodesText = getMultipleNodesText(nodeName)

  if (multipleNodesText) {
    return multipleNodesText
  }

  return `（${depth} 节点前）`
}

function onInputNodeChange(value: string) {
  emit('update:model-value', value)
}
</script>

<template>
  <n8n-select
    data-testid="ndv-input-select"
    filterable
    :modelValue="modelValue"
    no-data-text="未找到节点"
    placeholder="父节点"
    size="small"
    teleported
    @update:modelValue="onInputNodeChange"
  >
    <template #prefix>
      <NodeIcon
        :disabled="selectedInputNode?.disabled"
        :nodeType="selectedInputNodeType"
        :shrink="false"
        :size="14"
      />
    </template>

    <n8n-option
      v-for="{ node, type, depth } of inputNodes"
      :key="node.name"
      data-testid="ndv-input-option"
      :label="`${node.name} ${getMultipleNodesText(node.name)}`"
      :value="node.name"
    >
      <NodeIcon
        :disabled="node.disabled"
        :nodeType="type"
        :shrink="false"
        :size="14"
      />
      <span>
        {{ node.name }}
        <span v-if="node.disabled">（已停用）</span>
      </span>

      <span>
        {{
          connectedTo(node.name) ? connectedTo(node.name) : subtitle(node.name, depth)
        }}
      </span>
    </n8n-option>
  </n8n-select>
</template>
