<script setup lang="ts" generic="Value extends unknown = unknown">
interface TreeProps {
  value?: Record<string, Value>
  path?: Array<string | number>
  depth?: number
  nodeClass?: string
}

defineSlots<{
  label(props: { label: string, path: Array<string | number> }): never
  value(props: { value: Value }): never
}>()

defineOptions({ name: 'N8nTree' })

const props = withDefaults(defineProps<TreeProps>(), {
  value: () => ({}),
  path: () => [],
  depth: 0,
  nodeClass: '',
})

const classes = computed((): string => {
  const baseClasses = []

  if (props.nodeClass) {
    baseClasses.push(props.nodeClass)
  }

  if (props.depth > 0) {
    baseClasses.push('ml-4') // 对应原来的 margin-left: $--spacing
  }

  return baseClasses.join(' ')
})

const simpleClasses = computed((): string => {
  return 'max-w-xs ml-4 -indent-4' // 对应原来的 simple 样式
})

const isObject = (data: unknown): data is Record<string, Value> => {
  return typeof data === 'object' && data !== null
}

const isSimple = (data: Value): boolean => {
  if (data === null || data === undefined) {
    return true
  }

  if (typeof data === 'object' && Object.keys(data).length === 0) {
    return true
  }

  if (Array.isArray(data) && data.length === 0) {
    return true
  }

  return typeof data !== 'object'
}

const getPath = (key: string): Array<string | number> => {
  if (Array.isArray(props.value)) {
    return [...props.path, parseInt(key, 10)]
  }

  return [...props.path, key]
}
</script>

<template>
  <div
    v-if="isObject(value)"
    class="n8n-tree"
  >
    <div
      v-for="(label, i) of Object.keys(value)"
      :key="i"
      :class="classes"
    >
      <div
        v-if="isSimple(value[label])"
        :class="simpleClasses"
      >
        <slot
          v-if="$slots.label"
          :label="label"
          name="label"
          :path="getPath(label)"
        />
        <span v-else>{{ label }}</span>
        <span>:</span>
        <slot
          v-if="$slots.value"
          name="value"
          :value="value[label]"
        />
        <span v-else>{{ value[label] }}</span>
      </div>
      <div v-else>
        <slot
          v-if="$slots.label"
          :label="label"
          name="label"
          :path="getPath(label)"
        />
        <span v-else>{{ label }}</span>
        <RunDataTableTree
          v-if="isObject(value[label])"
          :depth="depth + 1"
          :nodeClass="nodeClass"
          :path="getPath(label)"
          :value="value[label]"
        >
          <template
            v-if="$slots.label"
            #label="data"
          >
            <slot
              name="label"
              v-bind="data"
            />
          </template>

          <template
            v-if="$slots.value"
            #value="data"
          >
            <slot
              name="value"
              v-bind="data"
            />
          </template>
        </RunDataTableTree>
      </div>
    </div>
  </div>
</template>
