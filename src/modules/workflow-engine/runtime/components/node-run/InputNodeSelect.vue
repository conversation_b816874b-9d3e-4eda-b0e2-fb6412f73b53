<script setup lang="ts">
import type { IConnectedNode, INodeTypeDescription, Workflow } from 'n8n-workflow'

import NodeIcon from '#wf/components/NodeIcon.vue'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { INodeUi } from '#wf/types/interface'
import { isPresent } from '#wf/utils/typesUtils'

interface Props {
  nodes: IConnectedNode[]
  workflow: Workflow
}

const props = defineProps<Props>()

const modelValue = defineModel<string | null>()

const workflowStore = useWorkflowStore()
const nodeTypesStore = useNodeTypesStore()

const ndvStore = useNDVStore()
const { activeNode } = storeToRefs(ndvStore)

const selectedInputNode = computed(() => workflowStore.getNodeByName(modelValue.value ?? ''))

const selectedInputNodeType = computed(() => {
  const node = selectedInputNode.value

  if (!node) {
    return null
  }

  return nodeTypesStore.getNodeType(node.type, node.typeVersion)
})

const activeNodeType = computed(() => {
  const node = activeNode.value

  if (!node) {
    return null
  }

  return nodeTypesStore.getNodeType(node.type, node.typeVersion)
})

const isMultiInputNode = computed(() => {
  const nodeType = activeNodeType.value

  return nodeType !== null && nodeType.inputs.length > 1
})

const getMultipleNodesText = (nodeName: string): string => {
  if (
    !nodeName
    || !isMultiInputNode.value
    || !activeNode.value
    || !activeNodeType.value?.inputNames
  ) { return '' }

  const activeNodeConnections = props.workflow.connectionsByDestinationNode[activeNode.value.name].main || []
  // Collect indexes of connected nodes
  const connectedInputIndexes = activeNodeConnections.reduce((acc: number[], node, index) => {
    if (node?.[0] && node[0].node === nodeName) {
      return [...acc, index]
    }

    return acc
  }, [])

  // Match connected input indexes to their names specified by active node
  const connectedInputs = connectedInputIndexes.map(
    (inputIndex) => activeNodeType.value?.inputNames?.[inputIndex],
  )

  if (connectedInputs.length === 0) {
    return ''
  }

  return `(${connectedInputs.join(' & ')})`
}

interface InputNodeOption extends ProSelectOption {
  node: INodeUi
  type: INodeTypeDescription | null
  depth: number
}

const selectOptions = computed<InputNodeOption[]>(() =>
  props.nodes
    .map((node) => {
      const fullNode = workflowStore.getNodeByName(node.name)

      if (!fullNode) {
        return null
      }

      const nodeType = nodeTypesStore.getNodeType(fullNode.type, fullNode.typeVersion)

      return {
        label: `${fullNode.name} ${getMultipleNodesText(fullNode.name)}`,
        value: fullNode.name,
        node: fullNode,
        type: nodeType,
        depth: node.depth,
      }
    })
    .filter(isPresent),
)

const connectedTo = (nodeName: string) => {
  const connections = ndvStore.ndvNodeInputNumber[nodeName]

  if (!connections) {
    return ''
  }

  if (connections.length === 1) {
    return `Input ${ndvStore.ndvNodeInputNumber[nodeName]}`
  }

  return `Inputs ${ndvStore.ndvNodeInputNumber[nodeName].join(', ')}`
}

function subtitle(nodeName: string, depth: number) {
  const multipleNodesText = getMultipleNodesText(nodeName)

  if (multipleNodesText) {
    return multipleNodesText
  }

  return `(${depth} 个节点前)`
}

function handleInputNodeChange(value: string) {
  modelValue.value = value
}
</script>

<template>
  <Select
    data-testid="ndv-input-select"
    emptyFilterMessage="未找到节点"
    filter
    filterPlaceholder="搜索节点..."
    fluid
    :modelValue="modelValue"
    optionLabel="label"
    :options="selectOptions"
    optionValue="value"
    placeholder="选择父节点"
    :pt="{
      overlay: 'max-w-[300px]',
    }"
    size="small"
    @update:modelValue="handleInputNodeChange"
  >
    <!-- 自定义选中值显示 -->
    <template #value="{ value }">
      <div
        v-if="value"
        class="gap-2 flex-center"
      >
        <div class="shrink-0">
          <NodeIcon
            :disabled="selectedInputNode?.disabled"
            :nodeType="selectedInputNodeType"
            :shrink="false"
            :size="14"
          />
        </div>

        <div class="flex-1 truncate text-sm">
          {{ value }}
        </div>
      </div>
    </template>

    <template #option="{ option }: { option: InputNodeOption }">
      <div class="w-full gap-2 text-sm flex-center">
        <div class="min-w-0 flex-1 gap-2 flex-center">
          <div class="shrink-0 inline-flex-center">
            <NodeIcon
              :disabled="option.node.disabled"
              :nodeType="option.type"
              :size="14"
            />
          </div>

          <div class="min-w-0 flex-1 flex-center">
            <span class="truncate">
              {{ option.node.name }}
            </span>

            <span
              v-if="option.node.disabled"
              class="opacity-60"
            >
              （已停用）
            </span>
          </div>
        </div>

        <span class="ml-auto shrink-0 text-xs tabular-nums">
          {{
            connectedTo(option.node.name) ? connectedTo(option.node.name) : subtitle(option.node.name, option.depth)
          }}
        </span>
      </div>
    </template>
  </Select>
</template>
