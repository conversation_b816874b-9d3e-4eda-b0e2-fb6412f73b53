<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'

import { GripVerticalIcon, TriangleAlertIcon } from 'lucide-vue-next'
import type { GenericValue, IDataObject, INodeExecutionData } from 'n8n-workflow'

import DraggableBox from '#wf/components/DraggableBox.vue'
import TextWithHighlight from '#wf/components/VirtualSchema/TextWithHighlight.vue'
import { useExecutionHelpers } from '#wf/composables/useExecutionHelpers'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { INodeUi, IRunDataDisplayMode, ITableData } from '#wf/types/interface'
import { getMappedExpression } from '#wf/utils/mappingUtils'
import { getPairedItemId } from '#wf/utils/pairedItemUtils'

import RunDataTableTree from './RunDataTableTree.vue'

const MAX_COLUMNS_LIMIT = 40

type DraggableRef = InstanceType<typeof DraggableBox>

type Props = {
  node: INodeUi
  inputData: INodeExecutionData[]
  distanceFromActive: number
  pageOffset: number
  runIndex?: number
  outputIndex?: number
  totalRuns?: number
  mappingEnabled?: boolean
  hasDefaultHoverState?: boolean
  search?: string
}

const props = withDefaults(defineProps<Props>(), {
  runIndex: 0,
  outputIndex: 0,
  totalRuns: 0,
  mappingEnabled: false,
  hasDefaultHoverState: false,
  search: '',
})
const emit = defineEmits<{
  activeRowChanged: [row: number | null]
  displayModeChange: [mode: IRunDataDisplayMode]
  mounted: [data: { avgRowHeight: number }]
}>()

const activeColumn = ref(-1)
const forceShowGrip = ref(false)
const draggedColumn = ref(false)
const draggingPath = ref<string | null>(null)
const hoveringPath = ref<string | null>(null)
const activeRow = ref<number | null>(null)
const columnLimit = ref(MAX_COLUMNS_LIMIT)
const columnLimitExceeded = ref(false)
const draggableRef = ref<DraggableRef>()

const ndvStore = useNDVStore()
const workflowsStore = useWorkflowStore()

const { resolveRelatedExecutionUrl } = useExecutionHelpers()

const {
  hoveringItem,
  focusedMappableInput,
  highlightDraggables: highlight,
} = storeToRefs(ndvStore)
const pairedItemMappings = computed(() => workflowsStore.workflowExecutionPairedItemMappings)

function convertToTable(inputData: INodeExecutionData[]): ITableData {
  const resultTableData: GenericValue[][] = []
  const tableColumns: string[] = []
  let leftEntryColumns: string[], entryRows: GenericValue[]
  // Go over all entries
  let entry: IDataObject

  const metadata: ITableData['metadata'] = {
    hasExecutionIds: false,
    data: [],
  }
  const hasJson: { [key: string]: boolean } = {}
  inputData.forEach((data) => {
    if (!Object.hasOwn(data, 'json')) {
      return
    }

    entry = data.json

    // Go over all keys of entry
    entryRows = []
    const entryColumns = Object.keys(entry || {})

    if (entryColumns.length > MAX_COLUMNS_LIMIT) {
      columnLimitExceeded.value = true
      leftEntryColumns = entryColumns.slice(0, MAX_COLUMNS_LIMIT)
    }
    else {
      leftEntryColumns = entryColumns
    }

    if (data.metadata?.subExecution) {
      metadata.data.push(data.metadata)
      metadata.hasExecutionIds = true
    }
    else {
      metadata.data.push(undefined)
    }

    // Go over all the already existing column-keys
    tableColumns.forEach((key) => {
      if (Object.hasOwn(entry, key)) {
        // Entry does have key so add its value
        entryRows.push(entry[key])
        // Remove key so that we know that it got added
        leftEntryColumns.splice(leftEntryColumns.indexOf(key), 1)

        hasJson[key] = hasJson[key]
          || (typeof entry[key] === 'object' && Object.keys(entry[key] ?? {}).length > 0)
          || false
      }
      else {
        // Entry does not have key so add undefined
        entryRows.push(undefined)
      }
    })

    // Go over all the columns the entry has but did not exist yet
    leftEntryColumns.forEach((key) => {
      // Add the key for all runs in the future
      tableColumns.push(key)
      // Add the value
      entryRows.push(entry[key])
      hasJson[key] = hasJson[key]
        || (typeof entry[key] === 'object' && Object.keys(entry[key] ?? {}).length > 0)
        || false
    })

    // Add the data of the entry
    resultTableData.push(entryRows)
  })

  // Make sure that all entry-rows have the same length
  resultTableData.forEach((rows) => {
    if (tableColumns.length > rows.length) {
      // Has fewer entries so add the missing ones
      rows.push(...new Array(tableColumns.length - rows.length))
    }
  })

  return {
    hasJson,
    columns: tableColumns,
    data: resultTableData,
    metadata,
  }
}

const tableData = computed(() => convertToTable(props.inputData))

onMounted(() => {
  if (tableData.value?.columns && draggableRef.value) {
    const tbody = draggableRef.value.$refs.wrapper as HTMLElement

    if (tbody) {
      emit('mounted', {
        avgRowHeight: tbody.offsetHeight / tableData.value.data.length,
      })
    }
  }
})

function isHoveringRow(row: number): boolean {
  if (row === activeRow.value) {
    return true
  }

  const itemIndex = props.pageOffset + row

  if (
    itemIndex === 0
    && !hoveringItem.value
    && props.hasDefaultHoverState
    && props.distanceFromActive === 1
  ) {
    return true
  }

  const itemNodeId = getPairedItemId(
    props.node?.name ?? '',
    props.runIndex || 0,
    props.outputIndex || 0,
    itemIndex,
  )

  if (!hoveringItem.value || !pairedItemMappings.value[itemNodeId]) {
    return false
  }

  const hoveringItemId = getPairedItemId(
    hoveringItem.value.nodeName,
    hoveringItem.value.runIndex,
    hoveringItem.value.outputIndex,
    hoveringItem.value.itemIndex,
  )

  return pairedItemMappings.value[itemNodeId].has(hoveringItemId)
}

function showExecutionLink(index: number) {
  if (index === activeRow.value) {
    return true
  }

  if (activeRow.value === null) {
    return index === 0
  }

  return false
}

function onMouseEnterCell(e: MouseEvent) {
  const target = e.target

  if (target && props.mappingEnabled) {
    const col = (target as HTMLElement).dataset.col

    if (col && !isNaN(parseInt(col, 10))) {
      activeColumn.value = parseInt(col, 10)
    }
  }

  if (target) {
    const row = (target as HTMLElement).dataset.row

    if (row && !isNaN(parseInt(row, 10))) {
      activeRow.value = parseInt(row, 10)
      emit('activeRowChanged', props.pageOffset + activeRow.value)
    }
  }
}

function onMouseLeaveCell() {
  activeColumn.value = -1
  activeRow.value = null
  emit('activeRowChanged', null)
}

function getCellExpression(path: Array<string | number>, colIndex: number) {
  if (!props.node) {
    return ''
  }

  const column = tableData.value.columns[colIndex]

  return getMappedExpression({
    nodeName: props.node.name,
    distanceFromActive: props.distanceFromActive,
    path: [column, ...path],
  })
}

function onMouseEnterKey(path: Array<string | number>, colIndex: number) {
  hoveringPath.value = getCellExpression(path, colIndex)
}

function onMouseLeaveKey() {
  hoveringPath.value = null
}

function isHovering(path: Array<string | number>, colIndex: number) {
  const expr = getCellExpression(path, colIndex)

  return hoveringPath.value === expr
}

function getExpression(column: string) {
  if (!props.node) {
    return ''
  }

  return getMappedExpression({
    nodeName: props.node.name,
    distanceFromActive: props.distanceFromActive,
    path: [column],
  })
}

function getPathNameFromTarget(el?: HTMLElement) {
  if (!el) {
    return ''
  }

  return el.dataset.name
}

function getCellPathName(path: Array<string | number>, colIndex: number) {
  const lastKey = path[path.length - 1]

  if (typeof lastKey === 'string') {
    return lastKey
  }

  if (path.length > 1) {
    const prevKey = path[path.length - 2]

    return `${prevKey}[${lastKey}]`
  }

  const column = tableData.value.columns[colIndex]

  return `${column}[${lastKey}]`
}

function isEmpty(value: unknown): boolean {
  return (
    value === ''
    || (Array.isArray(value) && value.length === 0)
    || (typeof value === 'object' && value !== null && Object.keys(value).length === 0)
    || value === null
    || value === undefined
  )
}

function getValueToRender(value: unknown): string {
  if (value === '') {
    return '[空]'
  }

  if (typeof value === 'string') {
    return value
  }

  if (Array.isArray(value) && value.length === 0) {
    return '[空数组]'
  }

  if (typeof value === 'object' && value !== null && Object.keys(value).length === 0) {
    return '[空对象]'
  }

  if (value === null || value === undefined) {
    return `[${value}]`
  }

  if (value === true || value === false || typeof value === 'number') {
    return value.toString()
  }

  return JSON.stringify(value)
}

function onDragStart() {
  draggedColumn.value = true
  // ndvStore.resetMappingTelemetry()
}

function onCellDragStart(el: HTMLElement) {
  if (el?.dataset.value) {
    draggingPath.value = el.dataset.value
  }

  onDragStart()
}

function onDragEnd(column: string, src: string, depth = '0') {
  setTimeout(() => {
    // void externalHooks.run('runDataTable.onDragEnd', telemetryPayload)
  }, 1000) // ensure dest data gets set if drop
}

function onCellDragEnd(el: HTMLElement) {
  draggingPath.value = null

  onDragEnd(el.dataset.name ?? '', 'tree', el.dataset.depth ?? '0')
}

function isDraggingKey(path: Array<string | number>, colIndex: number) {
  if (!draggingPath.value) {
    return
  }

  return draggingPath.value === getCellExpression(path, colIndex)
}

function isSimple(data: GenericValue): data is string | number | boolean | null | undefined {
  return (
    typeof data !== 'object'
    || data === null
    || (Array.isArray(data) && data.length === 0)
    || (typeof data === 'object' && Object.keys(data).length === 0)
  )
}

function isObject(data: GenericValue): data is Record<string, unknown> {
  return !isSimple(data)
}

function hasJsonInColumn(colIndex: number): boolean {
  return tableData.value.hasJson[tableData.value.columns[colIndex]]
}

function switchToJsonView() {
  emit('displayModeChange', 'json')
}

watch(focusedMappableInput, (curr) => {
  setTimeout(
    () => {
      forceShowGrip.value = !!focusedMappableInput.value
    },
    curr ? 300 : 150,
  )
})
</script>

<template>
  <div
    class="absolute inset-x-0 top-0 h-full overflow-y-auto pb-12 pl-card-container"
    :class="[{ 'text-app-accent-500': highlight }]"
    data-component-name="数据表格视图"
  >
    <table
      v-if="tableData.columns.length === 0"
      class="w-full border-collapse text-left text-sm"
    >
      <thead>
        <tr>
          <!-- 空列，用于执行链接 -->
          <th
            v-if="tableData.metadata.hasExecutionIds"
            class="sticky top-0 z-10 w-6 border-y border-l"
          />

          <th class="sticky top-0 z-10 h-8 border-y border-l" />

          <th class="border-y-0 border-l pl-card-container" />
        </tr>
      </thead>

      <tbody>
        <tr
          v-for="(_, index1) of tableData.data"
          :key="index1"
          class="group"
          :class="[{ 'hovering-row': isHoveringRow(index1) }]"
        >
          <td
            v-if="tableData.metadata.hasExecutionIds"
            class="p-1"
            :data-row="index1"
            @mouseenter="onMouseEnterCell"
            @mouseleave="onMouseLeaveCell"
          >
            <Button
              v-if="tableData.metadata.data[index1]"
              v-show="showExecutionLink(index1)"
              v-tooltip.left="`查看子执行 ${tableData.metadata.data[index1]?.subExecution.executionId}`"
              as="a"
              data-testid="debug-sub-execution"
              :href="resolveRelatedExecutionUrl(tableData.metadata.data[index1])"
              mini
              rel="noopener"
              size="small"
              target="_blank"
            >
              <template #icon>
                <TriangleAlertIcon
                  class="text-warning-500"
                  :size="14"
                />
              </template>
            </Button>
          </td>

          <td
            class="relative z-0 border-x border-b border-divider p-1 align-top"
            :data-col="0"
            :data-row="index1"
            @mouseenter="onMouseEnterCell"
            @mouseleave="onMouseLeaveCell"
          >
            这是一个项目，但它是空的。
          </td>

          <td class="border-y-0 border-r-0 pl-card-container" />
        </tr>
      </tbody>
    </table>

    <table
      v-else
      class="w-full border-collapse text-left text-sm"
    >
      <thead>
        <tr>
          <!-- 空列，用于执行链接 -->
          <th
            v-if="tableData.metadata.hasExecutionIds"
            class="sticky top-0 z-10 w-6 border-y border-l bg-content"
          />

          <th
            v-for="(column, i) of tableData.columns || []"
            :key="column"
            class="sticky top-0 z-10 border-y border-l border-divider bg-content p-1"
          >
            <ProPrimePopover
              :disabled="!mappingEnabled"
            >
              <template #content>
                <div>
                  <!-- <img src="/static/data-mapping-gif.gif"> -->
                  将列拖到字段上以将列映射到该字段
                </div>
              </template>

              <DraggableBox
                :data="getExpression(column)"
                :disabled="!mappingEnabled"
                type="mapping"
                @dragend="(column: HTMLElement) => onDragEnd(column?.textContent ?? '', 'column')"
                @dragstart="onDragStart"
              >
                <template #preview="{ canDrop }">
                  <!-- <MappingPill
                    :canDrop="canDrop"
                    :html="shorten(column, 16, 2)"
                  /> -->
                </template>

                <template #default="{ isDragging }">
                  <div
                    class="flex-center"
                    :class="[
                      {
                        'cursor-grab hover:bg-gray-100': mappingEnabled,
                        'text-app-accent-500': (i === activeColumn || forceShowGrip) && mappingEnabled,
                        'bg-app-accent-100 text-app-accent-500': isDragging,
                      },
                    ]"
                  >
                    <TextWithHighlight
                      :content="getValueToRender(column || '')"
                      :search="search"
                    />
                    <div
                      class="ml-1 opacity-0"
                      :class="[{ 'opacity-100': (i === activeColumn || forceShowGrip) && mappingEnabled }]"
                    >
                      <GripVerticalIcon :size="14" />
                    </div>
                  </div>
                </template>
              </DraggableBox>
            </ProPrimePopover>
          </th>

          <th
            v-if="columnLimitExceeded"
            class="sticky top-0 z-10 border-y border-l border-divider py-1 flex-center"
          >
            <span
              v-tooltip.left="{
                value: `您的数据有超过 ${columnLimit} 列，因此一些列被隐藏。切换到某些列已隐藏查看所有数据。`,
              }"
            >
              <TriangleAlertIcon
                class="text-warning-500"
                :size="14"
              />
              某些列已隐藏
            </span>
          </th>

          <!-- 空列，用于占位 -->
          <th class="border-y-0 border-l border-r-0 pl-card-container" />
        </tr>
      </thead>

      <DraggableBox
        ref="draggableRef"
        :disabled="!mappingEnabled"
        tag="tbody"
        target-data-key="mappable"
        type="mapping"
        @dragend="onCellDragEnd"
        @dragstart="onCellDragStart"
      >
        <template #preview="{ canDrop, el }">
          <!-- <MappingPill
            :canDrop="canDrop"
            :html="shorten(getPathNameFromTarget(el) || '', 16, 2)"
          /> -->
        </template>

        <tr
          v-for="(row, index1) of tableData.data"
          :key="index1"
          class="group"
          :class="[{ 'hovering-row': isHoveringRow(index1) }]"
          :data-testid="isHoveringRow(index1) ? 'hovering-item' : undefined"
        >
          <td
            v-if="tableData.metadata.hasExecutionIds"
            class="p-1"
            :data-row="index1"
            @mouseenter="onMouseEnterCell"
            @mouseleave="onMouseLeaveCell"
          >
            <Button
              v-if="tableData.metadata.data[index1]"
              v-show="showExecutionLink(index1)"
              v-tooltip.left="`查看子执行 ${tableData.metadata.data[index1]?.subExecution.executionId}`"
              as="a"
              data-testid="debug-sub-execution"
              :href="resolveRelatedExecutionUrl(tableData.metadata.data[index1])"
              mini
              rel="noopener"
              size="small"
              target="_blank"
            >
              <template #icon>
                <TriangleAlertIcon
                  class="text-warning-500"
                  :size="14"
                />
              </template>
            </Button>
          </td>

          <td
            v-for="(data, index2) of row"
            :key="index2"
            class="relative z-0 break-words border-b border-l border-divider p-1 align-top"
            :class="[
              hasJsonInColumn(index2) ? 'min-w-60' : 'max-w-60',
            ]"
            :data-col="index2"
            :data-row="index1"
            @mouseenter="onMouseEnterCell"
            @mouseleave="onMouseLeaveCell"
          >
            <TextWithHighlight
              v-if="isSimple(data)"
              class="leading-normal"
              :class="[
                { 'text-danger-500': isEmpty(data) },
              ]"
              :content="getValueToRender(data)"
              :search="search"
            />

            <RunDataTableTree
              v-else-if="isObject(data)"
              class="mb-1"
              :value="data"
            >
              <template #label="{ label, path }">
                <span
                  class="mr-1 rounded px-1 font-bold leading-7 text-gray-800"
                  :class="[
                    {
                      'bg-gray-100': mappingEnabled && isHovering(path, index2),
                      'bg-app-accent-100': isDraggingKey(path, index2),
                      'cursor-grab': mappingEnabled,
                    },
                  ]"
                  :data-depth="path.length"
                  :data-name="getCellPathName(path, index2)"
                  data-target="mappable"
                  :data-value="getCellExpression(path, index2)"
                  @mouseenter="() => onMouseEnterKey(path, index2)"
                  @mouseleave="onMouseLeaveKey"
                >{{ label || '[未命名字段]' }}</span>
              </template>

              <template #value="{ value }">
                <TextWithHighlight
                  class="ml-1 leading-normal"
                  :class="[
                    { 'text-danger-500': isEmpty(value) },
                  ]"
                  :content="getValueToRender(value)"
                  :search="search"
                />
              </template>
            </RunDataTableTree>
          </td>

          <td
            v-if="columnLimitExceeded"
            class="border-b border-l border-divider"
          />

          <td class="border-y-0 border-l border-r-0 border-divider pl-card-container" />
        </tr>
      </DraggableBox>
    </table>
  </div>
</template>

<style scoped>
/* 处理 hovering-row 的复杂伪元素效果，这些无法用 TailwindCSS 直接表达 */
.hovering-row td:first-child::after,
.hovering-row td:nth-last-child(2)::after {
  content: '';
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: rgb(156 163 175); /* gray-400 */
}

.hovering-row td:nth-last-child(2)::after {
  right: -1px;
}

.hovering-row td:first-child::after {
  left: -1px;
}

/* 处理 mappingEnabled 时的 hover 效果 */
.cursor-grab:hover .opacity-0 {
  opacity: 1;
}
</style>
