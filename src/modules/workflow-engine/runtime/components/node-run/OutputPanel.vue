<script setup lang="ts">
import { DotIcon } from 'lucide-vue-next'
import type {
  IRunData,
  IRunExecutionData,
  Workflow,
} from 'n8n-workflow'

import RunData from '#wf/components/node-run/RunData.vue'
import RunInfo from '#wf/components/RunInfo.vue'
import { useNodeType } from '#wf/composables/useNodeType'
import { usePinnedData } from '#wf/composables/usePinnedData'
import { NodeConnectionType } from '#wf/constants/canvas'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import { waitingNodeTooltip } from '#wf/utils/executionUtils'

import { GlobalEvent } from '~/enums/event'

type RunDataRef = InstanceType<typeof RunData>

const OUTPUT_TYPE = {
  REGULAR: 'regular',
  LOGS: 'logs',
} as const

type OutputTypeKey = keyof typeof OUTPUT_TYPE

type OutputType = (typeof OUTPUT_TYPE)[OutputTypeKey]

type Props = {
  workflow: Workflow
  runIndex: number
  isReadOnly?: boolean
  linkedRuns?: boolean
  canLinkRuns?: boolean
  pushRef: string
  blockUI?: boolean
  isProductionExecutionPreview?: boolean
  isPaneActive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  blockUI: false,
  isProductionExecutionPreview: false,
  isPaneActive: false,
})

const emit = defineEmits<{
  linkRun: []
  unlinkRun: []
  runChange: [number]
  // activatePane: []
  // tableMounted: [{ avgRowHeight: number }]
  // itemHover: [item: { itemIndex: number, outputIndex: number } | null]
  search: [string]
  openSettings: []
}>()

// Stores

const nodeTypesStore = useNodeTypesStore()
const workflowsStore = useWorkflowStore()
const uiStore = useUIStore()

const ndvStore = useNDVStore()
const { activeNode } = storeToRefs(ndvStore)

// Composables

const { isSubNodeType } = useNodeType({
  node: activeNode,
})
const pinnedData = usePinnedData(activeNode, {
  runIndex: props.runIndex,
  displayMode: ndvStore.outputPanelDisplayMode,
})

// Data

const outputMode = ref<OutputType>(OUTPUT_TYPE.REGULAR)

const outputTypes = ref([
  { label: '输出', value: OUTPUT_TYPE.REGULAR },
  { label: '日志', value: OUTPUT_TYPE.LOGS },
])

const runDataRef = ref<RunDataRef>()

// Computed

const node = computed(() => {
  return ndvStore.activeNode ?? undefined
})

const isTriggerNode = computed(() => {
  return !!node.value && nodeTypesStore.isTriggerNode(node.value.type)
})

const workflowRunning = computed(() => uiStore.isActionActive.workflowRunning)

const workflowExecution = computed(() => {
  return workflowsStore.getWorkflowExecution
})

const workflowRunData = computed(() => {
  if (workflowExecution.value === null) {
    return null
  }

  const executionData: IRunExecutionData | undefined = workflowExecution.value.data

  if (!executionData?.resultData?.runData) {
    return null
  }

  return executionData.resultData.runData
})

const isNodeRunning = computed(() => {
  return workflowRunning.value && !!node.value && workflowsStore.isNodeExecuting(node.value.name)
})

const hasAiMetadata = computed(() => {
  if (isNodeRunning.value || !workflowRunData.value) {
    return false
  }

  if (node.value) {
    const connectedSubNodes = props.workflow.getParentNodes(node.value.name, 'ALL_NON_MAIN')
    const resultData = connectedSubNodes.map(workflowsStore.getWorkflowResultDataByNodeName)

    return resultData && Array.isArray(resultData) && resultData.length > 0
  }

  return false
})

const hasError = computed(() =>
  Boolean(
    workflowRunData.value
    && node.value
    && workflowRunData.value[node.value.name]?.[props.runIndex]?.error,
  ),
)

// Determine the initial output mode to logs if the node has an error and the logs are available
const defaultOutputMode = computed<OutputType>(() => {
  return hasError.value && hasAiMetadata.value ? OUTPUT_TYPE.LOGS : OUTPUT_TYPE.REGULAR
})

const hasNodeRun = computed(() => {
  if (workflowsStore.subWorkflowExecutionError) {
    return true
  }

  return Boolean(
    node.value && workflowRunData.value && Object.hasOwn(workflowRunData.value, node.value.name),
  )
})

const runTaskData = computed(() => {
  if (!node.value || workflowExecution.value === null) {
    return null
  }

  const runData = workflowRunData.value

  if (runData === null || !Object.hasOwn(runData, node.value.name)) {
    return null
  }

  if (runData[node.value.name].length <= props.runIndex) {
    return null
  }

  return runData[node.value.name][props.runIndex]
})

const runsCount = computed(() => {
  if (node.value === null) {
    return 0
  }

  const runData: IRunData | null = workflowRunData.value

  if (runData === null || (node.value && !Object.hasOwn(runData, node.value.name))) {
    return 0
  }

  if (node.value && runData[node.value.name].length) {
    return runData[node.value.name].length
  }

  return 0
})

const staleData = computed(() => {
  if (!node.value) {
    return false
  }

  const updatedAt = workflowsStore.getParametersLastUpdate(node.value.name)

  if (!updatedAt || !runTaskData.value) {
    return false
  }

  const runAt = runTaskData.value.startTime

  return updatedAt > runAt
})

const outputPanelEditMode = computed(() => {
  return ndvStore.outputPanelEditMode
})

const canPinData = computed(() => {
  return pinnedData.isValidNodeType.value && !props.isReadOnly
})

const allToolsWereUnusedNotice = computed(() => {
  if (!node.value || runsCount.value === 0 || hasError.value) {
    return undefined
  }

  // With pinned data there's no clear correct answer for whether
  // we should use historic or current parents, so we don't show the notice,
  // as it likely ends up unactionable noise to the user
  if (pinnedData.hasData.value) {
    return undefined
  }

  const toolsAvailable = props.workflow.getParentNodes(
    node.value.name,
    NodeConnectionType.AiTool as UnsafeAny,
    1,
  )
  const toolsUsedInLatestRun = toolsAvailable.filter(
    (tool) => !!workflowRunData.value?.[tool]?.[props.runIndex],
  )

  if (toolsAvailable.length > 0 && toolsUsedInLatestRun.length === 0) {
    return '此运行中未使用任何工具。尝试为您的工具提供更清晰的名字和描述，以帮助 AI'
  }
  else {
    return undefined
  }
})

// Methods

const insertTestData = () => {
  if (!runDataRef.value) {
    return
  }

  // We should be able to fix this when we migrate RunData.vue to composition API

  runDataRef.value.enterEditMode({
    origin: 'insertTestDataLink',
  })
}

const onLinkRun = () => {
  emit('linkRun')
}

const onUnlinkRun = () => {
  emit('unlinkRun')
}

const openSettings = () => {
  emit('openSettings')
}

const onRunIndexChange = (run: number) => {
  emit('runChange', run)
}

const handleUpdateOutputMode = (newOutputMode: OutputType) => {
  if (newOutputMode === OUTPUT_TYPE.LOGS) {
    // ndvEventBus.emit('setPositionByName', 'minLeft')
  }
  else {
    // ndvEventBus.emit('setPositionByName', 'initial')
  }
}

// Set the initial output mode when the component is mounted
onMounted(() => {
  outputMode.value = defaultOutputMode.value
})

// In case the output panel was opened when the node has not run yet,
// defaultOutputMode will be "regular" at the time of mounting.
// This is why we need to watch the defaultOutputMode and change the outputMode to "logs" if the node has run and criteria are met.
watch(defaultOutputMode, (newValue: OutputType, oldValue: OutputType) => {
  if (newValue === OUTPUT_TYPE.LOGS && oldValue === OUTPUT_TYPE.REGULAR && hasNodeRun.value) {
    outputMode.value = defaultOutputMode.value
  }
})

const handleRunNode = () => {
  if (activeNode.value) {
    emitter.emit(GlobalEvent.NodeRun, activeNode.value.id)
  }
}
</script>

<template>
  <RunData
    ref="runDataRef"
    :blockUI="blockUI"
    :calloutMessage="allToolsWereUnusedNotice"
    :canLinkRuns="canLinkRuns"
    :data-output-type="outputMode"
    executingMessage="正在执行节点..."
    :hidePagination="outputMode === 'logs'"
    :isExecuting="isNodeRunning"
    :isPaneActive="isPaneActive"
    :isProductionExecutionPreview="isProductionExecutionPreview"
    :linkedRuns="linkedRuns"
    noDataInBranchMessage="此分支中没有输出数据"
    :node="node"
    paneType="output"
    :pushRef="pushRef"
    :runIndex="runIndex"
    tooMuchDataTitle="输出数据过大"
    :workflow="workflow"
    @linkRun="onLinkRun"
    @runChange="onRunIndexChange"
    @search="emit('search', $event)"
    @unlinkRun="onUnlinkRun"
  >
    <template #header>
      <div class="gap-1 flex-center">
        <SelectButton
          v-if="hasAiMetadata"
          data-testid="ai-output-mode-select"
          :modelValue="outputMode"
          optionLabel="label"
          :options="outputTypes"
          optionValue="value"
          size="small"
          @update:modelValue="handleUpdateOutputMode"
        />

        <span
          v-else
          class="shrink-0 font-bold text-secondary"
        >
          {{ outputPanelEditMode.enabled ? '编辑输出' : '输出' }}
        </span>

        <div class="shrink-0 inline-flex-center">
          <RunInfo
            v-if="hasNodeRun && !pinnedData.hasData.value && runsCount === 1"
            v-show="!outputPanelEditMode.enabled"
            :hasPinData="pinnedData.hasData.value"
            :hasStaleData="staleData"
            :taskData="runTaskData"
          />
        </div>
      </div>
    </template>

    <template #node-not-run>
      <div
        v-if="workflowRunning && !isTriggerNode"
        class="p-8 text-center text-lg font-bold text-secondary"
        data-testid="ndv-output-waiting"
      >
        等待执行...
      </div>

      <div
        v-if="!workflowRunning"
        class="p-card-container"
        data-testid="ndv-output-run-node-hint"
      >
        <div
          v-if="isSubNodeType"
          class="py-5 text-center text-sm text-secondary"
        >
          等待父节点执行完成后，你将在这里看到输出结果。
        </div>

        <div v-else>
          <div class="py-5 text-sm text-secondary">
            当前节点尚未运行，无法显示输出数据。你可以：
          </div>

          <ul class="space-y-2 text-sm">
            <li class="flex-center">
              <DotIcon :size="16" />
              执行节点获取真实输出：
              <ProBtn
                :disabled="workflowRunning"
                label="调试此节点"
                mini
                size="small"
                variant="outlined"
                @click="handleRunNode()"
              />
            </li>
            <li
              v-if="canPinData"
              class="flex-center"
            >
              <DotIcon :size="16" />
              插入模拟数据进行调试：
              <ProBtn
                label="插入模拟数据"
                mini
                size="small"
                variant="outlined"
                @click="insertTestData()"
              />
            </li>
          </ul>
        </div>
      </div>
    </template>

    <template #node-waiting>
      <div class="flex flex-col items-center gap-2">
        <span class="text-lg font-bold">
          等待输入
        </span>
        <span
          class="text-sm text-secondary"
          v-html="waitingNodeTooltip(node)"
        />
      </div>
    </template>

    <template #no-output-data>
      <div class="flex flex-col items-center gap-2">
        <span class="text-lg font-bold">
          未返回输出数据
        </span>
        <span class="text-sm text-secondary">
          当节点没有输出数据时，n8n 将停止执行工作流。你可以通过
          <a @click="openSettings">设置</a>
          > “始终输出数据”
        </span>
      </div>
    </template>

    <template
      v-if="outputMode === 'logs' && node"
      #content
    >
      <!-- <RunDataAi
        :node="node"
        :runIndex="runIndex"
        :workflow="workflow"
      /> -->
    </template>

    <template #recovered-artificial-output-data>
      <div>
        <div class="text-lg font-bold">
          无法显示数据
        </div>
        <span>
          执行被中断，因此数据未保存。尝试修复工作流并重新执行。
        </span>
      </div>
    </template>

    <template
      v-if="!pinnedData.hasData.value && runsCount > 1"
      #run-info
    >
      <RunInfo :taskData="runTaskData" />
    </template>
  </RunData>
</template>
