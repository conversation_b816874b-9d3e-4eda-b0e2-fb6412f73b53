<script setup lang="ts">
import { uniqBy } from 'lodash-es'
import { CircleHelpIcon } from 'lucide-vue-next'
import type { INodeInputConfiguration, INodeOutputConfiguration, Workflow } from 'n8n-workflow'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'

import InputNodeSelect from '#wf/components/node-run/InputNodeSelect.vue'
import RunData from '#wf/components/node-run/RunData.vue'
import WireMeUp from '#wf/components/node-run/WireMeUp.vue'
import NodeExecuteButton from '#wf/components/NodeExecuteButton.vue'
import { NodeConnectionType } from '#wf/constants/canvas'
import {
  CRON_NODE_TYPE,
  INTERVAL_NODE_TYPE,
  MANUAL_TRIGGER_NODE_TYPE,
  START_NODE_TYPE,
} from '#wf/constants/common'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import { waitingNodeTooltip } from '#wf/utils/executionUtils'

type MappingMode = 'debugging' | 'mapping'

export type Props = {
  runIndex: number
  workflow: Workflow
  pushRef: string
  currentNodeName?: string
  canLinkRuns?: boolean
  linkedRuns?: boolean
  readOnly?: boolean
  isProductionExecutionPreview?: boolean
  isPaneActive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  currentNodeName: '',
  canLinkRuns: false,
  readOnly: false,
  isProductionExecutionPreview: false,
  isPaneActive: false,
})

const emit = defineEmits<{
  // itemHover: [
  //   {
  //     outputIndex: number
  //     itemIndex: number
  //   } | null,
  // ]
  // tableMounted: [
  //   {
  //     avgRowHeight: number
  //   },
  // ]
  linkRun: []
  unlinkRun: []
  runChange: [runIndex: number]
  search: [search: string]
  changeInputNode: [nodeName: string, index: number]
  execute: []
  // activatePane: []
}>()

const showDraggableHintWithDelay = ref(false)
const draggableHintShown = ref(false)

const mappedNode = ref<string | null>(null)
const inputModes = [
  { value: 'mapping', label: '映射' },
  { value: 'debugging', label: '调试' },
]

const nodeTypesStore = useNodeTypesStore()
const ndvStore = useNDVStore()
const workflowsStore = useWorkflowStore()
const uiStore = useUIStore()

const {
  activeNode,
  focusedMappableInput,
  isMappingOnboarded: isUserOnboarded,
} = storeToRefs(ndvStore)

const rootNode = computed(() => {
  if (!activeNode.value) {
    return null
  }

  return props.workflow.getChildNodes(activeNode.value.name, 'ALL').at(0) ?? null
})

const hasRootNodeRun = computed(() => {
  return !!(
    rootNode.value && workflowsStore.getWorkflowExecution?.data?.resultData.runData[rootNode.value]
  )
})

const inputMode = ref<MappingMode>(
  // Show debugging mode by default only when the node has already run
  activeNode.value
  && workflowsStore.getWorkflowExecution?.data?.resultData.runData[activeNode.value.name]
    ? 'debugging'
    : 'mapping',
)

function filterOutConnectionType(
  item: NodeConnectionType | INodeOutputConfiguration | INodeInputConfiguration,
  type: NodeConnectionType,
) {
  if (!item) {
    return false
  }

  return typeof item === 'string' ? item !== type : item.type !== (type as UnsafeAny)
}

const activeNodeType = computed(() => {
  if (!activeNode.value) {
    return null
  }

  return nodeTypesStore.getNodeType(activeNode.value.type, activeNode.value.typeVersion)
})

const isActiveNodeConfig = computed(() => {
  let inputs = activeNodeType.value?.inputs ?? []
  let outputs = activeNodeType.value?.outputs ?? []

  if (props.workflow && activeNode.value) {
    const node = props.workflow.getNode(activeNode.value.name)

    if (node && activeNodeType.value) {
      inputs = NodeHelpers.getNodeInputs(props.workflow, node, activeNodeType.value)
      outputs = NodeHelpers.getNodeOutputs(props.workflow, node, activeNodeType.value)
    }
  }

  // If we can not figure out the node type we set no outputs
  if (!Array.isArray(inputs)) {
    inputs = []
  }

  if (!Array.isArray(outputs)) {
    outputs = []
  }

  return (
    inputs.length === 0
    || (inputs.every((input) => filterOutConnectionType(input as UnsafeAny, NodeConnectionType.Main))
      && outputs.find((output) => filterOutConnectionType(output as UnsafeAny, NodeConnectionType.Main)))
  )
})

const isMappingMode = computed(() => isActiveNodeConfig.value && inputMode.value === 'mapping')

const currentNode = computed(() => {
  if (isActiveNodeConfig.value) {
    // if we're mapping node we want to show the output of the mapped node
    if (mappedNode.value) {
      return workflowsStore.getNodeByName(mappedNode.value)
    }

    // in debugging mode data does get set manually and is only for debugging
    // so we want to force the node to be the active node to make sure we show the correct data
    return activeNode.value
  }

  return workflowsStore.getNodeByName(props.currentNodeName ?? '')
})

const showDraggableHint = computed(() => {
  const toIgnore = [START_NODE_TYPE, MANUAL_TRIGGER_NODE_TYPE, CRON_NODE_TYPE, INTERVAL_NODE_TYPE]

  if (!currentNode.value || toIgnore.includes(currentNode.value.type)) {
    return false
  }

  return !!focusedMappableInput.value && !isUserOnboarded.value
})

const isMappingEnabled = computed(() => {
  if (props.readOnly) {
    return false
  }

  // Mapping is only enabled in mapping mode for config nodes and if node to map is selected
  if (isActiveNodeConfig.value) {
    return isMappingMode.value && mappedNode.value !== null
  }

  return true
})

const workflowRunning = computed(() => uiStore.isActionActive.workflowRunning)

const parentNodes = computed(() => {
  if (!activeNode.value) {
    return []
  }

  const parents = props.workflow
    .getParentNodesByDepth(activeNode.value.name)
    .filter((parent) => parent.name !== activeNode.value?.name)

  return uniqBy(parents, (parent) => parent.name)
})

const isExecutingPrevious = computed(() => {
  if (!workflowRunning.value) {
    return false
  }

  const triggeredNode = workflowsStore.executedNode
  const executingNode = workflowsStore.executingNode

  if (
    activeNode.value
    && triggeredNode === activeNode.value.name
    && workflowsStore.isNodeExecuting(props.currentNodeName)
  ) {
    return true
  }

  if (executingNode.length || triggeredNode) {
    return !!parentNodes.value.find(
      (node) => workflowsStore.isNodeExecuting(node.name) || node.name === triggeredNode,
    )
  }

  return false
})

const rootNodesParents = computed(() => {
  if (!rootNode.value) {
    return []
  }

  return props.workflow.getParentNodesByDepth(rootNode.value)
})

const connectedCurrentNodeOutputs = computed(() => {
  const search = parentNodes.value.find(({ name }) => name === props.currentNodeName)

  return search?.indicies
})

const currentNodeDepth = computed(() => {
  const node = parentNodes.value.find(
    (parent) => currentNode.value && parent.name === currentNode.value.name,
  )

  return node?.depth ?? -1
})

const waitingMessage = computed(() => {
  const parentNode = parentNodes.value[0]

  return parentNode && waitingNodeTooltip(workflowsStore.getNodeByName(parentNode.name))
})

function onLinkRun() {
  emit('linkRun')
}

function onUnlinkRun() {
  emit('unlinkRun')
}

function onRunIndexChange(run: number) {
  emit('runChange', run)
}

watch(
  inputMode,
  (mode) => {
    onRunIndexChange(-1)

    if (mode === 'mapping') {
      onUnlinkRun()
      mappedNode.value = rootNodesParents.value[0]?.name ?? null
    }
    else {
      mappedNode.value = null
    }
  },
  { immediate: true },
)

watch(showDraggableHint, (curr, prev) => {
  if (curr && !prev) {
    setTimeout(() => {
      if (draggableHintShown.value) {
        return
      }

      showDraggableHintWithDelay.value = showDraggableHint.value

      if (showDraggableHintWithDelay.value) {
        draggableHintShown.value = true
      }
    }, 1000)
  }
  else if (!curr) {
    showDraggableHintWithDelay.value = false
  }
})

function onInputModeChange(val: string) {
  inputMode.value = val as MappingMode
}

const handleNodeExecute = () => {
  emit('execute')
}

const onMappedNodeSelected = (val: string) => {
  mappedNode.value = val

  onRunIndexChange(0)
  onUnlinkRun()
}

function onSearch(search: string) {
  emit('search', search)
}

const onInputNodeChange = (value: string) => {
  const index = parentNodes.value.findIndex((node) => node.name === value) + 1
  emit('changeInputNode', value, index)
}
</script>

<template>
  <RunData
    :canLinkRuns="!mappedNode && canLinkRuns"
    data-testid="ndv-input-panel"
    :distanceFromActive="currentNodeDepth"
    executingMessage="正在执行上游节点..."
    :isExecuting="isExecutingPrevious"
    :isPaneActive="isPaneActive"
    :isProductionExecutionPreview="isProductionExecutionPreview"
    :linkedRuns="linkedRuns"
    :mappingEnabled="isMappingEnabled"
    noDataInBranchMessage="当前分支暂无数据"
    :node="currentNode"
    :nodes="isMappingMode ? rootNodesParents : parentNodes"
    :overrideOutputs="connectedCurrentNodeOutputs"
    paneType="input"
    :pushRef="pushRef"
    :runIndex="isMappingMode ? 0 : runIndex"
    tooMuchDataTitle="输入数据过大"
    :workflow="workflow"
    @linkRun="onLinkRun"
    @runChange="onRunIndexChange"
    @search="onSearch"
    @unlinkRun="onUnlinkRun"
  >
    <template #header>
      <div class="gap-1 flex-center">
        <span class="shrink-0 font-bold text-secondary">输入</span>

        <div
          v-if="isActiveNodeConfig && !readOnly"
          class="ml-auto"
          data-testid="input-panel-mode"
        >
          <SelectButton
            :modelValue="inputMode"
            optionLabel="label"
            :options="inputModes"
            optionValue="value"
            size="small"
            @update:modelValue="onInputModeChange"
          />
        </div>
      </div>
    </template>

    <template #input-select>
      <InputNodeSelect
        v-if="parentNodes.length && currentNodeName"
        :modelValue="currentNodeName"
        :nodes="parentNodes"
        :workflow="workflow"
        @update:modelValue="onInputNodeChange"
      />
    </template>

    <template
      v-if="isMappingMode"
      #before-data
    >
      <div>
        <InputNodeSelect
          :modelValue="mappedNode"
          :nodes="rootNodesParents"
          :workflow="workflow"
          @update:modelValue="onMappedNodeSelected"
        />
      </div>
    </template>

    <template #node-not-run>
      <div
        v-if="(isActiveNodeConfig && rootNode) || parentNodes.length"
        class="flex flex-col items-center p-8"
      >
        <div
          v-if="isMappingEnabled || hasRootNodeRun"
          class="font-bold"
        >
          此节点尚未接收到任何数据
        </div>

        <div
          v-else
          class="flex-col flex-center"
        >
          <div class="font-bold">
            上游节点尚未运行
          </div>

          <div class="pt-1 text-center text-sm text-secondary">
            如果你想查看这些节点的数据，请切换到
            <ProBtn
              data-testid="switch-to-mapping-mode-link"
              label="映射视图"
              mini
              size="small"
              variant="link"
              @click.prevent="onInputModeChange('mapping')"
            />
          </div>
        </div>

        <div class="pt-5">
          <NodeExecuteButton
            v-if="!readOnly"
            v-tooltip="{
              value: showDraggableHint && showDraggableHintWithDelay
                ? `首先单击此按钮，然后将数据从先前的节点映射到「${focusedMappableInput}」` : undefined,
            }"
            data-testid="execute-previous-node"
            label="执行上游节点"
            :nodeName="(isActiveNodeConfig ? rootNode : currentNodeName) ?? ''"
            @execute="handleNodeExecute()"
          />
        </div>

        <div
          v-if="!readOnly"
          class="max-w-60 pt-5 text-center text-sm text-secondary"
        >
          系统将从最近尚未运行的节点开始依次执行，以便为此节点提供数据。
        </div>
      </div>

      <div
        v-else
        class="flex-col p-8 flex-center"
      >
        <WireMeUp />

        <div class="py-5 font-bold">
          连接节点以继续
        </div>

        <div class="flex-col flex-center">
          <div class="pb-1 text-center text-sm text-secondary">
            拖动圆点连接到其他节点，激活数据输入。
          </div>

          <ProBtn
            as="a"
            href="https://docs.n8n.io/workflows/connections/"
            label="了解更多"
            mini
            size="small"
            target="_blank"
            variant="link"
          >
            <template #icon="{ size }">
              <CircleHelpIcon :size="size" />
            </template>
          </ProBtn>
        </div>
      </div>
    </template>

    <template #node-waiting>
      <span class="text-lg font-bold">
        Waiting for input
      </span>
      <span v-html="waitingMessage" />
    </template>

    <template #no-output-data>
      <div class="text-lg font-bold">
        无数据
      </div>
    </template>

    <template #recovered-artificial-output-data>
      <div>
        <div class="text-lg font-bold">
          无法显示数据
        </div>
        <div>
          执行被中断，因此数据未保存。尝试修复工作流并重新执行。
        </div>
      </div>
    </template>
  </RunData>
</template>
