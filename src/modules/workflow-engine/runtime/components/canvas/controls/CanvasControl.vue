<script setup lang="ts">
import { useVueFlow } from '@vue-flow/core'
import { MiniMap } from '@vue-flow/minimap'
import { FullscreenIcon, LayoutDashboardIcon, MessageSquareTextIcon, PlusIcon, TvMinimalIcon } from 'lucide-vue-next'

import CanvasControlDebug from '#wf/components/canvas/controls/CanvasControlDebug.vue'
import ToolBox from '#wf/components/ui/ToolBox.vue'
import { NODE_CREATOR_OPEN_SOURCES } from '#wf/constants/common'
import { useNodeCreatorStore } from '#wf/stores/nodeCreator.store'

const emit = defineEmits<{
  addStickyNote: []
}>()

const nodeCreatorStore = useNodeCreatorStore()

const showMiniMap = ref(false)

const { zoomIn, zoomOut, fitView, viewport, setViewport } = useVueFlow()

const enum ZoomOption {
  ZOOM_OUT,
  ZOOM_IN,
  FIT_VIEW,
  ZOOM_50,
  ZOOM_100,
  ZOOM_150,
  ZOOM_200,
}

/** 直接缩放到特定百分比值 */
const zoomTo = (zoomLevel: number, duration = 300) => {
  const currentViewport = viewport.value

  // 设置新的视口配置，保持当前位置，只修改缩放级别
  setViewport({
    x: currentViewport.x,
    y: currentViewport.y,
    zoom: zoomLevel,
  }, { duration })
}

const handleZoomOption = (option: ZoomOption) => {
  switch (option) {
    case ZoomOption.ZOOM_OUT:
      zoomOut({ duration: 300 })
      break

    case ZoomOption.ZOOM_IN:
      zoomIn({ duration: 300 })
      break

    case ZoomOption.FIT_VIEW:
      fitView({ duration: 300 })
      break

    case ZoomOption.ZOOM_50:
      zoomTo(0.5)
      break

    case ZoomOption.ZOOM_100:
      zoomTo(1)
      break

    case ZoomOption.ZOOM_150:
      zoomTo(1.5)
      break

    case ZoomOption.ZOOM_200:
      zoomTo(2)
      break
  }
}

const zoomMenuOptions = [
  {
    label: '缩小',
    command: () => handleZoomOption(ZoomOption.ZOOM_OUT),
  },
  {
    label: '放大',
    command: () => handleZoomOption(ZoomOption.ZOOM_IN),
  },
  {
    label: '自适应',
    command: () => handleZoomOption(ZoomOption.FIT_VIEW),
  },
  { separator: true },
  {
    label: '缩放到 50%',
    command: () => handleZoomOption(ZoomOption.ZOOM_50),
  },
  {
    label: '缩放到 100%',
    command: () => handleZoomOption(ZoomOption.ZOOM_100),
  },
  {
    label: '缩放到 150%',
    command: () => handleZoomOption(ZoomOption.ZOOM_150),
  },
  {
    label: '缩放到 200%',
    command: () => handleZoomOption(ZoomOption.ZOOM_200),
  },
]

const handleAddNode = () => {
  nodeCreatorStore.openNodeCreator({
    source: NODE_CREATOR_OPEN_SOURCES.ADD_NODE_BUTTON,
  })
}
</script>

<template>
  <div class="absolute bottom-0 left-1/2 z-40 -translate-x-1/2 p-card-container">
    <div class="justify-center gap-card-container flex-center">
      <div class="relative">
        <ToolBox>
          <div class="gap-1 flex-center">
            <PopupMenu :options="zoomMenuOptions">
              <ProBtn
                v-tooltip.top="'缩放控制'"
                highlight
                :label="`${Math.round(viewport.zoom * 100)}%`"
                mini
                severity="contrast"
                size="small"
                variant="text"
              >
                <template #icon="{ size }">
                  <FullscreenIcon :size="size" />
                </template>
              </ProBtn>
            </PopupMenu>

            <Divider
              class="!mx-1"
              layout="vertical"
            />

            <CanvasControlDebug v-if="isDev() || isTest()" />

            <ProBtn
              v-tooltip.top="'添加注释'"
              severity="contrast"
              size="small"
              variant="text"
              @click="emit('addStickyNote')"
            >
              <template #icon="{ size }">
                <MessageSquareTextIcon :size="size" />
              </template>
            </ProBtn>

            <ProBtn
              v-tooltip.top="'优化布局'"
              severity="contrast"
              size="small"
              variant="text"
              @click="fitView({ duration: 300 })"
            >
              <template #icon="{ size }">
                <LayoutDashboardIcon :size="size" />
              </template>
            </ProBtn>

            <ProBtn
              v-tooltip.top="'缩略图'"
              :highlight="showMiniMap"
              severity="contrast"
              size="small"
              variant="text"
              @click.stop="showMiniMap = !showMiniMap"
            >
              <template #icon="{ size }">
                <TvMinimalIcon :size="size" />
              </template>
            </ProBtn>

            <Divider
              class="!mx-1"
              layout="vertical"
            />

            <ProBtn
              highlight
              label="添加节点"
              mini
              severity="primary"
              size="small"
              variant="text"
              @click="handleAddNode()"
            >
              <template #icon="{ size }">
                <PlusIcon :size="size" />
              </template>
            </ProBtn>
          </div>
        </ToolBox>

        <div
          v-if="showMiniMap"
          class="absolute top-0 -translate-y-full overflow-hidden pb-3"
        >
          <ToolBox>
            <MiniMap
              class="!static !m-2"
              :height="120"
              pannable
              :width="200"
            />
          </ToolBox>
        </div>
      </div>
    </div>
  </div>
</template>
