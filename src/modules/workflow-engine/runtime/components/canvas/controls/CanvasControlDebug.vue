<script setup lang="ts">
import { useVueFlow } from '@vue-flow/core'
import { consola } from 'consola'
import { BugPlayIcon } from 'lucide-vue-next'

import { useCanvasMapping } from '#wf/composables/useCanvasMapping'
import { useCanvasOperations } from '#wf/composables/useCanvasOperations'
import { useNDVStore } from '#wf/stores/ndv.store'

const ndvStore = useNDVStore()

const { editableWorkflow, editableWorkflowObject } = useCanvasOperations()

const { updateNodeInternals } = useVueFlow()

const nodes = computed(() => editableWorkflow.value.nodes)
const connections = computed(() => editableWorkflow.value.connections)

const logNodesAndConnections = () => {
  const { mappedNodes, mappedConnections } = useCanvasMapping({
    nodes,
    connections,
    workflowObject: editableWorkflowObject,
  })

  consola.log('mappedNodes', mappedNodes.value)
  consola.log('mappedConnections', mappedConnections.value)
}

const logActiveNode = () => {
  consola.log('activeNode', ndvStore.activeNode)
}
</script>

<template>
  <ProPrimePopover>
    <ProBtn
      label="调试面板"
      onlyIcon
      severity="contrast"
      size="small"
      variant="text"
    >
      <template #icon="{ size }">
        <BugPlayIcon :size="size" />
      </template>
    </ProBtn>

    <template #content>
      <div class="max-w-[320px]">
        <div class="flex flex-col gap-2">
          <Button
            label="nodes | connections"
            @click="logNodesAndConnections"
          />
          <Button
            label="activeNode"
            @click="logActiveNode"
          />
          <Button
            label="updateNodeInternals"
            @click="updateNodeInternals()"
          />
        </div>
      </div>
    </template>
  </ProPrimePopover>
</template>
