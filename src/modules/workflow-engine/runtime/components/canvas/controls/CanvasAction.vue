<script setup lang="ts">
import { CircleStopIcon, EllipsisIcon, PlayIcon, SaveIcon, XIcon } from 'lucide-vue-next'

import ToolBox from '#wf/components/ui/ToolBox.vue'
import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { useRunWorkflow } from '#wf/composables/useRunWorkflow'
import { useUIStore } from '#wf/stores/ui.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'

import { GlobalEvent } from '~/enums/event'
import AppActionMenu from '~/features/space/components/app/AppActionMenu.vue'
import type { SpaceAppResource } from '~/features/space/types/space'

defineProps<{
  readOnly?: boolean
}>()

const workflowStore = useWorkflowStore()
const { isWorkflowRunning, workflowExecutionData, executionWaitingForWebhook, workflow } = storeToRefs(workflowStore)

const appResource = computed<SpaceAppResource | undefined>(() => {
  if (workflow.value) {
    return {
      ...workflow.value,
      updatedAt: String(workflow.value.updatedAt),
      createdAt: String(workflow.value.createdAt),
    }
  }

  return undefined
})

const uiStore = useUIStore()
const { isActionActive } = storeToRefs(uiStore)

const nodeHelpers = useNodeHelpers()

const { runEntireWorkflow, stopCurrentExecution, stopWaitingForWebhook } = useRunWorkflow()

const isWorkflowSaving = computed(() => {
  return isActionActive.value.workflowSaving
})

const isClearExecutionButtonVisible = computed(
  () =>
    !isWorkflowRunning.value
    && workflowExecutionData.value,
)

const isStopExecutionButtonVisible = computed(
  () => isWorkflowRunning.value && !executionWaitingForWebhook.value,
)

const isStopWaitingForWebhookButtonVisible = computed(
  () => isWorkflowRunning.value && executionWaitingForWebhook.value,
)

const handleWorkflowSave = async () => {
  if (!isWorkflowSaving.value) {
    emitter.emit(GlobalEvent.WorkflowSave)
  }
}

const handleWorkflowRun = async () => {
  await runEntireWorkflow('main')
}

const handleWorkflowClearRunData = async () => {
  workflowStore.workflowExecutionData = null
  nodeHelpers.updateNodesExecutionIssues()
  // TODO: 还要重置 document title
}

const runBtnLabel = computed(() => {
  if (isWorkflowRunning.value) {
    return '运行中'
  }

  if (executionWaitingForWebhook.value) {
    return '等待事件触发'
  }

  return '运行'
})

const isDividerShow = computed(() => {
  return isStopExecutionButtonVisible.value || isStopWaitingForWebhookButtonVisible.value || isClearExecutionButtonVisible.value
})

const isStoppingExecution = ref(false)

const handleStopExecution = async () => {
  isStoppingExecution.value = true
  await stopCurrentExecution()
  isStoppingExecution.value = false
}

const handleStopWaitingForWebhook = async () => {
  await stopWaitingForWebhook()
}
</script>

<template>
  <div class="flex h-full flex-col">
    <div
      v-if="!readOnly"
      class="gap-card-container p-card-container pb-0 inline-flex-center"
    >
      <div
        class="pointer-events-auto ml-auto gap-2 flex-center"
        data-testid="wf-editor-actions"
      >
        <ToolBox>
          <Button
            label="保存"
            severity="contrast"
            size="small"
            variant="text"
            @click="handleWorkflowSave()"
          >
            <template #icon>
              <SaveIcon :size="14" />
            </template>
          </Button>

          <Divider
            class="!mx-1"
            layout="vertical"
          />

          <Button
            :label="runBtnLabel"
            :loading="isWorkflowRunning"
            severity="success"
            size="small"
            variant="text"
            @click="handleWorkflowRun()"
          >
            <template #icon>
              <PlayIcon :size="14" />
            </template>
          </Button>

          <Divider
            v-if="isDividerShow"
            class="!mx-1"
            layout="vertical"
          />

          <Button
            v-if="isStopExecutionButtonVisible"
            :label="isStoppingExecution ? '停止中...' : '停止运行'"
            :loading="isStoppingExecution"
            size="small"
            variant="text"
            @click="handleStopExecution()"
          >
            <template #icon>
              <CircleStopIcon :size="14" />
            </template>
          </Button>

          <Button
            v-if="isStopWaitingForWebhookButtonVisible"
            label="停止等待"
            size="small"
            variant="text"
            @click="handleStopWaitingForWebhook()"
          >
            <template #icon>
              <CircleStopIcon :size="14" />
            </template>
          </Button>

          <Button
            v-if="isClearExecutionButtonVisible"
            label="清除运行数据"
            size="small"
            variant="text"
            @click="handleWorkflowClearRunData()"
          >
            <template #icon>
              <XIcon :size="14" />
            </template>
          </Button>
        </ToolBox>

        <ToolBox>
          <AppActionMenu
            v-if="appResource"
            :itemData="appResource"
            placement="canvas"
          >
            <Button
              class="!px-1.5"
              severity="contrast"
              size="small"
              variant="text"
            >
              <template #icon>
                <EllipsisIcon :size="14" />
              </template>
            </Button>
          </AppActionMenu>
        </ToolBox>
      </div>
    </div>

    <div class="relative flex-1">
      <slot />
    </div>
  </div>
</template>
