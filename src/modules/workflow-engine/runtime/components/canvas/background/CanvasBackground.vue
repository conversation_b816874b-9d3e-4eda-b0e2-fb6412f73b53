<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { Background } from '@vue-flow/background'
import { get } from 'lodash-es'

import { GRID_SIZE } from '#wf/utils/nodeViewUtils'

const { themeMode } = useTheme()

const flowBgPatternColor = computed(() => {
  const color = $dt('surface.400').value

  return get(color, `${themeMode.value}.value`)
})
</script>

<template>
  <Background
    :gap="GRID_SIZE"
    :patternColor="flowBgPatternColor"
  />
</template>
