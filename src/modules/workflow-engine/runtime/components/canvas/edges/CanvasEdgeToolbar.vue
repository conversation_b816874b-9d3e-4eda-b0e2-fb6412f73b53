<script setup lang="ts">
import { PlusIcon } from 'lucide-vue-next'

import { NodeConnectionType } from '#wf/constants/canvas'

const emit = defineEmits<{
  add: []
}>()

const props = defineProps<{
  type: NodeConnectionType
}>()

const isAddButtonVisible = computed(() => props.type === NodeConnectionType.Main)
</script>

<template>
  <div
    v-if="false"
    class="relative z-10 gap-1 flex-center"
    data-testid="canvas-edge-toolbar"
  >
    <ProBtn
      v-if="isAddButtonVisible"
      class="!bg-node-accent-500 !text-white outline outline-2 outline-node-accent-100"
      data-testid="add-connection-button"
      mini
      rounded
      size="small"
      :style="{
        background: 'var(--color-sticky-background-1)',
      }"
      title="添加中间节点"
      variant="outlined"
      @click="emit('add'), console.log('clicked')"
    >
      <template #icon>
        <PlusIcon :size="12" />
      </template>
    </ProBtn>
  </div>
</template>
