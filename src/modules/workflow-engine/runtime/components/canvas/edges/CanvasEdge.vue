<script setup lang="ts">
import type { Connection, EdgeProps } from '@vue-flow/core'
import { BaseEdge, EdgeLabelRenderer } from '@vue-flow/core'

import { NodeConnectionType } from '#wf/constants/canvas'
import type { CanvasConnectionData } from '#wf/types/canvas'
import { isValidNodeConnectionType } from '#wf/utils/typeGuards'

import { GlobalEvent } from '~/enums/event'

import { getEdgeRenderData } from './utils/edgeUtil'
import CanvasEdgeToolbar from './CanvasEdgeToolbar.vue'

interface CanvasEdgeProps extends EdgeProps<CanvasConnectionData> {
  readOnly?: boolean
  hovered?: boolean
  /** 是否将整个边层带到前面，默认是 false */
  bringToFront?: boolean
}

const props = defineProps<CanvasEdgeProps>()

const data = toRef(props, 'data')

const emit = defineEmits<{
  add: [connection: Connection]
  'update:label:hovered': [hovered: boolean]
}>()

const connectionType = computed(() =>
  isValidNodeConnectionType(props.data.source.type)
    ? props.data.source.type
    : NodeConnectionType.Main,
)

const delayedHovered = ref(props.hovered)
const delayedHoveredSetTimeoutRef = ref<NodeJS.Timeout | null>(null)
const delayedHoveredTimeout = 300

watch(
  () => props.hovered,
  (isHovered) => {
    if (isHovered) {
      if (delayedHoveredSetTimeoutRef.value) {
        window.clearTimeout(delayedHoveredSetTimeoutRef.value)
      }

      delayedHovered.value = true
    }
    else {
      delayedHoveredSetTimeoutRef.value = setTimeout(() => {
        delayedHovered.value = false
      }, delayedHoveredTimeout)
    }
  },
  { immediate: true },
)

const renderToolbar = computed(() => (props.selected || delayedHovered.value) && !props.readOnly)

const isMainConnection = computed(() => data.value.source.type === NodeConnectionType.Main)

const status = computed(() => props.data.status)

const edgeColorVar = computed(() => {
  if (delayedHovered.value) {
    return '--color-edge-hovered'
  }

  if (status.value === 'success') {
    return '--color-edge-success'
  }
  else if (status.value === 'pinned') {
    return '--color-edge-pinned'
  }
  else if (!isMainConnection.value) {
    return '--color-edge-secondary'
  }
  else if (props.selected) {
    return '--color-edge-hovered'
  }
  else {
    return '--color-edge-main'
  }
})

const edgeStyle = computed(() => ({
  ...props.style,
  ...(isMainConnection.value ? null : { strokeDasharray: '8,8' }),
  stroke: `var(${edgeColorVar.value})`,
  strokeWidth: props.selected || props.hovered ? 2 : 1.5,
}))

const renderData = computed(() =>
  getEdgeRenderData(props, {
    connectionType: connectionType.value,
  }),
)

const segments = computed(() => renderData.value.segments)

const labelPosition = computed(() => renderData.value.labelPosition)

const isConnectorStraight = computed(() => renderData.value.isConnectorStraight)

const edgeLabelStyle = computed(() => ({
  transform: `translate(0, ${isConnectorStraight.value ? '-100%' : '0%'})`,
}))

const edgeToolbarStyle = computed(() => ({
  transform: `translate(-50%, -50%) translate(${labelPosition.value[0]}px, ${labelPosition.value[1]}px)`,
  ...(delayedHovered.value ? { zIndex: 1 } : {}),
}))

const edgeToolbarClasses = computed(() => ({
  'vue-flow__edge-label': true,
  selected: props.selected,
}))

const connection = computed<Connection>(() => ({
  source: props.source,
  target: props.target,
  sourceHandle: props.sourceHandleId,
  targetHandle: props.targetHandleId,
}))

function onAdd() {
  emit('add', connection.value)
}

const handleDeleteEdge = () => {
  emitter.emit(GlobalEvent.EdgeDelete, connection.value)
}

function onEdgeLabelMouseEnter() {
  emit('update:label:hovered', true)
}

function onEdgeLabelMouseLeave() {
  emit('update:label:hovered', false)
}
</script>

<template>
  <g
    :data-source-node-name="data.source?.node"
    :data-target-node-name="data.target?.node"
    data-testid="edge"
  >
    <BaseEdge
      v-for="(segment, idx) of segments"
      :id="`${props.id}-${idx}`"
      :key="segment[0]"
      :class="{
        'bring-to-front': props.bringToFront,
      }"
      :interactionWidth="40"
      :marker-end="markerEnd"
      :path="segment[0]"
      :style="edgeStyle"
    />
  </g>

  <EdgeLabelRenderer>
    <div
      class="absolute"
      :class="edgeToolbarClasses"
      :data-edge-status="status"
      :data-source-node-name="data.source?.node"
      :data-target-node-name="data.target?.node"
      data-testid="edge-label"
      :style="edgeToolbarStyle"
      @mouseenter="onEdgeLabelMouseEnter"
      @mouseleave="onEdgeLabelMouseLeave"
    >
      <CanvasEdgeToolbar
        v-if="renderToolbar"
        :type="connectionType"
        @add="onAdd"
        @delete="handleDeleteEdge()"
      />

      <div
        v-else
        class="bg-emphasis text-sm"
        data-testid="edge-label-text"
        :style="edgeLabelStyle"
      >
        {{ label }}
      </div>
    </div>
  </EdgeLabelRenderer>
</template>
