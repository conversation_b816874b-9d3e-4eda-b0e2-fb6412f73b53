<script setup lang="ts">
import { type Connection, type EdgeProps, type NodeDragEvent, type NodeProps, useVueFlow, type ViewportTransform, VueFlow } from '@vue-flow/core'
import { debounce } from 'lodash-es'

import CanvasNode from '#wf/components/canvas/nodes/CanvasNode.vue'
import { useCanvasMapping } from '#wf/composables/useCanvasMapping'
import { useCanvasOperations } from '#wf/composables/useCanvasOperations'
import { useContextMenu } from '#wf/composables/useContextMenu'
import { useHistoryHelper } from '#wf/composables/useHistoryHelper'
import { useKeybindings } from '#wf/composables/useKeybindings'
import { CanvasNodeRenderType } from '#wf/constants/canvas'
import { DEFAULT_STICKY_HEIGHT, DEFAULT_STICKY_WIDTH, START_NODE_TYPE, STICKY_NODE_TYPE } from '#wf/constants/common'
import { CanvasKey } from '#wf/constants/injection'
import { useCanvasStore } from '#wf/stores/canvas.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useUIStore } from '#wf/stores/ui.store'
import type { CanvasConnectionData, CanvasNodeData, CanvasNodeMoveEvent, ConnectStartEvent } from '#wf/types/canvas'
import type { INodeUi } from '#wf/types/interface'
import type { WorkflowCanvasProps } from '#wf/types/workflow'
import { getMidCanvasPosition, GRID_SIZE } from '#wf/utils/nodeViewUtils'

import { GlobalEvent } from '~/enums/event'

import { useActions } from '../node-creator/composables/useActions'

import CanvasBackground from './background/CanvasBackground.vue'
import CanvasAction from './controls/CanvasAction.vue'
import CanvasControl from './controls/CanvasControl.vue'
import CanvasEdge from './edges/CanvasEdge.vue'
import CanvasContextMenu from './CanvasContextMenu.vue'

const props = withDefaults(defineProps<WorkflowCanvasProps>(), {
  flowGraphId: 'workflow-canvas',
  readOnly: false,
  disableKeyBindings: false,
})

const workflow = toRef(props, 'workflow')
const workflowObject = toRef(props, 'workflowObject')
const isExecuting = toRef(props, 'executing')

const connectingHandle = ref<ConnectStartEvent>()

const initialized = ref(false)

const {
  fitView, viewport,
  setInteractive, elementsSelectable,
  addEdges, addSelectedNodes, removeSelectedNodes, nodes: graphNodes, nodesSelectionActive,
  getSelectedNodes: selectedNodes, getSelectedEdges: selectedEdges,
  onConnect, onNodesInitialized,
  onEdgeMouseEnter, onEdgeMouseLeave,
} = useVueFlow(props.flowGraphId)

/**
 * 监听画布只读状态变化
 *
 * 当工作流进入只读模式时（如：工作流正在执行、用户无编辑权限、预览模式等）：
 * - 禁用画布交互功能，防止用户拖拽或修改节点连线
 * - 保持元素可选择状态，允许用户查看节点详情
 */
watch(() => props.readOnly, () => {
  setInteractive(!props.readOnly)
  elementsSelectable.value = true
}, { immediate: true })

onNodesInitialized(() => {
  initialized.value = true
})

onConnect(addEdges)

const nodeTypesStore = useNodeTypesStore()
const uiStore = useUIStore()

const canvasStore = useCanvasStore()
const { isLoading } = storeToRefs(canvasStore)

const emit = defineEmits<{
  'update:nodes:position': [events: CanvasNodeMoveEvent[]]
  'click:connection:add': [connection: Connection]
}>()

useHistoryHelper()

const layoutGraph = () => {
  fitView({ maxZoom: 1 })
}

const connectStartRecord = ref<ConnectStartEvent>()

const handleConnectStart = (params: ConnectStartEvent) => {
  connectStartRecord.value = params
}

// 连接创建事件处理
const handleConnect = (params: GlobalEvents[GlobalEvent.EdgeCreate]) => {
  emitter.emit(GlobalEvent.EdgeCreate, params)
}

const handleConnectEnd = (ev: MouseEvent) => {
  if (connectStartRecord.value) {
    const threshold = 10

    const startX = connectStartRecord.value.event?.clientX ?? 0
    const startY = connectStartRecord.value.event?.clientY ?? 0
    const handleType = connectStartRecord.value.handleType

    // 如果起始点在点击点附近，则认为是点击
    const isClick = (Math.abs(startX - ev.clientX) <= threshold) && (Math.abs(startY - ev.clientY) <= threshold)

    if (isClick && handleType === 'source') {
      const sourceNodeId = connectStartRecord.value.nodeId
      const sourceHandleId = connectStartRecord.value.handleId

      if (sourceNodeId && sourceHandleId) {
        emitter.emit(GlobalEvent.NodeActivateByTrigger, {
          sourceNodeId,
          sourceHandleId,
        })
      }
    }

    connectStartRecord.value = undefined
  }
}

const { editableWorkflow } = useCanvasOperations()

const triggerNodes = computed(() => {
  return editableWorkflow.value.nodes.filter(
    (node) => node.type === START_NODE_TYPE || nodeTypesStore.isTriggerNode(node.type),
  )
})

const showFallbackNodes = computed(() => triggerNodes.value.length === 0)

const fallbackNodes = computed<INodeUi[]>(() =>
  isLoading.value
    ? []
    : [
        {
          id: CanvasNodeRenderType.AddNodes,
          name: CanvasNodeRenderType.AddNodes,
          type: CanvasNodeRenderType.AddNodes,
          typeVersion: 1,
          position: [0, 0],
          parameters: {},
        },
      ],
)

const { getAddedNodesAndConnections } = useActions()

const nodes = computed(() => {
  return showFallbackNodes.value
    ? [...props.workflow.nodes, ...fallbackNodes.value]
    : props.workflow.nodes
})

const connections = computed(() => workflow.value.connections)

const { mappedNodes, mappedConnections } = useCanvasMapping({
  nodes,
  connections,
  workflowObject,
})

/**
 * Edge and Nodes Hovering
 */

const edgesHoveredById = ref<Record<string, boolean>>({})
const edgesBringToFrontById = ref<Record<string, boolean>>({})

onEdgeMouseEnter(({ edge }) => {
  edgesBringToFrontById.value = { [edge.id]: true }
  edgesHoveredById.value = { [edge.id]: true }
})

onEdgeMouseLeave(({ edge }) => {
  edgesBringToFrontById.value = { [edge.id]: false }
  edgesHoveredById.value = { [edge.id]: false }
})

const onUpdateEdgeLabelHovered = (id: string, hovered: boolean) => {
  edgesBringToFrontById.value = { [id]: true }
  edgesHoveredById.value[id] = hovered
}

const onClickConnectionAdd = (connection: Connection) => {
  emit('click:connection:add', connection)
}

const viewportTransform = ref<ViewportTransform>({ x: 0, y: 0, zoom: 1 })

const handleViewportChange = (event: ViewportTransform) => {
  viewportTransform.value = event
  uiStore.nodeViewOffsetPosition = [event.x, event.y]
}

defineSlots<{
  default: VNode[]
  nodeCreator?: VNode[]
  nodeDetailsView?: VNode[]
}>()

const contextMenu = useContextMenu()

const handleOpenContextMenu = (options: GlobalEvents[GlobalEvent.OpenContextMenu]) => {
  const { event, ...rest } = options

  // if (selectedNodeIds.value.includes(nodeId)) {
  //   onOpenContextMenu(event);
  // }

  contextMenu.open(event, rest)
}

const handleSelectAllNodes = () => {
  addSelectedNodes(graphNodes.value)
  nodesSelectionActive.value = true
}

const handleDeselectAllNodes = () => {
  removeSelectedNodes(selectedNodes.value)
  nodesSelectionActive.value = false
}

const handleAddStickyNote = () => {
  if (document.activeElement) {
    (document.activeElement as HTMLElement).blur()
  }

  const offset: [number, number] = [...uiStore.nodeViewOffsetPosition]

  const position = getMidCanvasPosition(viewportTransform.value.zoom, offset)
  position[0] -= DEFAULT_STICKY_WIDTH / 2
  position[1] -= DEFAULT_STICKY_HEIGHT / 2

  const addedNodesAndConnections = getAddedNodesAndConnections([
    { type: STICKY_NODE_TYPE, position },
  ])

  emitter.emit(GlobalEvent.NodeAdd, addedNodesAndConnections)
}

const { on } = useEmitter()

onMounted(() => {
  on(GlobalEvent.OpenContextMenu, handleOpenContextMenu)
  on(GlobalEvent.NodeSelectAll, handleSelectAllNodes)
  on(GlobalEvent.NodeDeselectAll, handleDeselectAllNodes)
  on(GlobalEvent.AddStickyNote, handleAddStickyNote)
})

const handleNodeDragStop = (ev: NodeDragEvent) => {
  emit('update:nodes:position', ev.nodes.map(({ id, position }) => ({ id, position })))
}

function handleCanvasContextMenu({ event }: { event: MouseEvent }) {
  handleOpenContextMenu({
    event,
    nodeIds: selectedNodes.value.map((node) => node.id),
    source: 'canvas',
  })
}

const emitWithSelectedNodes = (emitFn: (ids: string[]) => void) => {
  return () => {
    const hasSelection = selectedNodes.value.length > 0

    if (hasSelection) {
      emitFn(selectedNodes.value.map((node) => node.id))
    }
  }
}

const keyMap = computed(() => ({
  // FIXME: 复制其他文本内容时，会意外触发复制节点，暂时禁用，待排查原因
  // ctrl_c: emitWithSelectedNodes((ids) => emitter.emit(GlobalEvent.NodeCopy, ids)),
  ctrl_a: () => { handleSelectAllNodes() },

  ...(props.readOnly
    ? {}
    : {
        // ctrl_x: emitWithSelectedNodes((ids) => emit('cut:nodes', ids)),
        'delete|backspace': () => {
          if (selectedEdges.value.length > 0) {
            selectedEdges.value.forEach((edge) => {
              emitter.emit(GlobalEvent.EdgeDelete, edge)
            })
          }

          if (selectedNodes.value.length > 0) {
            emitter.emit(GlobalEvent.NodeDelete, selectedNodes.value.map((node) => node.id))
          }
        },
        ctrl_d: emitWithSelectedNodes((ids) => emitter.emit(GlobalEvent.NodeDuplicate, ids)),
        ctrl_s: () => {
          emitter.emit(GlobalEvent.WorkflowSave)
        },
      }),
}))

const handlePaste = debounce((event: ClipboardEvent) => {
  const clipboardData = event.clipboardData

  if (clipboardData !== null) {
    const clipboardValue = clipboardData.getData('text/plain')
    emitter.emit(GlobalEvent.NodePaste, clipboardValue)
  }
}, 500, { leading: true })

useKeybindings(keyMap, {
  disabled: props.disableKeyBindings,
  onPaste: handlePaste,
})

provide(CanvasKey, {
  connectingHandle,
  isExecuting,
  initialized,
  viewport,
})
</script>

<template>
  <div
    class="size-full"
    data-testid="wf-canvas"
  >
    <VueFlow
      :defaultZoom="1"
      :deleteKeyCode="null"
      disableKeyboardA11y
      :edges="mappedConnections"
      :maxZoom="4"
      :minZoom="0.2"
      :nodes="mappedNodes"
      :snapGrid="[GRID_SIZE, GRID_SIZE]"
      snapToGrid
      @connect="handleConnect"
      @connectEnd="handleConnectEnd"
      @connectStart="handleConnectStart"
      @nodeDragStop="handleNodeDragStop"
      @nodesInitialized="layoutGraph()"
      @paneContextMenu="handleCanvasContextMenu({ event: $event })"
      @selectionContextMenu="handleCanvasContextMenu"
      @selectionDragStop="handleNodeDragStop"
      @viewportChange="handleViewportChange"
    >
      <template #node-canvas-node="nodeProps: NodeProps<CanvasNodeData>">
        <CanvasNode
          v-bind="nodeProps"
          :readOnly="readOnly"
        />
      </template>

      <template #edge-canvas-edge="edgeProps: EdgeProps<CanvasConnectionData>">
        <CanvasEdge
          v-bind="edgeProps"
          :bringToFront="edgesBringToFrontById[edgeProps.id]"
          :hovered="edgesHoveredById[edgeProps.id]"
          :readOnly="readOnly"
          @add="onClickConnectionAdd"
          @update:label:hovered="onUpdateEdgeLabelHovered(edgeProps.id, $event)"
        />
      </template>

      <div class="pointer-events-none absolute inset-0 z-50">
        <CanvasAction :readOnly="readOnly">
          <slot name="nodeCreator" />
          <slot name="nodeDetailsView" />
        </CanvasAction>
      </div>

      <CanvasControl
        v-if="!readOnly"
        @addStickyNote="handleAddStickyNote"
      />

      <CanvasBackground />

      <CanvasContextMenu />
    </VueFlow>

    <slot />
  </div>
</template>
