<script setup lang="ts">
import { CheckIcon } from 'lucide-vue-next'

import { useCanvasNode } from '#wf/composables/useCanvasNode'
import { CanvasNodeKey } from '#wf/constants/injection'
import { useUIStore } from '#wf/stores/ui.store'
import type { CanvasNodeStickyNoteRender } from '#wf/types/canvas'

import { GlobalEvent } from '~/enums/event'

import CanvasNodeStickyColor from './CanvasNodeStickyColor.vue'

const node = inject(CanvasNodeKey)
const nodeId = computed(() => node?.id.value)

const uiStore = useUIStore()

const colors = computed(() => Array.from({ length: 7 }).map((_, index) => index + 1))

const { render } = useCanvasNode()

const renderOptions = computed(() => render.value.options as CanvasNodeStickyNoteRender['options'])

const handleSelectColor = (color: number) => {
  if (nodeId.value) {
    emitter.emit(GlobalEvent.NodeUpdate, {
      nodeId: nodeId.value,
      parameters: { color },
    })
  }
}

const handleShowToolbar = () => {
  if (nodeId.value) {
    uiStore.showToolbar({ source: 'node', nodeId: nodeId.value })
  }
}

const handleHideToolbar = () => {
  if (nodeId.value) {
    uiStore.hideToolbar()
  }
}

const handleStyleEditing = (id: GlobalEvents[GlobalEvent.StickyNoteStyleEditorOn]) => {
  if (id === nodeId.value) {
    // TODO:
  }
}

const { on } = useEmitter()

onMounted(() => {
  on(GlobalEvent.StickyNoteStyleEditorOn, handleStyleEditing)
})
</script>

<template>
  <ProPrimePopover
    @hide="handleHideToolbar()"
    @show="handleShowToolbar()"
  >
    <template #default="{ isPopoverShow }">
      <ProBtn
        :highlight="isPopoverShow"
        label="样式"
        mini
        severity="contrast"
        size="small"
      >
        <template #icon="{ size }">
          <div class="justify-center flex-center">
            <CanvasNodeStickyColor
              :color="renderOptions.color"
              :height="size"
              :width="size"
            />
          </div>
        </template>
      </ProBtn>
    </template>

    <template #content>
      <div class="flex flex-wrap gap-2">
        <div
          v-for="color of colors"
          :key="color"
          class="cursor-pointer inline-flex-center"
          @click="handleSelectColor(color)"
        >
          <CanvasNodeStickyColor
            :color="color"
            :height="20"
            :width="20"
          >
            <span
              v-if="color === renderOptions.color"
              class="absolute inset-0 z-10 justify-center flex-center"
            >
              <CheckIcon
                :size="12"
                :strokeWidth="2.5"
              />
            </span>
          </CanvasNodeStickyColor>
        </div>
      </div>
    </template>
  </ProPrimePopover>
</template>
