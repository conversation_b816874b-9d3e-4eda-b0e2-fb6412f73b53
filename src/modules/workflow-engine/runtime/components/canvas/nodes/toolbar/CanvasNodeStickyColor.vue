<script setup lang="ts">
withDefaults(defineProps<{
  height?: number
  width?: number
  color?: number
}>(), {
  height: 16,
  width: 16,
})
</script>

<template>
  <div
    class="relative z-0 rounded-full"
    :style="{
      height: `${height}px`,
      width: `${width}px`,
    }"
  >
    <div
      class="relative justify-center rounded-full border border-solid inline-flex-center"
      :style="{
        height: `${height}px`,
        width: `${width}px`,
        color: `var(--color-sticky-${color})`,
        backgroundColor: `var(--color-sticky-background-${color})`,
        borderColor: `var(--color-sticky-border-${color})`,
      }"
    >
      <slot />
    </div>

    <div
      class="absolute inset-0 -z-10 rounded-full outline-dotted
     outline-1 -outline-offset-[1.5px]"
    />
  </div>
</template>
