<script setup lang="ts">
import { type NodeProps, Position } from '@vue-flow/core'

import CanvasHandleRenderer from '#wf/components/canvas/handles/CanvasHandleRenderer.vue'
import CanvasNodeRender from '#wf/components/canvas/nodes/CanvasNodeRender.vue'
import CanvasNodeToolbar from '#wf/components/canvas/nodes/CanvasNodeToolbar.vue'
import { useCanvas } from '#wf/composables/useCanvas'
import { useContextMenu } from '#wf/composables/useContextMenu'
import { useNodeConnections } from '#wf/composables/useNodeConnections'
import { CanvasConnectionMode } from '#wf/constants/canvas'
import { CanvasNodeKey } from '#wf/constants/injection'
import { useUIStore } from '#wf/stores/ui.store'
import type { CanvasConnectionPort, CanvasElementPortWithRenderData, CanvasNodeData } from '#wf/types/canvas'
import { createCanvasConnectionHandleString, insertSpacersBetweenEndpoints } from '#wf/utils/canvasUtil'

interface CanvasNodeProps extends NodeProps<CanvasNodeData> {
  readOnly?: boolean
}

const props = defineProps<CanvasNodeProps>()

const id = toRef(props, 'id')
const data = toRef(props, 'data')
const label = toRef(props, 'label')
const selected = toRef(props, 'selected')
const readOnly = toRef(props, 'readOnly')

provide(CanvasNodeKey, {
  id,
  data,
  label,
  selected,
  readOnly,
})

const uiStore = useUIStore()
const { toolbarState } = storeToRefs(uiStore)

const contextMenu = useContextMenu()

const isContextMenuActive = computed(() => {
  const target = contextMenu.target.value

  return contextMenu.isOpen && target?.source === 'node-button' && target.nodeId === id.value
})

const showToolbar = computed(() => {
  // 1. 判断是否通过右键菜单激活
  if (isContextMenuActive.value) {
    return true
  }

  // 2. 判断是否通过工具栏状态激活
  let isToolbarStateActive = false

  if (toolbarState.value.visible) {
    // 当前节点被单独选中的情况
    const isCurrentNodeSelected = toolbarState.value.source === 'node'
      && toolbarState.value.nodeId === id.value

    // 当前节点作为画布中多选的一部分
    const isPartOfCanvasSelection = toolbarState.value.source === 'canvas'
      && toolbarState.value.nodeIds.includes(id.value)

    isToolbarStateActive = isCurrentNodeSelected || isPartOfCanvasSelection
  }

  return isToolbarStateActive
})

const { connectingHandle } = useCanvas()

const inputs = computed(() => props.data.inputs)
const outputs = computed(() => props.data.outputs)
const connections = computed(() => props.data.connections)

const createEndpointMappingFn = ({
  mode,
  position,
  offsetAxis,
}: {
  mode: CanvasConnectionMode
  position: Position
  offsetAxis: 'top' | 'left'
}) =>
  (
    endpoint: CanvasConnectionPort | null,
    index: number,
    endpoints: Array<CanvasConnectionPort | null>,
  ): CanvasElementPortWithRenderData | undefined => {
    if (!endpoint) {
      return
    }

    const handleId = createCanvasConnectionHandleString({
      mode,
      type: endpoint.type,
      index: endpoint.index,
    })
    const handleType = mode === CanvasConnectionMode.Input ? 'target' : 'source'

    const connectionsCount = connections.value[mode][endpoint.type]?.[endpoint.index]?.length ?? 0

    const isConnecting = connectingHandle.value?.nodeId === props.id
      && connectingHandle.value?.handleType === handleType
      && connectingHandle.value?.handleId === handleId

    return {
      ...endpoint,
      handleId,
      connectionsCount,
      isConnecting,
      position,
      offset: {
        [offsetAxis]: `${(100 / (endpoints.length + 1)) * (index + 1)}%`,
      },
    }
  }

const mainOutputsMappingFn = createEndpointMappingFn({
  mode: CanvasConnectionMode.Output,
  position: Position.Right,
  offsetAxis: 'top',
})

const nonMainOutputsMappingFn = createEndpointMappingFn({
  mode: CanvasConnectionMode.Output,
  position: Position.Top,
  offsetAxis: 'left',
})

const mainInputsMappingFn = createEndpointMappingFn({
  mode: CanvasConnectionMode.Input,
  position: Position.Left,
  offsetAxis: 'top',
})

const nonMainInputsMappingFn = createEndpointMappingFn({
  mode: CanvasConnectionMode.Input,
  position: Position.Bottom,
  offsetAxis: 'left',
})

const {
  mainInputs,
  nonMainInputs,
  requiredNonMainInputs,
  mainOutputs,
  nonMainOutputs,
  isValidConnection,
} = useNodeConnections({
  inputs,
  outputs,
  connections,
})

const mappedOutputs = computed(() => {
  return [
    ...mainOutputs.value.map(mainOutputsMappingFn),
    ...nonMainOutputs.value.map(nonMainOutputsMappingFn),
  ].filter((endpoint) => !!endpoint)
})

const nonMainInputsWithSpacer = computed(() =>
  insertSpacersBetweenEndpoints(nonMainInputs.value, requiredNonMainInputs.value.length),
)

const mappedInputs = computed(() => {
  return [
    ...mainInputs.value.map(mainInputsMappingFn),
    ...nonMainInputsWithSpacer.value.map(nonMainInputsMappingFn),
  ].filter((endpoint) => !!endpoint)
})
</script>

<template>
  <div
    class="group/node relative"
    :data-canvas-node-id="id"
    data-testid="canvas-node"
  >
    <div
      class="nodrag pointer-events-none absolute right-0 top-0 z-10 -translate-y-full pb-1.5 opacity-0 group-focus-within/node:opacity-100 group-hover/node:pointer-events-auto group-hover/node:opacity-100"
      :class="{ '!pointer-events-auto !opacity-100': showToolbar }"
    >
      <CanvasNodeToolbar :readOnly="readOnly" />
    </div>

    <CanvasNodeRender />

    <template
      v-for="source of mappedOutputs"
      :key="`${source.handleId}(${source.index + 1}/${mappedOutputs.length})`"
    >
      <CanvasHandleRenderer
        v-bind="source"
        :data-connection-type="source.type"
        :data-index="source.index"
        :data-node-name="data.name"
        data-testid="canvas-node-output-handle"
        :isReadOnly="readOnly"
        :isValidConnection="isValidConnection"
        :mode="CanvasConnectionMode.Output"
      />
    </template>

    <template
      v-for="target of mappedInputs"
      :key="`${target.handleId}(${target.index + 1}/${mappedInputs.length})`"
    >
      <CanvasHandleRenderer
        v-bind="target"
        :data-connection-type="target.type"
        :data-index="target.index"
        :data-node-name="data.name"
        data-testid="canvas-node-input-handle"
        :isReadOnly="readOnly"
        :isValidConnection="isValidConnection"
        :mode="CanvasConnectionMode.Input"
      />
    </template>
  </div>
</template>
