<script setup lang="ts">
import { CanvasNodeRenderType } from '#wf/constants/canvas'
import { CanvasNodeKey } from '#wf/constants/injection'

import CanvasNodeAddNodes from './render-types/CanvasNodeAddNodes.vue'
import CanvasNodeDefault from './render-types/CanvasNodeDefault.vue'
import CanvasNodeStickyNote from './render-types/CanvasNodeStickyNote.vue'

const node = inject(CanvasNodeKey)

const slots = defineSlots<{
  default?: VNode[]
}>()

const Render = () => {
  const renderType = node?.data.value.render.type ?? CanvasNodeRenderType.Default

  let Component

  switch (renderType) {
    case CanvasNodeRenderType.StickyNote:
      Component = CanvasNodeStickyNote
      break

    case CanvasNodeRenderType.AddNodes:
      Component = CanvasNodeAddNodes
      break

    default:
      Component = CanvasNodeDefault
  }

  return h(
    Component,
    slots.default,
  )
}
</script>

<template>
  <Render />
</template>
