<script setup lang="ts">
import { EllipsisIcon, PencilLineIcon, PlayIcon } from 'lucide-vue-next'

import { useCanvasNode } from '#wf/composables/useCanvasNode'
import { useContextMenu } from '#wf/composables/useContextMenu'
import { CanvasNodeRenderType } from '#wf/constants/canvas'

import { GlobalEvent } from '~/enums/event'

import CanvasNodeStickyColorSelector from './toolbar/CanvasNodeStickyColorSelector.vue'

defineProps<{
  readOnly?: boolean
}>()

const { nodeId, render } = useCanvasNode()

const isExecuteNode = computed(() => {
  return (
    render.value.type === CanvasNodeRenderType.Default
    && 'configuration' in render.value.options
    && !render.value.options.configuration
  )
})

const isStickyNote = computed(
  () => render.value.type === CanvasNodeRenderType.StickyNote,
)

const contextMenu = useContextMenu()

const isContextMenuOpen = computed(() => {
  const target = contextMenu.target.value

  return contextMenu.isOpen
    && target?.source === 'node-button'
    && target.nodeId === nodeId.value
})

const handleRunNode = () => {
  emitter.emit(GlobalEvent.NodeRun, nodeId.value)
}

const handleEditStickyNote = () => {
  emitter.emit(GlobalEvent.StickyNoteEditModeOn, nodeId.value)
}

const handleOpenContextMenu = (event: MouseEvent) => {
  emitter.emit(GlobalEvent.OpenContextMenu, {
    event,
    nodeId: nodeId.value,
    source: 'node-button',
  })
}
</script>

<template>
  <div
    class="gap-1 rounded-lg bg-content p-0.5 shadow-sm inline-flex-center"
    :class="{ '!hidden': readOnly }"
    data-testid="canvas-node-toolbar"
  >
    <ProBtn
      v-if="isExecuteNode"
      label="调试此步骤"
      mini
      severity="contrast"
      size="small"
      @click.stop="handleRunNode()"
    >
      <template #icon="{ size }">
        <PlayIcon :size="size" />
      </template>
    </ProBtn>

    <CanvasNodeStickyColorSelector
      v-if="isStickyNote"
    />

    <ProBtn
      v-if="isStickyNote"
      label="编辑"
      mini
      severity="contrast"
      size="small"
      @click.stop="handleEditStickyNote()"
    >
      <template #icon="{ size }">
        <PencilLineIcon :size="size" />
      </template>
    </ProBtn>

    <ProBtn
      :highlight="isContextMenuOpen"
      mini
      onlyIcon
      severity="contrast"
      size="small"
      @click="handleOpenContextMenu($event)"
    >
      <template #icon="{ size }">
        <EllipsisIcon :size="size" />
      </template>
    </ProBtn>
  </div>
</template>
