<script setup lang="ts">
import { CheckCircleIcon, ChevronDownIcon, ChevronUpIcon, Loader2Icon, OctagonXIcon, TriangleAlertIcon } from 'lucide-vue-next'

import CanvasNodeBox from '#wf/components/canvas/nodes/CanvasNodeBox.vue'
import NodeIcon from '#wf/components/NodeIcon.vue'
import { useCanvasNode } from '#wf/composables/useCanvasNode'
import { useContextMenu } from '#wf/composables/useContextMenu'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'

import { GlobalEvent } from '~/enums/event'

const enum NodeStatus {
  Waiting = 'Waiting',
  Running = 'Running',
  Success = 'Success',
  Failed = 'Failed',
  Issue = 'Issue',
}

const {
  node,
  nodeId,
  name,
  issues,
  subtitle,
  isSelected,
  hasRunData,
  hasIssues,
  executionStatus,
  executionWaiting,
  executionRunning,
} = useCanvasNode()

const ndvStore = useNDVStore()
const { activeNode } = storeToRefs(ndvStore)

const nodeTypesStore = useNodeTypesStore()

const nodeType = computed(() => {
  if (node?.data.value.type && node?.data.value.typeVersion) {
    return nodeTypesStore.getNodeType(node.data.value.type, node.data.value.typeVersion)
  }

  return null
})

defineSlots<{
  content: VNode[]
}>()

const contextMenu = useContextMenu()

const isContextMenuActive = computed(() => {
  const target = contextMenu.target.value

  if (contextMenu.isOpen) {
    if (target?.source === 'node-button' || target?.source === 'node-right-click') {
      return target.nodeId === nodeId.value
    }
  }

  return false
})

const handleNodeClick = () => {
  emitter.emit(GlobalEvent.NodeActivate, nodeId.value)
}

const handleContextMenu = (event: MouseEvent) => {
  emitter.emit(GlobalEvent.OpenContextMenu, {
    event,
    nodeId: nodeId.value,
    source: 'node-right-click',
  })
}

const nodeStatus = computed<NodeStatus | null>(() => {
  if (hasIssues.value) {
    if (executionStatus.value === 'error') {
      return NodeStatus.Failed
    }

    return NodeStatus.Issue
  }

  if (executionWaiting.value || executionStatus.value === 'waiting') {
    return NodeStatus.Waiting
  }

  if (executionRunning.value) {
    return NodeStatus.Running
  }

  if (hasRunData.value) {
    return NodeStatus.Success
  }

  return null
})

const isRunDataInfoOpen = ref(false)

const isActive = computed(() => {
  return activeNode?.value?.id === node?.id.value
})
</script>

<template>
  <div class="relative">
    <CanvasNodeBox
      :class="{
        '!border-[var(--color-canvas-node-success)]': !isActive && nodeStatus === NodeStatus.Success,
        '!border-[var(--color-canvas-node-error)]': !isActive && nodeStatus === NodeStatus.Failed,
        '!border-[var(--color-canvas-node-warning)]': !isActive && (nodeStatus === NodeStatus.Waiting || nodeStatus === NodeStatus.Running),
        '!border-[var(--color-canvas-node-border-selected)]': !isActive && (isSelected || isContextMenuActive),
        '!border-node-accent-500': isActive,
        '!shadow-md': isActive,
      }"
      @click.stop="handleNodeClick()"
      @contextmenu.prevent="handleContextMenu($event)"
    >
      <div class="p-node-content flex-center">
        <div class="mr-2 shrink-0 inline-flex-center">
          <NodeIcon
            :nodeType="nodeType"
            :size="20"
          />
        </div>

        <div
          v-if="name"
          class="flex-1 truncate font-semibold leading-none"
        >
          {{ name }}
        </div>
      </div>

      <div
        v-if="subtitle"
        class="p-node-content pt-0 text-sm"
      >
        <div class="text-sm text-secondary">
          {{ subtitle }}
        </div>

        <slot name="content" />
      </div>
    </CanvasNodeBox>

    <div
      v-if="nodeStatus"
      class="absolute inset-x-0 z-50 translate-y-1"
    >
      <div class="nodrag cursor-default overflow-hidden rounded-md border border-divider bg-content text-xs">
        <template v-if="nodeStatus === NodeStatus.Waiting || nodeStatus === NodeStatus.Running">
          <div class="gap-2 p-1.5 font-medium flex-center">
            <Loader2Icon
              class="animate-spin text-[var(--color-canvas-node-warning)]"
              :size="13"
              :strokeWidth="2.5"
            />
            运行中
          </div>
        </template>

        <template v-else-if="nodeStatus === NodeStatus.Success">
          <div class="gap-2 p-1.5 font-medium flex-center">
            <CheckCircleIcon
              class="text-[var(--color-canvas-node-success)]"
              :size="13"
              :strokeWidth="2.5"
            />
            运行成功
          </div>
        </template>

        <template
          v-else-if="
            nodeStatus === NodeStatus.Issue
              || nodeStatus === NodeStatus.Failed
          "
        >
          <div
            class="p-1"
            @click.stop
          >
            <template v-if="nodeStatus === NodeStatus.Issue">
              <div
                class="gap-2 rounded p-0.5 font-medium flex-center hover:bg-emphasis"
                @click="isRunDataInfoOpen = !isRunDataInfoOpen"
              >
                <TriangleAlertIcon
                  class="text-[var(--color-canvas-node-warning)]"
                  :size="13"
                  :strokeWidth="2.5"
                />
                存在问题

                <span class="ml-auto">
                  <ChevronUpIcon
                    v-if="isRunDataInfoOpen"
                    class="text-secondary"
                    :size="13"
                    :strokeWidth="2.5"
                  />
                  <ChevronDownIcon
                    v-else
                    class="text-secondary"
                    :size="13"
                    :strokeWidth="2.5"
                  />
                </span>
              </div>

              <div
                v-if="isRunDataInfoOpen"
                class="p-1"
              >
                <ul class="space-y-0.5 text-secondary">
                  <li
                    v-for="issue of issues"
                    :key="issue"
                  >
                    <span class="font-bold">
                      -
                    </span>
                    {{ issue }}
                  </li>
                </ul>
              </div>
            </template>

            <template v-else-if="nodeStatus === NodeStatus.Failed">
              <div
                class="gap-2 rounded p-0.5 font-medium flex-center hover:bg-emphasis"
                @click="isRunDataInfoOpen = !isRunDataInfoOpen"
              >
                <OctagonXIcon
                  class="text-[var(--color-canvas-node-error)]"
                  :size="13"
                  :strokeWidth="2.5"
                />
                运行失败

                <span class="ml-auto">
                  <ChevronUpIcon
                    v-if="isRunDataInfoOpen"
                    class="text-secondary"
                    :size="13"
                    :strokeWidth="2.5"
                  />
                  <ChevronDownIcon
                    v-else
                    class="text-secondary"
                    :size="13"
                    :strokeWidth="2.5"
                  />
                </span>
              </div>

              <div
                v-if="isRunDataInfoOpen"
                class="p-1"
              >
                检查以下问题：
                <ul class="text-secondary">
                  <li
                    v-for="issue of issues"
                    :key="issue"
                  >
                    - {{ issue }}
                  </li>
                </ul>
              </div>
            </template>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
