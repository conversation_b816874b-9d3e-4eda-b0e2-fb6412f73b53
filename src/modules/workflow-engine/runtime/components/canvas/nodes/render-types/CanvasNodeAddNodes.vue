<script setup lang="ts">
import { CirclePlusIcon } from 'lucide-vue-next'

import CanvasNodeBox from '#wf/components/canvas/nodes/CanvasNodeBox.vue'
import { NODE_CREATOR_OPEN_SOURCES } from '#wf/constants/common'
import { useNodeCreatorStore } from '#wf/stores/nodeCreator.store'

const nodeCreatorStore = useNodeCreatorStore()

const handleAddNode = () => {
  nodeCreatorStore.openNodeCreatorForTriggerNodes(
    NODE_CREATOR_OPEN_SOURCES.TRIGGER_PLACEHOLDER_BUTTON,
  )
}
</script>

<template>
  <CanvasNodeBox @click.stop="handleAddNode()">
    <div class="p-node-content flex-center">
      <div class="mr-2 shrink-0 inline-flex-center">
        <CirclePlusIcon
          :size="16"
        />
      </div>

      <div class="flex-1 truncate font-semibold leading-none">
        添加第一步
      </div>
    </div>

    <div class="p-node-content pt-0 text-sm">
      <div class="text-sm text-secondary">
        在执行工作流之前添加一个触发器节点
      </div>
    </div>
  </CanvasNodeBox>
</template>
