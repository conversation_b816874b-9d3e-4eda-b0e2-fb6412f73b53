<script setup lang="ts">
import { NodeResizer, type OnResize } from '@vue-flow/node-resizer'

import MarkdownRender from '#wf/components/MarkdownRender.vue'
import { useCanvasNode } from '#wf/composables/useCanvasNode'
import type { CanvasNodeStickyNoteRender } from '#wf/types/canvas'

import { useRef } from '@/composables/useRef'
import { GlobalEvent } from '~/enums/event'

const { nodeId, isReadOnly, render } = useCanvasNode()

const renderOptions = computed(() => render.value.options as CanvasNodeStickyNoteRender['options'])

const handleResize = (ev: OnResize) => {
  emitter.emit(GlobalEvent.NodeMove, {
    nodeId: nodeId.value,
    position: {
      x: ev.params.x,
      y: ev.params.y,
    },
  })

  emitter.emit(GlobalEvent.NodeUpdate, {
    nodeId: nodeId.value,
    parameters: {
      ...(ev.params.width ? { width: ev.params.width } : {}),
      ...(ev.params.height ? { height: ev.params.height } : {}),
    },
  })
}

const { refKey, ref: textareaRef } = useRef<{ $el: HTMLTextAreaElement }>()

/** 是否处于编辑模式 */
const isEditMode = ref(false)

const toggleEditModeOn = () => {
  isEditMode.value = true

  nextTick(() => {
    textareaRef.value?.$el.focus()
  })
}

const toggleEditModeOff = () => {
  isEditMode.value = false
}

const handleEditModeOn = (triggerNodeId: string) => {
  if (nodeId.value === triggerNodeId) {
    toggleEditModeOn()
  }
}

const { on } = useEmitter()

onMounted(() => {
  on(GlobalEvent.StickyNoteEditModeOn, handleEditModeOn)
})

const handleUpdateNoteContent = (val: string) => {
  emitter.emit(GlobalEvent.NodeUpdate, {
    nodeId: nodeId.value,
    parameters: {
      content: val,
    },
  })
}

const handleContextMenu = (event: MouseEvent) => {
  emitter.emit(GlobalEvent.OpenContextMenu, {
    event,
    nodeId: nodeId.value,
    source: 'node-right-click',
  })
}
</script>

<template>
  <NodeResizer
    v-if="!isEditMode"
    :height="renderOptions.height"
    :isVisible="!isReadOnly"
    :minHeight="80"
    :minWidth="150"
    :width="renderOptions.width"
    @resize="handleResize"
  />

  <div
    class="nowheel overflow-hidden rounded-lg border border-solid"
    :style="{
      height: `${renderOptions.height}px`,
      width: `${renderOptions.width}px`,
      color: `var(--color-sticky-foreground-${renderOptions.color})`,
      backgroundColor: `var(--color-sticky-background-${renderOptions.color})`,
      borderColor: `var(--color-sticky-border-${renderOptions.color})`,
    }"
    @click.stop
    @contextmenu.prevent="handleContextMenu($event)"
    @keydown.prevent
  >
    <div
      v-if="!isEditMode"
      class="h-full overflow-auto p-3"
      @dblclick.stop="toggleEditModeOn()"
    >
      <MarkdownRender :content="renderOptions.content" />
    </div>

    <div
      v-else
      class="h-full"
      @click.stop
      @keydown.esc="toggleEditModeOff()"
      @keydown.stop
      @mousedown.stop
      @mouseup.stop
    >
      <Textarea
        :ref="refKey"
        class="h-full resize-none !border-none !outline-none"
        fluid
        :modelValue="renderOptions.content"
        @blur="toggleEditModeOff()"
        @update:modelValue="handleUpdateNoteContent"
      />
    </div>
  </div>
</template>
