<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
// 导入所需 Lucide 图标
import {
  CheckSquare,
  CircleOff,
  Copy,
  <PERSON>pyP<PERSON>,
  <PERSON><PERSON>,
  PencilIcon,
  Pin,
  Play,
  Plus,
  Square,
  StickyNote,
  Trash2,
} from 'lucide-vue-next'

import { useContextMenu } from '#wf/composables/useContextMenu'
import { useMenuPosition } from '#wf/composables/useMenuPosition'
import type { ContextMenuAction } from '#wf/types/action-dropdown'
import { ContextMenuActionType } from '#wf/types/action-dropdown'

import ProMenu from '~/components/pro/menu/ProMenu.vue'
import { GlobalEvent } from '~/enums/event'
import { ProMenuActionType } from '~/enums/pro'

const MENU_ITEM_HEIGHT = 40 // 估计每个菜单项的高度
const MENU_PADDING = 20 // 菜单上下内边距总和
const SAFE_MARGIN = 50 // 安全边距

const contextMenu = useContextMenu()
const { position, isOpen, actions, target, targetNodeIds } = contextMenu

const container = ref<HTMLDivElement>()
const menuRef = ref<HTMLElement>()

const actualMenuHeight = ref(0)
const actualMenuWidth = ref(0)

const handleActionSelect = (actionKey: ContextMenuAction) => {
  const firstNodeId = targetNodeIds.value[0]

  switch (actionKey) {
    case ContextMenuActionType.ADD_NODE:
      emitter.emit(GlobalEvent.ToggleNodeCreator, {
        createNodeActive: true,
        source: 'context_menu',
      })
      break

    case ContextMenuActionType.ADD_STICKY:
      emitter.emit(GlobalEvent.AddStickyNote)
      break

    case ContextMenuActionType.COPY:
      emitter.emit(GlobalEvent.NodeCopy, targetNodeIds.value)
      break

    case ContextMenuActionType.DELETE:
      emitter.emit(GlobalEvent.NodeDelete, targetNodeIds.value)
      break

    case ContextMenuActionType.SELECT_ALL:
      emitter.emit(GlobalEvent.NodeSelectAll)
      break

    case ContextMenuActionType.DESELECT_ALL:
      emitter.emit(GlobalEvent.NodeDeselectAll)
      break

    case ContextMenuActionType.DUPLICATE:
      emitter.emit(GlobalEvent.NodeDuplicate, targetNodeIds.value)
      break

    case ContextMenuActionType.TOGGLE_PIN:
      // return emit('update:nodes:pin', nodeIds, 'context-menu')
      break

    case ContextMenuActionType.EXECUTE:
      emitter.emit(GlobalEvent.NodeRun, firstNodeId)
      break

    case ContextMenuActionType.TOGGLE_ACTIVATION:
      // return emit('update:nodes:enabled', nodeIds)
      break

    case ContextMenuActionType.OPEN:
      emitter.emit(GlobalEvent.NodeActivate, firstNodeId)
      break

    case ContextMenuActionType.CHANGE_COLOR:
      emitter.emit(GlobalEvent.StickyNoteStyleEditorOn, firstNodeId)
      break
  }
}

// 根据 action.id 获取相应的图标组件
const getIconByActionId = (actionId: ContextMenuAction) => {
  const iconMap = {
    [ContextMenuActionType.ADD_NODE]: Plus,
    [ContextMenuActionType.ADD_STICKY]: StickyNote,
    [ContextMenuActionType.COPY]: Copy,
    [ContextMenuActionType.DELETE]: Trash2,
    [ContextMenuActionType.SELECT_ALL]: CheckSquare,
    [ContextMenuActionType.DESELECT_ALL]: Square,
    [ContextMenuActionType.DUPLICATE]: CopyPlus,
    [ContextMenuActionType.TOGGLE_PIN]: Pin,
    [ContextMenuActionType.EXECUTE]: Play,
    [ContextMenuActionType.TOGGLE_ACTIVATION]: CircleOff,
    [ContextMenuActionType.OPEN]: PencilIcon,
    [ContextMenuActionType.CHANGE_COLOR]: Palette,
  }

  return iconMap[actionId] || null
}

const handleCloseMenu = () => {
  contextMenu.close()
}

const moreOptions = computed(() => {
  return actions.value.reduce<ProMenuItem[]>((acc, action) => {
    if (action.divided) {
      acc.push({
        actionType: ProMenuActionType.Separator,
      })
    }

    acc.push({
      label: action.label,
      actionType: action.id === 'delete' ? ProMenuActionType.Delete : undefined,
      disabled: action.disabled,
      itemIcon: getIconByActionId(action.id),
      command: () => {
        handleActionSelect(action.id)
        handleCloseMenu()
      },
    })

    return acc
  }, [])
})

// 估计菜单高度
const estimatedMenuHeight = computed(() => {
  // 计算菜单项的数量，包括分隔符
  const itemCount = moreOptions.value.length

  // 根据菜单项数量估算高度
  return MENU_ITEM_HEIGHT * itemCount + MENU_PADDING
})

// 菜单的有效高度（优先使用实际高度，否则使用估计高度）
const menuHeight = computed(() => {
  return actualMenuHeight.value > 0 ? actualMenuHeight.value : estimatedMenuHeight.value
})

// 菜单的有效宽度
const menuWidth = computed(() => {
  return actualMenuWidth.value
})

// 使用封装的菜单定位组合式函数
const sourceType = computed(() => target.value?.source)
const { horizontalPositionClass, verticalPositionClass } = useMenuPosition({
  position,
  menuWidth,
  menuHeight,
  sourceType,
  safeMargin: SAFE_MARGIN,
})

// 当菜单显示时，更新实际菜单高度
watch(() => isOpen.value, (newValue) => {
  if (newValue) {
    nextTick(() => {
      if (menuRef.value) {
        // 获取菜单的实际尺寸
        actualMenuHeight.value = menuRef.value.offsetHeight
        actualMenuWidth.value = menuRef.value.offsetWidth
      }
    })
  }
  else {
    // 重置尺寸
    actualMenuHeight.value = 0
    actualMenuWidth.value = 0
  }
})

onClickOutside(container, handleCloseMenu)
</script>

<template>
  <Teleport
    v-if="isOpen"
    to="body"
  >
    <div
      ref="container"
      class="absolute z-50"
      :style="{
        left: `${position[0]}px`,
        top: `${position[1]}px`,
      }"
    >
      <div
        ref="menuRef"
        class="absolute"
        :class="[horizontalPositionClass, verticalPositionClass]"
      >
        <ProMenu :model="moreOptions" />
      </div>
    </div>
  </Teleport>
</template>
