<script setup lang="ts">
import { PlusIcon } from 'lucide-vue-next'

import { CanvasNodeHandleKey, CanvasNodeKey } from '#wf/constants/injection'

import { GlobalEvent } from '~/enums/event'

const props = withDefaults(
  defineProps<{
    position?: 'top' | 'right' | 'bottom' | 'left'
    // plusSize?: number
    // lineSize?: number
    type?: 'success' | 'secondary' | 'default'
  }>(),
  {
    position: 'right',
    plusSize: 24,
    lineSize: 46,
    type: 'default',
  },
)

const node = inject(CanvasNodeKey)
const handle = inject(CanvasNodeHandleKey)

const getStrokeColor = computed(() => {
  switch (props.type) {
    case 'secondary':
      return 'var(--color-edge-secondary)'

    case 'success':
      return 'var(--color-edge-secondary)'

    default:
      return 'var(--color-edge-main)'
  }
})

// const viewBox = computed(() => {
//   switch (props.position) {
//     case 'bottom':
//       // fall through

//     case 'top':
//       return {
//         width: props.plusSize,
//         height: props.lineSize + props.plusSize,
//       }

//     default:
//       return {
//         width: props.lineSize + props.plusSize,
//         height: props.plusSize,
//       }
//   }
// })

// const styles = computed(() => ({
//   width: `${viewBox.value.width}px`,
//   height: `${viewBox.value.height}px`,
// }))

// const linePosition = computed(() => {
//   switch (props.position) {
//     case 'top':
//       return [
//         [viewBox.value.width / 2, viewBox.value.height - props.lineSize + 1],
//         [viewBox.value.width / 2, viewBox.value.height],
//       ]

//     case 'bottom':
//       return [
//         [viewBox.value.width / 2, 0],
//         [viewBox.value.width / 2, props.lineSize + 1],
//       ]

//     case 'left':
//       return [
//         [viewBox.value.width - props.lineSize - 1, viewBox.value.height / 2],
//         [viewBox.value.width, viewBox.value.height / 2],
//       ]

//     default:
//       return [
//         [0, viewBox.value.height / 2],
//         [props.lineSize + 1, viewBox.value.height / 2],
//       ]
//   }
// })

// const plusPosition = computed(() => {
//   switch (props.position) {
//     case 'bottom':
//       return [0, viewBox.value.height - props.plusSize]

//     case 'top':
//       return [0, 0]

//     case 'left':
//       return [0, 0]

//     default:
//       return [viewBox.value.width - props.plusSize, 0]
//   }
// })

function handleClickPlus() {
  if (node && handle) {
    emitter.emit(GlobalEvent.NodeActivateByTrigger, {
      sourceNodeId: node.id.value,
      sourceHandleId: handle.id.value,
    })
  }
}
</script>

<template>
  <span
    class="absolute text-[var(--color-canvas-node-border)]"
    :class="{
      'right-[calc(var(--size-canvas-handle)_/_2)]': position === 'left',
      'left-[calc(var(--size-canvas-handle)_/_2)]': position === 'right',
      'top-1/2 -translate-y-1/2 flex-center': position === 'left' || position === 'right',
      'bottom-[var(--size-canvas-handle)]': position === 'top',
      'top-[var(--size-canvas-handle)]': position === 'bottom',
      'left-1/2 flex -translate-x-1/2 flex-col justify-center': position === 'top' || position === 'bottom',
    }"
  >
    <span
      class="pointer-events-none relative inline-block shrink-0 whitespace-nowrap"
      :class="{
        'w-4': position === 'left' || position === 'right',
        'h-6': position === 'top' || position === 'bottom',
      }"
    >
      <span
        class="absolute inline-block bg-[var(--color-canvas-handle)]"
        :class="{
          'inset-x-0 top-1/2 h-0.5 w-full -translate-y-1/2': position === 'left' || position === 'right',
          'inset-y-0 left-1/2 h-full w-0.5 -translate-x-1/2': position === 'top' || position === 'bottom',
        }"
      />
    </span>

    <span class="shrink-0 inline-flex-center">
      <ProBtn
        class="!size-5 !rounded !border-2 !border-[var(--color-canvas-node-background)] !p-0 !text-surface-50 dark:!text-surface-900"
        mini
        size="small"
        :style="{
          backgroundColor: getStrokeColor,
        }"
        title="添加节点"
        variant="outlined"
        @click.stop="handleClickPlus()"
      >
        <template #icon="{ size }">
          <PlusIcon
            :size="size"
            :strokeWidth="3"
          />
        </template>
      </ProBtn>
    </span>

    <!-- <svg
      class="relative"
      data-testid="canvas-handle-plus-wrapper"
      :style="styles"
      :viewBox="`0 0 ${viewBox.width} ${viewBox.height}`"
    >
      <line
        :stroke="getStrokeColor"
        stroke-width="1.5"
        :x1="linePosition[0][0]"
        :x2="linePosition[1][0]"
        :y1="linePosition[0][1]"
        :y2="linePosition[1][1]"
      />
      <g
        class="group cursor-pointer"
        data-testid="canvas-handle-plus"
        :transform="`translate(${plusPosition[0]}, ${plusPosition[1]})`"
        @click.stop="handleClickPlus"
      >
        <rect
          class="fill-transparent group-hover:stroke-node-accent-500"
          height="20"
          rx="4"
          :stroke="getStrokeColor"
          stroke-width="2"
          width="20"
          x="2"
          y="2"
        />
        <path
          class="group-hover:fill-node-accent-500"
          d="m16.40655,10.89837l-3.30491,0l0,-3.30491c0,-0.40555 -0.32889,-0.73443 -0.73443,-0.73443l-0.73443,0c-0.40554,0 -0.73442,0.32888 -0.73442,0.73443l0,3.30491l-3.30491,0c-0.40555,0 -0.73443,0.32888 -0.73443,0.73442l0,0.73443c0,0.40554 0.32888,0.73443 0.73443,0.73443l3.30491,0l0,3.30491c0,0.40554 0.32888,0.73442 0.73442,0.73442l0.73443,0c0.40554,0 0.73443,-0.32888 0.73443,-0.73442l0,-3.30491l3.30491,0c0.40554,0 0.73442,-0.32889 0.73442,-0.73443l0,-0.73443c0,-0.40554 -0.32888,-0.73442 -0.73442,-0.73442z"
          :fill="getStrokeColor"
        />
      </g>
    </svg> -->
  </span>
</template>
