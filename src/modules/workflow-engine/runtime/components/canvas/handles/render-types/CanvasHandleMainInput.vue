<script setup lang="ts">
import { useCanvasNodeHandle } from '#wf/composables/useCanvasNodeHandle'

const { label, isRequired } = useCanvasNodeHandle()

const labelClasses = computed(() => ([
  'absolute z-10 top-1/2 -translate-y-1/2 -translate-x-full',
  'text-sm text-secondary',
  'bg-[var(--color-canvas-label-background)] text-center truncate',
  { 'after:content-["*"] after:text-[var(--color-canvas-node-error)]': isRequired.value },
]))
</script>

<template>
  <div
    class="flex-col justify-center flex-center"
    data-testid="canvas-handle-main-output"
  >
    <div :class="labelClasses">
      {{ label }}
    </div>
  </div>
</template>
