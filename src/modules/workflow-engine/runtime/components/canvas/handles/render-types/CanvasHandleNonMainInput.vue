<script setup lang="ts">
import CanvasHandlePlus from '#wf/components/canvas/handles/render-types/parts/CanvasHandlePlus.vue'
import { useCanvasNodeHandle } from '#wf/composables/useCanvasNodeHandle'

const { label, isConnected, isConnecting, isRequired, maxConnections } = useCanvasNodeHandle()

const labelClasses = computed(() => [
  'absolute z-10 top-[var(--size-canvas-handle)] left-1/2 -translate-x-1/2',
  'text-sm text-secondary leading-none',
  'bg-[var(--color-canvas-label-background)] text-center truncate',
  { 'after:content-["*"] after:text-[var(--color-canvas-node-error)]': isRequired.value },
])

const isHandlePlusAvailable = computed(
  () => !isConnected.value || !maxConnections.value || maxConnections.value > 1,
)

const isHovered = ref(false)

const isHandlePlusVisible = computed(
  () => !isConnecting.value || isHovered.value || !maxConnections.value || maxConnections.value > 1,
)

function onMouseEnter() {
  isHovered.value = true
}

function onMouseLeave() {
  isHovered.value = false
}
</script>

<template>
  <div
    class="flex-col justify-center flex-center"
    data-testid="canvas-handle-non-main-input"
  >
    <div :class="labelClasses">
      {{ label }}
    </div>

    <CanvasHandlePlus
      v-if="isHandlePlusAvailable"
      v-show="isHandlePlusVisible"
      position="bottom"
      type="secondary"
      @mouseenter="onMouseEnter"
      @mouseleave="onMouseLeave"
    />
  </div>
</template>
