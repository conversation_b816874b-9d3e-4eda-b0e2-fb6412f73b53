<script setup lang="ts">
import CanvasHandlePlus from '#wf/components/canvas/handles/render-types/parts/CanvasHandlePlus.vue'
import { useCanvasNode } from '#wf/composables/useCanvasNode'
import { useCanvasNodeHandle } from '#wf/composables/useCanvasNodeHandle'
import type { CanvasNodeDefaultRender } from '#wf/types/canvas'

const { render } = useCanvasNode()
const { label, isConnected, isConnecting, isReadOnly, isRequired, runData } = useCanvasNodeHandle()

const baseLabelClasses = computed(() => [
  'absolute bg-[var(--color-canvas-label-background)] z-10 pointer-events-none',
  'truncate leading-none text-sm font-medium',
])

const outputLabelClasses = computed(() => [
  ...baseLabelClasses.value,
  'top-1/2 left-2 translate-y-[-50%] text-secondary',
  { 'max-w-[96px]': isConnected.value },
  { 'after:content-["*"] after:text-[var(--color-canvas-node-error)]': isRequired.value },
])

// 运行数据标签类
const runDataLabelClasses = computed(() => [
  ...baseLabelClasses.value,
  'top-1/2 left-1/2 -translate-x-1/2 -translate-y-[150%]',
  'text-[var(--color-canvas-node-success)]',
])

const isHovered = ref(false)

const renderOptions = computed(() => render.value.options as CanvasNodeDefaultRender['options'])

const runDataTotal = computed(() => runData.value?.total ?? 0)

const runDataLabel = computed(() =>
  !isConnected.value && runData.value && runData.value.total > 0
    ? `${runData.value.total} 项`
    : '',
)

const isHandlePlusVisible = computed(() => !isConnecting.value || isHovered.value)

const plusType = computed(() => (runDataTotal.value > 0 ? 'success' : 'default'))

const plusLineSize = computed(
  () =>
    ({
      small: 46,
      medium: 66,
      large: 80,
    })[(runDataTotal.value > 0 ? 'large' : renderOptions.value.outputs?.labelSize) ?? 'small'],
)

function onMouseEnter() {
  isHovered.value = true
}

function onMouseLeave() {
  isHovered.value = false
}
</script>

<template>
  <div
    class="relative"
    data-testid="canvas-handle-main-output"
  >
    <div
      v-if="label"
      :class="outputLabelClasses"
      :title="label"
    >
      {{ label }}
    </div>

    <div
      v-if="runData"
      class="text-sm"
      :class="runDataLabelClasses"
    >
      {{ runDataLabel }}
    </div>

    <CanvasHandlePlus
      v-if="!isConnected && !isReadOnly"
      v-show="isHandlePlusVisible"
      :data-plus-type="plusType"
      :lineSize="plusLineSize"
      :type="plusType"
      @mouseenter="onMouseEnter"
      @mouseleave="onMouseLeave"
    />
  </div>
</template>
