<script setup lang="ts">
import { useCanvasNodeHandle } from '#wf/composables/useCanvasNodeHandle'

const { label, isRequired } = useCanvasNodeHandle()

const labelClasses = computed(() => [
  'absolute top-[-20px] left-1/2 -translate-x-1/2 translate-y-0',
  'text-sm text-[var(--node-type-supplemental-color)]',
  'bg-[var(--color-canvas-label-background)] z-0 truncate leading-none',
  { 'after:content-["*"] after:text-[var(--color-danger)]': isRequired.value },
])
</script>

<template>
  <div
    class="justify-center flex-center"
    data-testid="canvas-handle-non-main-output"
  >
    <div :class="labelClasses">
      {{ label }}
    </div>
  </div>
</template>
