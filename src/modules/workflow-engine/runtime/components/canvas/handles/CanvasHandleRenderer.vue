<script setup lang="ts">
import { Handle, type HandleType, type ValidConnectionFunc } from '@vue-flow/core'

import CanvasHandleMainInput from '#wf/components/canvas/handles/render-types/CanvasHandleMainInput.vue'
import CanvasHandleMainOutput from '#wf/components/canvas/handles/render-types/CanvasHandleMainOutput.vue'
import CanvasHandleNonMainInput from '#wf/components/canvas/handles/render-types/CanvasHandleNonMainInput.vue'
import CanvasHandleNonMainOutput from '#wf/components/canvas/handles/render-types/CanvasHandleNonMainOutput.vue'
import { useCanvasNode } from '#wf/composables/useCanvasNode'
import { CanvasConnectionMode, NodeConnectionType } from '#wf/constants/canvas'
import { CanvasNodeHandleKey } from '#wf/constants/injection'
import type { CanvasConnectionPort, CanvasElementPortWithRenderData } from '#wf/types/canvas'

const props = defineProps<
  CanvasElementPortWithRenderData & {
    type: CanvasConnectionPort['type']
    required?: CanvasConnectionPort['required']
    maxConnections?: CanvasConnectionPort['maxConnections']
    index: CanvasConnectionPort['index']
    label?: CanvasConnectionPort['label']
    handleId: CanvasElementPortWithRenderData['handleId']
    connectionsCount: CanvasElementPortWithRenderData['connectionsCount']
    isConnecting: CanvasElementPortWithRenderData['isConnecting']
    position: CanvasElementPortWithRenderData['position']
    offset?: CanvasElementPortWithRenderData['offset']
    mode: CanvasConnectionMode
    isReadOnly?: boolean
    isValidConnection: ValidConnectionFunc
  }
>()

const handleId = toRef(props, 'handleId')
const label = toRef(props, 'label')
const isConnecting = toRef(props, 'isConnecting')
const isReadOnly = toRef(props, 'isReadOnly')
const mode = toRef(props, 'mode')
const type = toRef(props, 'type')
const index = toRef(props, 'index')
const isRequired = toRef(props, 'required')
const maxConnections = toRef(props, 'maxConnections')
const position = toRef(props, 'position')

defineOptions({
  inheritAttrs: false,
})

const handleType = computed<HandleType>(() =>
  props.mode === CanvasConnectionMode.Input ? 'target' : 'source',
)

/** 是否已被连接 */
const isConnected = computed(() => props.connectionsCount > 0)

const handleClasses = computed(() => {
  if (props.mode === CanvasConnectionMode.Input) {
    if (isConnected.value) {
      if (props.type === NodeConnectionType.Main) {
        return 'vf-handle-input-main-connected'
      }

      return 'vf-handle-input-non-main-connected'
    }

    return 'vf-handle-input-main'
  }

  if (props.mode === CanvasConnectionMode.Output) {
    if (isConnected.value) {
      if (props.type === NodeConnectionType.Main) {
        return 'vf-handle-output-main-connected'
      }

      return 'vf-handle-output-non-main-connected'
    }
  }

  return 'vf-handle-default'
})

const connectionsLimitReached = computed(() => {
  return props.maxConnections && props.connectionsCount >= props.maxConnections
})

const isConnectableStart = computed(() => {
  if (connectionsLimitReached.value) {
    return false
  }

  return props.mode === CanvasConnectionMode.Output || props.type !== NodeConnectionType.Main
})

const isConnectableEnd = computed(() => {
  if (connectionsLimitReached.value) {
    return false
  }

  return props.mode === CanvasConnectionMode.Input || props.type !== NodeConnectionType.Main
})

/**
 * Run data
 */

const { runDataOutputMap } = useCanvasNode()

const runData = computed(() =>
  props.mode === CanvasConnectionMode.Output
    ? runDataOutputMap.value[props.type]?.[props.index]
    : undefined,
)

const renderTypeClasses = computed(() => {
  switch (props.position) {
    case 'top':
      return 'translate-y-[-50%]'

    case 'right':
      return 'translate-x-[50%]'

    case 'left':
      return 'translate-x-[-50%]'

    case 'bottom':
      return 'translate-y-[50%]'

    default:
      return ''
  }
})

const RenderType = () => {
  let Component

  if (props.mode === CanvasConnectionMode.Output) {
    if (props.type === NodeConnectionType.Main) {
      Component = CanvasHandleMainOutput
    }
    else {
      Component = CanvasHandleNonMainOutput
    }
  }
  else {
    if (props.type === NodeConnectionType.Main) {
      Component = CanvasHandleMainInput
    }
    else {
      Component = CanvasHandleNonMainInput
    }
  }

  return Component ? h(Component) : null
}

provide(CanvasNodeHandleKey, {
  id: handleId,
  label,
  mode,
  type,
  index,
  runData,
  isRequired,
  isConnected,
  isConnecting,
  isReadOnly,
  maxConnections,
  position,
})
</script>

<template>
  <Handle
    v-bind="$attrs"
    :id="handleId"
    :class="[handleClasses, position]"
    :connectableEnd="isConnectableEnd"
    :connectableStart="isConnectableStart"
    :isValidConnection="isValidConnection"
    :position="position"
    :style="offset"
    :type="handleType"
    @click.stop
  >
    <RenderType
      :class="renderTypeClasses"
      :isConnected="isConnected"
      :label="label"
      :maxConnections="maxConnections"
      :style="offset"
    />
  </Handle>
</template>
