<script setup lang="ts">
import { PinIcon } from 'lucide-vue-next'

import type { usePinnedData } from '#wf/composables/usePinnedData'

type Props = {
  tooltipContentsVisibility: {
    binaryDataTooltipContent: boolean
    pinDataDiscoveryTooltipContent: boolean
  }
  dataPinningDocsUrl: string
  pinnedData: ReturnType<typeof usePinnedData>
  disabled: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  togglePinData: []
}>()

const visible = computed(() =>
  props.tooltipContentsVisibility.pinDataDiscoveryTooltipContent ? true : undefined,
)
</script>

<template>
  <ProPrimePopover
    rootClass="inline-flex-center"
    triggerClass="inline-flex-center"
    :visible="visible"
  >
    <ProBtn
      :active="props.pinnedData.hasData.value"
      data-testid="ndv-pin-data"
      :disabled="props.disabled"
      mini
      onlyIcon
      size="small"
      @click="emit('togglePinData')"
    >
      <template #icon="{ size }">
        <PinIcon :size="size" />
      </template>
    </ProBtn>

    <template #content>
      <div v-if="props.tooltipContentsVisibility.binaryDataTooltipContent">
        由于此节点的输出包含二进制数据，因此已禁用固定数据。
      </div>
      <div v-else-if="props.tooltipContentsVisibility.pinDataDiscoveryTooltipContent">
        您可以固定此输出，而不必等待测试事件。
      </div>
      <div v-else>
        <strong>固定数据</strong>
        <p class="text-sm">
          节点将始终输出此数据而不执行。

          <Button
            label="更多信息"
            size="small"
            :to="props.dataPinningDocsUrl"
            variant="link"
          />
        </p>
      </div>
    </template>
  </ProPrimePopover>
</template>
