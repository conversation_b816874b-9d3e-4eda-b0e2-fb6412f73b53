import { useSettingsStore } from '#wf/stores/setting.store'

let coreInitialized = false

/**
 * 初始化核心必要的 Stores 和 Hooks，只会在第一次加载时执行
 */
export async function initializeCore() {
  if (!coreInitialized) {
    const settingsStore = useSettingsStore()
    // const versionsStore = useVersionsStore()

    await settingsStore.initialize()

    // void useExternalHooks().run('app.mount')

    // if (!settingsStore.isPreviewMode) {
    //   void versionsStore.checkForNewVersions()
    // }

    coreInitialized = true
  }
}
