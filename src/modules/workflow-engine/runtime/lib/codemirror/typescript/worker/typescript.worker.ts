import { ChangeSet, Text } from '@codemirror/state'
import { pascalCase } from 'change-case'
import * as Comlink from 'comlink'
import type { CodeExecutionMode } from 'n8n-workflow'

import type { LanguageServiceWorker, LanguageServiceWorkerInit } from '../types'

import runOnceForAllItemsTypes from './type-declarations/n8n-once-for-all-items.d.ts?raw'
import runOnceForEachItemTypes from './type-declarations/n8n-once-for-each-item.d.ts?raw'
import { indexedDbCache } from './cache'
import { getCompletionsAtPos } from './completions'
import { LUXON_VERSION, TYPESCRIPT_FILES } from './constants'
import {
  getDynamicInputNodeTypes,
  getDynamicNodeTypes,
  getDynamicVariableTypes,
  schemaToTypescriptTypes,
} from './dynamicTypes'
import { setupTypescriptEnv } from './env'
import { getHoverTooltip } from './hoverTooltip'
import { getDiagnostics } from './linter'
import { loadTypes } from './npmTypesLoader'
import { getUsedNodeNames } from './typescriptAst'
import { bufferChangeSets, fnPrefix } from './utils'

// 创建简单的响应式替代品
class SimpleReactive<T> {
  private _value: T
  private _watchers: Array<(newValue: T, oldValue: T) => void> = []

  constructor(initialValue: T) {
    this._value = initialValue
  }

  get value(): T {
    return this._value
  }

  set value(newValue: T) {
    const oldValue = this._value
    this._value = newValue
    this._watchers.forEach((watcher) => watcher(newValue, oldValue))
  }

  watch(callback: (newValue: T, oldValue: T) => void, options?: { immediate?: boolean }) {
    this._watchers.push(callback)

    if (options?.immediate) {
      callback(this._value, this._value)
    }
  }
}

class SimpleMap<K, V> extends Map<K, V> {
  private _watchers: Array<(map: Map<K, V>) => void> = []

  override set(key: K, value: V): this {
    super.set(key, value)
    this._watchers.forEach((watcher) => watcher(this))

    return this
  }

  override delete(key: K): boolean {
    const result = super.delete(key)

    if (result) {
      this._watchers.forEach((watcher) => watcher(this))
    }

    return result
  }

  watch(callback: (map: Map<K, V>) => void, options?: { immediate?: boolean }) {
    this._watchers.push(callback)

    if (options?.immediate) {
      callback(this)
    }
  }
}

// 简单的 until 函数替代
function until<T>(ref: SimpleReactive<T>) {
  return {
    toBe(value: T, options?: { timeout?: number }) {
      return new Promise<void>((resolve, reject) => {
        if (ref.value === value) {
          resolve()

          return
        }

        const timeout = options?.timeout || 1000
        const timer = setTimeout(() => {
          reject(new Error('Timeout'))
        }, timeout)

        const unwatch = () => {
          clearTimeout(timer)
        }

        ref.watch((newValue) => {
          if (newValue === value) {
            unwatch()
            resolve()
          }
        })
      })
    },
  }
}

self.process = { env: {} } as NodeJS.Process

const worker: LanguageServiceWorkerInit = {
  async init(options, nodeDataFetcher) {
    const loadedNodeTypesMap = new SimpleMap<string, { type: string, typeName: string }>()

    const inputNodeNames = options.inputNodeNames
    const allNodeNames = options.allNodeNames
    const codeFileName = `${options.id}.js`
    const mode = new SimpleReactive<CodeExecutionMode>(options.mode)
    const busyApplyingChangesToCode = new SimpleReactive(false)

    const cache = await indexedDbCache('typescript-cache', 'fs-map')
    const env = await setupTypescriptEnv({
      cache,
      mode: mode.value,
      code: { content: Text.of(options.content).toString(), fileName: codeFileName },
    })

    // 使用简单的计算属性替代
    const getPrefix = () => fnPrefix(mode.value)

    function editorPositionToTypescript(pos: number) {
      return pos + getPrefix().length
    }

    function typescriptPositionToEditor(pos: number) {
      return pos - getPrefix().length
    }

    async function loadNodeTypes(nodeName: string) {
      const data = await nodeDataFetcher(nodeName)

      const typeName = pascalCase(nodeName)
      const jsonType = data?.json
        ? schemaToTypescriptTypes(data.json, `${typeName}Json`)
        : `type ${typeName}Json = N8nJson`
      const paramsType = data?.params
        ? schemaToTypescriptTypes(data.params, `${typeName}Params`)
        : `type ${typeName}Params = {}`

      // Using || on purpose to handle empty string

      const binaryType = `type ${typeName}BinaryKeys = ${data?.binary.map((key) => `'${key}'`).join(' | ') || 'string'}`
      const contextType = `type ${typeName}Context = {}`
      const type = [jsonType, binaryType, paramsType, contextType].join('\n')
      loadedNodeTypesMap.set(nodeName, { type, typeName })
    }

    async function loadTypesIfNeeded() {
      const file = env.getSourceFile(codeFileName)

      if (!file) {
        return
      }

      const nodeNames = await getUsedNodeNames(file)

      for (const nodeName of nodeNames) {
        if (!loadedNodeTypesMap.has(nodeName)) {
          await loadNodeTypes(nodeName)
        }
      }
    }

    async function loadLuxonTypes() {
      if (cache.getItem('/node_modules/@types/luxon/package.json')) {
        const fileMap = await cache.getAllWithPrefix('/node_modules/@types/luxon')

        for (const [path, content] of Object.entries(fileMap)) {
          updateFile(path, content)
        }
      }
      else {
        await loadTypes('luxon', LUXON_VERSION, (path, types) => {
          cache.setItem(path, types)
          updateFile(path, types)
        })
      }
    }

    async function setVariableTypes() {
      updateFile(
        TYPESCRIPT_FILES.DYNAMIC_VARIABLES_TYPES,
        await getDynamicVariableTypes(options.variables),
      )
    }

    function updateFile(fileName: string, content: string) {
      const exists = env.getSourceFile(fileName)

      if (exists) {
        env.updateFile(fileName, content)
      }
      else {
        env.createFile(fileName, content)
      }
    }

    const loadInputNodes = options.inputNodeNames.map(
      async (nodeName) => await loadNodeTypes(nodeName),
    )
    await Promise.all(
      loadInputNodes.concat(loadTypesIfNeeded(), loadLuxonTypes(), setVariableTypes()),
    )

    loadedNodeTypesMap.watch(async (loadedNodes) => {
      updateFile(
        TYPESCRIPT_FILES.DYNAMIC_INPUT_TYPES,
        await getDynamicInputNodeTypes(inputNodeNames),
      )
      updateFile(
        TYPESCRIPT_FILES.DYNAMIC_TYPES,
        await getDynamicNodeTypes({ nodeNames: allNodeNames, loadedNodes }),
      )
    }, { immediate: true })

    mode.watch((newMode) => {
      updateFile(
        TYPESCRIPT_FILES.MODE_TYPES,
        newMode === 'runOnceForAllItems' ? runOnceForAllItemsTypes : runOnceForEachItemTypes,
      )
    }, { immediate: true })

    mode.watch((newPrefix, oldPrefix) => {
      env.updateFile(codeFileName, getPrefix(), { start: 0, length: oldPrefix?.length || 0 })
    })

    const applyChangesToCode = bufferChangeSets((bufferedChanges) => {
      bufferedChanges.iterChanges((start, end, _fromNew, _toNew, text) => {
        const length = end - start

        env.updateFile(codeFileName, text.toString(), {
          start: editorPositionToTypescript(start),
          length,
        })
      })

      void loadTypesIfNeeded()
    })

    const waitForChangesAppliedToCode = async () => {
      await until(busyApplyingChangesToCode).toBe(false, { timeout: 500 })
    }

    return Comlink.proxy<LanguageServiceWorker>({
      updateFile: async (changes) => {
        busyApplyingChangesToCode.value = true
        void applyChangesToCode(ChangeSet.fromJSON(changes)).then(() => {
          busyApplyingChangesToCode.value = false
        })
      },
      async getCompletionsAtPos(pos) {
        await waitForChangesAppliedToCode()

        return await getCompletionsAtPos({
          pos: editorPositionToTypescript(pos),
          fileName: codeFileName,
          env,
        })
      },
      getDiagnostics() {
        return getDiagnostics({ env, fileName: codeFileName }).map((diagnostic) => ({
          ...diagnostic,
          from: typescriptPositionToEditor(diagnostic.from),
          to: typescriptPositionToEditor(diagnostic.to),
        }))
      },
      getHoverTooltip(pos) {
        const tooltip = getHoverTooltip({
          pos: editorPositionToTypescript(pos),
          fileName: codeFileName,
          env,
        })

        if (!tooltip) {
          return null
        }

        tooltip.start = typescriptPositionToEditor(tooltip.start)
        tooltip.end = typescriptPositionToEditor(tooltip.end)

        return tooltip
      },
      async updateMode(newMode) {
        mode.value = newMode
      },
      async updateNodeTypes() {
        const loadedNodeNames = Array.from(loadedNodeTypesMap.keys())
        await Promise.all(loadedNodeNames.map(async (nodeName) => await loadNodeTypes(nodeName)))
      },
    })
  },
}

Comlink.expose(worker)
