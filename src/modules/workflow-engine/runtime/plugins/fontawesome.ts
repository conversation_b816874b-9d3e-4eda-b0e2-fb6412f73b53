import { config, type IconDefinition, type IconName, type IconPrefix, library } from '@fortawesome/fontawesome-svg-core'
import { faStickyNote } from '@fortawesome/free-regular-svg-icons'
import {
  faAdjust, faAngleDoubleLeft,
  faAngleDown,
  faAngleLeft,
  faAngleRight,
  faAngleUp,
  faArchive,
  faArrowDown,
  faArrowLeft,
  faArrowRight,
  faArrowUp,
  faAt,
  faBalanceScaleLeft,
  faBan,
  faBars,
  faBolt,
  faBook,
  faBoxOpen,
  faBrain,
  faBug,
  faCalculator,
  faCalendar,
  faChartBar,
  faCheck,
  faCheckCircle,
  faCheckSquare,
  faChevronDown,
  faChevronLeft,
  faChevronRight,
  faChevronUp,
  faCircle,
  faClipboardList,
  faClock,
  faClone,
  faCloud,
  faCloudDownloadAlt,
  faCode,
  faCodeBranch,
  faCog,
  faCogs,
  faComment,
  faComments,
  faCopy,
  faCube,
  faCut,
  faDatabase,
  faDotCircle,
  faDownload,
  faEdit,
  faEllipsisH,
  faEllipsisV,
  faEnvelope,
  faEquals,
  faExchangeAlt,
  faExclamationCircle,
  faExclamationTriangle,
  faExpand,
  faExpandAlt,
  faExternalLinkAlt,
  faEye,
  faEyeSlash,
  faFile,
  faFileAlt,
  faFileArchive,
  faFileCode,
  faFileDownload,
  faFileExport,
  faFileImport,
  faFilePdf,
  faFilter,
  faFingerprint,
  faFlask,
  faFolderOpen,
  faFont,
  faGem,
  faGift,
  faGlobe,
  faGlobeAmericas,
  faGraduationCap,
  faGripLinesVertical,
  faGripVertical,
  faHandHoldingUsd,
  faHandPointLeft,
  faHandScissors,
  faHandshake,
  faHashtag,
  faHdd,
  faHistory,
  faHome,
  faHourglass,
  faImage,
  faInbox,
  faInfo,
  faInfoCircle,
  faKey,
  faLanguage,
  faLayerGroup,
  faLightbulb,
  faLink,
  faList,
  faLock,
  faMapSigns,
  faMinusCircle,
  faMousePointer,
  faNetworkWired,
  faPalette,
  faPaperPlane,
  faPause,
  faPauseCircle,
  faPen,
  faPencilAlt,
  faPlay,
  faPlayCircle,
  faPlug,
  faPlus,
  faPlusCircle,
  faPlusSquare,
  faPowerOff,
  faProjectDiagram,
  faQuestion,
  faQuestionCircle,
  faRedo,
  faRemoveFormat,
  faRobot,
  faRss,
  faSatelliteDish,
  faSave,
  faScrewdriver,
  faSearch,
  faSearchMinus,
  faSearchPlus,
  faServer,
  faSignInAlt,
  faSignOutAlt,
  faSlidersH,
  faSmile,
  faSpinner,
  faStickyNote as faSolidStickyNote,
  faStop,
  faStream,
  faSun,
  faSync,
  faSyncAlt,
  faTable,
  faTags,
  faTasks,
  faTerminal,
  faThLarge,
  faThumbsDown,
  faThumbsUp,
  faThumbtack,
  faTimes,
  faTimesCircle,
  faToolbox,
  faTools,
  faTrash,
  faTree,
  faUndo,
  faUnlink,
  faUser,
  faUserCheck,
  faUserCircle,
  faUserFriends,
  faUserLock,
  faUsers,
  faVectorSquare,
  faVideo } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const faVariable: IconDefinition = {
  prefix: 'fas' as IconPrefix,
  iconName: 'variable' as IconName,
  icon: [
    52,
    52,
    [],
    'e001',
    'M42.6,17.8c2.4,0,7.2-2,7.2-8.4c0-6.4-4.6-6.8-6.1-6.8c-2.8,0-5.6,2-8.1,6.3c-2.5,4.4-5.3,9.1-5.3,9.1 l-0.1,0c-0.6-3.1-1.1-5.6-1.3-6.7c-0.5-2.7-3.6-8.4-9.9-8.4c-6.4,0-12.2,3.7-12.2,3.7l0,0C5.8,7.3,5.1,8.5,5.1,9.9 c0,2.1,1.7,3.9,3.9,3.9c0.6,0,1.2-0.2,1.7-0.4l0,0c0,0,4.8-2.7,5.9,0c0.3,0.8,0.6,1.7,0.9,2.7c1.2,4.2,2.4,9.1,3.3,13.5l-4.2,6 c0,0-4.7-1.7-7.1-1.7s-7.2,2-7.2,8.4s4.6,6.8,6.1,6.8c2.8,0,5.6-2,8.1-6.3c2.5-4.4,5.3-9.1,5.3-9.1c0.8,4,1.5,7.1,1.9,8.5 c1.6,4.5,5.3,7.2,10.1,7.2c0,0,5,0,10.9-3.3c1.4-0.6,2.4-2,2.4-3.6c0-2.1-1.7-3.9-3.9-3.9c-0.6,0-1.2,0.2-1.7,0.4l0,0 c0,0-4.2,2.4-5.6,0.5c-1-2-1.9-4.6-2.6-7.8c-0.6-2.8-1.3-6.2-2-9.5l4.3-6.2C35.5,16.1,40.2,17.8,42.6,17.8z',
  ],
}

const faVault: IconDefinition = {
  prefix: 'fas' as IconPrefix,
  iconName: 'vault' as IconName,
  icon: [
    576,
    512,
    [],
    'e006',
    'M64 0C28.7 0 0 28.7 0 64v352c0 35.3 28.7 64 64 64h16l16 32h64l16-32h224l16 32h64l16-32h16c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64H64zm160 320a80 80 0 1 0 0-160a80 80 0 1 0 0 160zm0-240a160 160 0 1 1 0 320a160 160 0 1 1 0-320zm256 141.3V336c0 8.8-7.2 16-16 16s-16-7.2-16-16V221.3c-18.6-6.6-32-24.4-32-45.3c0-26.5 21.5-48 48-48s48 21.5 48 48c0 20.9-13.4 38.7-32 45.3z',
  ],
}

const faXmark: IconDefinition = {
  prefix: 'fas' as IconPrefix,
  iconName: 'xmark' as IconName,
  icon: [
    400,
    400,
    [],
    '',
    'M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z',
  ],
}

const faRefresh: IconDefinition = {
  prefix: 'fas' as IconPrefix,
  iconName: 'refresh' as IconName,

  icon: [
    12,
    13,
    [],
    '',
    'M8.67188 3.64062C7.94531 2.96094 6.98438 2.5625 5.97656 2.5625C4.17188 2.58594 2.60156 3.82812 2.17969 5.53906C2.13281 5.67969 2.01562 5.75 1.89844 5.75H0.5625C0.375 5.75 0.234375 5.60938 0.28125 5.42188C0.773438 2.72656 3.14062 0.6875 6 0.6875C7.54688 0.6875 8.95312 1.32031 10.0078 2.30469L10.8516 1.46094C11.2031 1.10938 11.8125 1.36719 11.8125 1.85938V5C11.8125 5.32812 11.5547 5.5625 11.25 5.5625H8.08594C7.59375 5.5625 7.33594 4.97656 7.6875 4.625L8.67188 3.64062ZM0.75 7.4375H3.89062C4.38281 7.4375 4.64062 8.04688 4.28906 8.39844L3.30469 9.38281C4.03125 10.0625 4.99219 10.4609 6 10.4609C7.80469 10.4375 9.375 9.19531 9.79688 7.48438C9.84375 7.34375 9.96094 7.27344 10.0781 7.27344H11.4141C11.6016 7.27344 11.7422 7.41406 11.6953 7.60156C11.2031 10.2969 8.83594 12.3125 6 12.3125C4.42969 12.3125 3.02344 11.7031 1.96875 10.7188L1.125 11.5625C0.773438 11.9141 0.1875 11.6562 0.1875 11.1641V8C0.1875 7.69531 0.421875 7.4375 0.75 7.4375Z',
  ],
}

function addIcon(icon: IconDefinition) {
  library.add(icon)
}

function addIcons() {
  addIcon(faAngleDoubleLeft)
  addIcon(faAngleDown)
  addIcon(faAngleLeft)
  addIcon(faAngleRight)
  addIcon(faAngleUp)
  addIcon(faArchive)
  addIcon(faArrowLeft)
  addIcon(faArrowRight)
  addIcon(faArrowUp)
  addIcon(faArrowDown)
  addIcon(faAt)
  addIcon(faBan)
  addIcon(faBalanceScaleLeft)
  addIcon(faBars)
  addIcon(faBolt)
  addIcon(faBook)
  addIcon(faBoxOpen)
  addIcon(faBug)
  addIcon(faBrain)
  addIcon(faCalculator)
  addIcon(faCalendar)
  addIcon(faChartBar)
  addIcon(faCheck)
  addIcon(faCheckCircle)
  addIcon(faCheckSquare)
  addIcon(faChevronLeft)
  addIcon(faChevronRight)
  addIcon(faChevronDown)
  addIcon(faChevronUp)
  addIcon(faCircle)
  addIcon(faCode)
  addIcon(faCodeBranch)
  addIcon(faCog)
  addIcon(faCogs)
  addIcon(faComment)
  addIcon(faComments)
  addIcon(faClipboardList)
  addIcon(faClock)
  addIcon(faClone)
  addIcon(faCloud)
  addIcon(faCloudDownloadAlt)
  addIcon(faCopy)
  addIcon(faCube)
  addIcon(faCut)
  addIcon(faDatabase)
  addIcon(faDotCircle)
  addIcon(faGripLinesVertical)
  addIcon(faGripVertical)
  addIcon(faEdit)
  addIcon(faEllipsisH)
  addIcon(faEllipsisV)
  addIcon(faEnvelope)
  addIcon(faEquals)
  addIcon(faEye)
  addIcon(faEyeSlash)
  addIcon(faExclamationTriangle)
  addIcon(faExclamationCircle)
  addIcon(faExpand)
  addIcon(faExpandAlt)
  addIcon(faExternalLinkAlt)
  addIcon(faExchangeAlt)
  addIcon(faFile)
  addIcon(faFileAlt)
  addIcon(faFileArchive)
  addIcon(faFileCode)
  addIcon(faFileDownload)
  addIcon(faFileExport)
  addIcon(faFileImport)
  addIcon(faFilePdf)
  addIcon(faFilter)
  addIcon(faFingerprint)
  addIcon(faFlask)
  addIcon(faFolderOpen)
  addIcon(faFont)
  addIcon(faGift)
  addIcon(faGlobe)
  addIcon(faGlobeAmericas)
  addIcon(faGraduationCap)
  addIcon(faHandHoldingUsd)
  addIcon(faHandScissors)
  addIcon(faHandshake)
  addIcon(faHandPointLeft)
  addIcon(faHashtag)
  addIcon(faUserCheck)
  addIcon(faHdd)
  addIcon(faHistory)
  addIcon(faHome)
  addIcon(faHourglass)
  addIcon(faImage)
  addIcon(faInbox)
  addIcon(faInfo)
  addIcon(faInfoCircle)
  addIcon(faKey)
  addIcon(faLanguage)
  addIcon(faLayerGroup)
  addIcon(faLink)
  addIcon(faList)
  addIcon(faLightbulb)
  addIcon(faLock)
  addIcon(faMapSigns)
  addIcon(faMousePointer)
  addIcon(faNetworkWired)
  addIcon(faPalette)
  addIcon(faPause)
  addIcon(faPauseCircle)
  addIcon(faPen)
  addIcon(faPencilAlt)
  addIcon(faPlay)
  addIcon(faPlayCircle)
  addIcon(faPlug)
  addIcon(faPlus)
  addIcon(faPlusCircle)
  addIcon(faPlusSquare)
  addIcon(faProjectDiagram)
  addIcon(faQuestion)
  addIcon(faQuestionCircle)
  addIcon(faRedo)
  addIcon(faRemoveFormat)
  addIcon(faRobot)
  addIcon(faRss)
  addIcon(faSave)
  addIcon(faSatelliteDish)
  addIcon(faSearch)
  addIcon(faSearchMinus)
  addIcon(faSearchPlus)
  addIcon(faServer)
  addIcon(faScrewdriver)
  addIcon(faSmile)
  addIcon(faSignInAlt)
  addIcon(faSignOutAlt)
  addIcon(faSlidersH)
  addIcon(faSpinner)
  addIcon(faSolidStickyNote)
  addIcon(faStickyNote as IconDefinition)
  addIcon(faStop)
  addIcon(faStream)
  addIcon(faSun)
  addIcon(faSync)
  addIcon(faSyncAlt)
  addIcon(faTable)
  addIcon(faTags)
  addIcon(faTasks)
  addIcon(faTerminal)
  addIcon(faThLarge)
  addIcon(faThumbtack)
  addIcon(faThumbsDown)
  addIcon(faThumbsUp)
  addIcon(faTimes)
  addIcon(faTimesCircle)
  addIcon(faToolbox)
  addIcon(faTools)
  addIcon(faTrash)
  addIcon(faUndo)
  addIcon(faUnlink)
  addIcon(faUser)
  addIcon(faUserCircle)
  addIcon(faUserFriends)
  addIcon(faUsers)
  addIcon(faVariable)
  addIcon(faVault)
  addIcon(faVectorSquare)
  addIcon(faVideo)
  addIcon(faTree)
  addIcon(faUserLock)
  addIcon(faGem)
  addIcon(faXmark)
  addIcon(faDownload)
  addIcon(faPowerOff)
  addIcon(faPaperPlane)
  addIcon(faRefresh)
  addIcon(faMinusCircle)
  addIcon(faAdjust)
}

// 禁用自动添加CSS，由Nuxt模块统一管理
config.autoAddCss = false

// 添加图标库
addIcons()

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.component('FontAwesomeIcon', FontAwesomeIcon)
})
