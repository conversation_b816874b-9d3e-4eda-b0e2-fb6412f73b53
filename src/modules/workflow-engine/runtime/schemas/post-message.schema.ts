import { boolean, literal, number, object, optional, record, string, union } from 'valibot'

const workflowSchema = object({
  id: optional(string()),
  name: optional(string()),
  nodes: optional(record(string(), record(string(), string()))),
  connections: optional(record(string(), record(string(), string()))),
})

const openWorkflowSchema = object({
  command: literal('openWorkflow'),
  workflow: workflowSchema,
  canOpenNDV: optional(boolean()),
  hideNodeIssues: optional(boolean()),
})

const openExecutionSchema = object({
  command: literal('openExecution'),
  executionId: number(),
  executionMode: optional(string()),
  canOpenNDV: optional(boolean()),
  hideNodeIssues: optional(boolean()),
})

const setActiveExecutionSchema = object({
  command: literal('setActiveExecution'),
  executionId: number(),
})

const n8nReadySchema = object({
  command: literal('n8nReady'),
  version: optional(string()),
})

const errorSchema = object({
  command: literal('error'),
  message: string(),
})

const simpleCommandSchema = object({
  command: union([
    literal('openNDV'),
    literal('closeNDV'),
  ]),
})

// 联合所有可能的消息类型
export const postMessageSchema = union([
  openWorkflowSchema,
  openExecutionSchema,
  setActiveExecutionSchema,
  n8nReadySchema,
  errorSchema,
  simpleCommandSchema,
])
