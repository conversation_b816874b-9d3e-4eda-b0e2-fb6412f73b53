import type { IDataObject } from 'n8n-workflow'

import type { ICredentialsResponse, IShareCredentialsPayload } from '#wf/types/interface'

export const credentialsEeService = {
  setCredentialSharedWith(id: string, data: IShareCredentialsPayload) {
    return useRequest<ICredentialsResponse>(`/credentials/${id}/share`, {
      method: 'PUT',
      body: data as unknown as IDataObject,
    })
  },

  moveCredentialToProject(id: string, destinationProjectId: string) {
    return useRequest(`/credentials/${id}/transfer`, {
      method: 'PUT',
      body: {
        destinationProjectId,
      },
    })
  },
}
