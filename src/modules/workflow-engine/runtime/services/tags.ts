import type { CreateOrUpdateTagRequestDto, RetrieveTagQueryDto } from '@n8n/api-types'

import type { ITag } from '#wf/types/interface'

type TagsApiEndpoint = '/tags' | '/annotation-tags'

export function createTagsApi(endpoint: TagsApiEndpoint) {
  return {
    getTags: async (data: RetrieveTagQueryDto): Promise<ITag[]> => {
      return await useRequest<ITag[]>(endpoint, {
        method: 'GET',
        body: data,
      })
    },
    createTag: async (
      data: CreateOrUpdateTagRequestDto,
    ): Promise<ITag> => {
      return await useRequest<ITag>(endpoint, {
        method: 'POST',
        body: data,
      })
    },
    updateTag: async (
      id: string,
      data: CreateOrUpdateTagRequestDto,
    ): Promise<ITag> => {
      return await useRequest<ITag>(`${endpoint}/${id}`, {
        method: 'PATCH',
        body: data,
      })
    },
    deleteTag: async (id: string): Promise<boolean> => {
      return await useRequest<boolean>(`${endpoint}/${id}`, {
        method: 'DELETE',
      })
    },
  }
}
