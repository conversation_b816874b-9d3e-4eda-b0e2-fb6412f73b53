import type { EnvironmentVariable } from '#wf/types/interface'

export const EnvService = {
  getVariables() {
    return useRequest<EnvironmentVariable[]>('/variables')
  },

  createVariable(data: Omit<EnvironmentVariable, 'id'>) {
    return useRequest<EnvironmentVariable>('/variables', {
      method: 'POST',
      body: data,
    })
  },

  updateVariable({ id, ...data }: EnvironmentVariable) {
    return useRequest<EnvironmentVariable>(`/variables/${id}`, {
      method: 'PUT',
      body: data,
    })
  },

  deleteVariable({ id }: { id: EnvironmentVariable['id'] }) {
    return useRequest(`/variables/${id}`, {
      method: 'DELETE',
    })
  },
}
