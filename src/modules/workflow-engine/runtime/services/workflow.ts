import type { OptionsRequestDto, ResourceLocatorRequestDto } from '@n8n/api-types'
import type { JSONSchema7 } from 'json-schema'
import type { INodeListSearchResult, INodePropertyOptions } from 'n8n-workflow'

import type { IWorkflowDataUpdate, IWorkflowDb } from '#wf/types/interface'

import { ApiStatus } from '~/enums/common'

export type GetSchemaPreviewOptions = {
  nodeType: string
  version: number
  resource?: string
  operation?: string
}

const padVersion = (version: number) => {
  return version.toString().split('.').concat(['0', '0']).slice(0, 3).join('.')
}

export const workflowService = {
  updateWorkflow(id: string, data: IWorkflowDataUpdate) {
    return useRequest<IWorkflowDb>(`/workflows/${id}`, {
      method: 'PUT',
      body: data,
    })
  },

  getNodeTypes() {
    return useRequest<unknown>('/workflows/nodes.json')
  },

  getCredentialTypes() {
    return useRequest<unknown>('/workflows/credentials.json')
  },

  getResourceLocatorResults(sendData: ResourceLocatorRequestDto) {
    return useRequest<INodeListSearchResult>('/w/rest/dynamic-node-parameters/resource-locator-results', {
      method: 'POST',
      body: sendData,
      responseAdapter: (data) => {
        return {
          status: ApiStatus.Success,
          result: (data as N8nResponse<INodeListSearchResult[]>).data,
        }
      },
    })
  },

  getSchemaPreview(options: GetSchemaPreviewOptions): Promise<JSONSchema7> {
    const { nodeType, version, resource, operation } = options

    const versionString = padVersion(version)
    const path = ['schemas', nodeType, versionString, resource, operation].filter(Boolean).join('/')

    return useRequest<JSONSchema7>(`${path}.json`, {
      method: 'GET',
    })
  },

  activeWorkflowError(workflowId: IWorkflowDb['id']) {
    // TODO: 需要新增接口
    return useRequest<string>(`/active-workflows/error/${workflowId}`)
  },

  /** 动态获取节点的参数选项 */
  getNodeParameterOptions(sendData: OptionsRequestDto): Promise<INodePropertyOptions[]> {
    return useRequest<INodePropertyOptions[]>('/w/rest/dynamic-node-parameters/options', {
      method: 'POST',
      body: sendData,
      responseAdapter: (data) => {
        return {
          status: ApiStatus.Success,
          result: (data as N8nResponse<INodePropertyOptions[]>).data,
        }
      },
    })
  },
}
