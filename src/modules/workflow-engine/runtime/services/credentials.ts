import type { ICredentialsDecrypted, ICredentialType, INodeCredentialTestRequest, INodeCredentialTestResult } from 'n8n-workflow'

import { workflowService } from '#wf/services/workflow'
import type { ICredentialsDecryptedResponse, ICredentialsResponse } from '#wf/types/interface'

import { ApiStatus } from '~/enums/common'
import type { CreateSpaceCredentialDto, SpaceCredential, SpaceData } from '~/features/space/types/space'

export const credentialsService = {
  getCredentialTypes: async (): Promise<ICredentialType[]> => {
    const data = await workflowService.getCredentialTypes()

    if (Array.isArray(data)) {
      return data
    }

    return []
  },

  getAllCredentials(
    filter?: object,
    includeScopes?: boolean,
  ) {
    return useRequest<ICredentialsResponse[]>('/workflows/credentials', {
      query: {
        ...(includeScopes ? { includeScopes } : {}),
        includeData: true,
        ...(filter ? { filter } : {}),
      },
    })
  },

  async getAllCredentialsForWorkflow(
    options: { workflowId: string, projectId: string },
  ) {
    return useRequest<ICredentialsResponse[]>('/w/rest/credentials/for-workflow', {
      query: options,
      responseAdapter: (data) => {
        return {
          status: ApiStatus.Success,
          result: (data as N8nResponse<ICredentialsResponse[]>).data,
        }
      },
    })
  },

  getCredentialData(id: string) {
    return useRequest<{ data: ICredentialsDecryptedResponse | undefined }>(
      `/workflows/credential/${id}`,
      {
        query: {
          includeData: true,
        },
      },
    )
  },

  createNewCredential(payload: CreateSpaceCredentialDto & { projectId?: SpaceData['id'] }) {
    return useRequest<ICredentialsResponse>('/credentials', {
      method: 'POST',
      body: payload,
    })
  },

  updateCredential(
    id: string,
    data: ICredentialsDecrypted,
  ) {
    return useRequest<ICredentialsResponse>(`/workflows/credential/${id}`, {
      method: 'PUT',
      body: data,
    })
  },

  deleteCredential(id: SpaceCredential['id']) {
    return useRequest<boolean>(`/workflows/credential/${id}`, {
      method: 'DELETE',
    })
  },

  oAuth2CredentialAuthorize(data: ICredentialsResponse) {
    return useRequest<string>('/oauth2-credential/auth', {
      method: 'GET',
      query: data,
    })
  },

  oAuth1CredentialAuthorize(data: ICredentialsResponse) {
    return useRequest<string>('/oauth1-credential/auth', {
      method: 'GET',
      query: data,
    })
  },

  testCredential(data: INodeCredentialTestRequest) {
    return useRequest<INodeCredentialTestResult>('/w/rest/credentials/test', {
      method: 'POST',
      body: data,
      responseAdapter: (data) => {
        return {
          status: ApiStatus.Success,
          result: (data as N8nResponse<INodeCredentialTestResult[]>).data,
        }
      },
    })
  },
}
