import type { ExternalSecretsProvider } from '#wf/types/interface'

export const ExternalSecretsService = {
  getExternalSecrets() {
    return useRequest<Record<string, string[]>>('/external-secrets/secrets')
  },
  getExternalSecretsProviders() {
    return useRequest<ExternalSecretsProvider[]>('/external-secrets/providers')
  },
  getExternalSecretsProvider(id: string) {
    return useRequest<ExternalSecretsProvider>(`/external-secrets/providers/${id}`)
  },
  testExternalSecretsProviderConnection(id: string, data: ExternalSecretsProvider['data']) {
    return useRequest<{ testState: ExternalSecretsProvider['state'] }>(`/external-secrets/providers/${id}/test`, {
      method: 'POST',
      body: data,
    })
  },
  updateProvider(id: string, data: ExternalSecretsProvider['data']) {
    return useRequest<boolean>(`/external-secrets/providers/${id}`, {
      method: 'POST',
      body: data,
    })
  },
  reloadProvider(id: string) {
    return useRequest<{ updated: boolean }>(`/external-secrets/providers/${id}/update`, {
      method: 'POST',
    })
  },
  connectProvider(id: string, connected: boolean) {
    return useRequest<boolean>(`/external-secrets/providers/${id}/connect`, {
      method: 'POST',
      body: { connected },
    })
  },
}
