import type { ResourceLocatorRequestDto } from '@n8n/api-types'
import type { INode, INodeTypeDescription, INodeTypeNameVersion, Workflow } from 'n8n-workflow'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'

import { NodeConnectionType } from '#wf/constants/canvas'
import { StoreKey } from '#wf/constants/store'
import { workflowService } from '#wf/services/workflow'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import type { NodeTypesByTypeNameAndVersion } from '#wf/types/interface'
import { groupNodeTypesByNameAndType } from '#wf/utils/nodeTypeTransforms'
import { omit } from '#wf/utils/typesUtils'

export const useNodeTypesStore = defineStore(StoreKey.NodeTypes, () => {
  const nodeTypes = ref<NodeTypesByTypeNameAndVersion>({})

  const setNodeTypes = (newNodeTypes: INodeTypeDescription[]) => {
    const groupedNodeTypes = groupNodeTypesByNameAndType(newNodeTypes)

    nodeTypes.value = {
      ...nodeTypes.value,
      ...groupedNodeTypes,
    }
  }

  const removeNodeTypes = (nodeTypesToRemove: INodeTypeDescription[]) => {
    nodeTypes.value = nodeTypesToRemove.reduce(
      (oldNodes, newNodeType) => omit(newNodeType.name, oldNodes),
      nodeTypes.value,
    )
  }

  const getNodeTypes = async () => {
    const nodeTypes = await workflowService.getNodeTypes()

    if (Array.isArray(nodeTypes)) {
      setNodeTypes(nodeTypes)
    }
  }

  const getNodesInformation = async (
    nodeInfos: INodeTypeNameVersion[],
    replace = true,
  ): Promise<INodeTypeDescription[]> => {
    // HACK:
    if (nodeInfos.length === 0 || replace) {
      //
    }

    return Promise.resolve([])
    // const nodesInformation = await nodeTypesApi.getNodesInformation(
    //   rootStore.restApiContext,
    //   nodeInfos,
    // )

    // nodesInformation.forEach((nodeInformation) => {
    //   if (nodeInformation.translation) {
    //     const nodeType = nodeInformation.name.replace('n8n-nodes-base.', '')

    //     addNodeTranslation({ [nodeType]: nodeInformation.translation }, rootStore.defaultLocale)
    //   }
    // })

    // if (replace) {
    //   setNodeTypes(nodesInformation)
    // }

    // return nodesInformation
  }

  const getNodeType = computed(() => {
    return (nodeTypeName: string, version?: number): INodeTypeDescription | null => {
      const nodeVersions = nodeTypes.value[nodeTypeName]

      if (!nodeVersions) {
        return null
      }

      const versionNumbers = Object.keys(nodeVersions).map(Number)
      const nodeType = nodeVersions[version ?? Math.max(...versionNumbers)]

      return nodeType ?? null
    }
  })

  const allNodeTypes = computed(() => {
    return Object.values(nodeTypes.value).reduce<INodeTypeDescription[]>(
      (allNodeTypes, nodeType) => {
        const versionNumbers = Object.keys(nodeType).map(Number)
        const allNodeVersions = versionNumbers.map((version) => nodeType[version])

        return [...allNodeTypes, ...allNodeVersions]
      },
      [],
    )
  })

  const allLatestNodeTypes = computed(() => {
    return Object.values(nodeTypes.value).reduce<INodeTypeDescription[]>(
      (allLatestNodeTypes, nodeVersions) => {
        const versionNumbers = Object.keys(nodeVersions).map(Number)
        const latestNodeVersion = nodeVersions[Math.max(...versionNumbers)]

        if (!latestNodeVersion) {
          return allLatestNodeTypes
        }

        return [...allLatestNodeTypes, latestNodeVersion]
      },
      [],
    )
  })

  const isConfigNode = computed(() => {
    return (workflow: Workflow, node: INode, nodeTypeName: string): boolean => {
      if (!workflow.nodes[node.name]) {
        return false
      }

      const nodeType = getNodeType.value(nodeTypeName)

      if (!nodeType) {
        return false
      }

      const outputs = NodeHelpers.getNodeOutputs(workflow, node, nodeType)
      const outputTypes = NodeHelpers.getConnectionTypes(outputs)

      return outputTypes
        ? outputTypes.filter((output) => (output as unknown as NodeConnectionType) !== NodeConnectionType.Main).length > 0
        : false
    }
  })

  const isTriggerNode = computed(() => {
    return (nodeTypeName: string) => {
      const nodeType = getNodeType.value(nodeTypeName)

      return !!(nodeType && nodeType.group.includes('trigger'))
    }
  })

  const isConfigurableNode = computed(() => {
    return (workflow: Workflow, node: INode, nodeTypeName: string): boolean => {
      const nodeType = getNodeType.value(nodeTypeName)

      if (nodeType === null) {
        return false
      }

      const inputs = NodeHelpers.getNodeInputs(workflow, node, nodeType)
      const inputTypes = NodeHelpers.getConnectionTypes(inputs)

      return inputTypes
        ? inputTypes.filter((input) => (input as unknown as NodeConnectionType) !== NodeConnectionType.Main).length > 0
        : false
    }
  })

  const visibleNodeTypes = computed(() => {
    return allLatestNodeTypes.value.filter((nodeType: INodeTypeDescription) => !nodeType.hidden)
  })

  const visibleNodeTypesByOutputConnectionTypeNames = computed(() => {
    const nodesByOutputType = visibleNodeTypes.value.reduce(
      (acc, node) => {
        const outputTypes = node.outputs

        if (Array.isArray(outputTypes)) {
          outputTypes.forEach((value) => {
            const outputType = typeof value === 'string' ? value : value.type

            if (!acc[outputType]) {
              acc[outputType] = []
            }

            acc[outputType].push(node.name)
          })
        }
        else {
          // If outputs is not an array, it must be a string expression
          // in which case we'll try to match all possible non-main output types that are supported
          const connectorTypes: NodeConnectionType[] = [
            NodeConnectionType.AiVectorStore,
            NodeConnectionType.AiChain,
            NodeConnectionType.AiDocument,
            NodeConnectionType.AiEmbedding,
            NodeConnectionType.AiLanguageModel,
            NodeConnectionType.AiMemory,
            NodeConnectionType.AiOutputParser,
            NodeConnectionType.AiTextSplitter,
            NodeConnectionType.AiTool,
          ]
          connectorTypes.forEach((outputType: NodeConnectionType) => {
            if (outputTypes.includes(outputType)) {
              acc[outputType] = acc[outputType] || []
              acc[outputType].push(node.name)
            }
          })
        }

        return acc
      },
      {} as { [key: string]: string[] },
    )

    return nodesByOutputType
  })

  const visibleNodeTypesByInputConnectionTypeNames = computed(() => {
    const nodesByOutputType = visibleNodeTypes.value.reduce(
      (acc, node) => {
        const inputTypes = node.inputs

        if (Array.isArray(inputTypes)) {
          inputTypes.forEach(
            (value) => {
              const outputType = typeof value === 'string' ? value : value.type

              if (!acc[outputType]) {
                acc[outputType] = []
              }

              acc[outputType].push(node.name)
            },
          )
        }

        return acc
      },
      {} as { [key: string]: string[] },
    )

    return nodesByOutputType
  })

  const getFullNodesProperties = async (nodesToBeFetched: INodeTypeNameVersion[]) => {
    const credentialsStore = useCredentialsStore()
    await credentialsStore.fetchCredentialTypes(true)
    await getNodesInformation(nodesToBeFetched)
  }

  const getResourceLocatorResults = async (sendData: ResourceLocatorRequestDto) => {
    return await workflowService.getResourceLocatorResults(sendData)
  }

  return {
    nodeTypes,
    allNodeTypes,
    getNodeType,
    getNodesInformation,

    getNodeTypes,
    removeNodeTypes,

    allLatestNodeTypes,
    visibleNodeTypes,

    isConfigNode,
    isConfigurableNode,
    isTriggerNode,

    getFullNodesProperties,
    getResourceLocatorResults,

    visibleNodeTypesByOutputConnectionTypeNames,
    visibleNodeTypesByInputConnectionTypeNames,
  }
})
