import { StoreKey } from '#wf/constants/store'

export const useTemplatesStore = defineStore(StoreKey.WorkflowTemplate, () => {
  const websiteTemplateRepositoryParameters = computed(() => {
    const defaultParameters: Record<string, string> = {
      utm_source: 'n8n_app',
      utm_medium: 'template_library',
      utm_instance: 'http://localhost:8080/',
      utm_n8n_version: '1.80.0',
      utm_awc: '0',
    }

    return new URLSearchParams({
      ...defaultParameters,
    })
  })

  return {
    websiteTemplateRepositoryParameters,
  }
})
