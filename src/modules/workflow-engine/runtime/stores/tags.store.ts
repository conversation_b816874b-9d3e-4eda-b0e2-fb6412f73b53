import { StoreKey } from '#wf/constants/store'
import { createTagsApi } from '#wf/services/tags'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { ITag } from '#wf/types/interface'

const apiMapping = {
  [StoreKey.Tags]: createTagsApi('/tags'),
  [StoreKey.AnnotationTags]: createTagsApi('/annotation-tags'),
}

const createTagsStore = (id: StoreKey.Tags | StoreKey.AnnotationTags) => {
  const tagsApi = apiMapping[id]

  return defineStore(
    id,
    () => {
      const tagsById = ref<Record<string, ITag>>({})
      const loading = ref(false)
      const fetchedAll = ref(false)
      const fetchedUsageCount = ref(false)

      const workflowStore = useWorkflowStore()

      // Computed

      const allTags = computed(() => {
        return Object.values(tagsById.value).sort((a, b) => a.name.localeCompare(b.name))
      })

      const isLoading = computed(() => loading.value)

      const hasTags = computed(() => Object.keys(tagsById.value).length > 0)

      // Methods

      const setAllTags = (loadedTags: ITag[]) => {
        tagsById.value = loadedTags.reduce((accu: { [id: string]: ITag }, tag: ITag) => {
          accu[tag.id] = tag

          return accu
        }, {})
        fetchedAll.value = true
      }

      const upsertTags = (toUpsertTags: ITag[]) => {
        toUpsertTags.forEach((toUpsertTag) => {
          const tagId = toUpsertTag.id
          const currentTag = tagsById.value[tagId]

          if (currentTag) {
            const newTag = {
              ...currentTag,
              ...toUpsertTag,
            }
            tagsById.value = {
              ...tagsById.value,
              [tagId]: newTag,
            }
          }
          else {
            tagsById.value = {
              ...tagsById.value,
              [tagId]: toUpsertTag,
            }
          }
        })
      }

      const deleteTag = (id: string) => {
        const { [id]: deleted, ...rest } = tagsById.value
        tagsById.value = rest
      }

      const fetchAll = async (params?: { force?: boolean, withUsageCount?: boolean }) => {
        const { force = false, withUsageCount = false } = params || {}

        if (!force && fetchedAll.value && fetchedUsageCount.value === withUsageCount) {
          return Object.values(tagsById.value)
        }

        loading.value = true
        const retrievedTags = await tagsApi.getTags({
          withUsageCount,
        })
        setAllTags(retrievedTags)
        loading.value = false

        return retrievedTags
      }

      const create = async (
        name: string,
        { incrementExisting }: { incrementExisting?: boolean } = {},
      ) => {
        let tagName = name

        if (incrementExisting) {
          const tagNameRegex = new RegExp(tagName)
          const existingTags = allTags.value.filter((tag) => tagNameRegex.test(tag.name))

          if (existingTags.length > 0) {
            tagName = `${tagName} (${existingTags.length + 1})`
          }
        }

        const createdTag = await tagsApi.createTag({ name: tagName })
        upsertTags([createdTag])

        return createdTag
      }

      const rename = async ({ id, name }: { id: string, name: string }) => {
        const updatedTag = await tagsApi.updateTag(id, { name })
        upsertTags([updatedTag])

        return updatedTag
      }

      const deleteTagById = async (id: string) => {
        const deleted = await tagsApi.deleteTag(id)

        if (deleted) {
          deleteTag(id)
          workflowStore.removeWorkflowTagId(id)
        }

        return deleted
      }

      return {
        allTags,
        isLoading,
        hasTags,
        tagsById,
        fetchAll,
        create,
        rename,
        deleteTagById,
        upsertTags,
        deleteTag,
      }
    },
    {},
  )
}

export const useTagsStore = createTagsStore(StoreKey.Tags)

export const useAnnotationTagsStore = createTagsStore(StoreKey.AnnotationTags)
