import type { PushMessage } from '@n8n/api-types'

import { StoreKey } from '#wf/constants/store'
import { useEventSourceClient } from '#wf/push-connection/useEventSourceClient'
import { useWebSocketClient } from '#wf/push-connection/useWebSocketClient'

import { useRootStore } from './root.store'
import { useSettingsStore } from './setting.store'

export type OnPushMessageHandler = (event: PushMessage) => void

// 定义 store 的可能状态
type StoreState = 'initial' | 'initializing' | 'ready' | 'error'

// 定义客户端类型，方便类型检查
type PushClient = ReturnType<typeof useWebSocketClient> | ReturnType<typeof useEventSourceClient>

/**
 * Store for managing a push connection to the server
 */
export const usePushConnectionStore = defineStore(StoreKey.PushConnection, () => {
  const rootStore = useRootStore()
  const settingsStore = useSettingsStore()

  // 状态管理
  const state = ref<StoreState>('initial')
  const error = ref<Error | null>(null)
  const isInitialized = computed(() => state.value === 'ready')

  /**
   * Queue of messages to be sent to the server. Messages are queued if
   * the connection is down.
   */
  const outgoingQueue = ref<unknown[]>([])

  /** Whether the connection has been requested */
  const isConnectionRequested = ref(false)

  const onMessageReceivedHandlers = ref<OnPushMessageHandler[]>([])

  const addEventListener = (handler: OnPushMessageHandler) => {
    onMessageReceivedHandlers.value.push(handler)

    return () => {
      const index = onMessageReceivedHandlers.value.indexOf(handler)

      if (index !== -1) {
        onMessageReceivedHandlers.value.splice(index, 1)
      }
    }
  }

  // 将客户端声明为 ref，便于后续初始化
  const client = ref<PushClient | null>(null)

  /**
   * Process a newly received message
   */
  async function onMessage(data: unknown) {
    let receivedData: PushMessage

    try {
      receivedData = JSON.parse(data as string)
    }
    catch {
      return
    }

    onMessageReceivedHandlers.value.forEach((handler) => handler(receivedData))
  }

  /**
   * 获取连接 URL
   */
  const getConnectionUrl = () => {
    const restUrl = rootStore.restUrl
    const url = `/push?pushRef=${rootStore.pushRef}`

    // 确保 settingsStore 已初始化
    const useWebSockets = settingsStore.pushBackend === 'websocket'

    if (useWebSockets) {
      const { protocol, host } = window.location

      const baseUrl = restUrl.startsWith('http')
        ? restUrl.replace(/^http/, 'ws')
        : `${protocol === 'https:' ? 'wss' : 'ws'}://${host + restUrl}`

      return `${baseUrl}${url}`
    }
    else {
      return `${restUrl}${url}`
    }
  }

  function serializeAndSend(message: unknown) {
    if (!client.value) {
      // 如果客户端未初始化，将消息加入队列
      outgoingQueue.value.push(message)

      return
    }

    if (client.value.isConnected) {
      client.value.sendMessage(JSON.stringify(message))
    }
    else {
      outgoingQueue.value.push(message)
    }
  }

  /**
   * 确保客户端已正确初始化
   */
  const ensureInitialized = async (): Promise<void> => {
    // 如果已初始化，直接返回
    if (state.value === 'ready') {
      return
    }

    // 如果正在初始化，等待初始化完成
    if (state.value === 'initializing') {
      // 轮询状态直到初始化完成
      return new Promise((resolve, reject) => {
        const checkInterval = setInterval(() => {
          if (state.value === 'ready') {
            clearInterval(checkInterval)
            resolve()
          }
          else if (state.value === 'error') {
            clearInterval(checkInterval)
            reject(error.value)
          }
        }, 50)
      })
    }

    // 开始初始化
    state.value = 'initializing'

    try {
      // 确保 settingsStore 已初始化
      if (!settingsStore.isInitialized) {
        await settingsStore.initialize()
      }

      // 现在可以安全地初始化客户端
      const url = getConnectionUrl()
      const useWebSockets = settingsStore.pushBackend === 'websocket'

      client.value = useWebSockets
        ? useWebSocketClient({ url, onMessage })
        : useEventSourceClient({ url, onMessage })

      // 设置客户端连接状态监听
      watch(() => client.value?.isConnected, (didConnect) => {
        if (!didConnect) {
          return
        }

        // Send any buffered messages
        if (outgoingQueue.value.length) {
          for (const message of outgoingQueue.value) {
            serializeAndSend(message)
          }

          outgoingQueue.value = []
        }
      })

      state.value = 'ready'
    }
    catch (err) {
      state.value = 'error'
      error.value = err as Error
      throw err
    }
  }

  const pushConnect = async () => {
    await ensureInitialized()

    isConnectionRequested.value = true

    if (client.value) {
      client.value.connect()
    }
  }

  /**
   * 断开与服务器的连接。
   * 此方法可在任何状态下安全调用。
   */
  const pushDisconnect = () => {
    isConnectionRequested.value = false

    if (client.value) {
      client.value.disconnect()
    }

    // 确保状态重置
    if (state.value !== 'initial') {
      state.value = 'initial'
    }
  }

  /** Removes all buffered messages from the sent queue */
  const clearQueue = () => {
    outgoingQueue.value = []
  }

  const isConnected = computed(() => {
    if (client.value) {
      return client.value.isConnected
    }

    return false
  })

  return {
    isInitialized,
    isConnected,
    isConnectionRequested,
    onMessageReceivedHandlers,
    addEventListener,
    pushConnect,
    pushDisconnect,
    send: serializeAndSend,
    clearQueue,
    ensureInitialized,
  }
})
