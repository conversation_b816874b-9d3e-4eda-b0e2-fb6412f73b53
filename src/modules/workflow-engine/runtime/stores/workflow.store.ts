import type { PushPayload } from '@n8n/api-types'
import { findLast } from 'lodash-es'
import type { ExecutionSummary, IConnection, IConnections, IDataObject, INode, INodeConnections, INodeCredentials, INodeCredentialsDetails, INodeExecutionData, INodeIssueData, INodeIssueObjectProperty, INodeParameters, INodeType, INodeTypes, IPinData, IRunData, ITaskData } from 'n8n-workflow'
import { CHAT_TRIGGER_NODE_TYPE, ERROR_TRIGGER_NODE_TYPE, FORM_NODE_TYPE, START_NODE_TYPE, WAIT_NODE_TYPE } from 'n8n-workflow/dist/Constants.js'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'
import { deepCopy } from 'n8n-workflow/dist/utils.js'
import { Workflow } from 'n8n-workflow/dist/Workflow.js'

import { useExecutingNode } from '#wf/composables/useExecutingNode'
import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { NodeConnectionType } from '#wf/constants/canvas'
import { PLACEHOLDER_EMPTY_WORKFLOW_ID } from '#wf/constants/common'
import { StoreKey } from '#wf/constants/store'
import { workflowService } from '#wf/services/workflow'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useRootStore } from '#wf/stores/root.store'
import { useUIStore } from '#wf/stores/ui.store'
import type { IExecutionPushResponse, IExecutionResponse, INodeMetadata, INodeUi, INodeUpdatePropertiesInformation, IStartRunData, IUpdateInformation, IUsedCredential, IWorkflowDataUpdate, IWorkflowDb, NodeMetadataMap, WorkflowNode } from '#wf/types/interface'
import { unflattenExecutionData } from '#wf/utils/apiUtils'
import { getCredentialOnlyNodeTypeName } from '#wf/utils/credentialOnlyNodes'
import { clearPopupWindowState, openFormPopupWindow } from '#wf/utils/executionUtils'
import { isObject } from '#wf/utils/objectUtils'
import { getPairedItemsMapping } from '#wf/utils/pairedItemUtils'
import { isJsonKeyObject, isPresent, stringSizeInBytes } from '#wf/utils/typesUtils'

import { AppService } from '~/features/space/services/app'
import { ExecutionService } from '~/features/space/services/execution'

let cachedWorkflowKey: string | null = ''
let cachedWorkflow: Workflow | null = null

const defaults: Omit<IWorkflowDb, 'id'> & { settings: NonNullable<IWorkflowDb['settings']> } = {
  name: '',
  active: false,
  createdAt: -1,
  updatedAt: -1,
  nodes: [],
  connections: {},
  settings: {
    executionOrder: 'v1',
  },
  tags: [],
  pinData: {},
  versionId: '',
  usedCredentials: [],
}

const createEmptyWorkflow = (): IWorkflowDb => ({
  id: PLACEHOLDER_EMPTY_WORKFLOW_ID,
  ...defaults,
})

export const useWorkflowStore = defineStore(
  StoreKey.Workflow,
  () => {
    const isEditing = ref(false)
    const isDragging = ref(false)
    const isConnecting = ref(false)

    const route = useRoute()

    const isCanvasReadOnly = computed(() => {
      return isWorkflowDemo(route.path)
    })

    const workflow = ref<IWorkflowDb>(createEmptyWorkflow())
    const workflowId = computed(() => workflow.value.id)
    const workflowVersionId = computed(() => workflow.value.versionId)
    const workflowName = computed(() => workflow.value.name)
    const workflowSettings = computed(() => workflow.value.settings ?? { ...defaults.settings })

    const activeWorkflowExecution = ref<ExecutionSummary | null>(null)
    const chatPartialExecutionDestinationNode = ref<string | null>(null)
    const executionWaitingForWebhook = ref(false)

    const pinnedWorkflowData = computed(() => workflow.value.pinData)
    const shouldReplaceInputDataWithPinData = computed(() => {
      return !activeWorkflowExecution.value || activeWorkflowExecution.value.mode === 'manual'
    })

    const currentWorkflowExecutions = ref<ExecutionSummary[]>([])

    const uiStore = useUIStore()
    const rootStore = useRootStore()
    const usedCredentials = ref<Record<string, IUsedCredential>>({})

    const activeExecutionId = ref<string | null>(null)
    const subWorkflowExecutionError = ref<Error | null>(null)

    const workflowExecutionData = ref<IExecutionResponse | null>(null)
    const executedNode = computed(() => workflowExecutionData.value?.executedNode)

    const nodeMetadata = ref<NodeMetadataMap>({})

    const nodeHelpers = useNodeHelpers()
    const workflowHelpers = useWorkflowHelpers()

    const getWorkflowRunData = computed<IRunData | null>(() => {
      if (!workflowExecutionData.value?.data?.resultData) {
        return null
      }

      return workflowExecutionData.value.data.resultData.runData
    })

    // Names of all nodes currently on canvas.
    const canvasNames = computed(() => new Set(workflow.value.nodes.map((n) => n.name)))

    const getWorkflowExecution = computed(() => workflowExecutionData.value)

    const nodeTypesStore = useNodeTypesStore()

    const workflowTriggerNodes = computed(() =>
      workflow.value.nodes.filter((node: INodeUi) => {
        const nodeType = nodeTypesStore.getNodeType(node.type, node.typeVersion)

        return nodeType && nodeType.group.includes('trigger')
      }),
    )

    const isWorkflowRunning = computed(() => {
      if (uiStore.isActionActive.workflowRunning) {
        return true
      }

      if (activeExecutionId.value) {
        const execution = getWorkflowExecution

        if (execution.value && execution.value.status === 'waiting' && !execution.value.finished) {
          return true
        }
      }

      return false
    })

    // 返回一个浅拷贝的节点，这意味着所有较低级别的数据仍然只被引用，但顶层对象是一个不同的对象。
    // 这具有速度快且不会引起 vuex 问题（当工作流替换节点参数时）的优势。
    const getNodes = (): INodeUi[] => {
      return workflow.value.nodes.map((node) => ({ ...node }))
    }

    const getNodeTypes = (): INodeTypes => {
      const nodeTypes: INodeTypes = {
        nodeTypes: {},
        init: async (): Promise<void> => {},
        getByNameAndVersion: (nodeType: string, version?: number): INodeType | undefined => {
          const nodeTypeDescription = nodeTypesStore.getNodeType(nodeType, version)

          if (nodeTypeDescription === null) {
            return undefined
          }

          return {
            description: nodeTypeDescription,
            // 由于前端没有触发/轮询函数，所以使用信息来判断哪些是触发节点

            // @ts-ignore
            trigger:
              (![ERROR_TRIGGER_NODE_TYPE, START_NODE_TYPE].includes(nodeType)
                && nodeTypeDescription.inputs.length === 0
                && !nodeTypeDescription.webhooks)
              || undefined,
          }
        },
      } as unknown as INodeTypes

      return nodeTypes
    }

    const updateCachedWorkflow = () => {
      const nodeTypes = getNodeTypes()
      const nodes = getNodes()
      const connections = workflow.value.connections

      cachedWorkflow = new Workflow({
        id: workflowId.value,
        name: workflowName.value,
        nodes,
        connections,
        active: false,
        nodeTypes,
        settings: workflowSettings.value,
        pinData: pinnedWorkflowData.value,
      })
    }

    const setNodePristine = (nodeName: string, isPristine: boolean): void => {
      nodeMetadata.value[nodeName].pristine = isPristine
    }

    const addConnection = (data: { connection: IConnection[] }): void => {
      if (data.connection.length !== 2) {
        // All connections need two entries
        // TODO: Check if there is an error or whatever that is supposed to be returned
        return
      }

      const sourceData: IConnection = data.connection[0]
      const destinationData: IConnection = data.connection[1]

      // Check if source node and type exist already and if not add them
      if (!Object.hasOwn(workflow.value.connections, sourceData.node)) {
        workflow.value = {
          ...workflow.value,
          connections: {
            ...workflow.value.connections,
            [sourceData.node]: {},
          },
        }
      }

      if (!Object.hasOwn(workflow.value.connections[sourceData.node], sourceData.type)) {
        workflow.value = {
          ...workflow.value,
          connections: {
            ...workflow.value.connections,
            [sourceData.node]: {
              ...workflow.value.connections[sourceData.node],
              [sourceData.type]: [],
            },
          },
        }
      }

      if (
        workflow.value.connections[sourceData.node][sourceData.type].length
        < sourceData.index + 1
      ) {
        for (
          let i = workflow.value.connections[sourceData.node][sourceData.type].length;
          i <= sourceData.index;
          i++
        ) {
          workflow.value.connections[sourceData.node][sourceData.type].push([])
        }
      }

      // Check if the same connection exists already
      const checkProperties = ['index', 'node', 'type'] as Array<keyof IConnection>
      let propertyName: keyof IConnection
      let connectionExists = false

      const nodeConnections = workflow.value.connections[sourceData.node][sourceData.type]
      const connectionsToCheck = nodeConnections[sourceData.index]

      if (connectionsToCheck) {
        connectionLoop: for (const existingConnection of connectionsToCheck) {
          for (propertyName of checkProperties) {
            if (existingConnection[propertyName] !== destinationData[propertyName]) {
              continue connectionLoop
            }
          }

          connectionExists = true
          break
        }
      }

      // Add the new connection if it does not exist already
      if (!connectionExists) {
        nodeConnections[sourceData.index] = nodeConnections[sourceData.index] ?? []
        const connections = nodeConnections[sourceData.index]

        if (connections) {
          connections.push(destinationData)
        }
      }
    }

    const removeConnection = (data: { connection: IConnection[] }): void => {
      const sourceData = data.connection[0]
      const destinationData = data.connection[1]

      if (!Object.hasOwn(workflow.value.connections, sourceData.node)) {
        return
      }

      if (!Object.hasOwn(workflow.value.connections[sourceData.node], sourceData.type)) {
        return
      }

      if (
        workflow.value.connections[sourceData.node][sourceData.type].length
        < sourceData.index + 1
      ) {
        return
      }

      uiStore.stateIsDirty = true

      const connections
        = workflow.value.connections[sourceData.node][sourceData.type][sourceData.index]

      if (!connections) {
        return
      }

      for (const index in connections) {
        if (
          connections[index].node === destinationData.node
          && connections[index].type === destinationData.type
          && connections[index].index === destinationData.index
        ) {
          // Found the connection to remove
          connections.splice(parseInt(index, 10), 1)
        }
      }
    }

    // MARK: 工作流状态管理

    const workflowsById = ref<Record<string, IWorkflowDb>>({})
    const isWorkflowActive = computed(() => workflow.value.active)

    const addWorkflow = (workflow: IWorkflowDb) => {
      workflowsById.value = {
        ...workflowsById.value,
        [workflow.id]: {
          ...workflowsById.value[workflow.id],
          ...deepCopy(workflow),
        },
      }
    }

    async function fetchWorkflow(id: IWorkflowDb['id']): Promise<IWorkflowDb> {
      const workflowData = await AppService.getWorkflow(id)

      addWorkflow(workflowData)

      return workflowData
    }

    const setActive = (active: IWorkflowDb['active']) => {
      workflow.value.active = active
    }

    const setWorkflowId = (id?: IWorkflowDb['id']) => {
      workflow.value.id = !id || id === 'new' ? PLACEHOLDER_EMPTY_WORKFLOW_ID : id
    }

    const setWorkflowName = (data: { newName: IWorkflowDb['name'], setStateDirty: boolean }) => {
      if (data.setStateDirty) {
        uiStore.stateIsDirty = true
      }

      workflow.value.name = data.newName

      if (
        workflow.value.id !== PLACEHOLDER_EMPTY_WORKFLOW_ID
        && workflowsById.value[workflow.value.id]
      ) {
        workflowsById.value[workflow.value.id].name = data.newName
      }
    }

    const setWorkflowSettings = (workflowSettings: IWorkflowDb['settings']) => {
      // workflow.value = {
      //   ...workflow.value,
      //   settings: workflowSettings as IWorkflowDb['settings'],
      // }
      workflow.value.settings = workflowSettings
    }

    const setWorkflowPinData = (data: IWorkflowDb['pinData'] = {}) => {
      const validPinData = Object.keys(data).reduce((accu, nodeName) => {
        accu[nodeName] = data[nodeName].map((item) => {
          if (!isJsonKeyObject(item)) {
            return { json: item }
          }

          return item
        })

        return accu
      }, {} as IPinData)

      workflow.value.pinData = validPinData
      updateCachedWorkflow()
    }

    const setWorkflowVersionId = (versionId: IWorkflowDb['versionId']) => {
      workflow.value.versionId = versionId
    }

    const setWorkflowMetadata = (metadata: IWorkflowDb['meta'] | undefined): void => {
      workflow.value.meta = metadata
    }

    const setWorkflowScopes = (scopes: IWorkflowDb['scopes']): void => {
      workflow.value.scopes = scopes
    }

    const setUsedCredentials = (data: IUsedCredential[]) => {
      workflow.value.usedCredentials = data
      usedCredentials.value = data.reduce<{ [name: string]: IUsedCredential }>((accu, credential) => {
        accu[credential.id] = credential

        return accu
      }, {})
    }

    const setNodes = (nodes: IWorkflowDb['nodes']): void => {
      workflow.value.nodes = nodes
      nodes.forEach((node) => {
        if (!node.id) {
          nodeHelpers.assignNodeId(node)
        }

        if (!nodeMetadata.value[node.name]) {
          nodeMetadata.value[node.name] = { pristine: true }
        }
      })
    }

    const setConnections = (connections: IWorkflowDb['connections'], updateWorkflow = false): void => {
      workflow.value.connections = connections

      if (updateWorkflow) {
        updateCachedWorkflow()
      }
    }

    const setWorkflow = (value: IWorkflowDb) => {
      workflow.value = {
        ...value,
        ...(!Object.hasOwn(value, 'id') ? { id: PLACEHOLDER_EMPTY_WORKFLOW_ID } : {}),
        ...(!Object.hasOwn(value, 'active') ? { active: defaults.active } : {}),
        ...(!Object.hasOwn(value, 'connections') ? { connections: defaults.connections } : {}),
        ...(!Object.hasOwn(value, 'createdAt') ? { createdAt: defaults.createdAt } : {}),
        ...(!Object.hasOwn(value, 'updatedAt') ? { updatedAt: defaults.updatedAt } : {}),
        ...(!Object.hasOwn(value, 'nodes') ? { nodes: defaults.nodes } : {}),
        ...(!Object.hasOwn(value, 'settings') ? { settings: { ...defaults.settings } } : {}),
      }
    }

    const setWorkflowPartial = (data: Partial<IWorkflowDb>) => {
      workflow.value = {
        ...workflow.value,
        ...data,
      }
    }

    const resetWorkflow = () => {
      workflow.value = createEmptyWorkflow()
    }

    const updateNodeAtIndex = (nodeIndex: number, nodeData: Partial<INodeUi>): void => {
      if (nodeIndex !== -1) {
        Object.assign(workflow.value.nodes[nodeIndex], nodeData)
      }
    }

    const setNodeValue = (updateInformation: IUpdateInformation): void => {
      // Find the node that should be updated
      const nodeIndex = workflow.value.nodes.findIndex((node) => {
        return node.name === updateInformation.name
      })

      if (nodeIndex === -1 || !updateInformation.key) {
        throw new Error(
          `Node with the name "${updateInformation.name}" could not be found to set parameter.`,
        )
      }

      uiStore.stateIsDirty = true

      updateNodeAtIndex(nodeIndex, {
        [updateInformation.key]: updateInformation.value,
      })

      if (updateInformation.key !== 'position') {
        nodeMetadata.value[workflow.value.nodes[nodeIndex].name].parametersLastUpdatedAt = Date.now()
      }
    }

    const getWorkflow = (nodes: INodeUi[], connections: IConnections, copyData?: boolean): Workflow => {
      const nodeTypes = getNodeTypes()
      let cachedWorkflowId: string | undefined = workflow.value.id

      if (cachedWorkflowId && cachedWorkflowId === PLACEHOLDER_EMPTY_WORKFLOW_ID) {
        cachedWorkflowId = undefined
      }

      cachedWorkflow = new Workflow({
        id: cachedWorkflowId,
        name: workflowName.value,
        nodes: copyData ? deepCopy(nodes) : nodes,
        connections: copyData ? deepCopy(connections) : connections,
        active: false,
        nodeTypes,
        settings: workflowSettings.value,
        pinData: pinnedWorkflowData.value,
      })

      return cachedWorkflow
    }

    const getCurrentWorkflow = (copyData?: boolean): Workflow => {
      const nodes = workflow.value.nodes
      const connections = workflow.value.connections
      const cacheKey = JSON.stringify({ nodes, connections })

      if (!copyData && cachedWorkflow && cacheKey === cachedWorkflowKey) {
        return cachedWorkflow
      }

      cachedWorkflowKey = cacheKey

      return getWorkflow(nodes, connections, copyData)
    }

    const nodesByName = computed(() => {
      return workflow.value.nodes.reduce<Record<string, WorkflowNode>>((acc, node) => {
        acc[node.name] = node

        return acc
      }, {})
    })

    const getNodeById = (nodeId: string): INodeUi | undefined => {
      return workflow.value.nodes.find((node) => node.id === nodeId)
    }

    const getNodeByName = (nodeName: string): WorkflowNode | null => {
      return nodesByName.value[nodeName] || null
    }

    const pinDataByNodeName = (nodeName: string): INodeExecutionData[] | undefined => {
      if (!workflow.value.pinData?.[nodeName]) {
        return undefined
      }

      return workflow.value.pinData[nodeName].map((item) => item.json) as INodeExecutionData[]
    }

    function getWorkflowResultDataByNodeName(nodeName: string): ITaskData[] | null {
      if (getWorkflowRunData.value === null) {
        return null
      }

      if (!Object.hasOwn(getWorkflowRunData.value, nodeName)) {
        return null
      }

      return getWorkflowRunData.value[nodeName]
    }

    const { executingNode, addExecutingNode, removeExecutingNode, clearNodeExecutionQueue } = useExecutingNode()

    const isNodeExecuting = (nodeName: string): boolean => {
      return executingNode.value.includes(nodeName)
    }

    const setNodePositionById = (id: string, position: INodeUi['position']): void => {
      const node = workflow.value.nodes.find((n) => n.id === id)

      if (node) {
        setNodeValue({ name: node.name, key: 'position', value: position })
      }
    }

    function outgoingConnectionsByNodeName(nodeName: string): INodeConnections {
      if (Object.hasOwn(workflow.value.connections, nodeName)) {
        return workflow.value.connections[nodeName] as unknown as INodeConnections
      }

      return {}
    }

    function incomingConnectionsByNodeName(nodeName: string): INodeConnections {
      return getCurrentWorkflow().connectionsByDestinationNode[nodeName] ?? {}
    }

    function clearNodeExecutionData(nodeName: string): void {
      if (!workflowExecutionData.value?.data) {
        return
      }

      const { [nodeName]: removedRunData, ...remainingRunData }
        = workflowExecutionData.value.data.resultData.runData
      workflowExecutionData.value = {
        ...workflowExecutionData.value,
        data: {
          ...workflowExecutionData.value.data,
          resultData: {
            ...workflowExecutionData.value.data.resultData,
            runData: remainingRunData,
          },
        },
      }
    }

    function removeAllNodeConnection(
      node: INodeUi,
      { preserveInputConnections = false, preserveOutputConnections = false } = {},
    ): void {
      uiStore.stateIsDirty = true

      // Remove all source connections
      if (!preserveOutputConnections) {
        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
        delete workflow.value.connections[node.name]
      }

      // Remove all destination connections
      if (preserveInputConnections) {
        return
      }

      const indexesToRemove = []
      let sourceNode: string,
        type: string,
        sourceIndex: string,
        connectionIndex: string,
        connectionData: IConnection

      for (sourceNode of Object.keys(workflow.value.connections)) {
        for (type of Object.keys(workflow.value.connections[sourceNode])) {
          for (sourceIndex of Object.keys(workflow.value.connections[sourceNode][type])) {
            indexesToRemove.length = 0
            const connectionsToRemove
              = workflow.value.connections[sourceNode][type][parseInt(sourceIndex, 10)]

            if (connectionsToRemove) {
              for (connectionIndex of Object.keys(connectionsToRemove)) {
                connectionData = connectionsToRemove[parseInt(connectionIndex, 10)]

                if (connectionData.node === node.name) {
                  indexesToRemove.push(connectionIndex)
                }
              }

              indexesToRemove.forEach((index) => {
                connectionsToRemove.splice(parseInt(index, 10), 1)
              })
            }
          }
        }
      }
    }

    function removeNodeConnectionsById(nodeId: string): void {
      const node = getNodeById(nodeId)

      if (!node) {
        return
      }

      removeAllNodeConnection(node)
    }

    function removeNodeExecutionDataById(nodeId: string): void {
      const node = getNodeById(nodeId)

      if (!node) {
        return
      }

      clearNodeExecutionData(node.name)
    }

    function addNode(nodeData: INodeUi): void {
      if (!Object.hasOwn(nodeData, 'name')) {
        // All nodes have to have a name
        // TODO: Check if there is an error or whatever that is supposed to be returned
        return
      }

      if (nodeData.extendsCredential) {
        nodeData.type = getCredentialOnlyNodeTypeName(nodeData.extendsCredential)
      }

      workflow.value.nodes.push(nodeData)

      // Init node metadata
      if (!nodeMetadata.value[nodeData.name]) {
        nodeMetadata.value[nodeData.name] = {} as INodeMetadata
      }
    }

    const isChatPanelOpen = ref(false)
    const isLogsPanelOpen = ref(false)

    function setPanelOpen(panel: 'chat' | 'logs', isOpen: boolean) {
      if (panel === 'chat') {
        isChatPanelOpen.value = isOpen
      }

      // Logs panel open/close is tied to the chat panel open/close
      isLogsPanelOpen.value = isOpen
    }

    function removeNode(node: INodeUi): void {
      const { [node.name]: removedNodeMetadata, ...remainingNodeMetadata } = nodeMetadata.value
      nodeMetadata.value = remainingNodeMetadata

      // If chat trigger node is removed, close chat
      if (node.type === CHAT_TRIGGER_NODE_TYPE) {
        setPanelOpen('chat', false)
      }

      if (workflow.value.pinData && Object.hasOwn(workflow.value.pinData, node.name)) {
        const { [node.name]: removedPinData, ...remainingPinData } = workflow.value.pinData
        workflow.value = {
          ...workflow.value,
          pinData: remainingPinData,
        }
      }

      for (let i = 0; i < workflow.value.nodes.length; i++) {
        if (workflow.value.nodes[i].name === node.name) {
          workflow.value = {
            ...workflow.value,
            nodes: [...workflow.value.nodes.slice(0, i), ...workflow.value.nodes.slice(i + 1)],
          }

          uiStore.stateIsDirty = true

          return
        }
      }
    }

    function removeNodeById(nodeId: string): void {
      const node = getNodeById(nodeId)

      if (!node) {
        return
      }

      removeNode(node)
    }

    const setNodeIssue = (nodeIssueData: INodeIssueData): boolean => {
      const nodeIndex = workflow.value.nodes.findIndex((node) => {
        return node.name === nodeIssueData.node
      })

      if (nodeIndex === -1) {
        return false
      }

      const node = workflow.value.nodes[nodeIndex]

      if (nodeIssueData.value === null) {
        // Remove the value if one exists
        if (node.issues?.[nodeIssueData.type] === undefined) {
          // No values for type exist so nothing has to get removed
          return true
        }

        const { [nodeIssueData.type]: removedNodeIssue, ...remainingNodeIssues } = node.issues
        updateNodeAtIndex(nodeIndex, {
          issues: remainingNodeIssues,
        })
      }
      else {
        updateNodeAtIndex(nodeIndex, {
          issues: {
            ...node.issues,
            [nodeIssueData.type]: nodeIssueData.value as INodeIssueObjectProperty,
          },
        })
      }

      return true
    }

    const setNodeParameters = (updateInformation: IUpdateInformation, append?: boolean): void => {
      // Find the node that should be updated
      const nodeIndex = workflow.value.nodes.findIndex((node) => {
        return node.name === updateInformation.name
      })

      if (nodeIndex === -1) {
        throw new Error(
          `Node with the name "${updateInformation.name}" could not be found to set parameter.`,
        )
      }

      const node = workflow.value.nodes[nodeIndex]

      // uiStore.stateIsDirty = true
      const newParameters
        = !!append && isObject(updateInformation.value)
          ? { ...node.parameters, ...updateInformation.value }
          : updateInformation.value

      updateNodeAtIndex(nodeIndex, {
        parameters: newParameters as INodeParameters,
      })

      nodeMetadata.value[node.name].parametersLastUpdatedAt = Date.now()
    }

    const setLastNodeParameters = (updateInformation: IUpdateInformation): void => {
      const latestNode = findLast(
        workflow.value.nodes,
        (node) => node.type === updateInformation.key,
      ) as INodeUi
      const nodeType = useNodeTypesStore().getNodeType(latestNode.type)

      if (!nodeType) {
        return
      }

      const nodeParams = NodeHelpers.getNodeParameters(
        nodeType.properties,
        updateInformation.value as INodeParameters,
        true,
        false,
        latestNode,
      )

      if (latestNode) {
        setNodeParameters({ value: nodeParams, name: latestNode.name }, true)
      }
    }

    const setWorkflowTagIds = (tags: string[]) => {
      workflow.value.tags = tags
    }

    const updateWorkflow = async (
      id: string,
      data: IWorkflowDataUpdate,
      forceSave = false,
    ): Promise<IWorkflowDb> => {
      if (forceSave) {
        //
      }

      if (data.settings === null) {
        data.settings = undefined
      }

      const updatedWorkflow = await workflowService.updateWorkflow(id, data)

      // if (
      //   workflowHelpers.containsNodeFromPackage(updatedWorkflow, AI_NODES_PACKAGE_NAME)
      //   && !usersStore.isEasyAIWorkflowOnboardingDone
      // ) {
      //   await updateCurrentUserSettings(rootStore.restApiContext, {
      //     easyAIWorkflowOnboarded: true,
      //   })
      //   usersStore.setEasyAIWorkflowOnboardingDone()
      // }

      return updatedWorkflow
    }

    function renameNodeSelectedAndExecution(nameData: { old: string, new: string }): void {
      uiStore.stateIsDirty = true

      // If node has any WorkflowResultData rename also that one that the data
      // does still get displayed also after node got renamed
      if (
        workflowExecutionData.value?.data
        && Object.hasOwn(workflowExecutionData.value.data.resultData.runData, nameData.old)
      ) {
        workflowExecutionData.value.data.resultData.runData[nameData.new]
          = workflowExecutionData.value.data.resultData.runData[nameData.old]
        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
        delete workflowExecutionData.value.data.resultData.runData[nameData.old]
      }

      // In case the renamed node was last selected set it also there with the new name
      if (uiStore.lastSelectedNode === nameData.old) {
        uiStore.lastSelectedNode = nameData.new
      }

      const { [nameData.old]: removed, ...rest } = nodeMetadata.value
      nodeMetadata.value = { ...rest, [nameData.new]: nodeMetadata.value[nameData.old] }

      if (workflow.value.pinData && Object.hasOwn(workflow.value.pinData, nameData.old)) {
        const { [nameData.old]: renamed, ...restPinData } = workflow.value.pinData
        workflow.value = {
          ...workflow.value,
          pinData: {
            ...restPinData,
            [nameData.new]: renamed,
          },
        }
      }
    }

    function updateNodeProperties(updateInformation: INodeUpdatePropertiesInformation): void {
      // Find the node that should be updated
      const nodeIndex = workflow.value.nodes.findIndex((node) => {
        return node.name === updateInformation.name
      })

      if (nodeIndex !== -1) {
        for (const key of Object.keys(updateInformation.properties)) {
          uiStore.stateIsDirty = true

          updateNodeAtIndex(nodeIndex, {
            [key]: updateInformation.properties[key],
          })
        }
      }
    }

    async function runWorkflow(startRunData: IStartRunData): Promise<IExecutionPushResponse> {
      if (startRunData.workflowData.settings === null) {
        startRunData.workflowData.settings = undefined
      }

      const { data } = await AppService.runWorkflow(
        startRunData.workflowData.id as string,
        startRunData,
      )

      return data
    }

    const nodesIssuesExist = computed(() => {
      for (const node of workflow.value.nodes) {
        const isNodeDisabled = node.disabled === true
        const noNodeIssues = node.issues === undefined || Object.keys(node.issues).length === 0

        if (isNodeDisabled || noNodeIssues) {
          continue
        }

        return true
      }

      return false
    })

    function markExecutionAsStopped() {
      activeExecutionId.value = null
      clearNodeExecutionQueue()
      executionWaitingForWebhook.value = false
      uiStore.removeActiveAction('workflowRunning')
      workflowHelpers.setDocumentTitle(workflowName.value, 'IDLE')

      clearPopupWindowState()

      const runData = workflowExecutionData.value?.data?.resultData.runData ?? {}

      for (const nodeName in runData) {
        runData[nodeName] = runData[nodeName].filter(
          ({ executionStatus }) => executionStatus === 'success',
        )
      }
    }

    function checkIfNodeHasChatParent(nodeName: string): boolean {
      const workflow = getCurrentWorkflow()
      const parents = workflow.getParentNodes(nodeName, NodeConnectionType.Main as UnsafeAny)

      const matchedChatNode = parents.find((parent) => {
        const parentNodeType = getNodeByName(parent)?.type

        return parentNodeType === CHAT_TRIGGER_NODE_TYPE
      })

      return !!matchedChatNode
    }

    async function removeTestWebhook(targetWorkflowId: string): Promise<boolean> {
      return ExecutionService.stopWaitingWebhook(targetWorkflowId)
    }

    async function getActivationError(workflowId: IWorkflowDb['id']): Promise<string | undefined> {
      return await workflowService.activeWorkflowError(workflowId)
    }

    const workflowExecutionPairedItemMappings = ref<Record<string, Set<string>>>({})

    async function getExecution(id: string): Promise<IExecutionResponse | undefined> {
      const response = await ExecutionService.getExecution(id)

      if (response) {
        return unflattenExecutionData(response)
      }
    }

    const setWorkflowExecutionData = (workflowResultData: IExecutionResponse | null) => {
      if (workflowResultData?.data?.waitTill) {
        delete workflowResultData.data.resultData.runData[
          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
          workflowResultData.data.resultData.lastNodeExecuted as string
        ]
      }

      workflowExecutionData.value = workflowResultData
      workflowExecutionPairedItemMappings.value = getPairedItemsMapping(workflowResultData)
    }

    const getParametersLastUpdate = (nodeName: string): number | undefined => {
      return nodeMetadata.value[nodeName]?.parametersLastUpdatedAt
    }

    const activeWorkflows = ref<string[]>([])

    function setWorkflowInactive(targetWorkflowId: string) {
      const index = activeWorkflows.value.indexOf(targetWorkflowId)

      if (index !== -1) {
        activeWorkflows.value.splice(index, 1)
      }

      if (workflowsById.value[targetWorkflowId]) {
        workflowsById.value[targetWorkflowId].active = false
      }

      if (targetWorkflowId === workflow.value.id) {
        setActive(false)
      }
    }

    function getFormResumeUrl(node: INode, executionId: string) {
      const { webhookSuffix } = (node.parameters.options ?? {}) as IDataObject
      const suffix = webhookSuffix && typeof webhookSuffix !== 'object' ? `/${webhookSuffix}` : ''
      const testUrl = `${rootStore.formWaitingUrl}/${executionId}${suffix}`

      return testUrl
    }

    function updateNodeExecutionData(pushData: PushPayload<'nodeExecuteAfter'>): void {
      if (!workflowExecutionData.value?.data) {
        throw new Error('The "workflowExecutionData" is not initialized!')
      }

      const { nodeName, data, executionId } = pushData
      const isNodeWaiting = data.executionStatus === 'waiting'
      const node = getNodeByName(nodeName)

      if (!node) {
        return
      }

      if (workflowExecutionData.value.data.resultData.runData[nodeName] === undefined) {
        workflowExecutionData.value = {
          ...workflowExecutionData.value,
          data: {
            ...workflowExecutionData.value.data,
            resultData: {
              ...workflowExecutionData.value.data.resultData,
              runData: {
                ...workflowExecutionData.value.data.resultData.runData,
                [nodeName]: [],
              },
            },
          },
        }
      }

      const tasksData = workflowExecutionData.value.data!.resultData.runData[nodeName]

      if (isNodeWaiting) {
        tasksData.push(data)

        if (
          node.type === FORM_NODE_TYPE
          || (node.type === WAIT_NODE_TYPE && node.parameters.resume === 'form')
        ) {
          const testUrl = getFormResumeUrl(node, executionId)
          openFormPopupWindow(testUrl)
        }
      }
      else {
        if (tasksData.length && tasksData[tasksData.length - 1].executionStatus === 'waiting') {
          tasksData.splice(tasksData.length - 1, 1, data)
        }
        else {
          tasksData.push(data)
        }

        removeExecutingNode(nodeName)
      }
    }

    function getWorkflowById(id: string): IWorkflowDb {
      return workflowsById.value[id]
    }

    async function fetchExecutionDataById(executionId: string): Promise<IExecutionResponse | null> {
      return await AppService.getExecutionData(executionId)
    }

    function setWorkflowActive(targetWorkflowId: string) {
      uiStore.stateIsDirty = false
      const index = activeWorkflows.value.indexOf(targetWorkflowId)

      if (index === -1) {
        activeWorkflows.value.push(targetWorkflowId)
      }

      if (workflowsById.value[targetWorkflowId]) {
        workflowsById.value[targetWorkflowId].active = true
      }

      if (targetWorkflowId === workflow.value.id) {
        setActive(true)
      }
    }

    function getNodesByIds(nodeIds: string[]): INodeUi[] {
      return nodeIds.map(getNodeById).filter(isPresent)
    }

    function pinData(payload: { node: INodeUi, data: INodeExecutionData[] }): void {
      if (!workflow.value.pinData) {
        workflow.value = { ...workflow.value, pinData: {} }
      }

      if (!Array.isArray(payload.data)) {
        payload.data = [payload.data]
      }

      const storedPinData = payload.data.map((item) =>
        isJsonKeyObject(item) ? { json: item.json } : { json: item },
      )

      workflow.value = {
        ...workflow.value,
        pinData: {
          ...workflow.value.pinData,
          [payload.node.name]: storedPinData,
        },
      }

      uiStore.stateIsDirty = true
      updateCachedWorkflow()

      // dataPinningEventBus.emit('pin-data', { [payload.node.name]: storedPinData });
    }

    function unpinData(payload: { node: INodeUi }): void {
      if (!workflow.value.pinData) {
        workflow.value = { ...workflow.value, pinData: {} }
      }

      const { [payload.node.name]: _, ...pinData } = workflow.value.pinData as IPinData
      workflow.value = {
        ...workflow.value,
        pinData,
      }

      uiStore.stateIsDirty = true
      updateCachedWorkflow()

      // dataPinningEventBus.emit('unpin-data', {
      //   nodeNames: [payload.node.name],
      // });
    }

    const getBinaryUrl = (
      binaryDataId: string,
      action: 'view' | 'download',
      fileName: string,
      mimeType: string,
    ): string => {
      let restUrl = rootStore.restUrl

      if (restUrl.startsWith('/')) {
        restUrl = window.location.origin + restUrl
      }

      const url = new URL(`${restUrl}/binary-data`)
      url.searchParams.append('id', binaryDataId)
      url.searchParams.append('action', action)

      if (fileName) {
        url.searchParams.append('fileName', fileName)
      }

      if (mimeType) {
        url.searchParams.append('mimeType', mimeType)
      }

      return url.toString()
    }

    function getPinDataSize(pinData: Record<string, string | INodeExecutionData[]> = {}): number {
      return Object.values(pinData).reduce<number>((acc, value) => {
        return acc + stringSizeInBytes(value)
      }, 0)
    }

    function addWorkflowTagIds(tags: string[]) {
      workflow.value = {
        ...workflow.value,
        tags: [...new Set([...(workflow.value.tags ?? []), ...tags])] as IWorkflowDb['tags'],
      }
    }

    function removeWorkflowTagId(tagId: string) {
      const tags = workflow.value.tags as string[]
      const updated = tags.filter((id: string) => id !== tagId)
      workflow.value = {
        ...workflow.value,
        tags: updated as IWorkflowDb['tags'],
      }
    }

    function removeAllConnections(data: { setStateDirty: boolean }): void {
      if (data?.setStateDirty) {
        uiStore.stateIsDirty = true
      }

      workflow.value.connections = {}
    }

    function removeAllNodes(data: { setStateDirty: boolean, removePinData: boolean }): void {
      if (data.setStateDirty) {
        uiStore.stateIsDirty = true
      }

      if (data.removePinData) {
        workflow.value = {
          ...workflow.value,
          pinData: {},
        }
      }

      workflow.value.nodes.splice(0, workflow.value.nodes.length)
      nodeMetadata.value = {}
    }

    function resetAllNodesIssues(): boolean {
      workflow.value.nodes.forEach((node) => {
        node.issues = undefined
      })

      return true
    }

    // replace invalid credentials in workflow
    function replaceInvalidWorkflowCredentials(data: {
      credentials: INodeCredentialsDetails
      invalid: INodeCredentialsDetails
      type: string
    }) {
      workflow.value.nodes.forEach((node: INodeUi) => {
        const nodeCredentials: INodeCredentials | undefined = (node as unknown as INode).credentials

        if (!nodeCredentials?.[data.type]) {
          return
        }

        const nodeCredentialDetails: INodeCredentialsDetails | string = nodeCredentials[data.type]

        if (
          typeof nodeCredentialDetails === 'string'
          && nodeCredentialDetails === data.invalid.name
        ) {
          (node.credentials as INodeCredentials)[data.type] = data.credentials

          return
        }

        if (nodeCredentialDetails.id === null) {
          if (nodeCredentialDetails.name === data.invalid.name) {
            (node.credentials as INodeCredentials)[data.type] = data.credentials
          }

          return
        }

        if (nodeCredentialDetails.id === data.invalid.id) {
          (node.credentials as INodeCredentials)[data.type] = data.credentials
        }
      })
    }

    function resetState() {
      currentWorkflowExecutions.value = []
      removeAllConnections({ setStateDirty: false })
      removeAllNodes({ setStateDirty: false, removePinData: true })
      resetWorkflow()

      setWorkflowExecutionData(null)
      resetAllNodesIssues()

      setActive(defaults.active)
      setWorkflowId(PLACEHOLDER_EMPTY_WORKFLOW_ID)
      setWorkflowName({ newName: '', setStateDirty: false })
      setWorkflowSettings({ ...defaults.settings })
      setWorkflowTagIds([])

      activeExecutionId.value = null
      executingNode.value.length = 0
      executionWaitingForWebhook.value = false
    }

    return {
      workflow,
      resetWorkflow,
      isWorkflowActive,
      getNodeById,
      getCurrentWorkflow,
      pinDataByNodeName,
      getWorkflowResultDataByNodeName,
      isNodeExecuting,
      getWorkflowRunData,
      getWorkflowExecution,
      isWorkflowRunning,
      setNodes,
      setConnections,
      getWorkflowById,
      fetchExecutionDataById,
      setNodeValue,
      setNodeParameters,
      isCanvasReadOnly,

      addWorkflow,
      setWorkflow,
      fetchWorkflow,

      setWorkflowPartial,
      setActive,
      setWorkflowId,
      setWorkflowName,
      setWorkflowSettings,
      setWorkflowPinData,
      setWorkflowVersionId,
      setWorkflowMetadata,
      setWorkflowScopes,
      setUsedCredentials,
      setWorkflowActive,
      setWorkflowInactive,
      workflowSettings,

      removeTestWebhook,
      outgoingConnectionsByNodeName,
      incomingConnectionsByNodeName,
      removeNodeConnectionsById,
      resetState,

      addExecutingNode,
      executingNode,
      removeNodeExecutionDataById,
      clearNodeExecutionData,
      setWorkflowExecutionData,
      updateNodeExecutionData,
      workflowExecutionData,
      getActivationError,

      getNodeByName,
      pinnedWorkflowData,
      shouldReplaceInputDataWithPinData,
      workflowId,
      workflowVersionId,
      workflowTriggerNodes,
      setNodePositionById,
      setNodePristine,
      isEditing,
      isDragging,
      isConnecting,
      usedCredentials,
      getExecution,
      getParametersLastUpdate,
      setPanelOpen,
      getNodeTypes,
      executedNode,

      activeExecutionId,
      subWorkflowExecutionError,
      runWorkflow,
      nodesIssuesExist,
      chatPartialExecutionDestinationNode,
      checkIfNodeHasChatParent,
      markExecutionAsStopped,
      executionWaitingForWebhook,
      currentWorkflowExecutions,

      addConnection,
      removeConnection,

      addNode,
      removeNode,
      removeNodeById,
      activeWorkflows,

      updateNodeProperties,
      setLastNodeParameters,
      setNodeIssue,
      setWorkflowTagIds,
      updateWorkflow,
      canvasNames,
      renameNodeSelectedAndExecution,
      getNodesByIds,
      getWorkflow,
      addWorkflowTagIds,
      removeWorkflowTagId,
      replaceInvalidWorkflowCredentials,
      getBinaryUrl,
      workflowExecutionPairedItemMappings,

      pinData,
      unpinData,
      getPinDataSize,
    }
  },
)
