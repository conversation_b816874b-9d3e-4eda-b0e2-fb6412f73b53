import type { FrontendSettings } from '@n8n/api-types'
import * as ExpressionEvaluatorProxy from 'n8n-workflow/dist/ExpressionEvaluatorProxy.js'

import { INSECURE_CONNECTION_WARNING } from '#wf/constants/common'
import { StoreKey } from '#wf/constants/store'
import { useRootStore } from '#wf/stores/root.store'
import { useUIStore } from '#wf/stores/ui.store'

import { AppService } from '~/features/space/services/app'

export const useSettingsStore = defineStore(StoreKey.WorkflowSetting, () => {
  const isInitialized = ref(false)

  const settings = ref<FrontendSettings>({} as FrontendSettings)

  const setSettings = (newSettings: FrontendSettings) => {
    settings.value = newSettings
    // userManagement.value = newSettings.userManagement

    // if (userManagement.value) {
    //   userManagement.value.showSetupOnFirstLoad = !!settings.value.userManagement.showSetupOnFirstLoad
    // }

    // api.value = settings.value.publicApi

    // if (settings.value.sso?.ldap) {
    //   ldap.value.loginEnabled = settings.value.sso.ldap.loginEnabled
    //   ldap.value.loginLabel = settings.value.sso.ldap.loginLabel
    // }

    // if (settings.value.sso?.saml) {
    //   saml.value.loginEnabled = settings.value.sso.saml.loginEnabled
    //   saml.value.loginLabel = settings.value.sso.saml.loginLabel
    // }

    // mfa.value.enabled = settings.value.mfa?.enabled
    // folders.value.enabled = settings.value.folders?.enabled

    if (settings.value.enterprise?.showNonProdBanner) {
      useUIStore().pushBannerToStack('NON_PRODUCTION_LICENSE')
    }

    if (settings.value.versionCli) {
      useRootStore().setVersionCli(settings.value.versionCli)
    }

    if (settings.value.authCookie.secure) {
      if (
        location.protocol === 'http:'
        && (!['localhost', '127.0.0.1'].includes(location.hostname) || isSafari())
      ) {
        document.write(INSECURE_CONNECTION_WARNING)

        return
      }
    }

    const isV1BannerDismissedPermanently = (settings.value.banners?.dismissed || []).includes('V1')

    if (!isV1BannerDismissedPermanently && settings.value.versionCli.startsWith('1.')) {
      useUIStore().pushBannerToStack('V1')
    }
  }

  const isAskAiEnabled = computed(() => settings.value.askAi?.enabled)

  const isPreviewMode = computed(() => settings.value.previewMode)

  const isEnterpriseFeatureEnabled = computed(() => settings.value.enterprise)

  const isCloudDeployment = computed(() => settings.value.deployment?.type === 'cloud')

  const deploymentType = computed(() => settings.value.deployment?.type || 'default')
  const saveManualExecutions = ref(false)

  const partialExecutionVersion = computed(() => {
    const defaultVersion = settings.value.partialExecution?.version ?? 1
    // -1 means we pick the defaultVersion
    //  1 is the old flow
    //  2 is the new flow
    const userVersion = useLocalStorage('PartialExecution.version', -1).value
    const version = userVersion === -1 ? defaultVersion : userVersion

    // For backwards compatibility, e.g. if the user has 0 in their local
    // storage, which used to be allowed, but not anymore.
    if (![1, 2].includes(version)) {
      return 1
    }

    return version
  })

  const concurrency = computed(() => settings.value.concurrency)

  const isConcurrencyEnabled = computed(() => concurrency.value !== -1)

  const pushBackend = computed(() => settings.value.pushBackend)

  const { $toast } = useNuxtApp()

  const getSettings = async () => {
    const rootStore = useRootStore()

    const { data: fetchedSettings } = await AppService.getWorkflowSettings()

    setSettings({
      ...fetchedSettings,
      communityNodesEnabled: fetchedSettings.communityNodesEnabled,
    })

    // setAllowedModules(fetchedSettings.allowedModules)
    // setSaveDataErrorExecution(fetchedSettings.saveDataErrorExecution)
    // setSaveDataSuccessExecution(fetchedSettings.saveDataSuccessExecution)
    // setSaveDataProgressExecution(fetchedSettings.saveExecutionProgress)
    // setSaveManualExecutions(fetchedSettings.saveManualExecutions)

    rootStore.setUrlBaseWebhook(fetchedSettings.urlBaseWebhook)
    rootStore.setUrlBaseEditor(fetchedSettings.urlBaseEditor)
    rootStore.setEndpointForm(fetchedSettings.endpointForm)
    rootStore.setEndpointFormTest(fetchedSettings.endpointFormTest)
    rootStore.setEndpointFormWaiting(fetchedSettings.endpointFormWaiting)
    rootStore.setEndpointWebhook(fetchedSettings.endpointWebhook)
    rootStore.setEndpointWebhookTest(fetchedSettings.endpointWebhookTest)
    rootStore.setEndpointWebhookWaiting(fetchedSettings.endpointWebhookWaiting)
    rootStore.setTimezone(fetchedSettings.timezone)
    rootStore.setExecutionTimeout(fetchedSettings.executionTimeout)
    rootStore.setMaxExecutionTimeout(fetchedSettings.maxExecutionTimeout)
    rootStore.setInstanceId(fetchedSettings.instanceId)
    rootStore.setOauthCallbackUrls(fetchedSettings.oauthCallbackUrls)
    rootStore.setN8nMetadata(fetchedSettings.n8nMetadata || {})
    rootStore.setDefaultLocale(fetchedSettings.defaultLocale)
    rootStore.setBinaryDataMode(fetchedSettings.binaryDataMode)
    // useVersionsStore().setVersionNotificationSettings(fetchedSettings.versionNotifications)

    // if (fetchedSettings.telemetry.enabled) {
    //   void eventsApi.sessionStarted(rootStore.restApiContext)
    // }
  }

  const initialize = async () => {
    if (!isInitialized.value) {
      try {
        await getSettings()

        ExpressionEvaluatorProxy.setEvaluator(settings.value.expressions.evaluator)

        isInitialized.value = true
      }
      catch (e) {
        $toast.error({
          summary: '连接到系统时出错',
          detail: '无法连接到服务器，请刷新重试',
        })

        throw e
      }
    }
  }

  const areTagsEnabled = computed(() =>
    settings.value.workflowTagsDisabled !== undefined ? !settings.value.workflowTagsDisabled : true,
  )

  const allowedModules = computed(() => settings.value.allowedModules)

  return {
    initialize,
    isInitialized,
    isAskAiEnabled,
    isPreviewMode,
    isEnterpriseFeatureEnabled,

    settings,
    allowedModules,

    deploymentType,
    partialExecutionVersion,
    isConcurrencyEnabled,
    pushBackend,
    saveManualExecutions,
    areTagsEnabled,
    isCloudDeployment,
  }
})
