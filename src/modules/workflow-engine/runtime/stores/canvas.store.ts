import { StoreKey } from '#wf/constants/store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { INodeUi } from '#wf/types/interface'

export const useCanvasStore = defineStore(StoreKey.WorkflowCanvas, () => {
  const workflowStore = useWorkflowStore()

  const nodes = computed<INodeUi[]>(() => workflowStore.workflow.nodes)

  const aiNodes = computed<INodeUi[]>(() =>
    nodes.value.filter((node) => node.type.includes('langchain')),
  )

  const isLoading = ref(true)

  const setIsLoading = (value: boolean) => {
    isLoading.value = value
  }

  return {
    aiNodes,
    isLoading,
    setIsLoading,
  }
})
