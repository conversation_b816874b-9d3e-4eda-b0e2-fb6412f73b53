import type { Connection } from '@vue-flow/core'
import type { INodeInputConfiguration } from 'n8n-workflow'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'

import { CanvasConnectionMode, NodeConnectionType } from '#wf/constants/canvas'
import { AI_UNCATEGORIZED_CATEGORY, REGULAR_NODE_CREATOR_VIEW, TRIGGER_NODE_CREATOR_VIEW } from '#wf/constants/common'
import { StoreKey } from '#wf/constants/store'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useUIStore } from '#wf/stores/ui.store'
import { useViewStacksStore } from '#wf/stores/viewStacks.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { ActionsRecord, NodeCreatorOpenSource, NodeFilterType, SimplifiedNodeType, ToggleNodeCreatorOptions } from '#wf/types/interface'
import type { PartialBy } from '#wf/types/typeHelpers'
import { parseCanvasConnectionHandleString } from '#wf/utils/canvasUtil'
import { isVueFlowConnection } from '#wf/utils/typeGuards'
import { transformNodeType } from '#wf/utils/viewStackUtil'

export const useNodeCreatorStore = defineStore(StoreKey.NodeCreator, () => {
  const workflowStore = useWorkflowStore()
  const ndvStore = useNDVStore()
  const uiStore = useUIStore()
  const nodeTypesStore = useNodeTypesStore()

  const isCreateNodeActive = ref(false)

  const selectedView = ref<NodeFilterType>(TRIGGER_NODE_CREATOR_VIEW)

  const setSelectedView = (view: NodeFilterType) => {
    selectedView.value = view
  }

  const mergedNodes = ref<SimplifiedNodeType[]>([])

  const setMergeNodes = (nodes: SimplifiedNodeType[]) => {
    mergedNodes.value = nodes
  }

  const allNodeCreatorNodes = computed(() =>
    Object.values(mergedNodes.value).map((i) => transformNodeType(i)),
  )

  const openSource = ref<NodeCreatorOpenSource>('')

  const setOpenSource = (view: NodeCreatorOpenSource) => {
    openSource.value = view
  }

  const setNodeCreatorState = (options: ToggleNodeCreatorOptions) => {
    const { createNodeActive, source, nodeCreatorView } = options

    let view = nodeCreatorView

    if (!view) {
      view = workflowStore.workflowTriggerNodes.length > 0
        ? REGULAR_NODE_CREATOR_VIEW
        : TRIGGER_NODE_CREATOR_VIEW
    }

    // Default to the trigger tab in node creator if there's no trigger node yet
    setSelectedView(view)

    isCreateNodeActive.value = createNodeActive

    if (createNodeActive && source) {
      setOpenSource(source)
    }
  }

  const openNodeCreator = (options: Omit<ToggleNodeCreatorOptions, 'createNodeActive'>) => {
    setNodeCreatorState({
      ...options,
      createNodeActive: true,
    })
  }

  const closeNodeCreator = () => {
    setNodeCreatorState({
      createNodeActive: false,
    })

    useViewStacksStore().resetViewStacks()
  }

  const actions = ref<ActionsRecord<SimplifiedNodeType[]>>({})

  const setActions = (nodes: ActionsRecord<SimplifiedNodeType[]>) => {
    actions.value = nodes
  }

  const getNodeCreatorFilter = (nodeName: string, outputType?: NodeConnectionType) => {
    let filter
    const workflow = workflowStore.getCurrentWorkflow()
    const workflowNode = workflow.getNode(nodeName)

    if (!workflowNode) {
      return { nodes: [] }
    }

    const nodeType = nodeTypesStore.getNodeType(workflowNode?.type, workflowNode.typeVersion)

    if (nodeType) {
      const inputs = NodeHelpers.getNodeInputs(workflow, workflowNode, nodeType)

      const filterFound = inputs.filter((input) => {
        if (typeof input === 'string' || (input.type as unknown as NodeConnectionType) !== outputType || !input.filter) {
          // No filters defined or wrong connection type
          return false
        }

        return true
      }) as INodeInputConfiguration[]

      if (filterFound.length) {
        filter = filterFound[0].filter
      }
    }

    return filter
  }

  const openNodeCreatorForConnectingNode = ({
    connection,
    eventSource,
    nodeCreatorView,
  }: {
    connection: PartialBy<Connection, 'target' | 'targetHandle'>
    eventSource?: NodeCreatorOpenSource
    nodeCreatorView?: NodeFilterType
  }) => {
    // Get the node and set it as active that new nodes
    // which get created get automatically connected
    // to it.
    const sourceNode = workflowStore.getNodeById(connection.source)

    if (!sourceNode) {
      return
    }

    const { type, index, mode } = parseCanvasConnectionHandleString(connection.sourceHandle)

    uiStore.lastSelectedNode = sourceNode.name
    uiStore.lastSelectedNodeEndpointUuid = connection.sourceHandle ?? null
    uiStore.lastSelectedNodeOutputIndex = index

    if (isVueFlowConnection(connection)) {
      uiStore.lastInteractedWithNodeConnection = connection
    }

    uiStore.lastInteractedWithNodeHandle = connection.sourceHandle ?? null
    uiStore.lastInteractedWithNodeId = sourceNode.id

    const isOutput = mode === CanvasConnectionMode.Output
    const isScopedConnection = type !== NodeConnectionType.Main
    setNodeCreatorState({
      source: eventSource,
      createNodeActive: true,
      nodeCreatorView: isScopedConnection ? AI_UNCATEGORIZED_CATEGORY : nodeCreatorView,
    })

    // TODO: The animation is a bit glitchy because we're updating view stack immediately
    // after the node creator is opened
    if (isScopedConnection) {
      useViewStacksStore()
        .gotoCompatibleConnectionView(type, isOutput, getNodeCreatorFilter(sourceNode.name, type))
        .catch(() => {})
    }
  }

  const openNodeCreatorForTriggerNodes = (source: NodeCreatorOpenSource) => {
    ndvStore.setActiveNodeName(null)
    setSelectedView(TRIGGER_NODE_CREATOR_VIEW)
    setNodeCreatorState({
      source,
      createNodeActive: true,
      nodeCreatorView: TRIGGER_NODE_CREATOR_VIEW,
    })
  }

  return {
    isCreateNodeActive,
    openSource,
    selectedView,

    mergedNodes,
    setMergeNodes,

    actions,
    setActions,

    allNodeCreatorNodes,

    openNodeCreator,
    closeNodeCreator,

    openNodeCreatorForTriggerNodes,
    openNodeCreatorForConnectingNode,
    setNodeCreatorState,
  }
})
