import type { JSONSchema7 } from 'json-schema'
import type { Result } from 'n8n-workflow'
import { createResultError, createResultOk } from 'n8n-workflow/dist/result.js'

import { StoreKey } from '#wf/constants/store'
import { type GetSchemaPreviewOptions, workflowService } from '#wf/services/workflow'

export const useSchemaPreviewStore = defineStore(StoreKey.SchemaPreview, () => {
  // Type cast to avoid 'Type instantiation is excessively deep and possibly infinite'
  const schemaPreviews = reactive<Map<string, Result<JSONSchema7, Error>>>(new Map()) as Map<
    string,
    Result<JSONSchema7, Error>
  >

  function getSchemaPreviewKey({
    nodeType,
    version,
    operation,
    resource,
  }: GetSchemaPreviewOptions) {
    return `${nodeType}_${version}_${resource ?? 'all'}_${operation ?? 'all'}`
  }

  async function getSchemaPreview(
    options: GetSchemaPreviewOptions,
  ): Promise<Result<JSONSchema7, Error>> {
    const key = getSchemaPreviewKey(options)
    const cached = schemaPreviews.get(key)

    if (cached) {
      return cached
    }

    try {
      const preview = await workflowService.getSchemaPreview(options)
      const result = createResultOk(preview)
      schemaPreviews.set(key, result)

      return result
    }
    catch (err) {
      if (err instanceof Error) {
        const result = createResultError(err)
        schemaPreviews.set(key, result)

        return result
      }

      throw err
    }
  }

  return { getSchemaPreview }
})
