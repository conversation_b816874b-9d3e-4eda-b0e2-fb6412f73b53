import { difference } from 'lodash-es'
import type { INodeInputFilter } from 'n8n-workflow'
import { AI_TRANSFORM_NODE_TYPE } from 'n8n-workflow/dist/Constants.js'
import { nanoid } from 'nanoid'

import type { NodeViewItem } from '#wf/components/node-creator/viewsData'
import { AINodesView } from '#wf/components/node-creator/viewsData'
import type { NodeConnectionType } from '#wf/constants/canvas'
import { AI_CATEGORY_ROOT_NODES, AI_CATEGORY_TOOLS, AI_CODE_NODE_TYPE, AI_NODE_CREATOR_VIEW, AI_OTHERS_NODE_CREATOR_VIEW, AI_SUBCATEGORY, DEFAULT_SUBCATEGORY, TRIGGER_NODE_CREATOR_VIEW } from '#wf/constants/common'
import { StoreKey } from '#wf/constants/store'
import { useCanvasStore } from '#wf/stores/canvas.store'
import { useKeyboardNavStore } from '#wf/stores/keyboardNav.store'
import { useNodeCreatorStore } from '#wf/stores/nodeCreator.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useSettingsStore } from '#wf/stores/setting.store'
import type { ActionTypeDescription, INodeCreateElement, NodeCreateElement, NodeViewItemSection, ViewStack } from '#wf/types/node-create'
import { extendItemsWithUUID, flattenCreateElements, groupItemsInSections, isAINode, searchNodes, sortNodeCreateElements, subcategorizeItems, transformNodeType } from '#wf/utils/viewStackUtil'

function isAiRootView(stack?: ViewStack) {
  return stack?.rootView === AI_NODE_CREATOR_VIEW
}

function isAiSubcategoryView(stack: ViewStack) {
  return stack.rootView === AI_OTHERS_NODE_CREATOR_VIEW
}

function filterAiRootNodes(items: NodeCreateElement[]) {
  return items.filter((node) => {
    if (node.type !== 'node') {
      return false
    }

    const subcategories = node.properties.codex?.subcategories?.[AI_SUBCATEGORY] ?? []

    return (
      subcategories.includes(AI_CATEGORY_ROOT_NODES)
      && !subcategories?.includes(AI_CATEGORY_TOOLS)
    )
  })
}

export const useViewStacksStore = defineStore(StoreKey.NodeCreatorViewStacks, () => {
  const viewStacks = ref<ViewStack[]>([])

  const nodeCreatorStore = useNodeCreatorStore()
  const { openSource, mergedNodes } = storeToRefs(nodeCreatorStore)

  const canvasStore = useCanvasStore()
  const { aiNodes: canvasAiNodes } = storeToRefs(canvasStore)

  const settingsStore = useSettingsStore()
  const { isAskAiEnabled } = storeToRefs(settingsStore)

  const keyboardNavStore = useKeyboardNavStore()

  const getLastActiveStack = () => {
    return viewStacks.value.at(-1)
  }

  const groupIfAiNodes = (items: INodeCreateElement[], sortAlphabetically = true) => {
    const aiNodes = items.filter((node): node is NodeCreateElement => isAINode(node))
    const canvasHasAINodes = canvasAiNodes.value.length > 0

    if (aiNodes.length > 0 && (canvasHasAINodes || isAiRootView(getLastActiveStack()))) {
      const sectionsMap = new Map<string, NodeViewItemSection>()
      const aiRootNodes = filterAiRootNodes(aiNodes)
      const aiSubNodes = difference(aiNodes, aiRootNodes)

      aiSubNodes.forEach((node) => {
        const section = node.properties.codex?.subcategories?.[AI_SUBCATEGORY]?.[0]

        if (section) {
          const subSection = node.properties.codex?.subcategories?.[section]?.[0]
          const sectionKey = subSection ?? section
          const currentItems = sectionsMap.get(sectionKey)?.items ?? []
          const isSubnodesSection
            = !node.properties.codex?.subcategories?.[AI_SUBCATEGORY].includes(
              AI_CATEGORY_ROOT_NODES,
            )

          let title = section

          if (isSubnodesSection) {
            title = `${section}（子节点）`
          }

          if (subSection) {
            title = subSection
          }

          sectionsMap.set(sectionKey, {
            key: sectionKey,
            title,
            items: [...currentItems, node.key],
          })
        }
      })

      const nonAiNodes = difference(items, aiNodes)
      // Convert sectionsMap to array of sections
      const sections = Array.from(sectionsMap.values())

      return [
        ...nonAiNodes,
        ...aiRootNodes,
        ...groupItemsInSections(aiSubNodes, sections, sortAlphabetically),
      ]
    }

    return items
  }

  const searchBaseItems = computed<INodeCreateElement[]>(() => {
    const stack = getLastActiveStack()

    if (!stack?.searchItems) {
      return []
    }

    return stack.searchItems.map((item) => transformNodeType(item, stack.subcategory))
  })

  const filterOutAiNodes = (items: INodeCreateElement[]) => {
    const filteredSearchBase = items.filter((item) => {
      if (item.type === 'node') {
        const isAICategory = item.properties.codex?.categories?.includes(AI_SUBCATEGORY) === true

        if (!isAICategory) {
          return true
        }

        const isRootNodeSubcategory = item.properties.codex?.subcategories?.[AI_SUBCATEGORY]?.includes(AI_CATEGORY_ROOT_NODES)

        return isRootNodeSubcategory
      }

      return true
    })

    return filteredSearchBase
  }

  const activeStackItems = computed<INodeCreateElement[]>(() => {
    const stack = getLastActiveStack()

    if (!stack?.baselineItems) {
      return stack?.items ? extendItemsWithUUID(stack.items) : []
    }

    if (stack.search && searchBaseItems.value) {
      let searchBase: INodeCreateElement[] = searchBaseItems.value
      const canvasHasAINodes = canvasAiNodes.value.length > 0

      if (searchBaseItems.value.length === 0) {
        searchBase = flattenCreateElements(stack.baselineItems ?? [])
      }

      if (
      // Filter-out AI sub-nodes if canvas has no AI nodes and the root view is not AI
        !(isAiRootView(stack) || canvasHasAINodes)
        // or if the source is a plus endpoint or a node connection drop and the root view is not AI subcategory
        || (['plus_endpoint', 'node_connection_drop'].includes(openSource.value)
          && !isAiSubcategoryView(stack))
      ) {
        searchBase = filterOutAiNodes(searchBase)
      }

      const searchResults = extendItemsWithUUID(searchNodes(stack.search || '', searchBase))

      const groupedNodes = groupIfAiNodes(searchResults, false) ?? searchResults
      // Set the active index to the second item if there's a section
      // as the first item is collapsable
      stack.activeIndex = groupedNodes.some((node) => node.type === 'section') ? 1 : 0

      return groupedNodes
    }

    return extendItemsWithUUID(groupIfAiNodes(stack.baselineItems, true))
  })

  const activeViewStack = computed<ViewStack>(() => {
    const stack = getLastActiveStack()

    if (!stack) {
      return {}
    }

    const flatBaselineItems = flattenCreateElements(stack.baselineItems ?? [])

    return {
      ...stack,
      items: activeStackItems.value,
      hasSearch: flatBaselineItems.length > 8 || stack?.hasSearch,
    }
  })

  const resetViewStacks = () => {
    viewStacks.value = []
  }

  const updateCurrentViewStack = (stack: Partial<ViewStack>) => {
    const currentStack = getLastActiveStack()
    const matchedIndex = viewStacks.value.findIndex((s) => s.uuid === currentStack?.uuid)

    if (!currentStack) {
      return
    }

    // For each key in the stack, update the matched stack
    Object.keys(stack).forEach((key) => {
      const typedKey = key as keyof ViewStack
      viewStacks.value[matchedIndex] = {
        ...viewStacks.value[matchedIndex],
        [key]: stack[typedKey],
      }
    })
  }

  const itemsBySubcategory = computed(() => {
    return subcategorizeItems(mergedNodes.value)
  })

  const setStackBaselineItems = () => {
    const stack = getLastActiveStack()

    if (!stack || !activeViewStack.value.uuid) {
      return
    }

    let stackItems = stack?.items ?? []

    if (!stack?.items) {
      const subcategory = stack?.subcategory ?? DEFAULT_SUBCATEGORY

      let itemsInSubcategory = itemsBySubcategory.value[subcategory]

      if (!isAskAiEnabled.value) {
        itemsInSubcategory = itemsInSubcategory.filter(
          (item) => item.key !== AI_TRANSFORM_NODE_TYPE,
        )
      }

      const sections = stack.sections

      if (sections) {
        stackItems = groupItemsInSections(itemsInSubcategory, sections)
      }
      else {
        stackItems = itemsInSubcategory
      }
    }

    // Ensure that the nodes specified in `stack.forceIncludeNodes` are always included,
    // regardless of whether the subcategory is matched
    if ((stack.forceIncludeNodes ?? []).length > 0) {
      const matchedNodes = mergedNodes.value
        .filter((item) => stack.forceIncludeNodes?.includes(item.name))
        .map((item) => transformNodeType(item, stack.subcategory))

      stackItems.push(...matchedNodes)
    }

    if (stack.baseFilter) {
      stackItems = stackItems.filter(stack.baseFilter)
    }

    if (stack.itemsMapper) {
      stackItems = stackItems.map(stack.itemsMapper)
    }

    // Sort only if non-root view
    if (!stack.items) {
      stackItems = sortNodeCreateElements(stackItems)
    }

    updateCurrentViewStack({ baselineItems: stackItems })
  }

  const pushViewStack = (stack: ViewStack, options: { resetStacks?: boolean } = {}) => {
    if (options.resetStacks) {
      resetViewStacks()
    }

    if (activeViewStack.value.uuid) {
      updateCurrentViewStack({
        activeIndex: keyboardNavStore.getActiveItemIndex(),
      })
    }

    const newStackUuid = nanoid(4)
    viewStacks.value.push({
      ...stack,
      uuid: newStackUuid,
      transitionDirection: 'in',
      activeIndex: 0,
    })
    setStackBaselineItems()
  }

  const popViewStack = () => {
    if (activeViewStack.value.uuid) {
      viewStacks.value.pop()
      updateCurrentViewStack({ transitionDirection: 'out' })
    }
  }

  const activeViewStackMode = computed(
    () => activeViewStack.value.mode || TRIGGER_NODE_CREATOR_VIEW,
  )

  async function gotoCompatibleConnectionView(
    connectionType: NodeConnectionType,
    isOutput?: boolean,
    filter?: INodeInputFilter,
  ) {
    let nodesByConnectionType: { [key: string]: string[] }
    let relatedAIView: { properties: NodeViewItem['properties'] } | undefined

    if (isOutput === true) {
      nodesByConnectionType = useNodeTypesStore().visibleNodeTypesByInputConnectionTypeNames
      relatedAIView = {
        properties: {
          title: 'AI 节点',
          icon: 'robot',
        },
      }
    }
    else {
      nodesByConnectionType = useNodeTypesStore().visibleNodeTypesByOutputConnectionTypeNames

      relatedAIView = AINodesView([]).items.find(
        (item) => item.properties.connectionType === connectionType,
      )
    }

    // Only add info field if the view does not have any filters (e.g.
    let extendedInfo = {}

    if (!filter?.nodes?.length && relatedAIView?.properties.info) {
      extendedInfo = { info: relatedAIView?.properties.info }
    }

    await nextTick()

    pushViewStack(
      {
        title: relatedAIView?.properties.title,
        ...extendedInfo,
        rootView: AI_OTHERS_NODE_CREATOR_VIEW,
        mode: 'nodes',
        items: nodeCreatorStore.allNodeCreatorNodes,
        nodeIcon: {
          iconType: 'icon',
          icon: relatedAIView?.properties.icon,
          color: relatedAIView?.properties.iconProps?.color,
        },
        panelClass: relatedAIView?.properties.panelClass,
        baseFilter: (i: INodeCreateElement) => {
          // AI Code node could have any connection type so we don't want to display it
          // in the compatible connection view as it would be displayed in all of them
          if (i.key === AI_CODE_NODE_TYPE) {
            return false
          }

          const displayNode = nodesByConnectionType[connectionType].includes(i.key)

          // TODO: Filtering works currently fine for displaying compatible node when dropping
          //       input connections. However, it does not work for output connections.
          //       For that reason does it currently display nodes that are maybe not compatible
          //       but then errors once it got selected by the user.
          if (displayNode && filter?.nodes?.length) {
            return filter.nodes.includes(i.key)
          }

          return displayNode
        },
        itemsMapper(item) {
          return {
            ...item,
            subcategory: connectionType,
          }
        },
        actionsFilter: (items: ActionTypeDescription[]) => {
          // Filter out actions that are not compatible with the connection type
          if (items.some((item) => item.outputConnectionType)) {
            return items.filter((item) => item.outputConnectionType === connectionType)
          }

          return items
        },
        hideActions: true,
        preventBack: true,
      },
      { resetStacks: true },
    )
  }

  return {
    viewStacks,
    resetViewStacks,

    activeViewStack,
    activeViewStackMode,

    pushViewStack,
    popViewStack,

    gotoCompatibleConnectionView,
    updateCurrentViewStack,
  }
})
