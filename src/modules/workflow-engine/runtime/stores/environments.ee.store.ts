import { ExpressionError } from 'n8n-workflow/dist/errors/expression.error.js'

import { StoreKey } from '#wf/constants/store'
import { EnvService } from '#wf/services/environments.ee'
import type { EnvironmentVariable } from '#wf/types/interface'

export const useEnvironmentsStore = defineStore(StoreKey.Environments, () => {
  const variables = ref<EnvironmentVariable[]>([])

  async function fetchAllVariables() {
    const data = await EnvService.getVariables()

    variables.value = data

    return data
  }

  async function createVariable(variable: Omit<EnvironmentVariable, 'id'>) {
    const data = await EnvService.createVariable(variable)

    variables.value.unshift(data)

    return data
  }

  async function updateVariable(variable: EnvironmentVariable) {
    const data = await EnvService.updateVariable(variable)

    variables.value = variables.value.map((v) => (v.id === data.id ? data : v))

    return data
  }

  async function deleteVariable(variable: EnvironmentVariable) {
    const data = await EnvService.deleteVariable({ id: variable.id })

    variables.value = variables.value.filter((v) => v.id !== variable.id)

    return data
  }

  const variablesAsObject = computed(() => {
    const asObject = variables.value.reduce<Record<string, string | boolean | number>>(
      (acc, variable) => {
        acc[variable.key] = variable.value

        return acc
      },
      {},
    )

    return new Proxy(asObject, {
      set() {
        throw new ExpressionError('Cannot assign values to variables at runtime')
      },
    })
  })

  return {
    variables,
    variablesAsObject,
    fetchAllVariables,
    createVariable,
    updateVariable,
    deleteVariable,
  }
})
