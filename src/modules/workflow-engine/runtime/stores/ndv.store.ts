import { nanoid } from 'nanoid'

import { NodeConnectionType } from '#wf/constants/canvas'
import { LOCAL_STORAGE_MAPPING_IS_ONBOARDED, LOCAL_STORAGE_NDV_INPUT_PANEL_DISPLAY_MODE, LOCAL_STORAGE_NDV_OUTPUT_PANEL_DISPLAY_MODE } from '#wf/constants/common'
import { StoreKey } from '#wf/constants/store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { Draggable, InputPanel, IRunDataDisplayMode, NodePanelType, OutputPanel, TargetItem } from '#wf/types/interface'

import type { UnsafeAny } from '~/types/common'

export const useNDVStore = defineStore(StoreKey.NDV, () => {
  const workflowStore = useWorkflowStore()

  const activeNodeName = ref<string | null>(null)

  const setActiveNodeName = (nodeName: string | null) => {
    activeNodeName.value = nodeName
  }

  const activeNode = computed(() => {
    return workflowStore.getNodeByName(activeNodeName.value || '')
  })

  const input = ref<InputPanel>({
    nodeName: undefined,
    run: undefined,
    branch: undefined,
    data: {
      isEmpty: true,
    },
  })

  const focusedMappableInput = ref('')

  const setMappableNDVInputFocus = (paramName: string): void => {
    focusedMappableInput.value = paramName
  }

  const highlightDraggables = ref(false)

  const setHighlightDraggables = (highlight: boolean) => {
    highlightDraggables.value = highlight
  }

  const localStorageMappingIsOnboarded = useLocalStorage(LOCAL_STORAGE_MAPPING_IS_ONBOARDED)

  const isMappingOnboarded = ref(localStorageMappingIsOnboarded.value === 'true')

  const hoveringItem = ref<null | TargetItem>(null)
  const expressionOutputItemIndex = ref(0)

  const ndvInputNodeName = computed(() => {
    return input.value.nodeName
  })
  const ndvInputBranchIndex = computed(() => input.value.branch)

  const isInputParentOfActiveNode = computed(() => {
    const inputNodeName = ndvInputNodeName.value

    if (!activeNode.value || !inputNodeName) {
      return false
    }

    const workflow = workflowStore.getCurrentWorkflow()
    const parentNodes = workflow.getParentNodes(activeNode.value.name, NodeConnectionType.Main as UnsafeAny, 1)

    return parentNodes.includes(inputNodeName)
  })

  const getHoveringItem = computed(() => {
    if (isInputParentOfActiveNode.value) {
      return hoveringItem.value
    }

    return null
  })

  const ndvInputRunIndex = computed(() => input.value.run)

  const isInputPanelEmpty = computed(() => input.value.data.isEmpty)

  const expressionTargetItem = computed(() => {
    if (getHoveringItem.value) {
      return getHoveringItem.value
    }

    if (expressionOutputItemIndex.value && ndvInputNodeName.value) {
      return {
        nodeName: ndvInputNodeName.value,
        runIndex: ndvInputRunIndex.value ?? 0,
        outputIndex: ndvInputBranchIndex.value ?? 0,
        itemIndex: expressionOutputItemIndex.value,
      }
    }

    return null
  })

  const draggable = ref<Draggable>({
    isDragging: false,
    type: '',
    data: '',
    dimensions: null,
    activeTarget: null,
  })

  const isDraggableDragging = computed(() => draggable.value.isDragging)
  const draggableType = computed(() => draggable.value.type)
  const canDraggableDrop = computed(() => draggable.value.activeTarget !== null)
  const draggableStickyPos = computed(() => draggable.value.activeTarget?.stickyPosition ?? null)

  const draggableStartDragging = ({
    type,
    data,
    dimensions,
  }: { type: string, data: string, dimensions: DOMRect | null }): void => {
    draggable.value = {
      isDragging: true,
      type,
      data,
      dimensions,
      activeTarget: null,
    }
  }

  const draggableStopDragging = (): void => {
    draggable.value = {
      isDragging: false,
      type: '',
      data: '',
      dimensions: null,
      activeTarget: null,
    }
  }

  const ndvNodeInputNumber = computed(() => {
    const returnData: { [nodeName: string]: number[] } = {}
    const workflow = workflowStore.getCurrentWorkflow()
    const activeNodeConections = (
      workflow.connectionsByDestinationNode[activeNode.value?.name || ''] ?? {}
    ).main

    if (!activeNodeConections || activeNodeConections.length < 2) {
      return returnData
    }

    for (const [index, connection] of activeNodeConections.entries()) {
      for (const node of connection ?? []) {
        if (!returnData[node.node]) {
          returnData[node.node] = []
        }

        returnData[node.node].push(index + 1)
      }
    }

    return returnData
  })

  const output = ref<OutputPanel>({
    branch: undefined,
    data: {
      isEmpty: true,
    },
    editMode: {
      enabled: false,
      value: '',
    },
  })

  const inputPanelDisplayMode = useLocalStorage<IRunDataDisplayMode>(
    LOCAL_STORAGE_NDV_INPUT_PANEL_DISPLAY_MODE,
    'schema',
  )

  const outputPanelDisplayMode = useLocalStorage<IRunDataDisplayMode>(
    LOCAL_STORAGE_NDV_OUTPUT_PANEL_DISPLAY_MODE,
    'table',
  )

  const outputPanelEditMode = computed(() => output.value.editMode)

  const setPanelDisplayMode = (params: {
    pane: NodePanelType
    mode: IRunDataDisplayMode
  }): void => {
    if (params.pane === 'input') {
      inputPanelDisplayMode.value = params.mode
    }
    else {
      outputPanelDisplayMode.value = params.mode
    }
  }

  const setOutputPanelEditModeEnabled = (isEnabled: boolean): void => {
    output.value.editMode.enabled = isEnabled
  }

  const setOutputPanelEditModeValue = (payload: string): void => {
    output.value.editMode.value = payload
  }

  const setNDVPanelDataIsEmpty = (params: {
    panel: NodePanelType
    isEmpty: boolean
  }): void => {
    if (params.panel === 'input') {
      input.value.data.isEmpty = params.isEmpty
    }
    else {
      output.value.data.isEmpty = params.isEmpty
    }
  }

  const setNDVBranchIndex = (e: { pane: NodePanelType, branchIndex: number }): void => {
    if (e.pane === 'input') {
      input.value.branch = e.branchIndex
    }
    else {
      output.value.branch = e.branchIndex
    }
  }

  const isNDVOpen = computed(() => activeNodeName.value !== null)

  const reset = () => {
    setActiveNodeName(null)
  }

  const pushRef = ref('')

  const setNDVPushRef = (): void => {
    pushRef.value = `ndv-${nanoid(4)}`
  }

  const resetNDVPushRef = (): void => {
    pushRef.value = ''
  }

  const setInputNodeName = (nodeName: string | undefined): void => {
    input.value.nodeName = nodeName
  }

  const setInputRunIndex = (run?: number): void => {
    input.value.run = run
  }

  const draggableData = computed(() => draggable.value.data)

  const ndvInputData = computed(() => {
    const executionData = workflowStore.getWorkflowExecution
    const inputNodeName: string | undefined = input.value.nodeName
    const inputRunIndex: number = input.value.run ?? 0
    const inputBranchIndex: number = input.value.branch ?? 0

    if (
      !executionData
      || !inputNodeName
      || inputRunIndex === undefined
      || inputBranchIndex === undefined
    ) {
      return []
    }

    return (
      executionData.data?.resultData?.runData?.[inputNodeName]?.[inputRunIndex]?.data?.main?.[
        inputBranchIndex
      ] ?? []
    )
  })

  const focusedInputPath = ref('')

  const setFocusedInputPath = (path: string) => {
    focusedInputPath.value = path
  }

  return {
    isNDVOpen,

    activeNode,
    activeNodeName,
    setActiveNodeName,

    expressionTargetItem,
    isInputParentOfActiveNode,
    ndvInputNodeName,
    ndvInputBranchIndex,
    ndvInputRunIndex,
    isInputPanelEmpty,

    isMappingOnboarded,
    focusedMappableInput,
    setMappableNDVInputFocus,
    ndvNodeInputNumber,
    hoveringItem,
    getHoveringItem,

    pushRef,
    setNDVPushRef,
    resetNDVPushRef,

    highlightDraggables,
    setHighlightDraggables,

    setInputNodeName,
    setInputRunIndex,

    focusedInputPath,
    setFocusedInputPath,

    canDraggableDrop,
    draggableStickyPos,
    draggableStartDragging,
    draggableStopDragging,
    isDraggableDragging,
    draggableType,

    ndvInputData,
    draggableData,

    inputPanelDisplayMode,
    outputPanelDisplayMode,
    outputPanelEditMode,
    setPanelDisplayMode,
    setOutputPanelEditModeEnabled,
    setOutputPanelEditModeValue,
    setNDVPanelDataIsEmpty,
    setNDVBranchIndex,
    reset,
  }
})
