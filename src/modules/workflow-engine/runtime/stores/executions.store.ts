import type { AnnotationVote, ExecutionSummary, IDataObject } from 'n8n-workflow'

import { StoreKey } from '#wf/constants/store'
import { useSettingsStore } from '#wf/stores/setting.store'
import type {
  ExecutionFilterType,
  ExecutionsQueryFilter,
  ExecutionSummaryWithScopes,
  IExecutionDeleteFilter,
  IExecutionResponse,
  IExecutionsStopData,
} from '#wf/types/interface'
import { unflattenExecutionData } from '#wf/utils/apiUtils'
import { executionFilterToQueryFilter, getDefaultExecutionFilters } from '#wf/utils/executionUtils'

import { ExecutionService } from '~/features/space/services/execution'
import { useSpaceStore } from '~/features/space/stores/space'

export const useExecutionsStore = defineStore(StoreKey.Executions, () => {
  const spaceStore = useSpaceStore()
  const settingsStore = useSettingsStore()

  const loading = ref(false)
  const itemsPerPage = ref(10)

  const activeExecution = ref<ExecutionSummary | null>(null)

  const setActiveExecution = (execution: ExecutionSummary) => {
    activeExecution.value = execution
  }

  const filters = ref<ExecutionFilterType>(getDefaultExecutionFilters())
  const executionsFilters = computed<ExecutionsQueryFilter>(() => {
    const filter = executionFilterToQueryFilter(filters.value)

    if (spaceStore.activeSpaceId) {
      filter.projectId = spaceStore.activeSpaceId
    }

    return filter
  })
  const currentExecutionsFilters = computed<Partial<ExecutionFilterType>>(() => ({
    ...(filters.value.workflowId !== 'all' ? { workflowId: filters.value.workflowId } : {}),
  }))

  const autoRefresh = ref(true)
  const autoRefreshTimeout = ref<NodeJS.Timeout | null>(null)
  const autoRefreshDelay = ref(4 * 1000) // Refresh data every 4 secs

  const executionsById = ref<Record<string, ExecutionSummaryWithScopes>>({})
  const executionsCount = ref(0)
  const executionsCountEstimated = ref(false)
  const executions = computed(() => {
    const data = Object.values(executionsById.value)

    data.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    })

    return data
  })

  const executionsByWorkflowId = computed(() =>
    executions.value.reduce<Record<string, ExecutionSummary[]>>((acc, execution) => {
      if (!acc[execution.workflowId]) {
        acc[execution.workflowId] = []
      }

      acc[execution.workflowId].push(execution)

      return acc
    }, {}),
  )

  const currentExecutionsById = ref<Record<string, ExecutionSummaryWithScopes>>({})
  const startedAtSortFn = (a: ExecutionSummary, b: ExecutionSummary) =>
    new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime()

  /**
   * Prioritize `running` over `new` executions, then sort by start timestamp.
   */
  const statusThenStartedAtSortFn = (a: ExecutionSummary, b: ExecutionSummary) => {
    if (a.status && b.status) {
      const statusPriority: { [key: string]: number } = { running: 1, new: 2 }
      const statusComparison = statusPriority[a.status] - statusPriority[b.status]

      if (statusComparison !== 0) {
        return statusComparison
      }
    }

    return startedAtSortFn(a, b)
  }

  const sortFn = settingsStore.isConcurrencyEnabled ? statusThenStartedAtSortFn : startedAtSortFn

  const currentExecutions = computed(() => {
    const data = Object.values(currentExecutionsById.value)

    data.sort(sortFn)

    return data
  })

  const currentExecutionsByWorkflowId = computed(() =>
    currentExecutions.value.reduce<Record<string, ExecutionSummary[]>>((acc, execution) => {
      if (!acc[execution.workflowId]) {
        acc[execution.workflowId] = []
      }

      acc[execution.workflowId].push(execution)

      return acc
    }, {}),
  )

  const allExecutions = computed(() => [...currentExecutions.value, ...executions.value])

  function addExecution(execution: ExecutionSummaryWithScopes) {
    executionsById.value = {
      ...executionsById.value,
      [execution.id]: {
        ...execution,
        mode: execution.mode,
      },
    }
  }

  function addCurrentExecution(execution: ExecutionSummaryWithScopes) {
    currentExecutionsById.value[execution.id] = {
      ...execution,
      mode: execution.mode,
    }
  }

  async function fetchExecutions(
    filter = executionsFilters.value,
    page = 1,
  ) {
    loading.value = true

    try {
      const data = await ExecutionService.getExecutions({
        workflowId: filter.workflowId ?? '',
        page,
        page_size: itemsPerPage.value,
      })

      currentExecutionsById.value = {}
      data.list.forEach((execution) => {
        if (['new', 'running'].includes(execution.status as string)) {
          addCurrentExecution(execution)
        }
        else {
          addExecution(execution)
        }
      })

      executionsCount.value = data.page_sum * itemsPerPage.value
      executionsCountEstimated.value = true

      return data
    }
    finally {
      loading.value = false
    }
  }

  function removeExecution(id: string) {
    const { [id]: _, ...rest } = executionsById.value
    executionsById.value = rest
  }

  function setFilters(value: ExecutionFilterType) {
    filters.value = value
  }

  function stopAutoRefreshInterval() {
    if (autoRefreshTimeout.value) {
      clearTimeout(autoRefreshTimeout.value)
      autoRefreshTimeout.value = null
    }
  }

  async function startAutoRefreshInterval(workflowId?: string) {
    stopAutoRefreshInterval()
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    await loadAutoRefresh(workflowId)
  }

  async function loadAutoRefresh(workflowId?: string): Promise<void> {
    const autoRefreshExecutionFilters = {
      ...executionsFilters.value,
      ...(workflowId ? { workflowId } : {}),
    }

    autoRefreshTimeout.value = setTimeout(async () => {
      if (autoRefresh.value) {
        await fetchExecutions(autoRefreshExecutionFilters)
        void startAutoRefreshInterval(workflowId)
      }
    }, autoRefreshDelay.value)
  }

  async function initialize(workflowId?: string) {
    if (workflowId) {
      filters.value.workflowId = workflowId
    }

    await fetchExecutions()
    await startAutoRefreshInterval(workflowId)
  }

  async function fetchExecution(id: string): Promise<IExecutionResponse | undefined> {
    const response = await ExecutionService.getExecution(id)

    return response ? unflattenExecutionData(response) : undefined
  }

  async function annotateExecution(
    id: string,
    data: { tags?: string[], vote?: AnnotationVote | null },
  ): Promise<void> {
    const updatedExecution = await ExecutionService.updateExecution(id, data)

    addExecution(updatedExecution)

    if (updatedExecution.id === activeExecution.value?.id) {
      setActiveExecution(updatedExecution)
    }
  }

  async function stopCurrentExecution(executionId: string): Promise<IExecutionsStopData> {
    return await ExecutionService.stopExecution(executionId)
  }

  async function retryExecution(id: string, loadWorkflow?: boolean): Promise<boolean> {
    return await ExecutionService.retryExecution(loadWorkflow
      ? {
          loadWorkflow: true,
        }
      : undefined)
  }

  async function deleteExecutions(sendData: IExecutionDeleteFilter): Promise<void> {
    await ExecutionService.deleteExecution(sendData as unknown as IDataObject)

    if (sendData.ids) {
      sendData.ids.forEach(removeExecution)
    }

    if (sendData.deleteBefore) {
      const deleteBefore = new Date(sendData.deleteBefore)
      allExecutions.value.forEach((execution) => {
        if (new Date(execution.startedAt) < deleteBefore) {
          removeExecution(execution.id)
        }
      })
    }
  }

  function resetData() {
    executionsById.value = {}
    currentExecutionsById.value = {}
    executionsCount.value = 0
    executionsCountEstimated.value = false
  }

  function reset() {
    itemsPerPage.value = 10
    filters.value = getDefaultExecutionFilters()
    autoRefresh.value = true
    resetData()
    stopAutoRefreshInterval()
  }

  return {
    loading,
    annotateExecution,
    executionsById,
    executions,
    executionsCount,
    executionsCountEstimated,
    executionsByWorkflowId,
    currentExecutions,
    currentExecutionsByWorkflowId,
    activeExecution,
    setActiveExecution,
    fetchExecutions,
    fetchExecution,
    autoRefresh,
    autoRefreshTimeout,
    startAutoRefreshInterval,
    stopAutoRefreshInterval,
    initialize,
    filters,
    setFilters,
    executionsFilters,
    currentExecutionsFilters,
    allExecutions,
    stopCurrentExecution,
    retryExecution,
    deleteExecutions,
    addExecution,
    resetData,
    reset,
  }
})
