import type {
  ICredentialsDecrypted,
  ICredentialType,
  INodeCredentialTestResult,
  ProjectSharingData,
} from 'n8n-workflow'

import { EnterpriseEditionFeature } from '#wf/constants/common'
import { StoreKey } from '#wf/constants/store'
import { aiService } from '#wf/services/ai'
import { credentialsService } from '#wf/services/credentials'
import { credentialsEeService } from '#wf/services/credentials-ee'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useSettingsStore } from '#wf/stores/setting.store'
import type {
  ICredentialMap,
  ICredentialsResponse,
  ICredentialsState,
  ICredentialTypeMap,
  INodeUi,
  IUsedCredential,
} from '#wf/types/interface'
import { splitName } from '#wf/utils/projects.utils'
import { isEmpty, isPresent } from '#wf/utils/typesUtils'

export type CredentialsStore = ReturnType<typeof useCredentialsStore>

export const useCredentialsStore = defineStore(StoreKey.NodeCredentials, () => {
  const state = ref<ICredentialsState>({ credentialTypes: {}, credentials: {} })

  // MARK: Computed

  const credentialTypesById = computed(() => {
    return state.value.credentialTypes
  })

  const allCredentialTypes = computed(() => {
    return Object.values(state.value.credentialTypes).sort((a, b) =>
      a.displayName.localeCompare(b.displayName),
    )
  })

  const allCredentials = computed(() => {
    return Object.values(state.value.credentials).sort((a, b) => a.name.localeCompare(b.name))
  })

  const allCredentialsByType = computed(() => {
    const credentials = allCredentials.value

    const types = allCredentialTypes.value

    return types.reduce(
      (accu: { [type: string]: ICredentialsResponse[] }, type: ICredentialType) => {
        accu[type.name] = credentials.filter(
          (cred: ICredentialsResponse) => cred.type === type.name,
        )

        return accu
      },
      {},
    )
  })

  const allUsableCredentialsByType = computed(() => {
    const credentials = allCredentials.value
    const types = allCredentialTypes.value

    return types.reduce(
      (accu: { [type: string]: ICredentialsResponse[] }, type: ICredentialType) => {
        accu[type.name] = credentials.filter((cred: ICredentialsResponse) => {
          return cred.type === type.name
        })

        return accu
      },
      {},
    )
  })

  const allUsableCredentialsForNode = computed(() => {
    return (node: INodeUi): ICredentialsResponse[] => {
      let credentials: ICredentialsResponse[] = []
      const nodeType = useNodeTypesStore().getNodeType(node.type, node.typeVersion)

      if (nodeType?.credentials) {
        nodeType.credentials.forEach((cred) => {
          credentials = credentials.concat(allUsableCredentialsByType.value[cred.name])
        })
      }

      return credentials.sort((a, b) => {
        const aDate = new Date(a.updatedAt)
        const bDate = new Date(b.updatedAt)

        return aDate.getTime() - bDate.getTime()
      })
    }
  })

  const getCredentialTypeByName = computed(() => {
    return (type: string): ICredentialType | undefined => state.value.credentialTypes[type]
  })

  const getCredentialById = computed(() => {
    return (id: string): ICredentialsResponse => state.value.credentials[id]
  })

  const getCredentialByIdAndType = computed(() => {
    return (id: string, type: string): ICredentialsResponse | undefined => {
      const credential = state.value.credentials[id]

      return !credential || credential.type !== type ? undefined : credential
    }
  })

  const getCredentialsByType = computed(() => {
    return (credentialType: string): ICredentialsResponse[] => {
      return allCredentialsByType.value[credentialType] || []
    }
  })

  const getUsableCredentialByType = computed(() => {
    return (credentialType: string): ICredentialsResponse[] => {
      return allUsableCredentialsByType.value[credentialType] || []
    }
  })

  const getNodesWithAccess = computed(() => {
    return (credentialTypeName: string) => {
      const credentialType = getCredentialTypeByName.value(credentialTypeName)

      if (!credentialType) {
        return []
      }

      const nodeTypesStore = useNodeTypesStore()

      return (credentialType.supportedNodes ?? [])
        .map((nodeType) => nodeTypesStore.getNodeType(nodeType))
        .filter(isPresent)
    }
  })

  const getScopesByCredentialType = computed(() => {
    return (credentialTypeName: string) => {
      const credentialType = getCredentialTypeByName.value(credentialTypeName)

      if (!credentialType) {
        return []
      }

      const scopeProperty = credentialType.properties.find((p) => p.name === 'scope')

      if (
        !scopeProperty
        || !scopeProperty.default
        || typeof scopeProperty.default !== 'string'
        || scopeProperty.default === ''
      ) {
        return []
      }

      let { default: scopeDefault } = scopeProperty

      // disregard expressions for display
      scopeDefault = scopeDefault.replace(/^=/, '').replace(/\{\{.*\}\}/, '')

      if (/ /.test(scopeDefault)) {
        return scopeDefault.split(' ')
      }

      if (/,/.test(scopeDefault)) {
        return scopeDefault.split(',')
      }

      return [scopeDefault]
    }
  })

  const getCredentialOwnerName = computed(() => {
    return (credential: ICredentialsResponse | IUsedCredential | undefined): string => {
      const { name, email } = splitName(credential?.homeProject?.name ?? '')

      return name
        ? email
          ? `${name} (${email})`
          : name
        : (email ?? '拥有者')
    }
  })

  const getCredentialOwnerNameById = computed(() => {
    return (credentialId: string): string => {
      const credential = getCredentialById.value(credentialId)

      return getCredentialOwnerName.value(credential)
    }
  })

  const httpOnlyCredentialTypes = computed(() => {
    return allCredentialTypes.value.filter(
      (credentialType) => credentialType.httpRequestNode && !credentialType.httpRequestNode.hidden,
    )
  })

  // ---------------------------------------------------------------------------
  // #region Methods
  // ---------------------------------------------------------------------------

  const setCredentialTypes = (credentialTypes: ICredentialType[]) => {
    state.value.credentialTypes = credentialTypes.reduce(
      (accu: ICredentialTypeMap, cred: ICredentialType) => {
        accu[cred.name] = cred

        return accu
      },
      {},
    )
  }

  const addCredentials = (credentials: ICredentialsResponse[]) => {
    credentials.forEach((cred: ICredentialsResponse) => {
      if (cred.id) {
        state.value.credentials[cred.id] = { ...state.value.credentials[cred.id], ...cred }
      }
    })
  }

  const setCredentials = (credentials: ICredentialsResponse[]) => {
    state.value.credentials = credentials.reduce(
      (accu: ICredentialMap, cred: ICredentialsResponse) => {
        if (cred.id) {
          accu[cred.id] = cred
        }

        return accu
      },
      {},
    )
  }

  const upsertCredential = (credential: ICredentialsResponse) => {
    if (credential.id) {
      state.value.credentials = {
        ...state.value.credentials,
        [credential.id]: {
          ...state.value.credentials[credential.id],
          ...credential,
        },
      }
    }
  }

  const fetchCredentialTypes = async (forceFetch: boolean) => {
    if (allCredentialTypes.value.length > 0 && !forceFetch) {
      return
    }

    const credentialTypes = await credentialsService.getCredentialTypes()
    setCredentialTypes(credentialTypes)
  }

  const fetchAllCredentials = async (
    projectId?: string,
    includeScopes = true,
  ): Promise<ICredentialsResponse[]> => {
    const filter = {
      projectId,
    }

    const credentials = await credentialsService.getAllCredentials(
      isEmpty(filter) ? undefined : filter,
      includeScopes,
    )
    setCredentials(credentials)

    return credentials
  }

  const fetchAllCredentialsForWorkflow = async (
    options: { workflowId: string, projectId: string },
  ): Promise<ICredentialsResponse[]> => {
    const credentials = await credentialsService.getAllCredentialsForWorkflow(options)
    setCredentials(credentials)

    return credentials
  }

  const setCredentialSharedWith = async (payload: {
    sharedWithProjects: ProjectSharingData[]
    credentialId: string
  }): Promise<ICredentialsResponse> => {
    if (useSettingsStore().isEnterpriseFeatureEnabled[EnterpriseEditionFeature.Sharing]) {
      await credentialsEeService.setCredentialSharedWith(
        payload.credentialId,
        {
          shareWithIds: payload.sharedWithProjects.map((project) => project.id),
        },
      )

      state.value.credentials[payload.credentialId] = {
        ...state.value.credentials[payload.credentialId],
        sharedWithProjects: payload.sharedWithProjects,
      }
    }

    return state.value.credentials[payload.credentialId]
  }

  const createNewCredential = async (
    data: ICredentialsDecrypted,
    projectId?: string,
  ): Promise<ICredentialsResponse> => {
    const settingsStore = useSettingsStore()
    const credential = await credentialsService.createNewCredential({
      name: data.name,
      type: data.type,
      data: data.data ?? {},
      projectId,
    })

    if (data?.homeProject && !credential.homeProject) {
      credential.homeProject = data.homeProject as ProjectSharingData
    }

    if (settingsStore.isEnterpriseFeatureEnabled[EnterpriseEditionFeature.Sharing]) {
      upsertCredential(credential)

      if (data.sharedWithProjects) {
        await setCredentialSharedWith({
          credentialId: credential.id,
          sharedWithProjects: data.sharedWithProjects,
        })
      }
    }
    else {
      upsertCredential(credential)
    }

    return credential
  }

  const updateCredential = async (params: {
    id: string
    data: ICredentialsDecrypted
  }): Promise<ICredentialsResponse> => {
    const { id, data } = params
    const credential = await credentialsService.updateCredential(id, data)

    upsertCredential(credential)

    return credential
  }

  const deleteCredential = async ({ id }: { id: string }) => {
    const deleted = await credentialsService.deleteCredential(id)

    if (deleted) {
      const { [id]: deletedCredential, ...rest } = state.value.credentials
      state.value.credentials = rest
    }
  }

  const oAuth2Authorize = async (data: ICredentialsResponse): Promise<string> => {
    return await credentialsService.oAuth2CredentialAuthorize(data)
  }

  const oAuth1Authorize = async (data: ICredentialsResponse): Promise<string> => {
    return await credentialsService.oAuth1CredentialAuthorize(data)
  }

  const testCredential = async (
    data: ICredentialsDecrypted,
  ): Promise<INodeCredentialTestResult> => {
    return await credentialsService.testCredential({ credentials: data })
  }

  const claimFreeAiCredits = async (projectId?: string): Promise<ICredentialsResponse> => {
    const credential = await aiService.claimFreeAiCredits({ projectId })
    upsertCredential(credential)

    return credential
  }

  return {
    state,
    getCredentialOwnerName,
    getCredentialsByType,
    getCredentialById,
    getCredentialTypeByName,
    getCredentialByIdAndType,
    getNodesWithAccess,
    getUsableCredentialByType,
    credentialTypesById,
    httpOnlyCredentialTypes,
    getScopesByCredentialType,
    getCredentialOwnerNameById,
    allUsableCredentialsForNode,
    allCredentials,
    allCredentialTypes,
    allUsableCredentialsByType,
    setCredentialTypes,
    addCredentials,
    setCredentials,
    deleteCredential,
    upsertCredential,
    fetchCredentialTypes,
    fetchAllCredentials,
    fetchAllCredentialsForWorkflow,
    createNewCredential,
    updateCredential,
    oAuth1Authorize,
    oAuth2Authorize,
    testCredential,
    setCredentialSharedWith,
    claimFreeAiCredits,
  }
})
