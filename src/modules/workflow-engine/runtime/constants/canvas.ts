export const enum CanvasConnectionMode {
  Input = 'inputs',
  Output = 'outputs',
}

export const enum NodeConnectionType {
  AiAgent = 'ai_agent',
  AiChain = 'ai_chain',
  AiDocument = 'ai_document',
  AiEmbedding = 'ai_embedding',
  AiLanguageModel = 'ai_languageModel',
  AiMemory = 'ai_memory',
  AiOutputParser = 'ai_outputParser',
  AiRetriever = 'ai_retriever',
  AiTextSplitter = 'ai_textSplitter',
  AiTool = 'ai_tool',
  AiVectorStore = 'ai_vectorStore',
  Main = 'main',
}

export const canvasConnectionModes = [
  CanvasConnectionMode.Input,
  CanvasConnectionMode.Output,
] as const

export const enum CanvasNodeRenderType {
  Default = 'default',
  StickyNote = 'n8n-nodes-base.stickyNote',
  AddNodes = 'n8n-nodes-internal.addNodes',
}
