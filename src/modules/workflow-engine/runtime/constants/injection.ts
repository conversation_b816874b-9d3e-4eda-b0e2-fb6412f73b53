import type { INodeProperties } from 'n8n-workflow'

import type { CanvasInjectionData, CanvasNodeHandleInjectionData, CanvasNodeInjectionData } from '#wf/types/canvas'

export const CanvasKey = 'canvas' as unknown as InjectionKey<CanvasInjectionData>

export const CanvasNodeKey = 'canvasNode' as unknown as InjectionKey<CanvasNodeInjectionData>

export const CanvasNodeHandleKey = 'canvasNodeHandle' as unknown as InjectionKey<CanvasNodeHandleInjectionData>

/** 节点参数注入 */
export const NodeParamKey = 'nodeParam' as unknown as InjectionKey<INodeProperties>
