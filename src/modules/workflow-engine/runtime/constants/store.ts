export const enum StoreKey {
  Workflow = 'workflow',
  NodeTypes = 'nodeTypes',
  NDV = 'ndv',
  Root = 'root',
  NodeCreator = 'nodeCreator',
  NodeCreatorViewStacks = 'nodeCreatorViewStacks',
  NodeCreatorKeyboardNav = 'nodeCreatorKeyboardNavigation',
  WorkflowSetting = 'workflowSetting',
  WorkflowCanvas = 'canvas',
  WorkflowTemplate = 'workflowTemplate',
  NodeCredentials = 'nodeCredentials',
  History = 'history',
  UI = 'ui',
  Executions = 'executions',
  PushConnection = 'pushConnection',
  Tags = 'tags',
  AnnotationTags = 'annotationTags',
  Environments = 'environments',
  ExternalSecrets = 'externalSecrets',
  SchemaPreview = 'schemaPreview',
}
