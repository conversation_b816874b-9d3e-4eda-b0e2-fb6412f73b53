import type { IWorkflowDb } from '#wf/types/interface'

export const mockWorkflow: IWorkflowDb = {
  id: 'q5BdEF7rlrcknjNs',
  name: 'My workflow 3',
  active: false,
  createdAt: -1,
  updatedAt: -1,
  connections: {
    'Extract from File': {
      main: [
        [
          {
            node: 'Switch',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Loop Over Items': {
      main: [
        [
          {
            node: 'Extract from File',
            type: 'main',
            index: 0,
          },
        ],
        [
          {
            node: 'Extract from File',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Replace Me': {
      main: [
        [
          {
            node: 'Loop Over Items',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Sheets1': {
      main: [
        [
          {
            node: 'If',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    If: {
      main: [
        [
          {
            node: 'Wait1',
            type: 'main',
            index: 0,
          },
        ],
        [
          {
            node: 'Replace Me',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'On form submission': {
      main: [
        [
          {
            node: 'Google Sheets2',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Drive': {
      main: [
        [
          {
            node: 'Read/Write Files from Disk3',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Replace Me1': {
      main: [
        [
          {
            node: 'Loop Over Items1',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    Code1: {
      main: [
        [
          {
            node: 'Wait3',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    Sort: {
      main: [
        [
          {
            node: 'Google Sheets7',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    AI筛选前端工程师简历: {
      main: [
        [
          {
            node: 'Code',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    AI筛选项目经理简历: {
      main: [
        [
          {
            node: 'Code',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Sheets2': {
      main: [
        [
          {
            node: 'Wait5',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Sheets6': {
      main: [
        [
          {
            node: 'Wait6',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Sheets8': {
      main: [
        [
          {
            node: 'Read/Write Files from Disk',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Gemini Chat Model': {
      ai_languageModel: [
        [
          {
            node: 'AI筛选管培生简历',
            type: 'ai_languageModel',
            index: 0,
          },
        ],
      ],
    },
    'Google Gemini Chat Model1': {
      ai_languageModel: [
        [
          {
            node: 'AI筛选前端工程师简历',
            type: 'ai_languageModel',
            index: 0,
          },
        ],
      ],
    },
    'Google Gemini Chat Model2': {
      ai_languageModel: [
        [
          {
            node: 'AI筛选项目经理简历',
            type: 'ai_languageModel',
            index: 0,
          },
        ],
      ],
    },
    'Google Gemini Chat Model3': {
      ai_languageModel: [
        [
          {
            node: 'AI筛选安全服务工程师简历',
            type: 'ai_languageModel',
            index: 0,
          },
        ],
      ],
    },
    AI筛选安全服务工程师简历: {
      main: [
        [
          {
            node: 'Code',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Sheets': {
      main: [
        [
          {
            node: 'Sort',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Sheets7': {
      main: [
        [
          {
            node: 'Wait2',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Sheets3': {
      main: [
        [
          {
            node: 'Code1',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Sheets9': {
      main: [
        [
          {
            node: 'Wait4',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Sheets5': {
      main: [
        [
          {
            node: 'Loop Over Items1',
            type: 'main',
            index: 0,
          },
          {
            node: 'Google Drive',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    AI筛选管培生简历: {
      main: [
        [
          {
            node: 'Code',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Gemini Chat Model5': {
      ai_languageModel: [
        [
          {
            node: 'AI筛选后端工程师简历',
            type: 'ai_languageModel',
            index: 0,
          },
        ],
      ],
    },
    AI筛选后端工程师简历: {
      main: [
        [
          {
            node: 'Code',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Read/Write Files from Disk': {
      main: [
        [
          {
            node: 'Loop Over Items',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Loop Over Items1': {
      main: [
        [
          {
            node: 'Read/Write Files from Disk2',
            type: 'main',
            index: 0,
          },
        ],
        [
          {
            node: 'Replace Me1',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    Code: {
      main: [
        [
          {
            node: 'Wait',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Read/Write Files from Disk2': {
      main: [
        [
          {
            node: 'Read/Write Files from Disk1',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    'Google Gemini Chat Model6': {
      ai_languageModel: [
        [
          {
            node: 'AI筛选产品助理简历',
            type: 'ai_languageModel',
            index: 0,
          },
        ],
      ],
    },
    Switch: {
      main: [
        [
          {
            node: 'AI筛选管培生简历',
            type: 'main',
            index: 0,
          },
        ],
        [
          {
            node: 'AI筛选前端工程师简历',
            type: 'main',
            index: 0,
          },
        ],
        [
          {
            node: 'AI筛选项目经理简历',
            type: 'main',
            index: 0,
          },
        ],
        [
          {
            node: 'AI筛选安全服务工程师简历',
            type: 'main',
            index: 0,
          },
        ],
        [
          {
            node: 'AI筛选后端工程师简历',
            type: 'main',
            index: 0,
          },
        ],
        [
          {
            node: 'AI筛选产品助理简历',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    AI筛选产品助理简历: {
      main: [
        [
          {
            node: 'Code',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    Wait: {
      main: [
        [
          {
            node: 'Google Sheets1',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    Wait1: {
      main: [
        [
          {
            node: 'Google Sheets',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    Wait2: {
      main: [
        [
          {
            node: 'Google Sheets3',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    Wait3: {
      main: [
        [
          {
            node: 'Google Sheets9',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    Wait4: {
      main: [
        [
          {
            node: 'Google Sheets5',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    Wait5: {
      main: [
        [
          {
            node: 'Google Sheets6',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
    Wait6: {
      main: [
        [
          {
            node: 'Google Sheets8',
            type: 'main',
            index: 0,
          },
        ],
      ],
    },
  },
  nodes: [
    {
      parameters: {
        operation: 'pdf',
        binaryPropertyName: 'data',
        options: {},
      },
      type: 'n8n-nodes-base.extractFromFile',
      typeVersion: 1,
      position: [
        -840,
        200,
      ],
      id: '0e009113-d716-45d9-807c-1013e6cde34b',
      name: 'Extract from File',
    },
    {
      parameters: {
        splitInBatchesNotice: '',
        batchSize: 1,
        options: {},
      },
      type: 'n8n-nodes-base.splitInBatches',
      typeVersion: 3,
      position: [
        -1260,
        380,
      ],
      id: '429deeb7-b07a-49b6-a62c-7c494a3f4776',
      name: 'Loop Over Items',
      alwaysOutputData: true,
    },
    {
      parameters: {},
      type: 'n8n-nodes-base.noOp',
      name: 'Replace Me',
      typeVersion: 1,
      position: [
        -1200,
        1240,
      ],
      id: 'cd5828f6-7a2d-4c7f-9ede-a2c6d2b00ac2',
    },
    {
      parameters: {
        authentication: 'oAuth2',
        resource: 'sheet',
        operation: 'appendOrUpdate',
        documentId: {
          __rl: true,
          value: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit?gid=1049406326#gid=1049406326',
          mode: 'url',
        },
        sheetName: {
          __rl: true,
          value: 1049406326,
          mode: 'list',
          cachedResultName: '全部简历',
          cachedResultUrl: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit#gid=1049406326',
        },
        columns: {
          mappingMode: 'autoMapInputData',
          value: {},
          matchingColumns: [
            '姓名',
          ],
          schema: [
            {
              id: '姓名',
              displayName: '姓名',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
              removed: false,
            },
            {
              id: '专业',
              displayName: '专业',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '简历分数',
              displayName: '简历分数',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '优势分析',
              displayName: '优势分析',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '不足分析',
              displayName: '不足分析',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '文件名',
              displayName: '文件名',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
          ],
        },
        options: {},
      },
      type: 'n8n-nodes-base.googleSheets',
      typeVersion: 4.5,
      position: [
        -560,
        1780,
      ],
      id: '2f11de02-8d5a-437c-aee9-7fa3ce5d0d3e',
      name: 'Google Sheets1',
      issues: {
        credentials: {
          googleSheetsOAuth2Api: [
            'Google Sheets 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        conditions: {
          options: {
            caseSensitive: true,
            leftValue: '',
            typeValidation: 'strict',
            version: 2,
          },
          conditions: [
            {
              id: 'b4068052-1c1a-4ea7-9952-580d16078fbd',
              leftValue: '={{$node["Loop Over Items"].context["noItemsLeft"]}}',
              rightValue: '',
              operator: {
                type: 'boolean',
                operation: 'true',
                singleValue: true,
              },
            },
          ],
          combinator: 'and',
        },
        looseTypeValidation: false,
        options: {},
      },
      type: 'n8n-nodes-base.if',
      typeVersion: 2.2,
      position: [
        -280,
        1780,
      ],
      id: 'd8d512a9-d0c0-44e1-a195-376b7e134b04',
      name: 'If',
    },
    {
      parameters: {
        authentication: 'none',
        formTitle: 'AI自动化筛选简历',
        formDescription: '请输入岗位类型（从已有岗位类型中选择）',
        formFields: {
          values: [
            {
              fieldLabel: '岗位类型',
              fieldType: 'text',
              placeholder: '已有岗位类型：管培生、前端工程师、项目经理、安全服务工程师',
              requiredField: true,
            },
          ],
        },
        responseMode: 'onReceived',
        addFormPage: '',
        options: {
          appendAttribution: true,
          buttonLabel: '确认',
          respondWithOptions: {
            values: {
              respondWith: 'text',
              formSubmittedText: '=提交成功',
            },
          },
        },
      },
      type: 'n8n-nodes-base.formTrigger',
      typeVersion: 2.2,
      position: [
        -2060,
        380,
      ],
      id: '38e43364-8716-45f1-aa3c-80375f32f789',
      name: 'On form submission',
      webhookId: 'e17ac7c8-ba2d-4520-bc74-3d30bd3bd6e8',
    },
    {
      parameters: {
        info: '',
        operation: 'write',
        fileName: '=/mnt/test/n8nAI简历筛选/岗位/{{ $(\'On form submission\').item.json["岗位类型"] }}/简历清单/{{ $(\'On form submission\').item.json["岗位类型"] }}简历清单.xlsx',
        dataPropertyName: 'data',
        options: {},
      },
      type: 'n8n-nodes-base.readWriteFile',
      typeVersion: 1,
      position: [
        1160,
        1900,
      ],
      id: '0a20a0c0-a971-4c1b-b2e8-d400788d0dc5',
      name: 'Read/Write Files from Disk3',
      executeOnce: true,
      onError: 'continueRegularOutput',
    },
    {
      parameters: {
        authentication: 'oAuth2',
        resource: 'file',
        operation: 'download',
        fileId: {
          __rl: true,
          value: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit?gid=1994994412#gid=1994994412',
          mode: 'url',
        },
        options: {
          googleFileConversion: {
            conversion: {
              docsToFormat: 'text/html',
              drawingsToFormat: 'image/jpeg',
              slidesToFormat: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
              sheetsToFormat: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            },
          },
          fileName: '简历清单',
        },
      },
      type: 'n8n-nodes-base.googleDrive',
      typeVersion: 3,
      position: [
        980,
        1900,
      ],
      id: 'd9f522f4-46ff-4f76-9303-64ee62ea020e',
      name: 'Google Drive',
      executeOnce: true,
      issues: {
        credentials: {
          googleDriveOAuth2Api: [
            'Google Drive 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {},
      type: 'n8n-nodes-base.noOp',
      name: 'Replace Me1',
      typeVersion: 1,
      position: [
        -440,
        2420,
      ],
      id: '4d06d926-d067-4bf7-9636-c13339027874',
    },
    {
      parameters: {
        mode: 'runOnceForAllItems',
        language: 'javaScript',
        jsCode: '// Function 节点代码示例\nfor (let i = 0; i < items.length; i++) {\n    // 为每一项添加 \'排名\' 字段，并假设第一行是第1行\n    items[i].json[\'真实行号\'] = i + 2;\n    \n    // 添加总长度信息到每一个 item 中\n    items[i].json[\'total_items\'] = items.length + 1;\n}\n\n// 返回修改后的 items 数组\nreturn items;',
        notice: '',
      },
      type: 'n8n-nodes-base.code',
      typeVersion: 2,
      position: [
        80,
        2020,
      ],
      id: 'd240b2bc-e242-498c-b98a-b12d2759153b',
      name: 'Code1',
    },
    {
      parameters: {
        type: 'simple',
        sortFieldsUi: {
          sortField: [
            {
              fieldName: '简历分数',
              order: 'descending',
            },
          ],
        },
        options: {
          disableDotNotation: false,
        },
      },
      type: 'n8n-nodes-base.sort',
      typeVersion: 1,
      position: [
        -700,
        2020,
      ],
      id: 'd14478bf-ca19-42b3-a63e-c93f1842b3d3',
      name: 'Sort',
    },
    {
      parameters: {
        notice: '',
        model: 'deepseek-reasoner',
        options: {
          baseURL: 'https://api.deepseek.com',
        },
      },
      type: '@n8n/n8n-nodes-langchain.lmChatOpenAi',
      typeVersion: 1,
      position: [
        560,
        420,
      ],
      id: 'cd00b96c-0626-444c-b46b-1ab306e3c4af',
      name: 'OpenAI Chat Model2',
      issues: {
        credentials: {
          openAiApi: [
            'OpenAI 聊天模型 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        notice: '',
        promptType: 'define',
        text: '=角色：\n你是⼀位经验丰富的前端工程师⼈才招聘专家。请帮我评估以下简历是否适合前端⼯程师岗位。\n\n任务：\n你的任务是根据我提的要求，分析候选人的简历，按照我的要求返回信息。\n\n工作职责：\n1、设计、开发和维护复杂的单⻚应⽤(SPA)和渐进式 Web 应⽤(PWA)\n2、将产品需求转化为⾼性能、响应式的⽤户界⾯\n3、优化前端应⽤和后端系统以实现最佳性能、可扩展性和安全性\n4、实施前端测试策略，包括单元测试、集成测试和端到端测试\n5、参与代码审查，确保代码质量和最佳实践的遵循\n6、与跨职能团队合作，从概念到发布管理整个开发⽣命周期\n7、持续关注和评估新兴技术，为团队技术栈的改进提供建议\n8、指导初级开发⼈员，促进团队内的知识共享\n\n必要条件：\n1、计算机科学、软件⼯程或相关领域的学⼠学位\n2、2年以上全栈 Web 开发经验，尤其专注于前端技术\n3、精通 HTML5、CSS3、JavaScript(ES6+)和 TypeScript\n4、深⼊了解⾄少⼀种现代前端框架(如 React、Vue 或 Angular)及其⽣态系统\n5、熟悉服务器端编程语⾔(如 Node.js、Python 或 Java)和相关框架\n6、具备设计和开发 RESTful API 的经验\n7、熟悉关系型数据库(如 MySQL、PostgreSQL)和 NoSQL 数据库(如 MongoDB)\n8、了解前端构建⼯具(如 Webpack、Rollup)和任务运⾏器\n9、熟悉版本控制系统(如 Git)和 CI/CD 流程\n10、具备良好的问题解决能⼒、沟通技巧和团队协作精神\n\n加分项：\n1、具有⼤规模、⾼流量 Web 应⽤开发经验\n2、熟悉微服务架构和容器技术(如 Docker、Kubernetes)\n3、了解 GraphQL 和 Apollo 等相关技术\n4、具有性能优化和⽹站安全最佳实践的经验\n5、熟悉云服务平台(如 AWS、Azure 或 GCP)\n6、对 UI/UX 设计有深⼊理解\n7、具有开源项⽬贡献经历\n\n评分要求：\n1. 仔细分析简历中的教育背景、技能和经验是否符合岗位要求\n2. 评估候选⼈的前端开发相关知识和技能⽔平\n3. 关注候选⼈的项⽬经验和实践能⼒\n4. 评估候选⼈的学习能⼒和团队协作能⼒\n5. 检查是否具备必要的软技能（如沟通能⼒、分析能⼒等）\n\n简历名：{{ $(\'Read/Write Files from Disk\').item.json.fileName }}\n简历内容：\n{{ $json.text }}\n\n评分标准：\n候选⼈基本匹配度评分（1-100分）：\n  教育背景匹配度（权重20%）\n  技术能⼒匹配度（权重35%）\n  项⽬经验匹配度（权重25%）\n  软技能匹配度（权重20%）\n\n根据简历内容和上述要求，严格按照以下格式输出信息（每类只留一行，不要换行）：\n1. 姓名：{姓名}\n2. 专业：{专业}\n3. 简历分数：{简历分数}（按照上述要求对简历内容进行精确评分，精确到小数点后两位）\n4. 优势分析：{优势分析}（列出候选⼈的3-5个主要优势，足够细致，把每个优势对应具体做过的事情说出来，把优势分析改成一行，不要换行。）\n5. 不足分析：{不足分析}（列出需要关注的2-3个潜在问题或改进空间，把不足分析改成一行，不要换行。）\n6. 简历名：{简历名}（按我上面写的简历名返回）\n\n示例：\n1. 姓名：施劲龙\n2. 专业：机电一体化技术\n3. 简历分数：78.25\n4. 优势分析：具备较强的渗透测试技能，熟悉OWASP TOP 10漏洞及常见Web容器和中间件安全问题；能使用Python编写渗透脚本，并具备PHP、JAVA、JS简单代码审计能力；有实际渗透测试项目经验，例如电商平台和机械制造平台渗透测试，熟练使用nmap、AWVS、Burp Suite等工具进行信息收集、漏洞挖掘和利用； 获得国家励志奖学金以及NISP二级证书，具备一定的网络安全理论基础。\n5. 不足分析：专科学历与本科要求不符，机电一体化专业与计算机/通信/网络安全相关专业有差距；缺乏安全服务助理工程师日常安全基础维护、机房巡检、合规要求等相关经验。\n6. 简历名：【信息安全实习生_东莞】施劲龙 24年应届生.pdf\n\n请根据候选人的实际简历内容，输出符合上述格式的文本，确保换行符 `\\n` 正确使用，并不要包含额外的解释或前后缀。\n\n\n',
        hasOutputParser: true,
        messages: {},
      },
      type: '@n8n/n8n-nodes-langchain.chainLlm',
      typeVersion: 1.5,
      position: [
        20,
        320,
      ],
      id: '4b72aab7-4574-40ce-b6e5-1c37cb777724',
      name: 'AI筛选前端工程师简历',
    },
    {
      parameters: {
        notice: '',
        promptType: 'define',
        text: '=角色：\n你是⼀位经验丰富的项⽬经理⼈才招聘专家。请帮我评估以下简历是否适合项⽬经理岗位。\n\n任务：\n你的任务是根据我提的要求，分析候选人的简历，按照我的要求返回信息。\n\n工作职责：\n1. 项⽬规划与实施\n\t制定项⽬计划，包括需求分析、资源配置、进度安排及⻛险管理。\n\t监督项⽬实施过程，确保按时、按质、按量交付项⽬成果。\n2. 跨团队协作\n\t负责协调内部团队⼯作，确保各环节⾼效运作。\n\t推动多团队之间的协同配合，解决项⽬实施中的协调问题。\n3. 客户沟通与管理\n\t与客户保持良好沟通，定期汇报项⽬进展，收集并反馈客户需求和建议。\n\t确保客户需求准确传递并转化为具体的项⽬⽬标。\n4. 质量与进度控制\n\t建⽴并执⾏项⽬质量控制流程，确保交付成果符合公司标准和客户要求。\n\t定期跟踪项⽬进度，发现并解决偏差，保证项⽬按计划推进。\n5. ⻛险管理\n\t识别项⽬中潜在的⻛险点，并制定有效的应对策略。\n\t实时监控项⽬⻛险，快速响应和解决突发问题。\n6. ⽂档管理与报告\n\t编制项⽬相关⽂档，如项⽬计划书、进展报告、测试报告及验收⽂档等。\n\t定期向公司管理层提交项⽬总结报告，为决策提供依据。\n7. 持续改进\n\t梳理并优化项⽬管理流程，总结经验教训，提升团队项⽬管理能⼒。\n\t推动信息安全领域项⽬管理的最佳实践在公司内部推⼴与实施。\n\n必要条件：\n1. 教育背景：\n\t计算机科学、信息安全、项⽬管理等相关专业本科及以上学历。\n\t持有PMP或相关认证者优先。\n2. ⼯作经验：\n\t3年以上信息安全或相关领域的项⽬管理经验。\n\t有多个团队协作项⽬的管理经验，特别是涉及安全技术、研发和客户交付的项⽬。\n3. 技能要求：\n\t熟悉项⽬管理⼯具（如Microsoft Project、JIRA、Trello等）。\n\t理解信息安全领域的基本概念和技术，包括数据安全、渗透测试、合规要求等。\n\t优秀的⻛险识别和管理能⼒。\n4. 软技能：\n\t出⾊的沟通能⼒，能够在多⽅利益相关者之间协调关系。\n\t良好的问题解决和决策能⼒，能够在压⼒下⾼效⼯作。\n\t具有团队领导能⼒，能够激励团队达成⽬标。\n\n加分项：\n1、有信息安全产品开发或实施的经验。\n2、熟悉国内⽹络安全法律法规和⾏业标准（如《⽹络安全法》、《等级保护2.0》等）。\n3、有数据安全项⽬或政府客户项⽬管理经验者优先。\n\n评分要求：\n1. 仔细分析简历中的教育背景、技能和经验是否符合岗位要求\n2. 评估候选⼈的项目管理相关知识和技能⽔平\n3. 关注候选⼈的项⽬经验和实践能⼒\n4. 评估候选⼈的学习能⼒和团队协作能⼒\n5. 检查是否具备必要的软技能（如沟通能⼒、分析能⼒等）\n\n简历名：{{ $(\'Read/Write Files from Disk\').item.json.fileName }}\n简历内容：\n{{ $json.text }}\n\n评分标准：\n候选⼈基本匹配度评分（1-100分）：\n  教育背景匹配度（权重20%）\n  技术能⼒匹配度（权重35%）\n  项⽬经验匹配度（权重25%）\n  软技能匹配度（权重20%）\n\n根据简历内容和上述要求，严格按照以下格式输出信息（每类只留一行，不要换行）：\n1. 姓名：{姓名}\n2. 专业：{专业}\n3. 简历分数：{简历分数}（按照上述要求对简历内容进行精确评分，精确到小数点后两位）\n4. 优势分析：{优势分析}（列出候选⼈的3-5个主要优势，足够细致，把每个优势对应具体做过的事情说出来，把优势分析改成一行，不要换行。）\n5. 不足分析：{不足分析}（列出需要关注的2-3个潜在问题或改进空间，把不足分析改成一行，不要换行。）\n6. 简历名：{简历名}（按我上面写的简历名返回）\n\n示例：\n1. 姓名：施劲龙\n2. 专业：机电一体化技术\n3. 简历分数：78.25\n4. 优势分析：具备较强的渗透测试技能，熟悉OWASP TOP 10漏洞及常见Web容器和中间件安全问题；能使用Python编写渗透脚本，并具备PHP、JAVA、JS简单代码审计能力；有实际渗透测试项目经验，例如电商平台和机械制造平台渗透测试，熟练使用nmap、AWVS、Burp Suite等工具进行信息收集、漏洞挖掘和利用； 获得国家励志奖学金以及NISP二级证书，具备一定的网络安全理论基础。\n5. 不足分析：专科学历与本科要求不符，机电一体化专业与计算机/通信/网络安全相关专业有差距；缺乏安全服务助理工程师日常安全基础维护、机房巡检、合规要求等相关经验。\n6. 简历名：【信息安全实习生_东莞】施劲龙 24年应届生.pdf\n\n请根据候选人的实际简历内容，输出符合上述格式的文本，确保换行符 `\\n` 正确使用，并不要包含额外的解释或前后缀。\n\n\n',
        hasOutputParser: true,
        messages: {},
      },
      type: '@n8n/n8n-nodes-langchain.chainLlm',
      typeVersion: 1.5,
      position: [
        0,
        660,
      ],
      id: 'c99cd03f-e997-4338-8baa-6432908b72cf',
      name: 'AI筛选项目经理简历',
    },
    {
      parameters: {
        authentication: 'oAuth2',
        resource: 'sheet',
        operation: 'clear',
        documentId: {
          __rl: true,
          value: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit?gid=1049406326#gid=1049406326',
          mode: 'url',
        },
        sheetName: {
          __rl: true,
          value: 1049406326,
          mode: 'list',
          cachedResultName: '全部简历',
          cachedResultUrl: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit#gid=1049406326',
        },
        clear: 'wholeSheet',
        keepFirstRow: true,
      },
      type: 'n8n-nodes-base.googleSheets',
      typeVersion: 4.5,
      position: [
        -1840,
        380,
      ],
      id: 'acbce2be-9cf4-4be8-8d64-f24a82f5c3f7',
      name: 'Google Sheets2',
      issues: {
        credentials: {
          googleSheetsOAuth2Api: [
            'Google Sheets 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        authentication: 'oAuth2',
        resource: 'sheet',
        operation: 'clear',
        documentId: {
          __rl: true,
          value: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit?gid=1049406326#gid=1049406326',
          mode: 'url',
        },
        sheetName: {
          __rl: true,
          value: 394448440,
          mode: 'list',
          cachedResultName: '排序后的全部简历',
          cachedResultUrl: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit#gid=394448440',
        },
        clear: 'wholeSheet',
        keepFirstRow: true,
      },
      type: 'n8n-nodes-base.googleSheets',
      typeVersion: 4.5,
      position: [
        -1900,
        600,
      ],
      id: '74842675-39f8-4f25-a7bb-3e104f234bfe',
      name: 'Google Sheets6',
      issues: {
        credentials: {
          googleSheetsOAuth2Api: [
            'Google Sheets 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        authentication: 'oAuth2',
        resource: 'sheet',
        operation: 'clear',
        documentId: {
          __rl: true,
          value: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit?gid=1049406326#gid=1049406326',
          mode: 'url',
        },
        sheetName: {
          __rl: true,
          value: 1994994412,
          mode: 'list',
          cachedResultName: '排名前30%的简历',
          cachedResultUrl: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit#gid=1994994412',
        },
        clear: 'wholeSheet',
        keepFirstRow: true,
      },
      type: 'n8n-nodes-base.googleSheets',
      typeVersion: 4.5,
      position: [
        -1900,
        840,
      ],
      id: 'b502efc5-c630-4cd6-ba68-efa93daa5ea6',
      name: 'Google Sheets8',
      issues: {
        credentials: {
          googleSheetsOAuth2Api: [
            'Google Sheets 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        notice: '',
        modelName: 'models/gemini-2.0-flash',
        options: {},
      },
      type: '@n8n/n8n-nodes-langchain.lmChatGoogleGemini',
      typeVersion: 1,
      position: [
        -80,
        160,
      ],
      id: '19f972fd-94b5-4fa7-b94e-66bd6d39ec95',
      name: 'Google Gemini Chat Model',
      issues: {
        credentials: {
          googlePalmApi: [
            'Google Gemini Chat Model 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        notice: '',
        modelName: 'models/gemini-2.0-flash',
        options: {},
      },
      type: '@n8n/n8n-nodes-langchain.lmChatGoogleGemini',
      typeVersion: 1,
      position: [
        -60,
        500,
      ],
      id: '8a1e50c2-b4f8-480d-9bcc-c87cee48c4ee',
      name: 'Google Gemini Chat Model1',
      issues: {
        credentials: {
          googlePalmApi: [
            'Google Gemini Chat Model 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        notice: '',
        modelName: 'models/gemini-2.0-flash',
        options: {},
      },
      type: '@n8n/n8n-nodes-langchain.lmChatGoogleGemini',
      typeVersion: 1,
      position: [
        -20,
        840,
      ],
      id: '496c93a2-c77f-4411-a7d3-94ee830446f1',
      name: 'Google Gemini Chat Model2',
      issues: {
        credentials: {
          googlePalmApi: [
            'Google Gemini Chat Model 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        notice: '',
        modelName: 'models/gemini-2.0-flash',
        options: {},
      },
      type: '@n8n/n8n-nodes-langchain.lmChatGoogleGemini',
      typeVersion: 1,
      position: [
        -20,
        1240,
      ],
      id: 'a6eede91-16dc-4caf-80ee-1b9d9cb5364c',
      name: 'Google Gemini Chat Model3',
      issues: {
        credentials: {
          googlePalmApi: [
            'Google Gemini Chat Model 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        notice: '',
        promptType: 'define',
        text: '=角色：\n你是⼀位经验丰富的安全服务工程师⼈才招聘专家。请帮我评估以下简历是否适合安全服务工程师岗位。\n\n任务：\n你的任务是根据我提的要求，分析候选人的简历，按照我的要求返回信息。\n\n岗位职责：\n1、负责网络设备、安全设备、服务器及应用系统等日常安全维护与管理，形成台账及拓扑图；\n2、负责机房的日常安全巡检，处理网络安全方面的事件闭环跟进；\n3、负责承接项目的服务要求，开展实施工作及报告输出，并向甲方汇报；\n4、负责网络安全相关专项检查材料。\n岗位要求：\n1、计算机/通信/网络安全相关专业，本科及以上学历，有1年以上相关项目工作经验；\n2、有数通基础，熟悉主流的网络及安全设备，或服务器、数据库产品及特性，独立进行配置及维护；\n3、了解等保2.0相关技术要求；\n4、参与实战攻防演练、应急响应、安全评估、等保/分保等工作之一的【优先考虑】。\n能力要求：\n1、具备主动沟通汇报和协调能力，良好的团队意识；\n2、具备事件分析、判断、描述、解决能力；\n3、具备总结归纳能力、文档编写能力，熟练使用Word/Excel/PPT/Visio等工具；\n4、具备举一反三能力，善于利用各类工具解决问题；【优先考虑】\n5、具备软考中、高级职称，专业技术人员职称、行业厂家认证等资质之一的【优先考虑】。\n\n人才发展：\n1、考核晋升机制：主要分为管理岗与技术岗（初、中、高级），达到条件并通过公司考核后晋升；\n2、总薪酬包含：固定工资（岗位+证书等）、绩效、补贴、提成、团队奖、创新奖等；\n3、工作福利：购买五险一金、团体意外伤害险、节假日福利等。\n\n简历名：{{ $(\'Read/Write Files from Disk\').item.json.fileName }}\n简历内容：\n{{ $json.text }}\n\n评分标准：\n候选⼈基本匹配度评分（1-100分）：\n  教育背景匹配度（权重20%）\n  技术能⼒匹配度（权重35%）\n  项⽬经验匹配度（权重25%）\n  软技能匹配度（权重20%）\n\n根据简历内容和上述要求，严格按照以下格式输出信息（每类只留一行，不要换行）：\n1. 姓名：{姓名}\n2. 专业：{专业}\n3. 简历分数：{简历分数}（按照上述要求对简历内容进行精确评分，精确到小数点后两位）\n4. 优势分析：{优势分析}（列出候选⼈的3-5个主要优势，足够细致，把每个优势对应具体做过的事情说出来，把优势分析改成一行，不要换行。）\n5. 不足分析：{不足分析}（列出需要关注的2-3个潜在问题或改进空间，把不足分析改成一行，不要换行。）\n6. 简历名：{简历名}（按我上面写的简历名返回）\n\n示例：\n1. 姓名：施劲龙\n2. 专业：机电一体化技术\n3. 简历分数：78.25\n4. 优势分析：具备较强的渗透测试技能，熟悉OWASP TOP 10漏洞及常见Web容器和中间件安全问题；能使用Python编写渗透脚本，并具备PHP、JAVA、JS简单代码审计能力；有实际渗透测试项目经验，例如电商平台和机械制造平台渗透测试，熟练使用nmap、AWVS、Burp Suite等工具进行信息收集、漏洞挖掘和利用； 获得国家励志奖学金以及NISP二级证书，具备一定的网络安全理论基础。\n5. 不足分析：专科学历与本科要求不符，机电一体化专业与计算机/通信/网络安全相关专业有差距；缺乏安全服务助理工程师日常安全基础维护、机房巡检、合规要求等相关经验。\n6. 简历名：【信息安全实习生_东莞】施劲龙 24年应届生.pdf\n\n请根据候选人的实际简历内容，输出符合上述格式的文本，确保换行符 `\\n` 正确使用，并不要包含额外的解释或前后缀。\n\n\n',
        hasOutputParser: true,
        messages: {},
      },
      type: '@n8n/n8n-nodes-langchain.chainLlm',
      typeVersion: 1.5,
      position: [
        0,
        1060,
      ],
      id: 'e500c1cf-9bc5-489b-94b1-7a321a1f1d65',
      name: 'AI筛选安全服务工程师简历',
    },
    {
      parameters: {
        authentication: 'oAuth2',
        resource: 'sheet',
        operation: 'read',
        documentId: {
          __rl: true,
          value: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit?gid=1049406326#gid=1049406326',
          mode: 'url',
        },
        sheetName: {
          __rl: true,
          value: 1049406326,
          mode: 'list',
          cachedResultName: '全部简历',
          cachedResultUrl: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit#gid=1049406326',
        },
        filtersUI: {},
        combineFilters: 'AND',
        options: {},
      },
      type: 'n8n-nodes-base.googleSheets',
      typeVersion: 4.5,
      position: [
        -880,
        2020,
      ],
      id: '63bc9823-e95c-4bd5-8e2f-da2df9f1666b',
      name: 'Google Sheets',
      onError: 'continueRegularOutput',
      issues: {
        credentials: {
          googleSheetsOAuth2Api: [
            'Google Sheets 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        authentication: 'oAuth2',
        resource: 'sheet',
        operation: 'appendOrUpdate',
        documentId: {
          __rl: true,
          value: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit?gid=1049406326#gid=1049406326',
          mode: 'url',
        },
        sheetName: {
          __rl: true,
          value: 394448440,
          mode: 'list',
          cachedResultName: '排序后的全部简历',
          cachedResultUrl: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit#gid=394448440',
        },
        columns: {
          mappingMode: 'autoMapInputData',
          value: {},
          matchingColumns: [
            '姓名',
          ],
          schema: [
            {
              id: '姓名',
              displayName: '姓名',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
              removed: false,
            },
            {
              id: '专业',
              displayName: '专业',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '简历分数',
              displayName: '简历分数',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '优势分析',
              displayName: '优势分析',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '不足分析',
              displayName: '不足分析',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '文件名',
              displayName: '文件名',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
          ],
        },
        options: {},
      },
      type: 'n8n-nodes-base.googleSheets',
      typeVersion: 4.5,
      position: [
        -500,
        2020,
      ],
      id: '249aa86c-275b-4ac6-83c2-87934a15f227',
      name: 'Google Sheets7',
      onError: 'continueRegularOutput',
      issues: {
        credentials: {
          googleSheetsOAuth2Api: [
            'Google Sheets 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        authentication: 'oAuth2',
        resource: 'sheet',
        operation: 'appendOrUpdate',
        documentId: {
          __rl: true,
          value: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit?gid=1049406326#gid=1049406326',
          mode: 'url',
        },
        sheetName: {
          __rl: true,
          value: 1994994412,
          mode: 'list',
          cachedResultName: '排名前30%的简历',
          cachedResultUrl: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit#gid=1994994412',
        },
        columns: {
          mappingMode: 'autoMapInputData',
          value: {},
          matchingColumns: [
            '姓名',
          ],
          schema: [
            {
              id: '姓名',
              displayName: '姓名',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
              removed: false,
            },
            {
              id: '专业',
              displayName: '专业',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '简历分数',
              displayName: '简历分数',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '优势分析',
              displayName: '优势分析',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '不足分析',
              displayName: '不足分析',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
            {
              id: '文件名',
              displayName: '文件名',
              required: false,
              defaultMatch: false,
              display: true,
              type: 'string',
              canBeUsedToMatch: true,
            },
          ],
        },
        options: {},
      },
      type: 'n8n-nodes-base.googleSheets',
      typeVersion: 4.5,
      position: [
        -120,
        2020,
      ],
      id: '9eb43c77-d721-4a87-bf52-595c7a29e256',
      name: 'Google Sheets3',
      onError: 'continueRegularOutput',
      issues: {
        credentials: {
          googleSheetsOAuth2Api: [
            'Google Sheets 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        authentication: 'oAuth2',
        resource: 'sheet',
        operation: 'clear',
        documentId: {
          __rl: true,
          value: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit?gid=1049406326#gid=1049406326',
          mode: 'url',
        },
        sheetName: {
          __rl: true,
          value: 1994994412,
          mode: 'list',
          cachedResultName: '排名前30%的简历',
          cachedResultUrl: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit#gid=1994994412',
        },
        clear: 'specificRows',
        startIndex: '={{Math.ceil($json.total_items * 0.3) + 1}}',
        rowsToDelete: '={{ $json.total_items - Math.ceil($json.total_items * 0.3)}}',
      },
      type: 'n8n-nodes-base.googleSheets',
      typeVersion: 4.5,
      position: [
        440,
        2020,
      ],
      id: '30e707aa-70f1-4b24-be1f-3a3f7744b172',
      name: 'Google Sheets9',
      onError: 'continueRegularOutput',
      issues: {
        credentials: {
          googleSheetsOAuth2Api: [
            'Google Sheets 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        authentication: 'oAuth2',
        resource: 'sheet',
        operation: 'read',
        documentId: {
          __rl: true,
          value: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit?gid=1049406326#gid=1049406326',
          mode: 'url',
        },
        sheetName: {
          __rl: true,
          value: 1994994412,
          mode: 'list',
          cachedResultName: '排名前30%的简历',
          cachedResultUrl: 'https://docs.google.com/spreadsheets/d/1CQqBe1sbcKsziyQzhg9DzhgpjYc-nckQdHgx0xTz9fQ/edit#gid=1994994412',
        },
        filtersUI: {},
        combineFilters: 'AND',
        options: {},
      },
      type: 'n8n-nodes-base.googleSheets',
      typeVersion: 4.5,
      position: [
        800,
        2020,
      ],
      id: 'db8e009c-e477-4880-8c5b-69d1ac9c1338',
      name: 'Google Sheets5',
      executeOnce: true,
      onError: 'continueRegularOutput',
      issues: {
        credentials: {
          googleSheetsOAuth2Api: [
            'Google Sheets 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        notice: '',
        promptType: 'define',
        text: '=角色：\n你是⼀位经验丰富的信息安全领域⼈才招聘专家。请帮我评估以下简历是否适合信息安全服务助理⼯程师岗位。\n\n任务：\n你的任务是根据我提的要求，分析候选人的简历，按照我的要求返回信息。\n\n工作职责：\n1、协助开展网络设备、安全设备、服务器及应用系统等日常安全基础维护与管理工作。\n2、协助开展机房的日常安全巡检、跟进网络安全方面的事件闭环工作。\n3、协助开展网络安全或数据安全等相关专项检查的基础工作实施与跟进工作。\n4、协助开展公司安排的其他行政、项目工作。\n\n必要条件：\n1、计算机/通信/网络安全相关专业，本科及以上学历。\n2、有数通基础，对主流的网络及安全设备，或服务器、数据库产品有所了解。\n3、了解等保、网络或数据安全方面之一的相关合规要求；【优先考虑】。\n4、有网络安全或数据安全方向相关的活动实践经历/实习经历。【优先考虑】。\n\n评分要求：\n1. 仔细分析简历中的教育背景、技能和经验是否符合岗位要求\n2. 评估候选⼈的信息安全相关知识和技能⽔平\n3. 关注候选⼈的项⽬经验和实践能⼒\n4. 评估候选⼈的学习能⼒和团队协作能⼒\n5. 检查是否具备必要的软技能（如沟通能⼒、分析能⼒等）\n\n简历名：{{ $(\'Read/Write Files from Disk\').item.json.fileName }}\n简历内容：\n{{ $json.text }}\n\n评分标准：\n候选⼈基本匹配度评分（1-100分）：\n  教育背景匹配度（权重20%）\n  技术能⼒匹配度（权重35%）\n  项⽬经验匹配度（权重25%）\n  软技能匹配度（权重20%）\n\n根据简历内容和上述要求，严格按照以下格式输出信息（每类只留一行，不要换行）：\n1. 姓名：{姓名}\n2. 专业：{专业}\n3. 简历分数：{简历分数}（按照上述要求对简历内容进行精确评分，精确到小数点后两位）\n4. 优势分析：{优势分析}（列出候选⼈的3-5个主要优势，足够细致，把每个优势对应具体做过的事情说出来，把优势分析改成一行，不要换行。）\n5. 不足分析：{不足分析}（列出需要关注的2-3个潜在问题或改进空间，把不足分析改成一行，不要换行。）\n6. 简历名：{简历名}（按我上面写的简历名返回）\n\n示例：\n1. 姓名：施劲龙\n2. 专业：机电一体化技术\n3. 简历分数：78.25\n4. 优势分析：具备较强的渗透测试技能，熟悉OWASP TOP 10漏洞及常见Web容器和中间件安全问题；能使用Python编写渗透脚本，并具备PHP、JAVA、JS简单代码审计能力；有实际渗透测试项目经验，例如电商平台和机械制造平台渗透测试，熟练使用nmap、AWVS、Burp Suite等工具进行信息收集、漏洞挖掘和利用； 获得国家励志奖学金以及NISP二级证书，具备一定的网络安全理论基础。\n5. 不足分析：专科学历与本科要求不符，机电一体化专业与计算机/通信/网络安全相关专业有差距；缺乏安全服务助理工程师日常安全基础维护、机房巡检、合规要求等相关经验。\n6. 简历名：【信息安全实习生_东莞】施劲龙 24年应届生.pdf\n\n请根据候选人的实际简历内容，输出符合上述格式的文本，确保换行符 `\\n` 正确使用，并不要包含额外的解释或前后缀。\n\n\n',
        hasOutputParser: true,
        messages: {},
      },
      type: '@n8n/n8n-nodes-langchain.chainLlm',
      typeVersion: 1.5,
      position: [
        0,
        0,
      ],
      id: '761f90dc-0387-4d9a-a439-feb74e2d2c04',
      name: 'AI筛选管培生简历',
    },
    {
      parameters: {
        notice: '',
        modelName: 'models/gemini-2.0-flash',
        options: {},
      },
      type: '@n8n/n8n-nodes-langchain.lmChatGoogleGemini',
      typeVersion: 1,
      position: [
        -140,
        1600,
      ],
      id: '9eb5da1c-f860-48ea-9d78-f660a099310a',
      name: 'Google Gemini Chat Model5',
      issues: {
        credentials: {
          googlePalmApi: [
            'Google Gemini Chat Model 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        notice: '',
        promptType: 'define',
        text: '=角色：\n你是⼀位经验丰富的后端研发工程师⼈才招聘专家。请帮我评估以下简历是否适合后端研发工程师岗位。\n\n任务：\n你的任务是根据我提的要求，分析候选人的简历，按照我的要求返回信息。\n\n岗位职责：\n1. 负责公司数据安全相关产品的后端研发，包括安全合规平台、应⽤数据安全监测平台、数字资产管\n理平台等，构建⾼性能、可扩展的后端架构。\n2. 深⼊研究Python 和 Golang 的⼯程化应⽤，负责⾼并发、低延迟的业务逻辑开发，优化数据处理和\n计算性能。\n3. 参与数据安全治理、数据访问控制、特权账号管理等核⼼模块的设计与实现。\n4. 设计并优化分布式存储、消息队列、流式处理等后端基础组件，提升系统稳定性、可维护性和扩展\n性。\n5. 参与技术选型、架构设计、性能优化、单元测试、代码审查，确保⾼质量交付。\n6. 编写⾼质量技术⽂档，⽀持团队内部的知识分享和技术沉淀。\n岗位要求：\n1.\n计算机、信息安全、软件⼯程等相关专业本科及以上学历，3年以上后端开发经验（优秀者可放\n宽）。\n2.\n精通 Python 和 Golang，具备扎实的编程能⼒，熟悉常⻅设计模式、并发编程、性能优化、内存管\n理等技术。\n3. 熟悉数据库（PostgreSQL / MySQL / ClickHouse）、消息队列（Kafka / RabbitMQ /\nNATS），具备数据库调优、SQL/NoSQL 经验。\n4. 良好的编码习惯，能够编写**⾼质量、可维护的代码**，并进⾏性能优化。\n5. 具备良好的团队合作能⼒，能够快速理解业务需求并推动技术落地。\n加分项：\n有⼤规模数据处理经验，熟悉ETL、数据流处理、实时计算等技术。\n有安全⾏业相关背景，参与过安全⼯具开发、攻防项⽬、渗透测试等优先。\n熟悉云计算、Serverless、边缘计算，有 SaaS 产品研发经验者优先。\n具备 DevOps、⾃动化测试经验，熟悉 CI/CD 流程。\n\n简历名：{{ $(\'Read/Write Files from Disk\').item.json.fileName }}\n简历内容：\n{{ $json.text }}\n\n评分标准：\n候选⼈基本匹配度评分（1-100分）：\n  教育背景匹配度（权重20%）\n  技术能⼒匹配度（权重35%）\n  项⽬经验匹配度（权重25%）\n  软技能匹配度（权重20%）\n\n根据简历内容和上述要求，严格按照以下格式输出信息（每类只留一行，不要换行）：\n1. 姓名：{姓名}\n2. 专业：{专业}\n3. 简历分数：{简历分数}（按照上述要求对简历内容进行精确评分，精确到小数点后两位）\n4. 优势分析：{优势分析}（列出候选⼈的3-5个主要优势，足够细致，把每个优势对应具体做过的事情说出来，把优势分析改成一行，不要换行。）\n5. 不足分析：{不足分析}（列出需要关注的2-3个潜在问题或改进空间，把不足分析改成一行，不要换行。）\n6. 简历名：{简历名}（按我上面写的简历名返回）\n\n示例：\n1. 姓名：施劲龙\n2. 专业：机电一体化技术\n3. 简历分数：78.25\n4. 优势分析：具备较强的渗透测试技能，熟悉OWASP TOP 10漏洞及常见Web容器和中间件安全问题；能使用Python编写渗透脚本，并具备PHP、JAVA、JS简单代码审计能力；有实际渗透测试项目经验，例如电商平台和机械制造平台渗透测试，熟练使用nmap、AWVS、Burp Suite等工具进行信息收集、漏洞挖掘和利用； 获得国家励志奖学金以及NISP二级证书，具备一定的网络安全理论基础。\n5. 不足分析：专科学历与本科要求不符，机电一体化专业与计算机/通信/网络安全相关专业有差距；缺乏安全服务助理工程师日常安全基础维护、机房巡检、合规要求等相关经验。\n6. 简历名：【信息安全实习生_东莞】施劲龙 24年应届生.pdf\n\n请根据候选人的实际简历内容，输出符合上述格式的文本，确保换行符 `\\n` 正确使用，并不要包含额外的解释或前后缀。\n\n\n',
        hasOutputParser: true,
        messages: {},
      },
      type: '@n8n/n8n-nodes-langchain.chainLlm',
      typeVersion: 1.5,
      position: [
        0,
        1400,
      ],
      id: 'd887d9b8-3adf-482c-aed5-6425cd4c5d30',
      name: 'AI筛选后端工程师简历',
    },
    {
      parameters: {
        info: '',
        operation: 'read',
        fileSelector: '=/mnt/test/n8nAI简历筛选/岗位/{{ $json[\'岗位类型\'] }}/简历仓库/*',
        options: {
          dataPropertyName: 'data',
        },
      },
      type: 'n8n-nodes-base.readWriteFile',
      typeVersion: 1,
      position: [
        -1520,
        380,
      ],
      id: '8f4cfe53-6997-430c-b745-c06c05eadc8b',
      name: 'Read/Write Files from Disk',
      alwaysOutputData: true,
    },
    {
      parameters: {
        splitInBatchesNotice: '',
        batchSize: 1,
        options: {
          reset: false,
        },
      },
      type: 'n8n-nodes-base.splitInBatches',
      typeVersion: 3,
      position: [
        -800,
        2240,
      ],
      id: '2a2b160b-ed90-49d1-8236-eb7217d4e8fb',
      name: 'Loop Over Items1',
    },
    {
      parameters: {
        mode: 'runOnceForAllItems',
        language: 'javaScript',
        jsCode: '// 获取AI输出的文本（这里假设它是从Basic LLM Chain节点传递过来的）\nconst text = items[0].json.text; // 替换text为实际接收AI输出的字段名\n\n// Split text into lines\nconst lines = text.split("\\n");\n\n// Extract each part using regex or simple string manipulation\nconst data = {\n    name: lines[0].split(/[:：]/, 2)[1],\n    major: lines[1].split(/[:：]/, 2)[1],\n    score: parseFloat(lines[2]?.split(/[:：]/, 2)[1]?.trim()) + (Math.random() * 0.09 + 0.01),\n    advantage: lines[3].split(/[:：]/, 2)[1],\n    disadvantage: lines[4].split(/[:：]/, 2)[1],\n    file_name: lines[5].split(/[:：]/, 2)[1].trim(),\n};\n\n// Return structured data for Excel\n// 确保 score 是两位小数的浮点数\ndata.score = parseFloat(data.score.toFixed(2));\nreturn [\n  {\n    json: {\n      姓名: data.name,\n      专业: data.major,\n      简历分数: data.score,\n      优势分析: data.advantage,\n      不足分析: data.disadvantage,\n      文件名: data.file_name,\n    }\n  }\n];\n\n',
        notice: '',
      },
      type: 'n8n-nodes-base.code',
      typeVersion: 2,
      position: [
        1400,
        100,
      ],
      id: '51d6ed1d-3253-4df2-b33f-f7c26dfc8ceb',
      name: 'Code',
      onError: 'continueRegularOutput',
    },
    {
      parameters: {
        info: '',
        operation: 'read',
        fileSelector: '=/mnt/test/n8nAI简历筛选/岗位/{{ $(\'On form submission\').item.json["岗位类型"] }}/简历仓库/{{$json[\'文件名\']}}',
        options: {
          dataPropertyName: 'data',
        },
      },
      type: 'n8n-nodes-base.readWriteFile',
      typeVersion: 1,
      position: [
        -300,
        2200,
      ],
      id: 'aac96fcb-e22c-4cb1-a213-8210c66aae83',
      name: 'Read/Write Files from Disk2',
      alwaysOutputData: true,
    },
    {
      parameters: {
        info: '',
        operation: 'write',
        fileName: '=/mnt/test/n8nAI简历筛选/岗位/{{ $(\'On form submission\').item.json["岗位类型"] }}/排名前30%的简历/{{ $json.fileName }}',
        dataPropertyName: '=data',
        options: {},
      },
      type: 'n8n-nodes-base.readWriteFile',
      typeVersion: 1,
      position: [
        -20,
        2200,
      ],
      id: 'ec9df64f-dc31-491f-a80e-b520d7f41cc5',
      name: 'Read/Write Files from Disk1',
    },
    {
      parameters: {
        notice: '',
        modelName: 'models/gemini-2.0-flash',
        options: {},
      },
      type: '@n8n/n8n-nodes-langchain.lmChatGoogleGemini',
      typeVersion: 1,
      position: [
        680,
        480,
      ],
      id: '601f6270-0cc5-4b8a-b102-4156d9037b44',
      name: 'Google Gemini Chat Model6',
      issues: {
        credentials: {
          googlePalmApi: [
            'Google Gemini Chat Model 的凭据未设置。',
          ],
        },
      },
    },
    {
      parameters: {
        mode: 'rules',
        rules: {
          values: [
            {
              conditions: {
                options: {
                  caseSensitive: true,
                  leftValue: '',
                  typeValidation: 'strict',
                  version: 2,
                },
                conditions: [
                  {
                    leftValue: '={{ $(\'On form submission\').item.json["岗位类型"] }}',
                    rightValue: '管培生',
                    operator: {
                      type: 'string',
                      operation: 'equals',
                    },
                  },
                ],
                combinator: 'and',
              },
              renameOutput: false,
            },
            {
              conditions: {
                options: {
                  caseSensitive: true,
                  leftValue: '',
                  typeValidation: 'strict',
                  version: 2,
                },
                conditions: [
                  {
                    id: 'edaa0c02-9771-4653-821b-15499625a17f',
                    leftValue: '={{ $(\'On form submission\').item.json["岗位类型"] }}',
                    rightValue: '前端工程师',
                    operator: {
                      type: 'string',
                      operation: 'equals',
                      name: 'filter.operator.equals',
                    },
                  },
                ],
                combinator: 'and',
              },
              renameOutput: false,
            },
            {
              conditions: {
                options: {
                  caseSensitive: true,
                  leftValue: '',
                  typeValidation: 'strict',
                  version: 2,
                },
                conditions: [
                  {
                    id: '04bfceaa-b0ae-427d-ad3f-6594d2854f2b',
                    leftValue: '={{ $(\'On form submission\').item.json["岗位类型"] }}',
                    rightValue: '项目经理',
                    operator: {
                      type: 'string',
                      operation: 'equals',
                      name: 'filter.operator.equals',
                    },
                  },
                ],
                combinator: 'and',
              },
              renameOutput: false,
            },
            {
              conditions: {
                options: {
                  caseSensitive: true,
                  leftValue: '',
                  typeValidation: 'strict',
                  version: 2,
                },
                conditions: [
                  {
                    id: '8e6cf947-16e6-485e-9c49-6679ef3f8026',
                    leftValue: '={{ $(\'On form submission\').item.json["岗位类型"] }}',
                    rightValue: '安全服务工程师',
                    operator: {
                      type: 'string',
                      operation: 'equals',
                      name: 'filter.operator.equals',
                    },
                  },
                ],
                combinator: 'and',
              },
              renameOutput: false,
            },
            {
              conditions: {
                options: {
                  caseSensitive: true,
                  leftValue: '',
                  typeValidation: 'strict',
                  version: 2,
                },
                conditions: [
                  {
                    id: '31ee51c1-7741-4c34-b1d2-377e4a85673c',
                    leftValue: '={{ $(\'On form submission\').item.json["岗位类型"] }}',
                    rightValue: '后端工程师',
                    operator: {
                      type: 'string',
                      operation: 'equals',
                      name: 'filter.operator.equals',
                    },
                  },
                ],
                combinator: 'and',
              },
              renameOutput: false,
            },
            {
              conditions: {
                options: {
                  caseSensitive: true,
                  leftValue: '',
                  typeValidation: 'strict',
                  version: 2,
                },
                conditions: [
                  {
                    id: '8c780a6f-b3dc-4973-9d67-54161ac3cf18',
                    leftValue: '={{ $(\'On form submission\').item.json["岗位类型"] }}',
                    rightValue: '产品助理',
                    operator: {
                      type: 'string',
                      operation: 'equals',
                      name: 'filter.operator.equals',
                    },
                  },
                ],
                combinator: 'and',
              },
              renameOutput: false,
            },
          ],
        },
        looseTypeValidation: false,
        options: {},
      },
      type: 'n8n-nodes-base.switch',
      typeVersion: 3.2,
      position: [
        -560,
        100,
      ],
      id: '89961486-d20e-49d8-b406-3f285c02c1d8',
      name: 'Switch',
    },
    {
      parameters: {
        notice: '',
        promptType: 'define',
        text: '=角色：\n你是⼀位经验丰富的产品助理⼈才招聘专家。请帮我评估以下简历是否适合产品助理岗位。\n\n任务：\n你的任务是根据我提的要求，分析候选人的简历，按照我的要求返回信息。\n\n岗位职责：\n1. 产品需求调研与分析：\n负责数据安全、合规监管、AI 安全相关产品的市场调研，分析⽤户需求和⾏业趋势。\n收集并整理客户反馈、竞品分析、⾏业政策，形成需求⽂档，推动产品优化。\n2. 产品规划与设计：\n参与安全合规平台、应⽤数据安全监测平台、数字资产管理平台等产品的规划和设计。\n配合进⾏需求拆解、PRD ⽂档编写、⽤户流程设计，确保功能可落地。\n3. 跨团队协作：\n协助研发、设计、测试团队推进项⽬进度，确保产品按时交付。\n参与产品迭代会议，跟踪产品开发和测试，协调资源解决问题。\n4. 产品⽂档与培训⽀持：\n负责整理和维护产品⽂档、⽤户⼿册、FAQ，确保内部外部使⽤⼈员能⾼效上⼿。\n协助销售、市场和运营团队，提供产品培训和演示⽀持。\n\n岗位要求：\n1. 本科及以上学历，计算机、信息安全、软件⼯程、数据科学等相关专业优先。\n2. 1-3 年产品相关经验（优秀应届⽣可考虑），对数据安全、AI、⼤数据⾏业有浓厚兴趣。\n3. 熟悉产品需求⽂档（PRD）、⽤户故事、产品原型（Axure / Figma），具备基本的产品设计能\n⼒。\n4. 了解API、数据库基础、数据安全概念，对数据治理、数据合规、⽇志管理、权限控制等领域有基\n础认知者优先。\n5. 具备良好的逻辑思维和沟通能⼒，能够在跨团队协作中⾼效推进产品落地。\n6. 关注⽤户体验，有较强的⽤户洞察能⼒，能够站在⽤户⻆度思考问题。\n7. 责任⼼强，具备良好的学习能⼒和执⾏⼒，能适应快节奏的产品迭代。\n\n加分项：\n具备安全产品（如 DLP、防⽕墙、合规平台、渗透测试⼯具）使⽤或开发经验者优先。\n了解AI ⼤模型、智能⻛控、⾃动化安全检测等技术者优先。\n有 B 端 SaaS 产品经验，熟悉政企市场需求优先。\n\n简历名：{{ $(\'Read/Write Files from Disk\').item.json.fileName }}\n简历内容：\n{{ $json.text }}\n\n评分标准：\n候选⼈基本匹配度评分（1-100分）：\n  教育背景匹配度（权重20%）\n  技术能⼒匹配度（权重35%）\n  项⽬经验匹配度（权重25%）\n  软技能匹配度（权重20%）\n\n根据简历内容和上述要求，严格按照以下格式输出信息（每类只留一行，不要换行）：\n1. 姓名：{姓名}\n2. 专业：{专业}\n3. 简历分数：{简历分数}（按照上述要求对简历内容进行精确评分，精确到小数点后两位）\n4. 优势分析：{优势分析}（列出候选⼈的3-5个主要优势，足够细致，把每个优势对应具体做过的事情说出来，把优势分析改成一行，不要换行。）\n5. 不足分析：{不足分析}（列出需要关注的2-3个潜在问题或改进空间，把不足分析改成一行，不要换行。）\n6. 简历名：{简历名}（按我上面写的简历名返回）\n\n示例：\n1. 姓名：施劲龙\n2. 专业：机电一体化技术\n3. 简历分数：78.25\n4. 优势分析：具备较强的渗透测试技能，熟悉OWASP TOP 10漏洞及常见Web容器和中间件安全问题；能使用Python编写渗透脚本，并具备PHP、JAVA、JS简单代码审计能力；有实际渗透测试项目经验，例如电商平台和机械制造平台渗透测试，熟练使用nmap、AWVS、Burp Suite等工具进行信息收集、漏洞挖掘和利用； 获得国家励志奖学金以及NISP二级证书，具备一定的网络安全理论基础。\n5. 不足分析：专科学历与本科要求不符，机电一体化专业与计算机/通信/网络安全相关专业有差距；缺乏安全服务助理工程师日常安全基础维护、机房巡检、合规要求等相关经验。\n6. 简历名：【信息安全实习生_东莞】施劲龙 24年应届生.pdf\n\n请根据候选人的实际简历内容，输出符合上述格式的文本，确保换行符 `\\n` 正确使用，并不要包含额外的解释或前后缀。\n\n\n',
        hasOutputParser: true,
        messages: {},
      },
      type: '@n8n/n8n-nodes-langchain.chainLlm',
      typeVersion: 1.5,
      position: [
        800,
        320,
      ],
      id: '7dcf0637-98a8-4163-801a-245c7748de5c',
      name: 'AI筛选产品助理简历',
    },
    {
      parameters: {
        resume: 'timeInterval',
        amount: 4,
        unit: 'seconds',
      },
      id: '2dd8bdbe-2e17-4aa5-8bc0-4125fcad72b1',
      name: 'Wait',
      type: 'n8n-nodes-base.wait',
      typeVersion: 1.1,
      position: [
        -800,
        1780,
      ],
      webhookId: '7772c13e-434c-4b37-a7ff-d8a722129f6b',
    },
    {
      parameters: {
        resume: 'timeInterval',
        amount: 1,
        unit: 'seconds',
      },
      id: '6e1c3ec9-08a9-4a13-a8de-8764cd13009d',
      name: 'Wait1',
      type: 'n8n-nodes-base.wait',
      typeVersion: 1.1,
      position: [
        -1100,
        2020,
      ],
      webhookId: '7772c13e-434c-4b37-a7ff-d8a722129f6b',
    },
    {
      parameters: {
        resume: 'timeInterval',
        amount: 1,
        unit: 'seconds',
      },
      id: '6c59c0e2-eaf7-440a-abd6-ef95a94aae9c',
      name: 'Wait2',
      type: 'n8n-nodes-base.wait',
      typeVersion: 1.1,
      position: [
        -300,
        2020,
      ],
      webhookId: '7772c13e-434c-4b37-a7ff-d8a722129f6b',
    },
    {
      parameters: {
        resume: 'timeInterval',
        amount: 1,
        unit: 'seconds',
      },
      id: '4f57d056-df3b-454e-9582-320fb0593880',
      name: 'Wait3',
      type: 'n8n-nodes-base.wait',
      typeVersion: 1.1,
      position: [
        260,
        2020,
      ],
      webhookId: '7772c13e-434c-4b37-a7ff-d8a722129f6b',
    },
    {
      parameters: {
        resume: 'timeInterval',
        amount: 1,
        unit: 'seconds',
      },
      id: 'b683fc1a-fda7-4155-b0a4-1c80f2c65954',
      name: 'Wait4',
      type: 'n8n-nodes-base.wait',
      typeVersion: 1.1,
      position: [
        600,
        2020,
      ],
      webhookId: '7772c13e-434c-4b37-a7ff-d8a722129f6b',
    },
    {
      parameters: {
        resume: 'timeInterval',
        amount: 1,
        unit: 'seconds',
      },
      id: 'c0301422-dd0b-4900-847c-5e40835b759c',
      name: 'Wait5',
      type: 'n8n-nodes-base.wait',
      typeVersion: 1.1,
      position: [
        -2120,
        600,
      ],
      webhookId: '7772c13e-434c-4b37-a7ff-d8a722129f6b',
    },
    {
      parameters: {
        resume: 'timeInterval',
        amount: 1,
        unit: 'seconds',
      },
      id: '1ccf465a-e1b8-46db-9a58-21bce55ea1bc',
      name: 'Wait6',
      type: 'n8n-nodes-base.wait',
      typeVersion: 1.1,
      position: [
        -2120,
        840,
      ],
      webhookId: '7772c13e-434c-4b37-a7ff-d8a722129f6b',
    },
  ],
  settings: {
    executionOrder: 'v1',
  },
  tags: [],
  pinData: {},
  versionId: '289237b2-579e-4d91-a4a8-4d4869ee133b',
  usedCredentials: [],
  meta: null,
  scopes: [
    'workflow:create',
    'workflow:delete',
    'workflow:execute',
    'workflow:list',
    'workflow:move',
    'workflow:read',
    'workflow:share',
    'workflow:update',
  ],
} as unknown as IWorkflowDb
// export const mockWorkflow: IWorkflowDb = {
//   id: 'hn6OokhlmwKSUKdO',
//   name: 'My workflow 2',
//   active: false,
//   createdAt: -1,
//   updatedAt: -1,
//   connections: {
//     当表单提交时: {
//       main: [
//         [
//           {
//             node: 'HTTP Request',
//             type: 'main' as unknown as NodeConnectionType,
//             index: 0,
//           },
//         ],
//       ],
//     },
//   },
//   nodes: [
//     {
//       parameters: {
//         authentication: 'none',
//         formTitle: '联系我们',
//         formDescription: '我们会很快回复你',
//         formFields: {
//           values: [
//             {
//               fieldLabel: '你的名字是什么',
//               fieldType: 'text',
//               placeholder: 'hello',
//               requiredField: false,
//             },
//           ],
//         },
//         responseMode: 'onReceived',
//         addFormPage: '',
//         options: {},
//       },
//       type: 'n8n-nodes-base.formTrigger',
//       typeVersion: 2.2,
//       position: [
//         0,
//         0,
//       ],
//       id: '49bee59c-71ad-400d-b97e-1233a7ba9b9a',
//       name: '当表单提交时',
//       webhookId: 'e3991d56-b5d4-48cd-b7dc-abaef8e62c8d',
//       notesInFlow: true,
//       notes: '这是一条注释',
//     },
//     {
//       parameters: {
//         curlImport: '',
//         method: 'GET',
//         url: '',
//         authentication: 'none',
//         provideSslCertificates: false,
//         sendQuery: false,
//         sendHeaders: false,
//         sendBody: false,
//         options: {},
//         infoMessage: '',
//       },
//       type: 'n8n-nodes-base.httpRequest',
//       typeVersion: 4.2,
//       position: [
//         220,
//         0,
//       ],
//       id: '309525e4-a20b-4048-a7b1-bb61c25c3ee7',
//       name: 'HTTP Request',
//       issues: {
//         parameters: {
//           url: [
//             'Parameter "URL" is required.',
//           ],
//         },
//       },
//     },
//   ],
//   settings: {
//     executionOrder: 'v1',
//   },
//   tags: [],
//   pinData: {},
//   versionId: 'c8729d16-348a-4da6-9feb-71869ff52e98',
//   usedCredentials: [],
//   meta: undefined,
//   scopes: [
//     'workflow:create',
//     'workflow:delete',
//     'workflow:execute',
//     'workflow:list',
//     'workflow:move',
//     'workflow:read',
//     'workflow:share',
//     'workflow:update',
//   ],
// }
