export const subcategoryNodeTitles = {
  appTriggerNodes: '应用事件',
  appRegularNodes: '应用中的操作',
  dataTransformation: '数据转换',
  files: '文件',
  flow: '流程',
  helpers: '核心',
  otherTriggerNodes: '其他方式...',
  agents: '代理',
  chains: '链',
  documentLoaders: '文档加载器',
  embeddings: '嵌入',
  languageModels: '语言模型',
  memory: '内存',
  outputParsers: '输出解析器',
  retrievers: '检索器',
  textSplitters: '文本分割器',
  tools: '工具',
  vectorStores: '向量存储',
  miscellaneous: '其他',
  humanInTheLoop: '人工干预',
}

export const subcategoryNodeDescriptions = {
  appTriggerNodes: '当应用程序发生某些事件时运行流程，例如 Telegram、Notion 或 Airtable',
  appRegularNodes: '在应用程序或服务中执行某些操作，例如 Google Sheets、Telegram 或 Notion',
  dataTransformation: '操作数据，运行 JavaScript 代码等',
  files: 'CSV、XLS、XML、文本、图像等',
  flow: '条件判断、分支、等待、比较和合并数据等',
  helpers: '代码、HTTP 请求（API 调用）、Webhook 和其他辅助功能',
  otherTriggerNodes: '在工作流错误、文件更改等情况下运行工作流',
  agents: '自主实体，用于交互和做出决策',
  chains: '用于特定任务的结构化组件',
  documentLoaders: '处理加载文档的过程',
  embeddings: '将文本转换为向量表示',
  languageModels: '了解和生成语言的 AI 模型',
  memory: '在执行期间管理信息的存储和检索',
  outputParsers: '确保输出符合定义的格式',
  retrievers: '从源中提取相关信息',
  textSplitters: '将文本分解为较小的部分',
  tools: '提供各种功能的实用组件',
  vectorStores: '处理向量表示的存储和检索',
  miscellaneous: '其他 AI 相关节点',
  humanInTheLoop: '在继续之前等待批准或人工输入',
}

export const subcategoryNodeInfos = {
  languageModels: 'Chat 模型设计用于交互式对话，并遵循指令良好，而文本完成模型专注于生成给定文本输入的延续',
  memory: 'Memory 允许 AI 模型记住并引用它过去的交互',
  vectorStores: '向量存储允许 AI 模型引用文档的相关部分，这对于问答和文档搜索非常有用',
}

export const nodeCreatorActionsPlaceholderNode = {
  scheduleTrigger: '定时触发',
  webhook: 'Webhook 调用',
}
