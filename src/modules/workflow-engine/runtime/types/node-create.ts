import type { IDataObject, IDisplayOptions, Themed } from 'n8n-workflow'

import type { NodeConnectionType } from '#wf/constants/canvas'
import type { CategoryItemProps, LabelItemProps, LinkItemProps, NodeFilterType, SimplifiedNodeType, SubcategoryItemProps, ViewItemProps } from '#wf/types/interface'

export interface CreateElementBase {
  uuid?: string
  key: string
}

export interface NodeCreateElement extends CreateElementBase {
  type: 'node'
  subcategory: string
  resource?: string
  operation?: string
  properties: SimplifiedNodeType
}

export interface CategoryCreateElement extends CreateElementBase {
  type: 'category'
  subcategory: string
  properties: CategoryItemProps
}

export interface SubcategoryCreateElement extends CreateElementBase {
  type: 'subcategory'
  properties: SubcategoryItemProps
}

export interface SectionCreateElement extends CreateElementBase {
  type: 'section'
  title: string
  children: INodeCreateElement[]
}

export interface ViewCreateElement extends CreateElementBase {
  type: 'view'
  properties: ViewItemProps
}

export interface LabelCreateElement extends CreateElementBase {
  type: 'label'
  subcategory: string
  properties: LabelItemProps
}

export interface LinkCreateElement extends CreateElementBase {
  type: 'link'
  properties: LinkItemProps
}

export interface ActionCreateElement extends CreateElementBase {
  type: 'action'
  subcategory: string
  properties: ActionTypeDescription
}

export type INodeCreateElement =
  | NodeCreateElement
  | CategoryCreateElement
  | SubcategoryCreateElement
  | SectionCreateElement
  | ViewCreateElement
  | LabelCreateElement
  | ActionCreateElement
  | LinkCreateElement

export interface ActionTypeDescription extends SimplifiedNodeType {
  displayOptions?: IDisplayOptions
  values?: IDataObject
  actionKey: string
  outputConnectionType?: NodeConnectionType
  codex: {
    label: string
    categories: string[]
  }
}

export interface NodeViewItemSection {
  key: string
  title: string
  items: string[]
}

export interface ViewStack {
  uuid?: string
  title?: string
  subtitle?: string
  search?: string
  subcategory?: string
  info?: string
  nodeIcon?: {
    iconType?: string
    icon?: Themed<string>
    color?: string
  }
  iconUrl?: string
  rootView?: NodeFilterType
  activeIndex?: number
  transitionDirection?: 'in' | 'out'
  hasSearch?: boolean
  preventBack?: boolean
  items?: INodeCreateElement[]
  baselineItems?: INodeCreateElement[]
  searchItems?: SimplifiedNodeType[]
  forceIncludeNodes?: string[]
  mode?: 'actions' | 'nodes'
  hideActions?: boolean
  baseFilter?: (item: INodeCreateElement) => boolean
  itemsMapper?: (item: INodeCreateElement) => INodeCreateElement
  actionsFilter?: (items: ActionTypeDescription[]) => ActionTypeDescription[]
  panelClass?: string
  sections?: string[] | NodeViewItemSection[]
}

export interface SubcategorizedNodeTypes {
  [subcategory: string]: INodeCreateElement[]
}

export type NodeCreatorTag = {
  text: string
  type?: string
}
