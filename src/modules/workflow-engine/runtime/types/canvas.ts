import type {
  DefaultEdge,
  Node,
  NodeProps,
  OnConnectStartParams,
  Position,
  ViewportTransform,
} from '@vue-flow/core'
import type { ExecutionStatus, INodeConnections } from 'n8n-workflow'

import type { CanvasConnectionMode, CanvasNodeRenderType, NodeConnectionType } from '#wf/constants/canvas'
import type { IExecutionResponse, INodeUi } from '#wf/types/interface'

export type CanvasConnectionPort = {
  node?: string
  type: NodeConnectionType
  index: number
  required?: boolean
  maxConnections?: number
  label?: string
}

export interface CanvasElementPortWithRenderData extends CanvasConnectionPort {
  handleId: string
  connectionsCount: number
  isConnecting: boolean
  position: Position
  offset?: { top?: string, left?: string }
}

export type CanvasNodeDefaultRenderLabelSize = 'small' | 'medium' | 'large'

export type CanvasNodeDefaultRender = {
  type: CanvasNodeRenderType.Default
  options: Partial<{
    configurable: boolean
    configuration: boolean
    trigger: boolean
    inputs: {
      labelSize: CanvasNodeDefaultRenderLabelSize
    }
    outputs: {
      labelSize: CanvasNodeDefaultRenderLabelSize
    }
    tooltip?: string
  }>
}

export type CanvasNodeAddNodesRender = {
  type: CanvasNodeRenderType.AddNodes
  options: Record<string, never>
}

export type CanvasNodeStickyNoteRender = {
  type: CanvasNodeRenderType.StickyNote
  options: Partial<{
    width: number
    height: number
    color: number
    content: string
  }>
}

export interface CanvasNodeData {
  id: INodeUi['id']
  name: INodeUi['name']
  subtitle: string
  type: INodeUi['type']
  typeVersion: INodeUi['typeVersion']
  disabled: INodeUi['disabled']
  inputs: CanvasConnectionPort[]
  outputs: CanvasConnectionPort[]
  connections: {
    [CanvasConnectionMode.Input]: INodeConnections
    [CanvasConnectionMode.Output]: INodeConnections
  }
  issues: {
    items: string[]
    visible: boolean
  }
  pinnedData: {
    count: number
    visible: boolean
  }
  execution: {
    status?: ExecutionStatus
    waiting?: string
    running: boolean
  }
  runData: {
    outputMap: ExecutionOutputMap
    iterations: number
    visible: boolean
  }
  render: CanvasNodeDefaultRender | CanvasNodeStickyNoteRender | CanvasNodeAddNodesRender
}

export type CanvasNode = Node<CanvasNodeData>

export interface CanvasConnectionData {
  source: CanvasConnectionPort
  target: CanvasConnectionPort
  status?: 'success' | 'error' | 'pinned' | 'running'
  maxConnections?: number
}

export type CanvasConnection = DefaultEdge<CanvasConnectionData>

export type CanvasConnectionCreateData = {
  source: string
  sourceHandle: string
  target: string
  targetHandle: string
  data: {
    source: CanvasConnectionPort
    target: CanvasConnectionPort
  }
}

export interface CanvasInjectionData {
  initialized: Ref<boolean>
  isExecuting: Ref<boolean | undefined>
  connectingHandle: Ref<ConnectStartEvent | undefined>
  viewport: Ref<ViewportTransform>
}

export type CanvasNodeEventBusEvents = {
  'update:sticky:color': never
  'update:node:active': never
  'update:node:class': { className: string, add?: boolean }
}

export type CanvasEventBusEvents = {
  fitView: never
  'saved:workflow': never
  'open:execution': IExecutionResponse
  'nodes:select': { ids: string[] }
  'nodes:action': {
    ids: string[]
    action: keyof CanvasNodeEventBusEvents
    payload?: CanvasNodeEventBusEvents[keyof CanvasNodeEventBusEvents]
  }
}

export interface CanvasNodeInjectionData {
  id: Ref<INodeUi['id']>
  data: Ref<CanvasNodeData>
  label: Ref<NodeProps['label']>
  selected: Ref<NodeProps['selected']>
  readOnly: Ref<boolean>
  // eventBus: Ref<EventBus<CanvasNodeEventBusEvents>>
}

export interface CanvasNodeHandleInjectionData {
  id: Ref<CanvasElementPortWithRenderData['handleId']>
  label: Ref<string | undefined>
  mode: Ref<CanvasConnectionMode>
  type: Ref<NodeConnectionType>
  index: Ref<number>
  isRequired: Ref<boolean | undefined>
  isConnected: ComputedRef<boolean | undefined>
  isConnecting: Ref<boolean | undefined>
  isReadOnly: Ref<boolean | undefined>
  maxConnections: Ref<number | undefined>
  runData: Ref<ExecutionOutputMapData | undefined>
  position: Ref<Position>
}

export type ConnectStartEvent = {
  event?: MouseEvent | undefined
} & OnConnectStartParams

export type CanvasNodeMoveEvent = { id: string, position: CanvasNode['position'] }

export type ExecutionOutputMapData = {
  total: number
  iterations: number
}

export type ExecutionOutputMap = {
  [connectionType: string]: {
    [outputIndex: string]: ExecutionOutputMapData
  }
}

export type BoundingBox = {
  x: number
  y: number
  width: number
  height: number
}
