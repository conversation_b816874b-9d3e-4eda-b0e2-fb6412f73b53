interface KeyboardShortcut {
  metaKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  keys: string[]
}

/**
 * 工作流上下文菜单操作类型常量
 */
export const enum ContextMenuActionType {
  // 添加操作
  ADD_NODE = 'add_node',
  ADD_STICKY = 'add_sticky',

  // 选择操作
  SELECT_ALL = 'select_all',
  DESELECT_ALL = 'deselect_all',

  // 节点操作
  COPY = 'copy',
  DUPLICATE = 'duplicate',
  DELETE = 'delete',
  TOGGLE_ACTIVATION = 'toggle_activation',
  EXECUTE = 'execute',
  OPEN = 'open',
  TOGGLE_PIN = 'toggle_pin',
  CHANGE_COLOR = 'change_color',
}

export type ContextMenuAction = ContextMenuActionType

export interface ActionDropdownItem {
  id: ContextMenuAction
  label: string
  badge?: string
  badgeProps?: Record<string, unknown>
  icon?: string
  divided?: boolean
  disabled?: boolean
  shortcut?: KeyboardShortcut
  customClass?: string
}
