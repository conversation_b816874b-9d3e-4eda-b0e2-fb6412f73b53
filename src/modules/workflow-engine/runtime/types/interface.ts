import type { FrontendSettings, Iso8601DateTimeString } from '@n8n/api-types'
import type { AnnotationVote, ExecutionStatus, ExecutionSummary, GenericValue, IConnections, ICredentialsDecrypted, ICredentialsEncrypted, ICredentialType, IDataObject, IDisplayOptions, INode, INodeCredentialsDetails, INodeExecutionData, INodeIssues, INodeListSearchItems, INodeParameters, INodeProperties, INodeTypeDescription, IPinData, IRunData, IRunExecutionData, ITaskData, IWorkflowSettings, NodeParameterValueType, ProjectSharingData, StartNodeData, WorkflowExecuteMode } from 'n8n-workflow'

import type { NodeConnectionType } from '#wf/constants/canvas'
import type { AI_NODE_CREATOR_VIEW, AI_OTHERS_NODE_CREATOR_VIEW, AI_UNCATEGORIZED_CATEGORY, CREDENTIAL_EDIT_MODAL_KEY, REGULAR_NODE_CREATOR_VIEW, TRIGGER_NODE_CREATOR_VIEW } from '#wf/constants/common'
import type { BulkCommand, Undoable } from '#wf/models/history'
import type { ActionTypeDescription, CreateElementBase, NodeCreatorTag } from '#wf/types/node-create'

export type AppliedThemeOption = 'light' | 'dark'

export type ThemeOption = AppliedThemeOption | 'system'

export type ModalState = {
  open: boolean
  mode?: string | null
  data?: Record<string, unknown>
  activeId?: string | null
  curlCommand?: string
  httpNodeParameters?: string
}

export interface NotificationOptions {
  message: string
}

export interface NewCredentialsModal extends ModalState {
  showAuthSelector?: boolean
}

export type Modals = {
  [CREDENTIAL_EDIT_MODAL_KEY]: NewCredentialsModal
  [key: string]: ModalState
}

export type ModalKey = keyof Modals

export interface ITag {
  id: string
  name: string
  usageCount?: number
  createdAt?: string
  updatedAt?: string
}

export interface WorkflowMetadata {
  onboardingId?: string
  templateId?: string
  instanceId?: string
  templateCredsSetupCompleted?: boolean
}

/** 工作流节点类型 */
export type WorkflowNode = INodeUi

/**
 * 工作流数据库模型接口，定义了存储在数据库中的工作流结构
 */
export interface IWorkflowDb {
  id: string
  /** 工作流的名称，用于在界面中显示 */
  name: string
  /** 工作流是否处于激活状态，激活的工作流可以被触发执行 */
  active: boolean
  /** 创建和更新时间戳，用于追踪工作流的生命周期 */
  createdAt: number | string
  updatedAt: number | string
  /** 工作流中的所有节点，每个节点代表一个操作或功能组件 */
  nodes: WorkflowNode[]
  /** 节点之间的连接关系，定义数据如何从一个节点流向另一个节点 */
  connections: IConnections
  /** 工作流执行配置，包括执行顺序等设置 */
  settings?: IWorkflowSettings
  /** 工作流的标签，用于分类和搜索 */
  tags?: ITag[] | string[] // string[] when store or requested, ITag[] from API response
  /** 固定的数据，用于测试和调试时替代节点实际执行结果 */
  pinData?: IPinData
  sharedWithProjects?: ProjectSharingData[]
  homeProject?: ProjectSharingData
  /** 版本ID，用于版本管理 */
  versionId: string
  // HACK: 临时屏蔽类型定义
  scopes?: UnsafeAny[]
  // HACK: 临时屏蔽类型定义
  usedCredentials?: UnsafeAny[]
  // scopes?: Scope[]
  // usedCredentials?: IUsedCredential[]
  /** 存储工作流相关的元数据信息 */
  meta?: WorkflowMetadata
}

export interface INodeMetadata {
  parametersLastUpdatedAt?: number
  pristine: boolean
}

export interface NodeMetadataMap {
  [nodeName: string]: INodeMetadata
}

export interface IExecutionBase {
  id?: string
  finished: boolean
  mode: WorkflowExecuteMode
  status: ExecutionStatus
  retryOf?: string
  retrySuccessId?: string
  startedAt: Date
  createdAt: Date
  stoppedAt?: Date
  workflowId?: string // To be able to filter executions easily //
}

export interface IExecutionResponse extends IExecutionBase {
  id: string
  data?: IRunExecutionData
  workflowData: IWorkflowDb
  executedNode?: string
  triggerNode?: string
}

export type XYPosition = [number, number]

export interface INodeUi extends INode {
  position: XYPosition
  color?: string
  notes?: string
  issues?: INodeIssues
  name: string
  pinData?: IDataObject
}

export type NodeTypesByTypeNameAndVersion = {
  [nodeType: string]: {
    [version: number]: INodeTypeDescription
  }
}

export interface TargetItem {
  nodeName: string
  itemIndex: number
  runIndex: number
  outputIndex: number
}

export interface RootState {
  baseUrl: string
  restEndpoint: string
  defaultLocale: string
  endpointForm: string
  endpointFormTest: string
  endpointFormWaiting: string
  endpointWebhook: string
  endpointWebhookTest: string
  endpointWebhookWaiting: string
  timezone: string
  executionTimeout: number
  maxExecutionTimeout: number
  versionCli: string
  oauthCallbackUrls: object
  n8nMetadata: {
    [key: string]: string | number | undefined
  }
  pushRef: string
  urlBaseWebhook: string
  urlBaseEditor: string
  instanceId: string
  binaryDataMode: 'default' | 'filesystem' | 's3'
}

export interface IUpdateInformation<T extends NodeParameterValueType = NodeParameterValueType> {
  name: string
  key?: string
  value: T
  node?: string
  oldValue?: string | number
  type?: 'optionsOrderChanged'
}

export type SimplifiedNodeType = Pick<
  INodeTypeDescription,
  | 'displayName'
  | 'description'
  | 'name'
  | 'group'
  | 'icon'
  | 'iconUrl'
  | 'iconColor'
  | 'badgeIconUrl'
  | 'codex'
  | 'defaults'
  | 'outputs'
> & {
  tag?: string
}

export interface IVersionNode {
  name: string
  displayName: string
  icon: string
  iconUrl?: string
  defaults: INodeParameters
  iconData: {
    type: string
    icon?: string
    fileBuffer?: string
  }
  typeVersion?: number
}

export type NodeCreatorOpenSource =
  | ''
  | 'context_menu'
  | 'no_trigger_execution_tooltip'
  | 'plus_endpoint'
  | 'add_input_endpoint'
  | 'trigger_placeholder_button'
  | 'tab'
  | 'node_connection_action'
  | 'node_connection_drop'
  | 'notice_error_message'
  | 'add_node_button'

export type NodeFilterType =
  | typeof REGULAR_NODE_CREATOR_VIEW
  | typeof TRIGGER_NODE_CREATOR_VIEW
  | typeof AI_NODE_CREATOR_VIEW
  | typeof AI_OTHERS_NODE_CREATOR_VIEW
  | typeof AI_UNCATEGORIZED_CATEGORY

export type ToggleNodeCreatorOptions = {
  createNodeActive: boolean
  source?: NodeCreatorOpenSource
  nodeCreatorView?: NodeFilterType
  hasAddedNodes?: boolean
}

export interface CategoryItemProps {
  name: string
  count: number
}

export interface SubcategoryItemProps {
  description?: string
  iconType?: string
  icon?: string
  iconProps?: {
    color?: string
  }
  panelClass?: string
  title?: string
  subcategory?: string
  defaults?: INodeParameters
  forceIncludeNodes?: string[]
  sections?: string[]
}

export interface ViewItemProps {
  title: string
  description: string
  icon: string
  tag?: NodeCreatorTag
  borderless?: boolean
}

export interface LabelItemProps {
  key: string
}

export interface LinkItemProps {
  url: string
  key: string
  newTab?: boolean
  title: string
  description: string
  icon: string
  tag?: NodeCreatorTag
}

export type EnterpriseEditionFeatureKey =
  | 'AdvancedExecutionFilters'
  | 'Sharing'
  | 'Ldap'
  | 'LogStreaming'
  | 'Variables'
  | 'Saml'
  | 'SourceControl'
  | 'ExternalSecrets'
  | 'AuditLogs'
  | 'DebugInEditor'
  | 'WorkflowHistory'
  | 'WorkerView'
  | 'AdvancedPermissions'

export type EnterpriseEditionFeatureValue = keyof Omit<FrontendSettings['enterprise'], 'projects'>

export type ExtractActionKeys<T> = T extends SimplifiedNodeType ? T['name'] : never

export type ActionsRecord<T extends SimplifiedNodeType[]> = {
  [K in ExtractActionKeys<T[number]>]: ActionTypeDescription[]
}

export type AddedNode = {
  type: string
  /** 是否自动打开节点配置，如果为 true，则在添加节点后会自动打开节点配置 */
  openDetail?: boolean
  isAutoAdd?: boolean
} & Partial<INodeUi>

export type AddedNodeConnection = {
  from: { nodeIndex: number, outputIndex?: number, type?: NodeConnectionType }
  to: { nodeIndex: number, inputIndex?: number, type?: NodeConnectionType }
}

export type AddedNodesAndConnections = {
  nodes: AddedNode[]
  connections: AddedNodeConnection[]
}

export interface ICredentialsResponse extends ICredentialsEncrypted {
  id: string
  createdAt: Iso8601DateTimeString
  updatedAt: Iso8601DateTimeString
  sharedWithProjects?: ProjectSharingData[]
  homeProject?: ProjectSharingData
  currentUserHasAccess?: boolean
  // scopes?: Scope[]
  // ownedBy?: Pick<IUserResponse, 'id' | 'firstName' | 'lastName' | 'email'>
  isManaged: boolean
}

export interface IUsedCredential {
  id: string
  name: string
  credentialType: string
  currentUserHasAccess: boolean
  homeProject?: ProjectSharingData
  sharedWithProjects?: ProjectSharingData[]
}

export interface ICredentialTypeMap {
  [name: string]: ICredentialType
}

export interface ICredentialMap {
  [name: string]: ICredentialsResponse
}

export interface ICredentialsState {
  credentialTypes: ICredentialTypeMap
  credentials: ICredentialMap
}

export interface ICredentialsBase {
  createdAt: Iso8601DateTimeString
  updatedAt: Iso8601DateTimeString
}

export interface ICredentialsDecryptedResponse extends ICredentialsBase, ICredentialsDecrypted {
  id: string
}

export interface IShareCredentialsPayload {
  shareWithIds: string[]
}

export interface ActionCreateElement extends CreateElementBase {
  type: 'action'
  subcategory: string
  properties: ActionTypeDescription
}

export interface HistoryState {
  redoStack: Undoable[]
  undoStack: Undoable[]
  currentBulkAction: BulkCommand | null
  bulkInProgress: boolean
}

export interface IWorkflowDataUpdate {
  id?: string
  name?: string
  nodes?: INode[]
  connections?: IConnections
  settings?: IWorkflowSettings
  active?: boolean
  tags?: ITag[] | string[] // string[] when store or requested, ITag[] from API response
  pinData?: IPinData
  versionId?: string
  meta?: WorkflowMetadata
}

export interface IWorkflowDataCreate extends IWorkflowDataUpdate {
  projectId?: string
}

export interface IWorkflowData {
  id?: string
  name?: string
  active?: boolean
  nodes: INode[]
  connections: IConnections
  settings?: IWorkflowSettings
  tags?: string[]
  pinData?: IPinData
  versionId?: string
  meta?: WorkflowMetadata
}

export interface IWorkflowToShare extends IWorkflowDataUpdate {
  meta: WorkflowMetadata
}

export interface INodeUpdatePropertiesInformation {
  name: string // Node-Name
  properties: {
    position: XYPosition
    [key: string]: IDataObject | XYPosition
  }
}

export interface IExecutionPushResponse {
  executionId?: string
  waitingForWebhook?: boolean
}

export interface IStartRunData {
  workflowData: IWorkflowData
  startNodes?: StartNodeData[]
  destinationNode?: string
  runData?: IRunData
  dirtyNodeNames?: string[]
  triggerToStartFrom?: {
    name: string
    data?: ITaskData
  }
}

export type ExecutionFilterMetadata = {
  key: string
  value: string
}

export type ExecutionFilterVote = AnnotationVote | 'all'

export type ExecutionsQueryFilter = {
  status?: ExecutionStatus[]
  projectId?: string
  workflowId?: string
  finished?: boolean
  waitTill?: boolean
  metadata?: Array<{ key: string, value: string }>
  startedAfter?: string
  startedBefore?: string
  annotationTags?: string[]
  vote?: ExecutionFilterVote
}

export type ExecutionFilterType = {
  status: string
  workflowId: string
  startDate: string | Date
  endDate: string | Date
  tags: string[]
  annotationTags: string[]
  vote: ExecutionFilterVote
  metadata: ExecutionFilterMetadata[]
}

export interface IExecutionDeleteFilter {
  deleteBefore?: Date
  filters?: ExecutionsQueryFilter
  ids?: string[]
}

export type WorkflowTitleStatus = 'EXECUTING' | 'IDLE' | 'ERROR' | 'DEBUG'

export type NodePanelType = 'input' | 'output'

export type ExecutionSummaryWithScopes = ExecutionSummary & {
  scopes: UnsafeAny[]
  // scopes: Scope[]
}

export interface IExecutionFlatted extends IExecutionBase {
  data: IRunExecutionData
  workflowData: IWorkflowDb
}

export interface IExecutionFlattedResponse extends IExecutionFlatted {
  id: string
}

export interface IExecutionsStopData {
  finished?: boolean
  mode: WorkflowExecuteMode
  startedAt: Date
  stoppedAt: Date
  status: ExecutionStatus
}

export interface INodeTypesMaxCount {
  [key: string]: {
    exist: number
    max: number
    nodeNames: string[]
  }
}

export type IRunDataDisplayMode = 'table' | 'json' | 'binary' | 'schema' | 'html' | 'ai'

export type Basic = string | number | boolean

export type Primitives = Basic | bigint | symbol

export type Optional<T> = T | undefined | null

export type SchemaType =
  | 'string'
  | 'number'
  | 'boolean'
  | 'bigint'
  | 'symbol'
  | 'array'
  | 'object'
  | 'function'
  | 'null'
  | 'undefined'

export type Schema = { type: SchemaType, key?: string, value: string | Schema[], path: string }

export interface IWorkflowTemplateNodeCredentials {
  [key: string]: string | INodeCredentialsDetails
}

export interface IWorkflowTemplateNode
  extends Pick<
    INodeUi,
    'name' | 'type' | 'position' | 'parameters' | 'typeVersion' | 'webhookId' | 'id' | 'disabled'
  > {
  // The credentials in a template workflow have a different type than in a regular workflow
  credentials?: IWorkflowTemplateNodeCredentials
}

export interface IWorkflowTemplate {
  id: number
  name: string
  workflow: Pick<IWorkflowData, 'connections' | 'settings' | 'pinData'> & {
    nodes: IWorkflowTemplateNode[]
  }
}

export interface IResourceLocatorResultExpanded extends INodeListSearchItems {
  linkAlt?: string
}

export type InputPanel = {
  nodeName?: string
  run?: number
  branch?: number
  data: {
    isEmpty: boolean
  }
}

export interface EnvironmentVariable {
  id: string
  key: string
  value: string
}

export type ExternalSecretsProviderState = 'connected' | 'tested' | 'initializing' | 'error'

export type ExternalSecretsProviderData = Record<string, IUpdateInformation['value']>

export type ExternalSecretsProviderProperty = INodeProperties

export interface ExternalSecretsProvider {
  icon: string
  name: string
  displayName: string
  connected: boolean
  connectedAt: string | false
  state: ExternalSecretsProviderState
  data?: ExternalSecretsProviderData
  properties?: ExternalSecretsProviderProperty[]
}

export interface CurlToJSONResponse {
  'parameters.url': string
  'parameters.authentication': string
  'parameters.method': string
  'parameters.sendHeaders': boolean
  'parameters.headerParameters.parameters.0.name': string
  'parameters.headerParameters.parameters.0.value': string
  'parameters.sendQuery': boolean
  'parameters.sendBody': boolean
}

export type NodeAuthenticationOption = {
  name: string
  value: string
  displayOptions?: IDisplayOptions
}

export interface ITab<Value extends string | number = string | number> {
  value: Value
  label?: string
  href?: string
  icon?: string
  align?: 'right'
  tooltip?: string
}

export interface OutputPanel {
  branch?: number
  data: {
    isEmpty: boolean
  }
  editMode: {
    enabled: boolean
    value: string
  }
}

export interface ITableData {
  columns: string[]
  data: GenericValue[][]
  hasJson: { [key: string]: boolean }
  metadata: {
    hasExecutionIds: boolean
    data: Array<INodeExecutionData['metadata'] | undefined>
  }
}

export type Draggable = {
  isDragging: boolean
  type: string
  data: string
  dimensions: DOMRect | null
  activeTarget: { id: string, stickyPosition: null | XYPosition } | null
}
