import type { AnyType } from '~/types/common'

// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
export type CallbackFn = Function

export type Payloads<ListenerMap> = {
  [E in keyof ListenerMap]: unknown
}

type Listener<Payload> = (payload: Payload) => void

export interface EventBus<ListenerMap extends Payloads<ListenerMap> = Record<string, AnyType>> {
  on<EventName extends keyof ListenerMap & string>(
    eventName: EventName,
    fn: Listener<ListenerMap[EventName]>,
  ): void

  once<EventName extends keyof ListenerMap & string>(
    eventName: EventName,
    fn: Listener<ListenerMap[EventName]>,
  ): void

  off<EventName extends keyof ListenerMap & string>(
    eventName: EventName,
    fn: Listener<ListenerMap[EventName]>,
  ): void

  emit<EventName extends keyof ListenerMap & string>(
    eventName: EventName,
    event?: ListenerMap[EventName],
  ): void
}
