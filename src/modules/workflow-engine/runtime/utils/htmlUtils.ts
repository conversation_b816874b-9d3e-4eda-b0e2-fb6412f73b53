import DOMPurify from 'dompurify'

import { ALLOWED_HTML_ATTRIBUTES, ALLOWED_HTML_TAGS } from '#wf/constants/common'

/** Constants and utility functions that help in HTML, CSS and DOM manipulation */

export function sanitizeHtml(dirtyHtml: string) {
  // 设置DOMPurify钩子来处理特殊属性验证
  DOMPurify.addHook('afterSanitizeAttributes', (node) => {
    // 处理img标签的src属性
    if (node.tagName === 'IMG' && node.hasAttribute('src')) {
      const src = node.getAttribute('src')

      if (src) {
        const isImageFile = src.split('#')[0].match(/\.(jpeg|jpg|gif|png|webp)$/) !== null
        const isStaticImageFile = isImageFile && src.startsWith('/static/')

        if (!src.startsWith('https://') && !isStaticImageFile) {
          node.removeAttribute('src')
        }
      }
    }

    // 处理href属性 - 只允许https和相对路径
    if (node.hasAttribute('href')) {
      const href = node.getAttribute('href')

      if (href && !href.match(/^https?:\/\//gm) && !href.startsWith('/')) {
        node.removeAttribute('href')
      }
    }
  })

  const result = DOMPurify.sanitize(dirtyHtml, {
    ALLOWED_TAGS: ALLOWED_HTML_TAGS,
    ALLOWED_ATTR: [...ALLOWED_HTML_ATTRIBUTES, 'src'],
    ADD_ATTR: ['data-*'],
    ALLOWED_URI_REGEXP: /^(?:(?:https?|ftp|mailto):|\/|[^a-z]|[a-z+.-]+(?:[^a-z+.-:]|$))/i,
  })

  // 清理钩子以避免内存泄漏
  DOMPurify.removeHook('afterSanitizeAttributes')

  return result
}

/**
 * Checks if the input is a string and sanitizes it by removing or escaping harmful characters,
 * returning the original input if it's not a string.
 */
export const sanitizeIfString = <T>(message: T): string | T => {
  if (typeof message === 'string') {
    return sanitizeHtml(message)
  }

  return message
}

export function convertRemToPixels(rem: string) {
  return parseInt(rem, 10) * parseFloat(getComputedStyle(document.documentElement).fontSize)
}

export function isChildOf(parent: Element, child: Element): boolean {
  if (child.parentElement === null) {
    return false
  }

  if (child.parentElement === parent) {
    return true
  }

  return isChildOf(parent, child.parentElement)
}

export const capitalizeFirstLetter = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1)
}

export const getBannerRowHeight = async (): Promise<number> => {
  return await new Promise((resolve) => {
    setTimeout(() => {
      resolve(document.getElementById('banners')?.clientHeight ?? 0)
    }, 0)
  })
}
