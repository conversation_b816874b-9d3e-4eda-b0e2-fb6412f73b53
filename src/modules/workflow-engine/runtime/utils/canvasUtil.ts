import type { Connection } from '@vue-flow/core'
import { consola } from 'consola'
import type { IConnection, IConnections, INodeTypeDescription } from 'n8n-workflow'

import { CanvasConnectionMode, NodeConnectionType } from '#wf/constants/canvas'
import type { BoundingBox, CanvasConnection, CanvasConnectionPort } from '#wf/types/canvas'
import type { INodeUi } from '#wf/types/interface'
import { isValidCanvasConnectionMode, isValidNodeConnectionType } from '#wf/utils/typeGuards'

import type { UnsafeAny } from '~/types/common'

/**
 * Creates a canvas connection handle string from its parts
 */
export function createCanvasConnectionHandleString({
  mode,
  type = NodeConnectionType.Main,
  index = 0,
}: {
  mode: 'inputs' | 'outputs'
  type?: NodeConnectionType
  index?: number
}) {
  return `${mode}/${type}/${index}`
}

/**
 * Creates a canvas connection ID from a connection
 */
export function createCanvasConnectionId(connection: Connection) {
  return `[${connection.source}/${connection.sourceHandle}][${connection.target}/${connection.targetHandle}]`
}

/**
 * Maps multiple legacy n8n connections to VueFlow connections
 */
export function mapLegacyConnectionsToCanvasConnections(
  legacyConnections: IConnections,
  nodes: INodeUi[],
): CanvasConnection[] {
  const mappedConnections: CanvasConnection[] = []

  Object.keys(legacyConnections).forEach((fromNodeName) => {
    const fromId = nodes.find((node) => node.name === fromNodeName)?.id ?? ''
    const fromConnectionTypes = Object.keys(
      legacyConnections[fromNodeName],
    ) as NodeConnectionType[]

    fromConnectionTypes.forEach((fromConnectionType) => {
      const fromPorts = legacyConnections[fromNodeName][fromConnectionType]
      fromPorts?.forEach((toPorts, fromIndex) => {
        toPorts?.forEach((toPort) => {
          const toNodeName = toPort.node
          const toId = nodes.find((node) => node.name === toNodeName)?.id ?? ''
          const toConnectionType = toPort.type as unknown as NodeConnectionType
          const toIndex = toPort.index

          const sourceHandle = createCanvasConnectionHandleString({
            mode: CanvasConnectionMode.Output,
            type: fromConnectionType,
            index: fromIndex,
          })

          const targetHandle = createCanvasConnectionHandleString({
            mode: CanvasConnectionMode.Input,
            type: toConnectionType,
            index: toIndex,
          })

          const connectionId = createCanvasConnectionId({
            source: fromId,
            sourceHandle,
            target: toId,
            targetHandle,
          })

          if (fromId && toId) {
            mappedConnections.push({
              id: connectionId,
              source: fromId,
              target: toId,
              sourceHandle,
              targetHandle,
              data: {
                source: {
                  node: fromNodeName,
                  index: fromIndex,
                  type: fromConnectionType,
                },
                target: {
                  node: toNodeName,
                  index: toIndex,
                  type: toConnectionType,
                },
              },
            })
          }
        })
      })
    })
  })

  return mappedConnections
}

/**
 * 将节点类型描述中的传统端点定义转换为画布连接端口
 *
 * 在工作流引擎的可视化编辑器中，此函数负责将节点的原始输入端点定义（来自 n8n 工作流定义）
 * 转换为前端画布可渲染的连接端口格式。这是工作流节点连接能力的核心转换函数。
 *
 * 主要业务作用：
 * - 支持节点间的可视化连接 - 使节点能在画布上创建和显示连接线
 * - 维护节点输入/输出的类型安全性 - 确保连接匹配正确的类型
 * - 处理必需和可选端点 - 标记哪些连接是必需的
 * - 处理最大连接数限制 - 限制端点可接受的连接数
 * - 提供端点描述性标签 - 增强用户界面可用性
 *
 * 返回的 CanvasConnectionPort 数组随后用于渲染节点的连接点和处理用户的连接操作。
 *
 * @param endpoints - 节点类型描述中的输入/输出端点定义
 * @param endpointNames - 端点名称数组，用于提供标签
 * @returns 画布连接端口数组，用于节点的可视化连接
 */
export function mapLegacyEndpointsToCanvasConnectionPort(
  endpoints: INodeTypeDescription['inputs'],
  endpointNames: string[] = [],
): CanvasConnectionPort[] {
  if (typeof endpoints === 'string') {
    consola.warn('Node endpoints have not been evaluated', endpoints)

    return []
  }

  return endpoints.map((endpoint, endpointIndex) => {
    const typeValue = typeof endpoint === 'string' ? endpoint : endpoint.type
    const type = isValidNodeConnectionType(typeValue) ? typeValue : NodeConnectionType.Main
    const label = typeof endpoint === 'string' ? endpointNames[endpointIndex] : endpoint.displayName
    const index = endpoints
      .slice(0, endpointIndex + 1)
      .filter((e) => ((typeof e === 'string' ? e : e.type) as unknown as NodeConnectionType) === type).length - 1
    const required = typeof endpoint === 'string' ? false : endpoint.required
    const maxConnections = typeof endpoint === 'string' ? undefined : endpoint.maxConnections

    return {
      type,
      index,
      label,
      ...(maxConnections ? { maxConnections } : {}),
      ...(required ? { required } : {}),
    }
  })
}

/**
 * Checks if two bounding boxes overlap
 */
export function checkOverlap(node1: BoundingBox, node2: BoundingBox) {
  return !(
  // node1 is completely to the left of node2
    (
      node1.x + node1.width <= node2.x
      // node2 is completely to the left of node1
      || node2.x + node2.width <= node1.x
      // node1 is completely above node2
      || node1.y + node1.height <= node2.y
      // node2 is completely above node1
      || node2.y + node2.height <= node1.y
    )
  )
}

/**
 * Parses a canvas connection handle string into its parts:
 * - mode
 * - type
 * - index
 */
export function parseCanvasConnectionHandleString(handle: string | null | undefined) {
  const [mode, type, index] = (handle ?? '').split('/')

  const resolvedMode = isValidCanvasConnectionMode(mode) ? mode : CanvasConnectionMode.Output
  const resolvedType = isValidNodeConnectionType(type) ? type : NodeConnectionType.Main
  let resolvedIndex = parseInt(index, 10)

  if (isNaN(resolvedIndex)) {
    resolvedIndex = 0
  }

  return {
    mode: resolvedMode,
    type: resolvedType,
    index: resolvedIndex,
  }
}

/**
 * Maps a VueFlow connection to a legacy n8n connection
 */
export function mapCanvasConnectionToLegacyConnection(
  sourceNode: INodeUi,
  targetNode: INodeUi,
  connection: Connection,
): [IConnection, IConnection] {
  // Output
  const sourceNodeName = sourceNode?.name ?? ''
  const { type: sourceType, index: sourceIndex } = parseCanvasConnectionHandleString(
    connection.sourceHandle,
  )

  // Input
  const targetNodeName = targetNode?.name ?? ''
  const { type: targetType, index: targetIndex } = parseCanvasConnectionHandleString(
    connection.targetHandle,
  )

  return [
    {
      node: sourceNodeName,
      type: sourceType as UnsafeAny,
      index: sourceIndex,
    },
    {
      node: targetNodeName,
      type: targetType as UnsafeAny,
      index: targetIndex,
    },
  ]
}

/**
 * Maps a single legacy n8n connection to a VueFlow connection
 */
export function mapLegacyConnectionToCanvasConnection(
  sourceNode: INodeUi,
  targetNode: INodeUi,
  legacyConnection: [IConnection, IConnection],
): Connection {
  const source = sourceNode.id
  const sourceHandle = createCanvasConnectionHandleString({
    mode: CanvasConnectionMode.Output,
    type: legacyConnection[0].type as unknown as NodeConnectionType,
    index: legacyConnection[0].index,
  })
  const target = targetNode.id
  const targetHandle = createCanvasConnectionHandleString({
    mode: CanvasConnectionMode.Input,
    type: legacyConnection[1].type as unknown as NodeConnectionType,
    index: legacyConnection[1].index,
  })

  return {
    source,
    sourceHandle,
    target,
    targetHandle,
  }
}

/**
 * Inserts spacers between endpoints to visually separate them
 */
export function insertSpacersBetweenEndpoints<T>(
  endpoints: T[],
  requiredEndpointsCount = 0,
  minEndpointsCount = 4,
) {
  const endpointsWithSpacers: Array<T | null> = [...endpoints]
  const optionalNonMainInputsCount = endpointsWithSpacers.length - requiredEndpointsCount
  const spacerCount = minEndpointsCount - requiredEndpointsCount - optionalNonMainInputsCount

  // Insert `null` in between required non-main inputs and non-required non-main inputs
  // to separate them visually if there are less than 4 inputs in total
  if (endpointsWithSpacers.length < minEndpointsCount) {
    for (let i = 0; i < spacerCount; i++) {
      endpointsWithSpacers.splice(requiredEndpointsCount + i, 0, null)
    }
  }

  return endpointsWithSpacers
}
