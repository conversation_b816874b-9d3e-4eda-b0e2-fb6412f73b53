import { sortBy } from 'lodash-es'
import { AI_TRANSFORM_NODE_TYPE, SEND_AND_WAIT_OPERATION } from 'n8n-workflow/dist/Constants.js'
import { nanoid } from 'nanoid'

import { AI_CATEGORY_AGENTS, AI_CATEGORY_OTHER_TOOLS, AI_SUBCATEGORY, CORE_NODES_CATEGORY, DEFAULT_SUBCATEGORY, HUMAN_IN_THE_LOOP_CATEGORY } from '#wf/constants/common'
import { useSettingsStore } from '#wf/stores/setting.store'
import type { ActionCreateElement, INodeCreateElement, NodeCreateElement, NodeViewItemSection, SectionCreateElement, SubcategorizedNodeTypes } from '#wf/types/node-create'

import type { SimplifiedNodeType } from '../types/interface'

const SEQUENTIAL_BONUS = 60 // bonus for adjacent matches
const SEPARATOR_BONUS = 30 // bonus if match occurs after a separator
const CAMEL_BONUS = 30 // bonus if match is uppercase and prev is lower
const FIRST_LETTER_BONUS = 15 // bonus if the first letter is matched

const LEADING_LETTER_PENALTY = -20 // penalty applied for every letter in str before the first match
const MAX_LEADING_LETTER_PENALTY = -200 // maximum penalty for leading letters
const UNMATCHED_LETTER_PENALTY = -5

export function flattenCreateElements(items: INodeCreateElement[]): INodeCreateElement[] {
  return items.map((item) => (item.type === 'section' ? item.children : item)).flat()
}

export function extendItemsWithUUID(items: INodeCreateElement[]) {
  return items.map((item) => ({
    ...item,
    uuid: `${item.key}-${nanoid()}`,
  }))
}

export function isAINode(node: INodeCreateElement) {
  const isNode = node.type === 'node'

  if (!isNode) {
    return false
  }

  if (node.properties.codex?.categories?.includes(AI_SUBCATEGORY)) {
    const isAgentSubcategory = node.properties.codex?.subcategories?.[AI_SUBCATEGORY]?.includes(AI_CATEGORY_AGENTS)

    return !isAgentSubcategory
  }

  return false
}

export function transformNodeType(
  node: SimplifiedNodeType,
  subcategory?: string,
  type: 'node' | 'action' = 'node',
): NodeCreateElement | ActionCreateElement {
  const createElement = {
    uuid: nanoid(),
    key: node.name,
    subcategory:
      subcategory ?? node.codex?.subcategories?.[CORE_NODES_CATEGORY]?.[0] ?? DEFAULT_SUBCATEGORY,
    properties: {
      ...node,
    },
    type,
  }

  return type === 'action'
    ? (createElement as ActionCreateElement)
    : (createElement as NodeCreateElement)
}

function getValue<T extends object>(obj: T, prop: string): unknown {
  if (Object.hasOwn(obj, prop)) {
    return obj[prop as keyof T]
  }

  const segments = prop.split('.')

  let result = obj
  let i = 0

  while (result && i < segments.length) {
    const key = segments[i] as keyof T
    result = result[key] as T
    i++
  }

  return result
}

function fuzzyMatchRecursive(
  pattern: string,
  target: string,
  patternCurIndex: number,
  targetCurrIndex: number,
  targetMatches: null | number[],
  matches: number[],
  maxMatches: number,
  nextMatch: number,
  recursionCount: number,
  recursionLimit: number,
): { matched: boolean, outScore: number } {
  let outScore = 0

  // Return if recursion limit is reached.
  if (++recursionCount >= recursionLimit) {
    return { matched: false, outScore }
  }

  // Return if we reached ends of strings.
  if (patternCurIndex === pattern.length || targetCurrIndex === target.length) {
    return { matched: false, outScore }
  }

  // Recursion params
  let recursiveMatch = false
  let bestRecursiveMatches: number[] = []
  let bestRecursiveScore = 0

  // Loop through pattern and str looking for a match.
  let firstMatch = true

  while (patternCurIndex < pattern.length && targetCurrIndex < target.length) {
    // Match found.
    if (pattern[patternCurIndex].toLowerCase() === target[targetCurrIndex].toLowerCase()) {
      if (nextMatch >= maxMatches) {
        return { matched: false, outScore }
      }

      if (firstMatch && targetMatches) {
        matches = [...targetMatches]
        firstMatch = false
      }

      const recursiveMatches: number[] = []
      const recursiveResult = fuzzyMatchRecursive(
        pattern,
        target,
        patternCurIndex,
        targetCurrIndex + 1,
        matches,
        recursiveMatches,
        maxMatches,
        nextMatch,
        recursionCount,
        recursionLimit,
      )

      const recursiveScore = recursiveResult.outScore

      if (recursiveResult.matched) {
        // Pick best recursive score.
        if (!recursiveMatch || recursiveScore > bestRecursiveScore) {
          bestRecursiveMatches = [...recursiveMatches]
          bestRecursiveScore = recursiveScore
        }

        recursiveMatch = true
      }

      matches[nextMatch++] = targetCurrIndex
      ++patternCurIndex
    }

    ++targetCurrIndex
  }

  const matched = patternCurIndex === pattern.length

  if (matched) {
    outScore = 100

    // Apply leading letter penalty (if not n8n-prefixed)
    if (!target.toLowerCase().startsWith('n8n')) {
      let penalty = LEADING_LETTER_PENALTY * matches[0]
      penalty = penalty < MAX_LEADING_LETTER_PENALTY ? MAX_LEADING_LETTER_PENALTY : penalty
      outScore += penalty
    }

    // Apply unmatched penalty
    const unmatched = target.length - nextMatch
    outScore += UNMATCHED_LETTER_PENALTY * unmatched

    // Apply ordering bonuses
    for (let i = 0; i < nextMatch; i++) {
      const currIdx = matches[i]

      if (i > 0) {
        const prevIdx = matches[i - 1]

        if (currIdx === prevIdx + 1) {
          outScore += SEQUENTIAL_BONUS
        }
      }

      // Check for bonuses based on neighbor character value.
      if (currIdx > 0) {
        // Camel case
        const neighbor = target[currIdx - 1]
        const curr = target[currIdx]

        if (neighbor !== neighbor.toUpperCase() && curr !== curr.toLowerCase()) {
          outScore += CAMEL_BONUS
        }

        const isNeighbourSeparator = neighbor === '_' || neighbor === ' '

        if (isNeighbourSeparator) {
          outScore += SEPARATOR_BONUS
        }
      }
      else {
        // First letter
        outScore += FIRST_LETTER_BONUS
      }
    }

    // Return best result
    if (recursiveMatch && (!matched || bestRecursiveScore > outScore)) {
      // Recursive score is better than "this"
      matches = [...bestRecursiveMatches]
      outScore = bestRecursiveScore

      return { matched: true, outScore }
    }
    else if (matched) {
      // "this" score is better than recursive
      return { matched: true, outScore }
    }
    else {
      return { matched: false, outScore }
    }
  }

  return { matched: false, outScore }
}

/**
 * Returns true if each character in pattern is found sequentially within target
 * @param {*} pattern string
 * @param {*} target string
 */
function fuzzyMatchSimple(pattern: string, target: string): boolean {
  let patternIdx = 0
  let strIdx = 0

  while (patternIdx < pattern.length && strIdx < target.length) {
    const patternChar = pattern.charAt(patternIdx).toLowerCase()
    const targetChar = target.charAt(strIdx).toLowerCase()

    if (patternChar === targetChar) {
      patternIdx++
    }

    ++strIdx
  }

  return pattern.length !== 0 && target.length !== 0 && patternIdx === pattern.length
}

/**
 * Does a fuzzy search to find pattern inside a string.
 * @param {*} pattern string        pattern to search for
 * @param {*} target     string        string which is being searched
 * @returns [boolean, number]       a boolean which tells if pattern was
 *                                  found or not and a search score
 */
function fuzzyMatch(pattern: string, target: string): { matched: boolean, outScore: number } {
  const recursionCount = 0
  const recursionLimit = 5
  const matches: number[] = []
  const maxMatches = 256

  return fuzzyMatchRecursive(
    pattern,
    target,
    0 /* patternCurIndex */,
    0 /* strCurrIndex */,
    null /* srcMatces */,
    matches,
    maxMatches,
    0 /* nextMatch */,
    recursionCount,
    recursionLimit,
  )
}

export function sublimeSearch<T extends object>(
  filter: string,
  data: Readonly<T[]>,
  keys: Array<{ key: string, weight: number }>,
): Array<{ score: number, item: T }> {
  const results = data.reduce((accu: Array<{ score: number, item: T }>, item: T) => {
    let values: Array<{ value: string, weight: number }> = []
    keys.forEach(({ key, weight }) => {
      const value = getValue(item, key)

      if (Array.isArray(value)) {
        values = values.concat(value.map((v) => ({ value: v, weight })))
      }
      else if (typeof value === 'string') {
        values.push({
          value,
          weight,
        })
      }
    })

    // for each item, check every key and get maximum score
    const itemMatch = values.reduce(
      (
        accu: null | { matched: boolean, outScore: number },
        { value, weight }: { value: string, weight: number },
      ) => {
        if (!fuzzyMatchSimple(filter, value)) {
          return accu
        }

        const match = fuzzyMatch(filter, value)
        match.outScore *= weight

        const { matched, outScore } = match

        if (!accu && matched) {
          return match
        }

        if (matched && accu && outScore > accu.outScore) {
          return match
        }

        return accu
      },
      null,
    )

    if (itemMatch) {
      accu.push({
        score: itemMatch.outScore,
        item,
      })
    }

    return accu
  }, [])

  results.sort((a, b) => {
    return b.score - a.score
  })

  return results
}

export function searchNodes(searchFilter: string, items: INodeCreateElement[]) {
  const askAiEnabled = useSettingsStore().isAskAiEnabled

  if (!askAiEnabled) {
    items = items.filter((item) => item.key !== AI_TRANSFORM_NODE_TYPE)
  }

  // In order to support the old search we need to remove the 'trigger' part
  const trimmedFilter = searchFilter.toLowerCase().replace('trigger', '').trimEnd()

  const result = (
    sublimeSearch<INodeCreateElement>(trimmedFilter, items, [
      { key: 'properties.displayName', weight: 1.3 },
      { key: 'properties.codex.alias', weight: 1 },
    ]) || []
  ).map(({ item }) => item)

  return result
}

export function sortNodeCreateElements(nodes: INodeCreateElement[]) {
  return nodes.sort((a, b) => {
    if (a.type !== 'node' || b.type !== 'node') {
      return 0
    }

    const displayNameA = a.properties?.displayName?.toLowerCase() || a.key
    const displayNameB = b.properties?.displayName?.toLowerCase() || b.key

    return displayNameA.localeCompare(displayNameB, undefined, { sensitivity: 'base' })
  })
}

const shouldRenderSectionSubtitle = (sections: SectionCreateElement[]) => {
  if (!sections.length) {
    return false
  }

  if (sections.length > 1) {
    return true
  }

  if (sections[0].key === SEND_AND_WAIT_OPERATION) {
    return true
  }

  return false
}

export function groupItemsInSections(
  items: INodeCreateElement[],
  sections: string[] | NodeViewItemSection[],
  sortAlphabetically = true,
): INodeCreateElement[] {
  const filteredSections = sections.filter(
    (section): section is NodeViewItemSection => typeof section === 'object',
  )

  const itemsBySection = (items2: INodeCreateElement[]) =>
    items2.reduce((acc: Record<string, INodeCreateElement[]>, item) => {
      const section = filteredSections.find((s) => s.items.includes(item.key))

      const key = section?.key ?? 'other'

      if (key) {
        acc[key] = [...(acc[key] ?? []), item]
      }

      return acc
    }, {})

  const mapNewSections = (
    newSections: NodeViewItemSection[],
    children: Record<string, INodeCreateElement[]>,
  ) =>
    newSections.map(
      (section): SectionCreateElement => ({
        type: 'section',
        key: section.key,
        title: section.title,
        children: sortAlphabetically
          ? sortNodeCreateElements(children[section.key] ?? [])
          : (children[section.key] ?? []),
      }),
    )

  const nonAINodes = items.filter((item) => !isAINode(item))
  const AINodes = items.filter((item) => isAINode(item))

  const nonAINodesBySection = itemsBySection(nonAINodes)
  const nonAINodesSections = mapNewSections(filteredSections, nonAINodesBySection)

  const AINodesBySection = itemsBySection(AINodes)

  const AINodesSections = mapNewSections(sortBy(filteredSections, ['title']), AINodesBySection)

  const result = [...nonAINodesSections, ...AINodesSections]
    .concat({
      type: 'section',
      key: 'other',
      title: '其他',
      children: sortNodeCreateElements(nonAINodesBySection.other ?? []),
    })
    .filter((section) => section.type !== 'section' || section.children.length > 0)

  result.sort((a, b) => {
    if (a.key.toLowerCase().includes('recommended')) {
      return -1
    }

    if (b.key.toLowerCase().includes('recommended')) {
      return 1
    }

    if (b.key === AI_CATEGORY_OTHER_TOOLS) {
      return -1
    }

    return 0
  })

  if (!shouldRenderSectionSubtitle(result)) {
    return items
  }

  return result
}

export function subcategorizeItems(items: SimplifiedNodeType[]) {
  const WHITE_LISTED_SUBCATEGORIES = [
    CORE_NODES_CATEGORY,
    AI_SUBCATEGORY,
    HUMAN_IN_THE_LOOP_CATEGORY,
  ]

  return items.reduce((acc: SubcategorizedNodeTypes, item) => {
    // Only some subcategories are allowed
    let subcategories: string[] = [DEFAULT_SUBCATEGORY]

    WHITE_LISTED_SUBCATEGORIES.forEach((category) => {
      if (item.codex?.categories?.includes(category)) {
        subcategories = item.codex?.subcategories?.[category] ?? []
      }
    })

    subcategories.forEach((subcategory: string) => {
      if (!acc[subcategory]) {
        acc[subcategory] = []
      }

      acc[subcategory].push(transformNodeType(item, subcategory))
    })

    return acc
  }, {})
}
