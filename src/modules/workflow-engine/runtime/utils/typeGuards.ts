import type { Connection } from '@vue-flow/core'
import type { INodeParameterResourceLocator, INodeTypeDescription, TriggerPanelDefinition } from 'n8n-workflow'
import { nodeConnectionTypes } from 'n8n-workflow/dist/Interfaces.js'

import type { CanvasConnectionMode, NodeConnectionType } from '#wf/constants/canvas'
import { canvasConnectionModes } from '#wf/constants/canvas'

import type { AnyType } from '~/types/common'

export function isValidNodeConnectionType(connectionType: string | undefined): connectionType is NodeConnectionType {
  return nodeConnectionTypes.includes(connectionType as AnyType)
}

export function isValidCanvasConnectionMode(mode: string): mode is CanvasConnectionMode {
  return canvasConnectionModes.includes(mode as CanvasConnectionMode)
}

export function isNotNull<T>(value: T | null): value is T {
  return value !== null
}

export function isString(value: unknown): value is string {
  return typeof value === 'string'
}

export function isVueFlowConnection(connection: object): connection is Connection {
  return (
    'source' in connection
    && 'target' in connection
    && 'sourceHandle' in connection
    && 'targetHandle' in connection
  )
}

export const isObj = (obj: unknown): obj is object =>
  !!obj && Object.getPrototypeOf(obj) === Object.prototype

export function isResourceLocatorValue(value: unknown): value is INodeParameterResourceLocator {
  return Boolean(typeof value === 'object' && value && 'mode' in value && 'value' in value)
}

export function isTriggerPanelObject(
  triggerPanel: INodeTypeDescription['triggerPanel'],
): triggerPanel is TriggerPanelDefinition {
  return triggerPanel !== undefined && typeof triggerPanel === 'object' && triggerPanel !== null
}
