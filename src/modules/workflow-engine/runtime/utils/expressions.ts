import type { Result } from 'n8n-workflow'
import { ExpressionError } from 'n8n-workflow/dist/errors/expression.error.js'
import * as ExpressionParser from 'n8n-workflow/dist/Extensions/ExpressionParser.js'

import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { ResolvableState } from '#wf/types/expressions'

export const isExpression = (expr: unknown) => {
  if (typeof expr !== 'string') {
    return false
  }

  return expr.startsWith('=')
}

export const isEmptyExpression = (expr: string) => {
  return /\{\{\s*\}\}/.test(expr)
}

export const unwrapExpression = (expr: string) => {
  return expr.replace(/\{\{(.*)\}\}/, '$1').trim()
}

export const removeExpressionPrefix = (expr: string | null | undefined) => {
  return expr?.startsWith('=') ? expr.slice(1) : (expr ?? '')
}

export const isTestableExpression = (expr: string) => {
  return ExpressionParser.splitExpression(expr).every((c) => {
    if (c.type === 'text') {
      return true
    }

    return /\$secrets(\.[a-zA-Z0-9_]+)+$/.test(c.text.trim())
  })
}

export const isNoExecDataExpressionError = (error: unknown): error is ExpressionError => {
  return error instanceof ExpressionError && error.context.type === 'no_execution_data'
}

export const isNoNodeExecDataExpressionError = (error: unknown): error is ExpressionError => {
  return error instanceof ExpressionError && error.context.type === 'no_node_execution_data'
}

export const isPairedItemIntermediateNodesError = (error: unknown): error is ExpressionError => {
  return (
    error instanceof ExpressionError && error.context.type === 'paired_item_intermediate_nodes'
  )
}

export const isPairedItemNoConnectionError = (error: unknown): error is ExpressionError => {
  return error instanceof ExpressionError && error.context.type === 'paired_item_no_connection'
}

export const isInvalidPairedItemError = (error: unknown): error is ExpressionError => {
  return error instanceof ExpressionError && error.context.type === 'paired_item_invalid_info'
}

export const isNoPairedItemError = (error: unknown): error is ExpressionError => {
  return error instanceof ExpressionError && error.context.type === 'paired_item_no_info'
}

export const isNoInputConnectionError = (error: unknown): error is ExpressionError => {
  return error instanceof ExpressionError && error.context.type === 'no_input_connection'
}

export const isAnyPairedItemError = (error: unknown): error is ExpressionError => {
  return error instanceof ExpressionError && error.functionality === 'pairedItem'
}

export const getResolvableState = (error: unknown, ignoreError = false): ResolvableState => {
  if (!error) {
    return 'valid'
  }

  if (
    isNoExecDataExpressionError(error)
    || isNoNodeExecDataExpressionError(error)
    || isPairedItemIntermediateNodesError(error)
    || ignoreError
  ) {
    return 'pending'
  }

  return 'invalid'
}

export const getExpressionErrorMessage = (error: Error, nodeHasRunData = false): string => {
  if (isNoExecDataExpressionError(error) || isPairedItemIntermediateNodesError(error)) {
    return '预览请执行先前的节点'
  }

  if (isNoNodeExecDataExpressionError(error)) {
    const nodeCause = error.context.nodeCause as string

    return `执行节点「${nodeCause}」进行预览`
  }

  if (isNoInputConnectionError(error)) {
    return '没有连接的输入'
  }

  if (isPairedItemNoConnectionError(error)) {
    return '没有返回到节点的路径'
  }

  if (isInvalidPairedItemError(error) || isNoPairedItemError(error)) {
    const nodeCause = error.context.nodeCause as string
    const isPinned = !!useWorkflowStore().pinDataByNodeName(nodeCause)

    if (isPinned) {
      return `取消固定节点「${nodeCause}」并执行`
    }
  }

  if (isAnyPairedItemError(error)) {
    return nodeHasRunData
      ? '无法确定要使用的项目'
      : '无法确定要使用的项目 - 执行节点获取更多信息'
  }

  return error.message
}

export const stringifyExpressionResult = (
  result: Result<unknown, Error>,
  nodeHasRunData = false,
): string => {
  if (!result.ok) {
    if (getResolvableState(result.error) !== 'invalid') {
      return ''
    }

    return `[错误：${getExpressionErrorMessage(result.error, nodeHasRunData)}]`
  }

  if (result.result === null) {
    return ''
  }

  if (typeof result.result === 'string' && result.result.length === 0) {
    return '[空]'
  }

  return typeof result.result === 'string' ? result.result : String(result.result)
}
