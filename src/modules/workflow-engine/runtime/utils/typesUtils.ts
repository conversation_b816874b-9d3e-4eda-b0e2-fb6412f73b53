import type { IDataObject } from 'n8n-workflow'
import { jsonParse } from 'n8n-workflow/dist/utils.js'

import { isObject } from '#wf/utils/objectUtils'

export const omit = (keyToOmit: string, { [keyToOmit]: _, ...remainder }) => remainder

export function isJsonKeyObject(item: unknown): item is {
  json: unknown
  [otherKeys: string]: unknown
} {
  if (!isObject(item)) {
    return false
  }

  return Object.keys(item).includes('json')
}

export const isEmpty = (value?: unknown): boolean => {
  if (!value && value !== 0) {
    return true
  }

  if (Array.isArray(value)) {
    return !value.length || value.every(isEmpty)
  }

  if (typeof value === 'object') {
    return !Object.keys(value).length || Object.values(value).every(isEmpty)
  }

  return false
}

export function isPresent<T>(arg: T): arg is Exclude<T, null | undefined> {
  return arg !== null && arg !== undefined
}

export function stringSizeInBytes(input: string | IDataObject | IDataObject[] | undefined): number {
  if (input === undefined) {
    return 0
  }

  return new Blob([typeof input === 'string' ? input : JSON.stringify(input)]).size
}

export function toMegaBytes(bytes: number, decimalPlaces: number = 2): number {
  const megabytes = bytes / 1024 / 1024

  return parseFloat(megabytes.toFixed(decimalPlaces))
}

export function shorten(s: string, limit: number, keep: number) {
  if (s.length <= limit) {
    return s
  }

  const first = s.slice(0, limit - keep)
  const last = s.slice(s.length - keep, s.length)

  return `${first}...${last}`
}

export function clearJsonKey(userInput: string | object) {
  const parsedUserInput = typeof userInput === 'string' ? jsonParse(userInput) : userInput

  if (!Array.isArray(parsedUserInput)) {
    return parsedUserInput
  }

  return parsedUserInput.map((item) => (isJsonKeyObject(item) ? item.json : item))
}
