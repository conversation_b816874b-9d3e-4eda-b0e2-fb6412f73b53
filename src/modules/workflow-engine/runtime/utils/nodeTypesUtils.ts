import type { IDataObject, INodeCredentialDescription, INodeExecutionData, INodeProperties, INodeTypeDescription, NodeParameterValueType, Themed } from 'n8n-workflow'

import { MAIN_AUTH_FIELD_NAME, NON_ACTIVATABLE_TRIGGER_NODE_TYPES } from '#wf/constants/common'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { INodeUi, INodeUpdatePropertiesInformation, IVersionNode, NodeAuthenticationOption, SimplifiedNodeType } from '#wf/types/interface'
import { isResourceLocatorValue } from '#wf/utils/typeGuards'
import { isJsonKeyObject } from '#wf/utils/typesUtils'

import type { ThemeMode } from '~/types/app'

const NODE_KEYWORDS_TO_FILTER = ['Trigger']

const COMMUNITY_PACKAGE_NAME_REGEX = /^(?!@n8n\/)(@\w+\/)?n8n-nodes-(?!base\b)\b\w+/g

export function getTriggerNodeServiceName(nodeType: INodeTypeDescription): string {
  return nodeType.displayName.replace(/ trigger/i, '')
}

export const getThemedValue = <T extends string>(
  value: Themed<T> | undefined,
  theme: ThemeMode = 'light',
): T | null => {
  if (!value) {
    return null
  }

  if (typeof value === 'string') {
    return value
  }

  return value[theme]
}

export const getNodeIcon = (
  nodeType: INodeTypeDescription | SimplifiedNodeType | IVersionNode,
  theme: ThemeMode = 'light',
): string | null => {
  return getThemedValue(nodeType.icon, theme)
}

export const getNodeIconUrl = (
  nodeType: INodeTypeDescription | SimplifiedNodeType | IVersionNode,
  theme: ThemeMode = 'light',
): string | null => {
  return getThemedValue(nodeType.iconUrl, theme)
}

export const getNodeIconColor = (
  nodeType?: INodeTypeDescription | SimplifiedNodeType | IVersionNode | null,
) => {
  if (nodeType && 'iconColor' in nodeType && nodeType.iconColor) {
    return `var(--color-node-icon-${nodeType.iconColor})`
  }

  return nodeType?.defaults?.color?.toString()
}

export function isCommunityPackageName(packageName: string): boolean {
  COMMUNITY_PACKAGE_NAME_REGEX.lastIndex = 0
  // Community packages names start with <@username/>n8n-nodes- not followed by word 'base'
  const nameMatch = COMMUNITY_PACKAGE_NAME_REGEX.exec(packageName)

  return !!nameMatch
}

export function getAppNameFromNodeName(name: string) {
  return name
    .split(' ')
    .filter((word) => !NODE_KEYWORDS_TO_FILTER.includes(word))
    .join(' ')
}

export const hasOnlyListMode = (parameter: INodeProperties): boolean => {
  return (
    parameter.modes !== undefined
    && parameter.modes.length === 1
    && parameter.modes[0].name === 'list'
  )
}

export function isValueExpression(
  parameter: INodeProperties,
  paramValue: NodeParameterValueType,
): boolean {
  if (parameter.noDataExpression === true) {
    return false
  }

  if (typeof paramValue === 'string' && paramValue.charAt(0) === '=') {
    return true
  }

  if (
    isResourceLocatorValue(paramValue)
    && paramValue.value
    && paramValue.value.toString().charAt(0) === '='
  ) {
    return true
  }

  return false
}

export const isNodeFieldMatchingNodeVersion = (
  nodeField: INodeProperties,
  nodeVersion: number | undefined,
) => {
  if (nodeVersion && nodeField.displayOptions?.show?.['@version']) {
    return nodeField.displayOptions.show['@version']?.includes(nodeVersion)
  }

  return true
}

export function getActivatableTriggerNodes(nodes: INodeUi[]) {
  return nodes.filter(
    (node: INodeUi) => !node.disabled && !NON_ACTIVATABLE_TRIGGER_NODE_TYPES.includes(node.type),
  )
}

export const isNodeParameterRequired = (
  nodeType: INodeTypeDescription,
  parameter: INodeProperties,
): boolean => {
  if (!parameter.displayOptions?.show) {
    return true
  }

  // If parameter itself contains 'none'?
  // Walk through dependencies and check if all their values are used in displayOptions
  Object.keys(parameter.displayOptions.show).forEach((name) => {
    const relatedField = nodeType.properties.find((prop) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      prop.name === name
    })

    if (relatedField && !isNodeParameterRequired(nodeType, relatedField)) {
      return false
    }
    else {
      return true
    }
  })

  return true
}

/**
 * Get all node type properties needed for determining whether to show authentication fields
 */
export const getNodeAuthFields = (
  nodeType: INodeTypeDescription | null,
  nodeVersion?: number,
): INodeProperties[] => {
  const authFields: INodeProperties[] = []

  if (nodeType?.credentials && nodeType.credentials.length > 0) {
    nodeType.credentials.forEach((cred) => {
      if (cred.displayOptions?.show) {
        Object.keys(cred.displayOptions.show).forEach((option) => {
          const nodeFieldsForName = nodeType.properties.filter((prop) => prop.name === option)

          if (nodeFieldsForName) {
            nodeFieldsForName.forEach((nodeField) => {
              if (
                !authFields.includes(nodeField)
                && isNodeFieldMatchingNodeVersion(nodeField, nodeVersion)
              ) {
                authFields.push(nodeField)
              }
            })
          }
        })
      }
    })
  }

  return authFields
}

/**
 * A field is considered main auth filed if:
 * 1. It is a credential dependency
 * 2. If all of it's possible values are used in credential's display options
 */
const findAlternativeAuthField = (
  nodeType: INodeTypeDescription,
  fields: INodeProperties[],
): INodeProperties | null => {
  const dependentAuthFieldValues: { [fieldName: string]: string[] } = {}
  nodeType.credentials?.forEach((cred) => {
    if (cred.displayOptions?.show) {
      for (const fieldName in cred.displayOptions.show) {
        dependentAuthFieldValues[fieldName] = (dependentAuthFieldValues[fieldName] || []).concat(
          (cred.displayOptions.show[fieldName] ?? []).map((val) => (val ? val.toString() : '')),
        )
      }
    }
  })
  const alternativeAuthField = fields.find((field) => {
    let required = true
    field.options?.forEach((option) => {
      if (
        'value' in option
        && typeof option.value === 'string'
        && !dependentAuthFieldValues[field.name].includes(option.value)
      ) {
        required = false
      }
    })

    return required
  })

  return alternativeAuthField || null
}

/**
 * Find the main authentication field for the node type.
 * It's the field that node's required credential depend on
 */
export const getMainAuthField = (nodeType: INodeTypeDescription | null): INodeProperties | null => {
  if (!nodeType) {
    return null
  }

  const credentialDependencies = getNodeAuthFields(nodeType)
  const authenticationField = credentialDependencies.find(
    (prop) =>
      prop.name === MAIN_AUTH_FIELD_NAME
      && !prop.options?.find((option) => 'value' in option && option.value === 'none'),
  ) ?? null

  // If there is a field name `authentication`, use it
  // Otherwise, try to find alternative main auth field
  const mainAuthFiled = authenticationField ?? findAlternativeAuthField(nodeType, credentialDependencies)
  // Main authentication field has to be required
  const isFieldRequired = mainAuthFiled ? isNodeParameterRequired(nodeType, mainAuthFiled) : false

  return mainAuthFiled && isFieldRequired ? mainAuthFiled : null
}

export const getNodeCredentialForSelectedAuthType = (
  nodeType: INodeTypeDescription,
  authType: string,
): INodeCredentialDescription | null => {
  const authField = getMainAuthField(nodeType)
  const authFieldName = authField ? authField.name : ''

  return (
    nodeType.credentials?.find(
      (cred) =>
        cred.displayOptions?.show && cred.displayOptions.show[authFieldName]?.includes(authType),
    ) || null
  )
}

/**
 * Gets all authentication types that a given node type supports
 */
export const getNodeAuthOptions = (
  nodeType: INodeTypeDescription | null,
  nodeVersion?: number,
): NodeAuthenticationOption[] => {
  if (!nodeType) {
    return []
  }

  const recommendedSuffix = '（推荐）'
  let options: NodeAuthenticationOption[] = []
  const authProp = getMainAuthField(nodeType)
  // Some nodes have multiple auth fields with same name but different display options so need
  // take them all into account
  const authProps = getNodeAuthFields(nodeType, nodeVersion).filter(
    (prop) => prop.name === authProp?.name,
  )

  authProps.forEach((field) => {
    if (field.options) {
      options = options.concat(
        field.options.map((option) => {
          const optionValue = 'value' in option ? `${option.value}` : ''

          // Check if credential type associated with this auth option has overwritten properties
          let hasOverrides = false
          const cred = getNodeCredentialForSelectedAuthType(nodeType, optionValue)

          if (cred) {
            hasOverrides = useCredentialsStore().getCredentialTypeByName(cred.name)?.__overwrittenProperties
              !== undefined
          }

          return {
            // Add recommended suffix if credentials have overrides and option is not already recommended
            name: hasOverrides && !option.name.endsWith(recommendedSuffix)
              ? `${option.name} ${recommendedSuffix}`
              : option.name,
            value: optionValue,
            // Also add in the display options so we can hide/show the option if necessary
            displayOptions: field.displayOptions,
          }
        }) || [],
      )
    }
  })
  // sort so recommended options are first
  options.forEach((item, i) => {
    if (item.name.includes(recommendedSuffix)) {
      options.splice(i, 1)
      options.unshift(item)
    }
  })

  return options
}

export function executionDataToJson(inputData: INodeExecutionData[]): IDataObject[] {
  return inputData.reduce<IDataObject[]>(
    (acc, item) => (isJsonKeyObject(item) ? acc.concat(item.json) : acc),
    [],
  )
}

export const getAllNodeCredentialForAuthType = (
  nodeType: INodeTypeDescription | null,
  authType: string,
): INodeCredentialDescription[] => {
  if (nodeType) {
    return (
      nodeType.credentials?.filter(
        (cred) => cred.displayOptions?.show && authType in (cred.displayOptions.show || {}),
      ) ?? []
    )
  }

  return []
}

export const getAuthTypeForNodeCredential = (
  nodeType: INodeTypeDescription | null | undefined,
  credentialType: INodeCredentialDescription | null | undefined,
): NodeAuthenticationOption | null => {
  if (nodeType && credentialType) {
    const authField = getMainAuthField(nodeType)
    const authFieldName = authField ? authField.name : ''
    const nodeAuthOptions = getNodeAuthOptions(nodeType)

    return (
      nodeAuthOptions.find(
        (option) =>
          credentialType.displayOptions?.show
          && credentialType.displayOptions?.show[authFieldName]?.includes(option.value),
      ) || null
    )
  }

  return null
}

/**
 * A credential type is considered required if it has no dependencies
 * or if it's only dependency is the main authentication fields
 */
export const isRequiredCredential = (
  nodeType: INodeTypeDescription | null,
  credential: INodeCredentialDescription,
): boolean => {
  if (!credential.displayOptions?.show) {
    return true
  }

  const mainAuthField = getMainAuthField(nodeType)

  if (mainAuthField) {
    return mainAuthField.name in credential.displayOptions.show
  }

  return false
}

export const updateNodeAuthType = (node: INodeUi | null, type: string) => {
  if (!node) {
    return
  }

  const nodeType = useNodeTypesStore().getNodeType(node.type, node.typeVersion)

  if (nodeType) {
    const nodeAuthField = getMainAuthField(nodeType)

    if (nodeAuthField) {
      const updateInformation = {
        name: node.name,
        properties: {
          parameters: {
            ...node.parameters,
            [nodeAuthField.name]: type,
          },
        } as IDataObject,
      } as INodeUpdatePropertiesInformation
      useWorkflowStore().updateNodeProperties(updateInformation)
    }
  }
}
