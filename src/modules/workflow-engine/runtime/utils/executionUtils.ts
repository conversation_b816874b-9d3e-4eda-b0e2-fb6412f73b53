import type {
  ExecutionStatus,
  IDataObject,
  INode,
  IPinData,
  IRunData,
  ITaskData,
} from 'n8n-workflow'
import { SEND_AND_WAIT_OPERATION, TRIMMED_TASK_DATA_CONNECTIONS_KEY } from 'n8n-workflow/dist/Constants.js'

import {
  FORM_NODE_TYPE,
  FORM_TRIGGER_NODE_TYPE,
  LOCAL_FILE_UPLOAD_TRIGGER_NODE_TYPE,
} from '#wf/constants/common'
import { useRootStore } from '#wf/stores/root.store'
import { useWorkflowStore } from '#wf/stores/workflow.store'
import type { ExecutionFilterType, ExecutionsQueryFilter, INodeUi } from '#wf/types/interface'
import { isEmpty } from '#wf/utils/typesUtils'

export function getDefaultExecutionFilters(): ExecutionFilterType {
  return {
    workflowId: 'all',
    status: 'all',
    startDate: '',
    endDate: '',
    tags: [],
    annotationTags: [],
    metadata: [],
    vote: 'all',
  }
}

export const executionFilterToQueryFilter = (
  filter: Partial<ExecutionFilterType>,
): ExecutionsQueryFilter => {
  const queryFilter: IDataObject = {}

  if (filter.workflowId !== 'all') {
    queryFilter.workflowId = filter.workflowId
  }

  if (!isEmpty(filter.tags)) {
    queryFilter.tags = filter.tags
  }

  if (!isEmpty(filter.annotationTags)) {
    queryFilter.annotationTags = filter.annotationTags
  }

  if (filter.vote !== 'all') {
    queryFilter.vote = filter.vote
  }

  if (!isEmpty(filter.metadata)) {
    queryFilter.metadata = filter.metadata
  }

  if (filter.startDate) {
    queryFilter.startedAfter = filter.startDate
  }

  if (filter.endDate) {
    queryFilter.startedBefore = filter.endDate
  }

  switch (filter.status as ExecutionStatus) {
    case 'waiting':
      queryFilter.status = ['waiting']
      break

    case 'error':
      queryFilter.status = ['crashed', 'error']
      break

    case 'success':
      queryFilter.status = ['success']
      break

    case 'running':
      queryFilter.status = ['running', 'new']
      break

    case 'canceled':
      queryFilter.status = ['canceled']
      break

    case 'new':
      queryFilter.status = ['new']
      break
  }

  return queryFilter
}

/** 表单弹出窗口状态标志，用于跟踪表单弹出窗口是否已打开，防止重复打开多个窗口 */
let formPopupWindow = false

/**
 * 打开表单弹出窗口
 *
 * 在工作流执行过程中，当遇到表单触发节点或文件上传触发节点时，
 * 通过该函数在新窗口中打开对应的表单界面。该函数确保同一时间
 * 只会打开一个表单窗口，避免多个重复窗口干扰用户体验。
 *
 * 业务场景：
 * - 工作流中需要用户填写表单数据时
 * - 需要用户上传文件继续工作流执行时
 * - 工作流执行需要暂停等待用户输入后继续时
 */
export function openFormPopupWindow(url: string) {
  if (!formPopupWindow) {
    const height = 700
    const width = window.innerHeight - 50
    const left = (window.innerWidth - height) / 2
    const top = 50

    const features = `width=${height},height=${width},left=${left},top=${top},resizable=yes,scrollbars=yes`
    const windowName = `form-waiting-since-${Date.now()}`

    window.open(url, windowName, features)

    formPopupWindow = true
  }
}

/**
 * 清除表单弹出窗口状态
 *
 * 重置表单弹出窗口状态标志，允许系统在需要时再次打开新的表单窗口。
 * 通常在关闭表单窗口或需要强制刷新状态时调用此函数。
 */
export function clearPopupWindowState() {
  formPopupWindow = false
}

interface DisplayFormParams {
  /** 工作流中的所有节点 */
  nodes: INode[]
  /** 运行时数据，记录节点执行状态 */
  runData: IRunData | undefined
  /** 固定数据，用于确定节点是否需要执行 */
  pinData: IPinData
  /** 目标节点名称 */
  destinationNode: string | undefined
  /** 触发节点名称 */
  triggerNode: string | undefined
  /** 直接父节点列表 */
  directParentNodes: string[]
  /** 触发来源 */
  source: string | undefined
  /** 获取测试 URL 的函数 */
  getTestUrl: (node: INode) => string
}

/**
 * 显示工作流表单并处理表单触发节点
 *
 * 该函数用于在工作流执行过程中，检测并处理表单类型的触发节点。当工作流中包含表单触发节点
 * 或文件上传触发节点时，该函数会自动打开一个新窗口来显示对应的表单界面，使用户能够通过
 * 填写表单或上传文件的方式与工作流进行交互。
 *
 * 业务场景：
 * - 工作流执行到需要用户输入的步骤时（如数据收集、审批流程等）
 * - 需要用户上传文件进行后续处理的场景
 * - 工作流执行需要暂停等待用户操作后继续的情况
 */
export function displayForm({
  nodes,
  runData,
  pinData,
  destinationNode,
  triggerNode,
  directParentNodes,
  source,
  getTestUrl,
}: DisplayFormParams) {
  // 遍历所有节点，筛选出符合条件的表单触发节点
  for (const node of nodes) {
    // 如果指定了触发节点但当前节点不是该触发节点，则跳过
    if (triggerNode !== undefined && triggerNode !== node.name) {
      continue
    }

    // 检查节点是否已经执行过
    const hasNodeRun = runData && Object.hasOwn(runData, node.name)

    // 如果节点已执行或存在固定数据，则跳过
    if (hasNodeRun || pinData[node.name]) {
      continue
    }

    // 检查节点类型是否为表单触发节点或文件上传触发节点
    if (![FORM_TRIGGER_NODE_TYPE, LOCAL_FILE_UPLOAD_TRIGGER_NODE_TYPE].includes(node.type)) {
      continue
    }

    // 根据目标节点和父节点关系确定是否需要显示表单
    if (destinationNode && destinationNode !== node.name && !directParentNodes.includes(node.name)) {
      continue
    }

    // 如果节点是目标节点或未被禁用，则获取测试URL并可能打开表单
    if (node.name === destinationNode || !node.disabled) {
      let testUrl = ''

      // 获取测试URL
      if (node.type === FORM_TRIGGER_NODE_TYPE || node.type === LOCAL_FILE_UPLOAD_TRIGGER_NODE_TYPE) {
        testUrl = getTestUrl(node)
      }

      // 在弹出窗口中打开表单（排除手动聊天消息的情况）
      if (testUrl && source !== 'RunData.ManualChatMessage') {
        clearPopupWindowState()
        openFormPopupWindow(testUrl)
      }
    }
  }
}

export const waitingNodeTooltip = (node: INodeUi | null | undefined) => {
  if (!node) {
    return ''
  }

  try {
    const resume = node?.parameters?.resume

    if (resume) {
      if (!['webhook', 'form'].includes(resume as string)) {
        return '当等待时间结束时，执行将继续'
      }

      const { webhookSuffix } = (node.parameters.options ?? {}) as { webhookSuffix: string }
      const suffix = webhookSuffix && typeof webhookSuffix !== 'object' ? `/${webhookSuffix}` : ''

      let message = ''
      let resumeUrl = ''

      if (resume === 'form') {
        resumeUrl = `${useRootStore().formWaitingUrl}/${useWorkflowStore().activeExecutionId}${suffix}`
        message = `当在 ${`<a href="${resumeUrl}" target="_blank">${resumeUrl}</a>`} 提交表单时，执行将继续`
      }

      if (resume === 'webhook') {
        resumeUrl = `${useRootStore().webhookWaitingUrl}/${useWorkflowStore().activeExecutionId}${suffix}`
        message = `当在 ${`<a href="${resumeUrl}" target="_blank">${resumeUrl}</a>`} 收到 Webhook 时，执行将继续`
      }

      if (message && resumeUrl) {
        return message
      }
    }

    if (node?.type === FORM_NODE_TYPE) {
      const resumeUrl = `${useRootStore().formWaitingUrl}/${useWorkflowStore().activeExecutionId}`

      return `当在 ${`<a href="${resumeUrl}" target="_blank">${resumeUrl}</a>`} 提交表单时，执行将继续`
    }

    if (node?.parameters.operation === SEND_AND_WAIT_OPERATION) {
      return '当用户响应后，执行将继续'
    }
  }
  catch {
    // do not throw error if could not compose tooltip
  }

  return ''
}

/**
 * Check whether task data contains a trimmed item.
 *
 * In manual executions in scaling mode, the payload in push messages may be
 * arbitrarily large. To protect Redis as it relays run data from workers to
 * main process, we set a limit on payload size. If the payload is oversize,
 * we replace it with a placeholder, which is later overridden on execution
 * finish, when the client receives the full data.
 */
export function hasTrimmedItem(taskData: ITaskData[]) {
  return taskData[0]?.data?.main?.[0]?.[0]?.json?.[TRIMMED_TASK_DATA_CONNECTIONS_KEY] ?? false
}

/**
 * Check whether run data contains any trimmed items.
 *
 * See {@link hasTrimmedItem} for more details.
 */
export function hasTrimmedData(runData: IRunData) {
  return Object.keys(runData).some((nodeName) => hasTrimmedItem(runData[nodeName]))
}
