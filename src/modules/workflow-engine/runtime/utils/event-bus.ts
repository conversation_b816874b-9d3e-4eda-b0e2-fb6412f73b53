import type { CallbackFn, EventBus, Payloads } from '#wf/types/event-bus'

/**
 * Creates an event bus with the given listener map.
 *
 * @example
 * ```ts
 * const eventBus = createEventBus<{
 *   'user-logged-in': { username: string };
 *   'user-logged-out': never;
 * }>();
 */
export function createEventBus<
  // TODO: Fix all usages of `createEventBus` and convert `any` to `unknown`
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ListenerMap extends Payloads<ListenerMap> = Record<string, any>,
>(): EventBus<ListenerMap> {
  const handlers = new Map<string, CallbackFn[]>()

  return {
    on(eventName, fn) {
      let eventFns = handlers.get(eventName)

      if (!eventFns) {
        eventFns = [fn]
      }
      else {
        eventFns.push(fn)
      }

      handlers.set(eventName, eventFns)
    },

    once(eventName, fn) {
      const handler: typeof fn = (payload) => {
        this.off(eventName, handler)
        fn(payload)
      }

      this.on(eventName, handler)
    },

    off(eventName, fn) {
      const eventFns = handlers.get(eventName)

      if (eventFns) {
        eventFns.splice(eventFns.indexOf(fn) >>> 0, 1)
      }
    },

    emit(eventName, event) {
      const eventFns = handlers.get(eventName)

      if (eventFns) {
        eventFns.slice().forEach((handler) => handler(event))
      }
    },
  }
}
