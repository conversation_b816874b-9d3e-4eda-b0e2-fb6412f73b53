import { addPlugin, addRouteMiddleware, createResolver, defineNuxtModule } from '@nuxt/kit'

export interface WorkflowEngineModuleOptions {
  defaultTheme?: 'light' | 'dark'
  autoSaveInterval?: number
}

export default defineNuxtModule<WorkflowEngineModuleOptions>({
  meta: {
    name: 'workflow-engine',
    configKey: 'workflowEngine',
    compatibility: {
      nuxt: '^3.0.0',
    },
  },

  defaults: {
    defaultTheme: 'light',
    autoSaveInterval: 30,
  },

  setup(options, nuxt) {
    const resolver = createResolver(import.meta.url)

    // 注册 FontAwesome 插件
    addPlugin(resolver.resolve('./runtime/plugins/fontawesome'))

    // 添加 FontAwesome CSS
    nuxt.options.css.push('@fortawesome/fontawesome-svg-core/styles.css')

    addRouteMiddleware({
      name: 'init-settings',
      path: resolver.resolve('./runtime/middleware/init-settings'),
      global: true,
    })

    // 暴露模块配置给运行时
    nuxt.options.runtimeConfig.public.workflowEngine = {
      defaultTheme: options.defaultTheme ?? 'light',
      autoSaveInterval: options.autoSaveInterval ?? 30,
    }
  },
})
