<script setup lang="ts">
import type { TabProps } from 'primevue'

import { RouteKey } from '~/enums/route'

definePageMeta({
  title: '项目详情',
  layoutConfig: {
    customContent: true,
    noContainerPadding: true,
  },
})

interface TabItem extends TabProps {
  label: string
  icon: Component | null
}

const tabs: TabItem[] = [
  {
    label: '项目详情',
    value: RouteKey.TW_项目详情,
    icon: getRouteIcon(RouteKey.TW_项目详情),
  },
  {
    label: '客户评价',
    value: RouteKey.TW_项目评价,
    icon: getRouteIcon(RouteKey.TW_项目评价),
  },
]

const activeTab = ref<RouteKey>(RouteKey.TW_项目详情)
</script>

<template>
  <LayoutAdminContentContainer>
    <div class="pb-admin-layout">
      <Tabs
        v-model:value="activeTab"
        :dt="{
          tab: {
            padding: '0.5rem 1rem',
          },
        }"
      >
        <TabList
          :pt="{
            tabList: {
              class: '!bg-transparent',
            },
          }"
        >
          <Tab
            v-for="tab of tabs"
            :key="tab.value"
            :value="tab.value"
          >
            <span class="gap-2 flex-center">
              <Component
                :is="tab.icon"
                :size="18"
              />
              {{ tab.label }}
            </span>
          </Tab>
        </TabList>
      </Tabs>
    </div>

    <ProjectSectionDetail v-if="activeTab === RouteKey.TW_项目详情" />
    <ProjectSectionFeedback v-else-if="activeTab === RouteKey.TW_项目评价" />
  </LayoutAdminContentContainer>
</template>
