<script setup lang="ts">
import { UserIcon } from 'lucide-vue-next'
import { Button, Tag } from 'primevue'
import { object } from 'valibot'

import NumericDisplay from '~/components/NumericDisplay.vue'
import PopupMenu from '~/components/PopupMenu.vue'
import ProBtnMore from '~/components/pro/btn/ProBtnMore.vue'
import TextLink from '~/components/TextLink.vue'
import TimeDisplay from '~/components/TimeDisplay.vue'
import UserSelectorChip from '~/components/user/selector/UserSelectorChip.vue'
import { ProjectStatusConfig } from '~/constants-tw/project'
import { queryKeys } from '~/constants-tw/query-key'
import { DateFormat } from '~/enums/common'
import { ProValueType } from '~/enums/pro'
import { RouteKey } from '~/enums/route'
import { ProjectService } from '~/services-tw/project'
import type { ProjectListItem } from '~/types-tw/project'

const { tableRef, tableAction } = useTableAction()

const { $openFlowSelectionDialog, $openFlowDetailDialog } = useNuxtApp()

const {
  loading,
  isFormCreate,
  isFormUpdate,
  modalVisible,
  formValues,
  updatingItem: updateRole,
  formResolver,
  handleClose,
  handleModalVisibleChange,
  handleSubmit,
  openCreateModal,
  formTitleText,
  confirmBtnLabel,
} = useFormControl<RoleFormValues, RoleListItem>({
  resolver: object({
    name: schemaNotEmptyString('请输入项目名称'),
  }),
  btnLabel: {
    create: '确认新增',
    update: '保存更改',
  },
  formTitle: {
    create: '新增项目',
    update: '编辑项目',
  },
  fetchDetail: async (updatingRole) => {
    const roleDetail = await UserService.getRole(updatingRole.id)

    return roleDetail
  },
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const payload = {
        name: states.name.value,
        menu_list: formValues.value.menu_list,
      }

      if (isFormCreate.value) {
        await UserService.createRole(payload)
      }
      else {
        if (isFormUpdate.value) {
          const roleId = updateRole.value?.id

          if (roleId) {
            await UserService.updateRole(roleId, payload)
          }
        }
      }
    }
  },
  onSubmitSuccess: () => {
    tableAction.value?.reload()
  },
})

const columns = computed<ProTableColumn<ProjectListItem>[]>(() => [
  {
    field: 'number',
    header: '项目编号',
    render: (data) => {
      return h('div', {
        class: 'text-sm',
      }, data.number)
    },
  },
  {
    field: 'name',
    header: '项目名称',
    render: (data) => {
      return h('div', {
        class: 'max-w-[250px] line-clamp-2 text-wrap text-sm',
        title: data.name,
      }, data.name)
    },
  },
  {
    field: 'leader_object.user',
    header: '负责人',
    render: (data) => {
      if (data.leader_object) {
        return h(UserSelectorChip, {
          value: {
            id: data.leader_object.user.id,
            username: data.leader_object.user.name,
            avatar: data.leader_object.user.avatar,
          },
        })
      }

      return '-'
    },
  },
  {
    field: 'sale_object.user',
    header: '销售人',
    render: (data) => {
      if (data.sale_object) {
        return h(UserSelectorChip, {
          value: {
            id: data.sale_object.user.id,
            username: data.sale_object.user.name,
            avatar: data.sale_object.user.avatar,
          },
        })
      }

      return '-'
    },
  },
  {
    field: 'progress',
    header: '项目进度',
    render: (data) => {
      const progress = data.statistics?.task?.count
        ? Math.round(((data.statistics.task.close ?? 0) / data.statistics.task.count) * 100)
        : 0

      return h('div', [
        h(Tag, {
          severity: ProjectStatusConfig[data.status].severity,
          value: `${ProjectStatusConfig[data.status].label}：${progress}%`,
        }),
      ])
    },
  },
  {
    field: 'statistics.task',
    header: '关联任务',
    render: (data) => {
      return h(NumericDisplay, {
        value: `${data.statistics?.task?.close ?? 0} / ${data.statistics?.task?.count ?? 0}`,
      })
    },
  },
  {
    field: 'statistics.team',
    header: '项目成员',
    render: (data) => {
      return h('div', {
        class: 'text-sm flex-center gap-1',
      }, [h(UserIcon, {
        size: 14,
      }), data.statistics?.team ?? 0, ' 人'])
    },
  },
  {
    field: 'start_time',
    header: '截止日期',
    render: (data) => {
      return h(TimeDisplay, {
        format: DateFormat.YYYY_MM_DD,
        time: data.start_date.at(1),
      })
    },
  },
  {
    field: 'flow_detail',
    header: '关联工作流',
    render: (data) => {
      const flowDetail = data.flow_detail

      if (flowDetail) {
        return h(TextLink, {
          class: 'text-sm',
          onClick: () => {
            if (flowDetail) {
              $openFlowDetailDialog(flowDetail)
            }
          },
        }, () => flowDetail.name)
      }

      return '-'
    },
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', { class: 'table-action-group' }, [
      h(PopupMenu, {
        options: [
          {
            label: '关联工作流',
            command: () => {
              $openFlowSelectionDialog({ id: data.id }, {
                initialSelection: data.flow_detail?.id ? [data.flow_detail.id] : undefined,
                onDone: () => {
                  tableAction.value?.reload()
                },
              })
            },
          },
          {
            label: '详情',
            command: () => {
              navigateTo(getRoutePath(RouteKey.TW_项目详情, {
                projectId: data.id,
              }))
            },
          },
        ],
      }, {
        default: () => h(ProBtnMore, {
          onlyIcon: true,
        }),
      }),
    ]),
  },
])
</script>

<template>
  <div>
    <ProTable
      :ref="tableRef"
      :columns="columns"
      :queryKey="queryKeys.project.list()"
      :requestFn="ProjectService.getProjects"
      :toolbarConfig="{
        search: {
          key: 'name',
          placeholder: '搜索项目名称',
        },
      }"
    >
      <template #toolbarRight>
        <div class="gap-2 py-1 flex-center">
          <div class="ml-auto gap-4 flex-center">
            <Button
              v-show="false"
              label="新增项目"
              @click="openCreateModal()"
            />
          </div>
        </div>
      </template>
    </ProTable>

    <ProDialogForm
      :formActionGroupProps="{
        confirmButtonProps: {
          label: confirmBtnLabel,
        },
      }"
      :initialValues="formValues"
      :loading="loading"
      :resolver="formResolver"
      :title="formTitleText"
      :visible="modalVisible"
      @cancel="handleClose"
      @submit="handleSubmit"
      @update:visible="handleModalVisibleChange"
    >
      <FormItem
        v-slot="{ id }"
        label="项目名称"
        name="name"
        required
      >
        <InputText
          :id="id"
          v-model="formValues.name"
          placeholder="例如：项目1"
        />
      </FormItem>
    </ProDialogForm>
  </div>
</template>
