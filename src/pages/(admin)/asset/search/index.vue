<script setup lang="ts">
import { useRouter } from 'vue-router'

import { RouteKey } from '~/enums/route'

definePageMeta({
  title: '资产搜索',
  description: '搜索和查看资产信息',
  layoutConfig: {
    showHeader: false,
  },
})

const router = useRouter()

const handleSearch = (v: string) => {
  if (v) {
    const searchKey = window.encodeURIComponent(v)

    router.push({
      path: getRoutePath(RouteKey.资产搜索结果, { searchKey }),
    })
  }
}
</script>

<template>
  <div class="flex h-full flex-col">
    <div class="flex flex-1 items-center justify-center p-admin-layout-content">
      <AssetSearchBox @triggerSearch="handleSearch" />
    </div>
  </div>
</template>
