<script setup lang="ts">
import { nullable, object, optional, safeParse, string } from 'valibot'

const route = useRoute()

const searchValue = ref<string>()

onMounted(() => {
  // 从路由参数中获取搜索关键字
  const searchKey = useRouteParam({
    name: 'searchKey',
    schema: string(),
  })

  if (searchKey.value) {
    searchValue.value = window.decodeURIComponent(searchKey.value)
  }

  // 同时保持对 query 参数的支持，会覆盖路由参数
  const parsed = safeParse(object({
    key: optional(nullable(string())),
  }), route.query)

  if (parsed.success) {
    const key = parsed.output.key

    if (key) {
      searchValue.value = key
    }
  }
})

const handleSearchValueChange = (v: string) => {
  searchValue.value = v
}
</script>

<template>
  <AssetSearchResult
    :searchValue="searchValue"
    @searchValueChange="handleSearchValueChange"
  />
</template>
