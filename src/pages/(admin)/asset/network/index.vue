<script setup lang="ts">
const store = useAssetInfoStore()
const { activeNetworkId } = storeToRefs(store)
</script>

<template>
  <ProSplitter
    :left="{ size: 40, minSize: 20 }"
    :right="{ minSize: 50 }"
  >
    <template #left>
      <AssetNetworkTree />
    </template>

    <template #right>
      <AssetNetworkDevice
        v-if="!!activeNetworkId"
        :networkId="activeNetworkId"
      />
    </template>
  </ProSplitter>
</template>
