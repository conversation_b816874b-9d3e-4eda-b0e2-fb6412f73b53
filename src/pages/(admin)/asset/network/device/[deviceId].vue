<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { EllipsisIcon, TextSearchIcon } from 'lucide-vue-next'
import { number } from 'valibot'

import { queryKeys } from '~/enums/query-key'

definePageMeta({
  title: '网络设备',
  layoutConfig: {
    showHeader: false,
  },
})

const deviceId = useRouteParam({
  name: 'deviceId',
  schema: number(),
})

const { data: deviceDetail } = useQuery({
  queryKey: queryKeys.asset.networkDevice.detail(deviceId),
  queryFn: () => {
    if (deviceId.value) {
      return AssetService.getNetDeviceDetail(deviceId.value)
    }

    return null
  },
})

const store = useAssetInfoStore()
const { activeNetworkDevice } = storeToRefs(store)

// 当获取到设备详情数据时，更新当前选中的网络设备
watchEffect(() => {
  activeNetworkDevice.value = deviceDetail.value || undefined
})

const deviceDetailVisible = ref(false)
const taskListVisible = ref(false)

const moreOptions = ref<PrimeVueMenuItem[]>([
  {
    label: '设备信息',
    command: () => {
      deviceDetailVisible.value = true
    },
  },
])
</script>

<template>
  <div class="flex h-full flex-col">
    <HackInjectComponent />

    <!-- 自定义设备详情页的头部 -->
    <LayoutAdminContentHeader
      class="!px-0"
      showBack
      :title="`网络设备 - ${activeNetworkDevice?.asset?.name || ''}`"
    >
      <template #titleRight>
        <div class="flex-wrap gap-2 flex-center">
          <AssetShareCollectionButton />

          <Button
            label="扫描任务"
            severity="secondary"
            size="small"
            variant="outlined"
            @click="taskListVisible = true"
          >
            <template #icon>
              <TextSearchIcon :size="16" />
            </template>
          </Button>

          <PopupMenu :options="moreOptions">
            <Button
              severity="secondary"
              size="small"
              variant="outlined"
            >
              <template #icon>
                <EllipsisIcon :size="16" />
              </template>
            </Button>
          </PopupMenu>
        </div>
      </template>
    </LayoutAdminContentHeader>

    <div class="flex min-h-0 flex-1">
      <AssetDetail />
    </div>

    <Dialog
      v-model:visible="taskListVisible"
      class="dialog-full"
      dismissableMask
      :draggable="false"
      header="扫描任务"
      maximizable
      modal
    >
      <TaskList :networkDeviceDetail="deviceDetail || undefined" />
    </Dialog>

    <Dialog
      v-model:visible="deviceDetailVisible"
      class="dialog-full"
      dismissableMask
      :draggable="false"
      header="设备信息"
      maximizable
      modal
    >
      <AssetNetworkDeviceInfo />
    </Dialog>
  </div>
</template>
