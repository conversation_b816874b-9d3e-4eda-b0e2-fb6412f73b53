<script setup lang="ts">
const enum TabType {
  Strategy,
  Compliance,
}

const tabs = [
  {
    label: '基线模板',
    value: TabType.Compliance,
  },
  {
    label: '系统策略',
    value: TabType.Strategy,
  },
]

const selectedTab = ref(TabType.Compliance)

const selectedBaselinePolicy = ref<BaselineSystem>()
const selectedBaselineTemplate = ref<BaselineTplListItem>()
</script>

<template>
  <Tabs
    v-model:value="selectedTab"
    class="h-full"
  >
    <TabList>
      <Tab
        v-for="tab of tabs"
        :key="tab.value"
        :value="tab.value"
      >
        {{ tab.label }}
      </Tab>
    </TabList>

    <TabPanels class="h-full !px-0">
      <TabPanel
        v-for="tab of tabs"
        :key="tab.value"
        class="h-full"
        :value="tab.value"
      >
        <ProSplitter v-if="selectedTab === TabType.Strategy">
          <template #left>
            <BaselinePolicyList
              v-model:selectItem="selectedBaselinePolicy"
            />
          </template>

          <template #right>
            <BaselinePolicyOptions
              v-if="selectedBaselinePolicy"
              :baselinePolicy="selectedBaselinePolicy"
            />
          </template>
        </ProSplitter>

        <ProSplitter v-if="selectedTab === TabType.Compliance">
          <template #left>
            <BaselineTplTree
              v-model:selectItem="selectedBaselineTemplate"
            />
          </template>

          <template #right>
            <BaselineTplOptions
              v-if="selectedBaselineTemplate"
              :baselineTemplate="selectedBaselineTemplate"
            />
          </template>
        </ProSplitter>
      </TabPanel>
    </TabPanels>
  </Tabs>
</template>
