<script setup lang="ts">
import { assetConfig, AssetType } from '~/enums/asset'

const assetList = Object.values(assetConfig)

const selectedAssetType = ref(AssetType.NetworkDevice)

const selectedAssetTemplate = ref<AssetTemplateListItem['id']>()
</script>

<template>
  <Tabs
    v-model:value="selectedAssetType"
    class="h-full"
  >
    <TabList>
      <Tab
        v-for="asset of assetList"
        :key="asset.value"
        :value="asset.value"
      >
        {{ asset.label }}
      </Tab>
    </TabList>

    <TabPanels class="h-full !px-0">
      <TabPanel
        v-for="asset of assetList"
        :key="asset.value"
        class="h-full"
        :value="asset.value"
      >
        <ProSplitter v-if="asset.value === selectedAssetType">
          <template #left>
            <AssetTplTree
              v-model:selectItem="selectedAssetTemplate"
              :assetType="asset.value"
            />
          </template>

          <template #right>
            <AssetTplFieldList
              v-if="selectedAssetTemplate"
              :assetTemplateId="selectedAssetTemplate"
            />
          </template>
        </ProSplitter>
      </TabPanel>
    </TabPanels>
  </Tabs>
</template>
