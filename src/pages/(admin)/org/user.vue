<script setup lang="ts">
import { MailIcon } from 'lucide-vue-next'
import { nullable, number, object, undefinedable } from 'valibot'

import NumericDisplay from '~/components/NumericDisplay.vue'
import PopupMenu from '~/components/PopupMenu.vue'
import ProBtnMore from '~/components/pro/btn/ProBtnMore.vue'
import UserAvatar from '~/components/user/UserAvatar.vue'
import UserRoleSelectX from '~/components/user/UserRoleSelectX.vue'
import { queryKeys } from '~/constants-tw/query-key'
import { ProValueType } from '~/enums/pro'
import { UserService } from '~/services-tw/org'
import type { TW_Role, TW_User, TW_UserListItem } from '~/types-tw/user'

definePageMeta({
  description: '管理组织架构、用户信息及权限分配，支持部门管理和用户状态维护',
})

const { tableRef, tableAction } = useTableAction()

const { $toast } = useNuxtApp()

const store = useAdminDepartmentStore()
const { activeDepartment, activeDepartmentId } = storeToRefs(store)

const activeUserId = ref<TW_User['id']>()

// 表单数据类型定义
interface UserFormValues {
  username: string
  email: string
  mobile?: string
  role_ids?: TW_Role['id'][]
  password?: string
}

// 表单控制逻辑
const {
  loading,
  isFormCreate,
  formTitleText,
  formValues,
  modalVisible,
  formResolver,
  openCreateModal,
  handleClose,
  handleSubmit,
  confirmBtnLabel,
  handleModalVisibleChange,
} = useFormControl<UserFormValues, TW_UserListItem>({
  resolver: object({
    email: schemaNotEmptyString('请输入邮箱'),
    username: schemaNotEmptyString('请输入用户名称'),
    mobile: nullable(undefinedable(schemaMobile)),
    role_ids: nullable(undefinedable(number('请选择角色'))),
    password: schemaNotEmptyString('请输入密码'),
  }),
  btnLabel: {
    create: '新增用户',
    update: '保存更改',
  },
  formTitle: {
    create: '新增用户',
    update: '编辑用户',
  },
  onSubmit: async ({ valid, states }) => {
    if (valid && activeDepartmentId.value) {
      const payload: UserFormValues = {
        username: states.username.value,
        email: states.email.value,
        mobile: states.mobile?.value,
        role_ids: states.role_ids?.value,
      }

      if (isFormCreate.value) {
        // TODO: 实际开发时需要实现 createUserInDepartment 接口
        // 暂时使用模拟调用
        console.log('创建用户请求:', {
          ...payload,
          password: states.password?.value,
          dept_id: activeDepartmentId.value,
        })

        // 模拟异步操作
        await new Promise((resolve) => setTimeout(resolve, 1000))
      }
    }
  },
  onSubmitSuccess: () => {
    tableAction.value?.reload()
    $toast.success({
      summary: '用户创建成功',
      detail: '新用户已成功添加到部门',
    })
  },
})

const columns = computed<ProTableColumn<TW_UserListItem>[]>(() => [
  {
    field: 'username',
    header: '成员',
    valueType: ProValueType.Text,
    render: (data) => h('span', { class: 'gap-2 whitespace-nowrap flex-center' }, [
      h(UserAvatar, {
        avatar: data.avatar,
        size: 'large',
        username: data.name,
        class: 'overflow-hidden',
      }),
      h('span', { class: 'flex flex-col' }, [
        h('span', { class: 'font-medium' }, data.name),
        h('span', { class: 'text-secondary text-sm' }, data.appointment),
      ]),
    ]),
  },
  {
    field: 'email',
    header: '邮箱',
    valueType: ProValueType.Text,
  },
  {
    field: 'mobile',
    header: '手机号',
    valueType: ProValueType.Text,
    render: (data) => h(NumericDisplay, {
      class: 'text-secondary text-sm',
      value: data.phone ? maskMobile(data.phone) : '-',
    }),
  },
  {
    field: 'role',
    header: '角色',
    valueType: ProValueType.Select,
    hideInFilter: true,
    render: (data) => {
      return h(UserRoleSelectX, {
        size: 'small',
        modelValue: data.roles?.map((r) => r.id),
        'onUpdate:modelValue': (selectedRoleIds?: TW_Role['id'][]) => {
          UserService.updateUser(data.id, {
            role_ids: selectedRoleIds,
          }).then(() => {
            $toast.success({
              summary: '角色更新成功',
              detail: `${data.name}的角色已更新`,
            })
          })
        },
      })
    },
  },
  {
    field: 'created_at',
    header: '创建时间',
    valueType: ProValueType.Date,
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', { class: 'table-action-group' }, [
      h(PopupMenu, {
        options: [
          {
            label: '详情',
            command: () => {
              activeUserId.value = data.id
            },
          },
        ],
      }, {
        default: () => h(ProBtnMore, {
          onlyIcon: true,
        }),
      }),
    ]),
  },
])
</script>

<template>
  <div class="flex h-full">
    <ProSplitter leftPanelContentClass="!p-0">
      <template #left>
        <DepartmentBlock />
      </template>

      <template #right>
        <div class="flex-1">
          <ProTable
            v-if="activeDepartmentId"
            :ref="tableRef"
            :columns="columns"
            :pagination="{
              show: false,
            }"
            :queryKey="queryKeys.user.list()"
            :queryParams="{
              dept_id: activeDepartmentId,
            }"
            :requestFn="async (queryParams) => {
              const res = await UserService.getUsers(queryParams)

              return {
                total: res.length,
                page: 1,
                page_sum: 1,
                list: res,
              }
            }"
            :toolbarConfig="{
              search: {
                key: 'username',
                placeholder: '搜索用户名称',
              },
            }"
          >
            <template
              v-if="activeDepartment"
              #headerLeft
            >
              <div class="text-lg font-semibold">
                {{ activeDepartment.name }}
              </div>
            </template>

            <template #toolbarRight>
              <div class="gap-2 py-1 flex-center">
                <div class="ml-auto gap-4 flex-center">
                  <Button
                    label="新增用户"
                    @click="openCreateModal()"
                  />
                </div>
              </div>
            </template>
          </ProTable>

          <LoadingSkeleton
            v-else
            :columns="5"
            :rows="5"
          />
        </div>
      </template>
    </ProSplitter>

    <Dialog
      class="dialog-half"
      dismissableMask
      :draggable="false"
      header="用户信息"
      modal
      :visible="!!activeUserId"
      @update:visible="activeUserId = undefined"
    >
      <OrgUserDetail :userId="activeUserId" />
    </Dialog>

    <!-- 新增用户表单弹窗 -->
    <ProDialogForm
      :formActionGroupProps="{
        confirmButtonProps: {
          label: confirmBtnLabel,
        },
      }"
      :initialValues="formValues"
      :loading="loading"
      :resolver="formResolver"
      :title="formTitleText"
      :visible="modalVisible"
      @cancel="handleClose"
      @submit="handleSubmit"
      @update:visible="handleModalVisibleChange"
    >
      <FormItem
        v-slot="{ id }"
        label="用户名称"
        name="username"
        required
      >
        <InputText
          :id="id"
          v-model="formValues.username"
          v-keyfilter="/[^s]/"
          autocomplete="off"
          placeholder="用于在页面中展示，可以随时更改"
        />
      </FormItem>

      <FormItem
        v-slot="{ id }"
        label="角色"
        name="role_ids"
      >
        <UserRoleSelectX
          v-model="formValues.role_ids"
          :labelId="id"
          placeholder="选择角色"
          single
        />
      </FormItem>

      <FormItem
        v-slot="{ id }"
        label="Email"
        name="email"
        required
      >
        <IconField>
          <InputIcon class="size-[1.12rem] inline-flex-center">
            <MailIcon :size="16" />
          </InputIcon>
          <InputText
            :id="id"
            v-model="formValues.email"
            autocomplete="off"
            fluid
          />
        </IconField>
      </FormItem>

      <FormItem
        v-slot="{ id }"
        label="手机号"
        name="mobile"
      >
        <InputText
          :id="id"
          v-model="formValues.mobile"
          v-keyfilter.int
          autocomplete="off"
          placeholder="用户的手机号码，用于登录"
        />
      </FormItem>

      <FormItem
        v-if="isFormCreate"
        label="登录密码"
        name="password"
        required
      >
        <template #default="{ id }">
          <Password
            v-model="formValues.password"
            :feedback="false"
            fluid
            :inputId="id"
            toggleMask
          />
        </template>

        <template #helpTip>
          登录密码至少 8 位，包含字母和数字
        </template>
      </FormItem>
    </ProDialogForm>
  </div>
</template>
