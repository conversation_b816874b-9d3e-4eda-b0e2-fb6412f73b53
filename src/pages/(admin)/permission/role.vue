<script setup lang="ts">
import { object } from 'valibot'

import ProBtnDelete from '~/components/pro/btn/ProBtnDelete.vue'
import ProBtnEdit from '~/components/pro/btn/ProBtnEdit.vue'
import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'

definePageMeta({
  description: '管理系统角色及其权限分配，支持灵活配置不同岗位的访问范围和操作权限',
})

const { $confirm, $toast } = useNuxtApp()

const { tableRef, tableAction } = useTableAction()

const {
  loading,
  isFormCreate,
  isFormUpdate,
  modalVisible,
  formValues,
  updatingItem: updateRole,
  formResolver,
  handleClose,
  handleModalVisibleChange,
  handleSubmit,
  openCreateModal,
  openUpdateModal,
  formTitleText,
  confirmBtnLabel,
} = useFormControl<RoleFormValues, RoleListItem>({
  resolver: object({
    name: schemaNotEmptyString('请输入角色名称'),
  }),
  btnLabel: {
    create: '确认新增',
    update: '保存更改',
  },
  formTitle: {
    create: '新增系统角色',
    update: '编辑系统角色',
  },
  fetchDetail: async (updatingRole) => {
    const roleDetail = await UserService.getRole(updatingRole.id)

    return roleDetail
  },
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const payload = {
        name: states.name.value,
        menu_list: formValues.value.menu_list,
      }

      if (isFormCreate.value) {
        await UserService.createRole(payload)
      }
      else {
        if (isFormUpdate.value) {
          const roleId = updateRole.value?.id

          if (roleId) {
            await UserService.updateRole(roleId, payload)
          }
        }
      }
    }
  },
  onSubmitSuccess: () => {
    tableAction.value?.reload()
  },
})

const handleDelete = async (role?: RoleListItem) => {
  if (role) {
    await UserService.deleteRole(role.id)
    tableAction.value?.reload()
  }
  else {
    $confirm.dialog({
      header: '谨慎操作',
      message: `确定删除角色「${updateRole.value?.name}」吗？`,
      accept: async () => {
        if (updateRole.value) {
          await UserService.deleteRole(updateRole.value.id)

          $toast.info({
            summary: '删除成功',
            detail: `角色「${updateRole.value.name}」已删除`,
          })

          tableAction.value?.reload()

          handleClose()
        }
      },
    })
  }
}

const sortField = ref<string>()

const handleSortFieldChange = (sort: string) => {
  sortField.value = sort
}

const columns = computed<ProTableColumn<RoleListItem>[]>(() => [
  {
    field: 'name',
    header: '角色',
  },
  {
    field: 'create_time',
    header: '创建时间',
    sortable: true,
    valueType: ProValueType.Date,
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', { class: 'table-action-group' }, [
      data.is_update && h(ProBtnEdit, {
        onlyIcon: true,
        onClick: () => openUpdateModal(data),
      }),
      data.is_update && h(ProBtnDelete, {
        confirmMessage: `确定要删除角色「${data.name}」吗？`,
        deleteSuccessMessage: `角色「${data.name}」已删除`,
        onlyIcon: true,
        onConfirm: () => handleDelete(data),
      }),
    ]),
  },
])
</script>

<template>
  <div>
    <ProTable
      :ref="tableRef"
      :columns="columns"
      :queryKey="queryKeys.role.all()"
      :requestFn="UserService.getRoleList"
      :toolbarConfig="{
        search: {
          key: 'name',
          placeholder: '搜索角色名称',
        },
      }"
      @update:sortField="handleSortFieldChange"
    >
      <template #toolbarRight>
        <div class="gap-2 py-1 flex-center">
          <div class="ml-auto gap-4 flex-center">
            <Button
              label="新增系统角色"
              @click="openCreateModal()"
            />
          </div>
        </div>
      </template>
    </ProTable>

    <ProDialogForm
      :formActionGroupProps="{
        confirmButtonProps: {
          label: confirmBtnLabel,
        },
      }"
      :initialValues="formValues"
      :loading="loading"
      :resolver="formResolver"
      :title="formTitleText"
      :visible="modalVisible"
      @cancel="handleClose"
      @submit="handleSubmit"
      @update:visible="handleModalVisibleChange"
    >
      <FormItem
        v-slot="{ id }"
        label="角色名称"
        name="name"
        required
      >
        <InputText
          :id="id"
          v-model="formValues.name"
          placeholder="例如：管理员、编辑者、访客"
        />
      </FormItem>

      <FormItem
        label="菜单权限"
      >
        <Message
          severity="secondary"
          size="small"
          variant="simple"
        >
          勾选对应的菜单，角色将拥有访问该菜单的权限
        </Message>

        <UserNavMenuTreeSelect
          v-model="formValues.menu_list"
        />
      </FormItem>

      <FormItem
        v-if="isFormUpdate"
        label="谨慎操作"
      >
        <Button
          label="删除该角色"
          severity="danger"
          variant="outlined"
          @click="handleDelete()"
        />
      </FormItem>
    </ProDialogForm>
  </div>
</template>
