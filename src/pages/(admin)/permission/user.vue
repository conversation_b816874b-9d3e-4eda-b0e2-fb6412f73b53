<script setup lang="ts">
import { MailIcon } from 'lucide-vue-next'
import { Tag } from 'primevue'
import { nullable, number, object, undefinedable } from 'valibot'

import NumericDisplay from '~/components/NumericDisplay.vue'
import ProBtnDelete from '~/components/pro/btn/ProBtnDelete.vue'
import ProBtnEdit from '~/components/pro/btn/ProBtnEdit.vue'
import UserAvatar from '~/components/user/UserAvatar.vue'
import type UserResetPwd from '~/components/user/UserResetPwd.vue'
import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'
import { UserStatus, userStatusConfig } from '~/enums/user'

definePageMeta({
  description: '管理组织架构、用户信息及权限分配，支持部门管理和用户状态维护',
})

const { $confirm, $toast } = useNuxtApp()

const { tableRef, tableAction } = useTableAction()

const store = useAdminUserGroupStore()
const { activeUserGroup } = storeToRefs(store)

const {
  loading,
  isFormCreate,
  isFormUpdate,
  formTitleText,
  formValues,
  updatingItem,
  modalVisible,
  formResolver,
  openCreateModal,
  openUpdateModal,
  handleClose,
  handleSubmit,
  confirmBtnLabel,
  handleModalVisibleChange,
} = useFormControl<UserFormValues, UserListItem>({
  resolver: object({
    email: schemaNotEmptyString('请输入邮箱'),
    username: schemaNotEmptyString('请输入用户名称'),
    mobile: nullable(undefinedable(schemaMobile)),
    role_id: number('请选择角色'),
    password: schemaNotEmptyString('请输入密码'),
  }),
  btnLabel: {
    create: '新增用户',
    update: '保存更改',
  },
  formTitle: {
    create: '新增用户',
    update: '编辑用户',
  },
  fetchDetail: (it) => UserService.getUser(it.id),
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const payload: UserFormValues = {
        username: states.username.value,
        role_id: states.role_id.value,
        email: states.email.value,
        mobile: states.mobile?.value,
      }

      if (isFormCreate.value) {
        if (activeUserGroup.value) {
          await UserService.createUser({
            ...payload,
            password: states.password?.value,
            organize_id: activeUserGroup.value.id,
          })
        }
      }
      else {
        if (isFormUpdate.value) {
          const id = updatingItem.value?.id

          if (id) {
            await UserService.updateUser(id, payload)
          }
        }
      }
    }
  },
  onSubmitSuccess: () => {
    tableAction.value?.reload()
  },
})

const handleToggleStatus = () => {
  $confirm.dialog({
    header: '谨慎操作',
    acceptProps: {
      label: updatingItem.value?.status === UserStatus.Resigned ? '设置为在职状态' : '设置为离职状态',
      severity: updatingItem.value?.status === UserStatus.Resigned ? 'success' : 'danger',
    },
    message: `确定将用户「${updatingItem.value?.username}」设置为${updatingItem.value?.status === UserStatus.Resigned ? '在职' : '离职'}状态吗？`,
    accept: async () => {
      if (updatingItem.value) {
        await UserService.updateUser(updatingItem.value.id, {
          status: updatingItem.value.status === UserStatus.Resigned ? UserStatus.Employed : UserStatus.Resigned,
        })

        $toast.success({
          summary: '用户状态更新',
          detail: `用户「${updatingItem.value.username}」状态已更新`,
        })

        tableAction.value?.reload()

        handleClose()
      }
    },
  })
}

const handleDelete = async (user?: UserListItem) => {
  if (user) {
    await UserService.deleteUser(user.id)
    tableAction.value?.reload()
  }
  else {
    $confirm.dialog({
      header: '谨慎操作',
      message: `确定删除用户「${updatingItem.value?.username}」吗？`,
      accept: async () => {
        if (updatingItem.value) {
          await UserService.deleteUser(updatingItem.value.id)

          $toast.info({
            summary: '删除成功',
            detail: `用户「${updatingItem.value.username}」已删除`,
          })

          tableAction.value?.reload()

          handleClose()
        }
      },
    })
  }
}

const userResetPwdRef = useTemplateRef<InstanceType<typeof UserResetPwd>>('user-reset-pwd')

const handleResetPwdClick = () => {
  userResetPwdRef.value?.open()
}

const sortField = ref<string>()

const handleSortFieldChange = (sort: string) => {
  sortField.value = sort
}

const columns = computed<ProTableColumn<UserListItem>[]>(() => [
  {
    field: 'username',
    header: '名称',
    valueType: ProValueType.Text,
    render: (data) => h('span', { class: 'gap-2 whitespace-nowrap flex-center' }, [
      h(UserAvatar, {
        avatar: data.avatar,
        class: 'overflow-hidden',
        username: data.username,
      }),
      h('span', { class: 'flex flex-col text-sm' }, [
        h('span', data.username),
        h('span', { class: 'text-secondary' }, data.email),
      ]),
    ]),
  },
  {
    field: 'mobile',
    header: '手机号',
    valueType: ProValueType.Text,
    render: (data) => h(NumericDisplay, {
      class: 'text-secondary text-[13px]',
      value: data.mobile ? maskMobile(data.mobile) : '-',
    }),
  },
  {
    field: 'role',
    header: '角色',
    valueType: ProValueType.Select,
    hideInFilter: true,
    render: (data) => data.role_name,
  },
  {
    field: 'status',
    header: '状态',
    valueType: ProValueType.Select,
    valueEnum: toValueEnum(userStatusConfig),
    render: (data) => h(Tag, {
      severity: userStatusConfig[data.status].severity,
      value: userStatusConfig[data.status].label,
    }),
  },
  {
    field: 'create_time',
    header: '创建时间',
    valueType: ProValueType.Date,
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', { class: 'table-action-group' }, [
      data.is_update && h(ProBtnEdit, {
        label: '编辑',
        onlyIcon: true,
        onClick: () => openUpdateModal(data),
      }),
      data.is_update && h(ProBtnDelete, {
        confirmMessage: `确定要删除用户「${data.username}」吗？`,
        deleteSuccessMessage: `用户「${data.username}」已删除`,
        onlyIcon: true,
        onConfirm: () => handleDelete(data),
      }),
    ]),
  },
])
</script>

<template>
  <div class="flex h-full">
    <ProSplitter leftPanelContentClass="!p-0">
      <template #left>
        <UserGroup />
      </template>

      <template #right>
        <div class="flex-1">
          <ProTable
            :ref="tableRef"
            :columns="columns"
            :queryKey="queryKeys.user.all"
            :queryParams="{
              organize_id: activeUserGroup?.id,
            }"
            :requestFn="UserService.getUserList"
            :toolbarConfig="{
              search: {
                key: 'username',
                placeholder: '搜索用户名称',
              },
            }"
            @update:sortField="handleSortFieldChange"
          >
            <template
              v-if="activeUserGroup"
              #headerLeft
            >
              <div class="text-lg font-semibold">
                {{ activeUserGroup.name }}
              </div>
            </template>

            <template #toolbarRight>
              <div class="gap-2 py-1 flex-center">
                <div class="ml-auto gap-4 flex-center">
                  <Button
                    label="新增用户"
                    @click="openCreateModal()"
                  />
                </div>
              </div>
            </template>
          </ProTable>

          <ProDialogForm
            :formActionGroupProps="{
              confirmButtonProps: {
                label: confirmBtnLabel,
              },
            }"
            :initialValues="formValues"
            :loading="loading"
            :resolver="formResolver"
            :title="formTitleText"
            :visible="modalVisible"
            @cancel="handleClose"
            @submit="handleSubmit"
            @update:visible="handleModalVisibleChange"
          >
            <FormItem
              v-slot="{ id }"
              label="用户名称"
              name="username"
              required
            >
              <InputText
                :id="id"
                v-model="formValues.username"
                v-keyfilter="/[^s]/"
                autocomplete="off"
                placeholder="用于在页面中展示，可以随时更改"
              />
            </FormItem>

            <FormItem
              v-slot="{ id }"
              label="角色"
              name="role_id"
              required
            >
              <UserRoleSelect
                v-model="formValues.role_id"
                :labelId="id"
                placeholder="选择角色"
              />
            </FormItem>

            <FormItem
              v-slot="{ id }"
              label="Email"
              name="email"
              required
            >
              <IconField>
                <InputIcon class="size-[1.12rem] inline-flex-center">
                  <MailIcon :size="16" />
                </InputIcon>
                <InputText
                  :id="id"
                  v-model="formValues.email"
                  autocomplete="off"
                  fluid
                />
              </IconField>
            </FormItem>

            <FormItem
              v-slot="{ id }"
              label="手机号"
              name="mobile"
            >
              <InputText
                :id="id"
                v-model="formValues.mobile"
                v-keyfilter.int
                autocomplete="off"
                placeholder="用户的手机号码，用于登录"
              />
            </FormItem>

            <FormItem
              v-if="isFormCreate"
              label="登录密码"
              name="password"
              required
            >
              <template #default="{ id }">
                <Password
                  v-model="formValues.password"
                  :feedback="false"
                  fluid
                  :inputId="id"
                  toggleMask
                />
              </template>

              <template #helpTip>
                登录密码至少 8 位，包含字母和数字
              </template>
            </FormItem>

            <Fieldset
              v-if="isFormUpdate"
              legend="谨慎操作"
            >
              <div class="grid grid-cols-2 gap-2 pt-in-fieldset">
                <Button
                  fluid
                  label="重置密码"
                  severity="info"
                  variant="outlined"
                  @click="handleResetPwdClick()"
                />

                <Button
                  v-if="updatingItem"
                  fluid
                  :label="updatingItem.status === UserStatus.Resigned ? '设置为在职状态' : '设置为离职状态'"
                  :severity="updatingItem.status === UserStatus.Resigned ? 'success' : 'danger'"
                  variant="outlined"
                  @click="handleToggleStatus()"
                />

                <Button
                  fluid
                  label="删除用户"
                  severity="danger"
                  variant="outlined"
                  @click="handleDelete()"
                />
              </div>
            </Fieldset>
          </ProDialogForm>

          <UserResetPwd
            ref="user-reset-pwd"
            :user="updatingItem"
          />
        </div>
      </template>
    </ProSplitter>
  </div>
</template>
