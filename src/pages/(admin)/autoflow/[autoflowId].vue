<script setup lang="ts">
import { string } from 'valibot'

definePageMeta({
  layoutConfig: {
    customContent: true,
  },
})

const autoflowId = useRouteParam({
  name: 'autoflowId',
  schema: string(),
})

const runtimeConfig = useRuntimeConfig()
const { n8nHost } = runtimeConfig.public

const src = `${n8nHost}/workflow/${autoflowId}`
</script>

<template>
  <div class="size-full">
    <iframe
      v-if="autoflowId && n8nHost"
      class="size-full"
      :src="src"
    />
  </div>
</template>
