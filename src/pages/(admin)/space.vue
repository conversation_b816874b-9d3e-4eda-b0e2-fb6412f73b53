<script setup lang="ts">
definePageMeta({
  layoutConfig: {
    showHeader: false,
    noContentPadding: true,
  },
})

const appStore = useAppStore()

const route = useRoute()
</script>

<template>
  <div class="h-full">
    <NuxtPage
      v-if="isWorkflowDemo(route.path)"
      :transition="{
        name: appStore.themeState.transition,
        mode: 'out-in',
      }"
    />

    <NuxtLayout
      v-else-if="isWorkflowApp(route.path)"
      name="space-app-workflow"
    >
      <NuxtPage
        :transition="{
          name: appStore.themeState.transition,
          mode: 'out-in',
        }"
      />
    </NuxtLayout>

    <NuxtLayout
      v-else
      name="space"
    >
      <NuxtPage
        :transition="{
          name: appStore.themeState.transition,
          mode: 'out-in',
        }"
      />
    </NuxtLayout>
  </div>
</template>
