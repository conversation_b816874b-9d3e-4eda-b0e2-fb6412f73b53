<script setup lang="ts">
import { valibotResolver } from '@primevue/forms/resolvers/valibot'
import { object } from 'valibot'

const { $toast } = useNuxtApp()

const loadingReset = ref(false)

const initialValues: ResetPasswordFormValues = {
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
}

const passwordForm = ref<ResetPasswordFormValues>({ ...initialValues })

const passwordResolver = valibotResolver(object({
  oldPassword: schemaNotEmptyString('旧密码不能为空'),
  newPassword: schemaPassword,
  confirmPassword: schemaNotEmptyString('确认密码不能为空'),
}))

const handlePasswordSubmit = async ({ valid, states }: FormSubmitOptions<ResetPasswordFormValues>) => {
  if (valid) {
    if (states.newPassword.value === states.confirmPassword.value) {
      loadingReset.value = true

      try {
        await UserService.resetPassword({
          oldPassword: states.oldPassword.value,
          newPassword: states.newPassword.value,
        })

        $toast.success({
          summary: '成功',
          detail: '密码修改成功，请重新登录',
        })

        // 重置表单，清空输入
        passwordForm.value = { ...initialValues }

        handleLogout()
      }
      finally {
        loadingReset.value = false
      }
    }
  }
}
</script>

<template>
  <div class="pt-in-tab md:w-1/2 lg:w-1/3">
    <div class="mb-8 flex flex-col gap-2">
      <h2 class="text-xl font-bold">
        重置密码
      </h2>

      <div class="inline-flex">
        <Message
          severity="secondary"
          size="small"
        >
          修改密码后，系统将自动退出登录，请使用新密码重新登录
        </Message>
      </div>
    </div>

    <Form
      :initialValues="passwordForm"
      :resolver="passwordResolver"
      @submit="handlePasswordSubmit"
    >
      <div class="flex flex-col gap-form-field-large">
        <FormItem
          label="旧密码"
          name="oldPassword"
        >
          <Password
            v-model="passwordForm.oldPassword"
            autocomplete="off"
            :feedback="false"
            fluid
            placeholder="请输入旧密码"
            toggleMask
          />
        </FormItem>

        <FormItem
          label="新密码"
          name="newPassword"
        >
          <Password
            v-model="passwordForm.newPassword"
            autocomplete="off"
            :feedback="false"
            fluid
            placeholder="请输入新密码"
            toggleMask
          />
        </FormItem>

        <FormItem
          label="确认新密码"
          name="confirmPassword"
        >
          <Password
            v-model="passwordForm.confirmPassword"
            autocomplete="off"
            :feedback="false"
            fluid
            placeholder="请输入新密码"
            toggleMask
          />

          <Message
            v-if="passwordForm.newPassword !== passwordForm.confirmPassword"
            severity="error"
            size="small"
            variant="simple"
          >
            两次输入的密码不一致
          </Message>
        </FormItem>

        <FormActionGroup
          :cancelButtonProps="false"
          :confirmButtonProps="{
            label: '确认更新密码',
            loading: loadingReset,
          }"
          justify="start"
        />
      </div>
    </Form>
  </div>
</template>
