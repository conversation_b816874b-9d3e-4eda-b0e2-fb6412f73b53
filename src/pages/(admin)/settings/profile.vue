<script setup lang="ts">
import { valibotResolver } from '@primevue/forms/resolvers/valibot'
import { LoaderIcon } from 'lucide-vue-next'
import { object } from 'valibot'

import AvatarUploader from '~/components/user/AvatarUploader.vue'

const { $toast } = useNuxtApp()

const userStore = useUserStore()
const { user } = storeToRefs(userStore)

const formData = ref<User>(user.value ?? {} as User)

const formResolver = valibotResolver(object({
  username: schemaNotEmptyString('用户名不能为空'),
}))

const avatar = ref<User['avatar']>(user.value?.avatar)

const isAvatarUploading = ref(false)

// 处理头像文件选择并上传
const handleAvatarSelect = async (file: File) => {
  isAvatarUploading.value = true

  try {
    const formData = new FormData()
    formData.append('file', file)

    const avatarResult = await UserService.uploadAvatar(formData)

    if (avatarResult) {
      avatar.value = avatarResult

      // 如果头像发生变化，则更新用户头像
      if (avatarResult !== user.value?.avatar && user.value?.id) {
        const newUserValues = {
          avatar: avatarResult,
        }

        await UserService.updateCurrentUser(newUserValues)

        userStore.updateUser(newUserValues)

        $toast.success({
          summary: '已更新用户头像',
        })
      }
    }
  }
  catch {
    $toast.error({
      summary: '头像上传失败',
      detail: '请稍后重试',
    })
  }
  finally {
    isAvatarUploading.value = false
  }
}

// 处理上传错误
const handleUploadError = (message: string) => {
  $toast.error({
    summary: '文件验证失败',
    detail: message,
  })
}

const showSuccessMessage = ref(false)
const messageLife = 3000

const handleSubmit = async ({ valid, states }: FormSubmitOptions<User>) => {
  if (valid) {
    const newUserValues = {
      username: states.username.value,
      avatar: avatar.value,
    }

    await UserService.updateCurrentUser(newUserValues)

    userStore.updateUser(newUserValues)

    showSuccessMessage.value = true

    setTimeout(() => {
      showSuccessMessage.value = false
    }, messageLife)
  }
}
</script>

<template>
  <div class="pt-in-tab md:w-1/2 lg:w-1/3">
    <Form
      :initialValues="formData"
      :resolver="formResolver"
      @submit="handleSubmit"
    >
      <div class="flex flex-col gap-form-field-large">
        <div class="flex items-end gap-4">
          <div class="relative inline-flex">
            <UserAvatar
              :avatar="avatar"
              class="!size-36 overflow-hidden !rounded-2xl"
              :username="user?.username"
            />

            <!-- 头像上传加载状态 -->
            <div
              v-if="isAvatarUploading"
              class="absolute inset-0 justify-center !rounded-2xl bg-surface-50/30 backdrop-blur-sm flex-center dark:bg-surface-500/50"
            >
              <LoaderIcon
                class="animate-spin"
                :size="20"
              />
            </div>
          </div>

          <div class="flex flex-col items-start gap-2">
            <AvatarUploader
              :maxSizeMB="5"
              @error="handleUploadError"
              @select="handleAvatarSelect"
            />

            <Message
              severity="secondary"
              size="small"
              variant="simple"
            >
              头像大小不能超过 5MB
            </Message>
          </div>
        </div>

        <FormItem
          v-slot="{ id }"
          label="用户名"
          name="username"
        >
          <InputText
            :id="id"
            v-model="formData.username"
          />
        </FormItem>

        <FormItem
          v-slot="{ id }"
          label="邮箱"
          name="email"
        >
          <InputText
            :id="id"
            v-model="formData.email"
          />
        </FormItem>

        <div class="space-y-2">
          <FormActionGroup
            :cancelButtonProps="false"
            :confirmButtonProps="{
              label: '更新个人信息',
            }"
            justify="start"
          />

          <Message
            v-if="showSuccessMessage"
            :life="messageLife"
            severity="success"
          >
            个人信息已更新
          </Message>
        </div>
      </div>
    </Form>
  </div>
</template>
