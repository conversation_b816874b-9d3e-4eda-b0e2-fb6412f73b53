<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/enums/query-key'

const { data: license, refetch: refetchLicense } = useQuery({
  queryKey: queryKeys.auth.license,
  queryFn: () => UserService.getLicense(),
})

const handleUploadSuccess = () => {
  refetchLicense()
}
</script>

<template>
  <div class="pt-in-tab">
    <div v-if="license">
      <ProFormLabelValue
        :data="[
          {
            label: '授权给',
            value: license.name,
          },
          {
            label: '授权版本',
            value: license.version,
          },
          {
            label: '过期时间',
            value: license.expiration_time || '永久生效',
          },
          {
            label: '颁发时间',
            value: license.create_time,
          },
          {
            label: '激活状态',
            value: license.status ? '已激活' : '未激活',
          },
        ]"
        labelColon
        layout="vertical"
        readOnly
      />

      <div class="flex justify-start gap-3 py-5">
        <LicenseUpload @uploadSuccess="handleUploadSuccess" />
      </div>
    </div>

    <div v-else>
      <div class="flex flex-col items-start gap-3">
        <div>
          未找到许可证，请上传许可证
        </div>

        <LicenseUpload @uploadSuccess="handleUploadSuccess" />
      </div>
    </div>
  </div>
</template>
