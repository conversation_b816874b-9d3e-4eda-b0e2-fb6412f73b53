<script setup lang="ts">
import { Avatar, AvatarGroup, Tooltip } from 'primevue'

import PopupMenu from '~/components/PopupMenu.vue'
import ProBtnMore from '~/components/pro/btn/ProBtnMore.vue'
import UserAvatar from '~/components/user/UserAvatar.vue'
import { queryKeys } from '~/constants-tw/query-key'
import { ProValueType } from '~/enums/pro'
import { UserService } from '~/services-tw/org'
import type { ClientListItem, TW_Client, WechatUser } from '~/types-tw/user'

const { tableRef } = useTableAction()

const activeClientId = ref<TW_Client['id']>()

const WechatUserAvatars = defineComponent({
  props: {
    users: {
      type: Array as PropType<WechatUser[]>,
      default: () => [],
    },
    maxCount: {
      type: Number,
      default: 4,
    },
  },
  setup(props) {
    return () => {
      if (Array.isArray(props.users) && props.users.length > 0) {
        return h(
          AvatarGroup,
          () => [
            ...(props.users.slice(0, props.maxCount).map((user) =>
              withDirectives(h(Avatar, {
                size: 'small',
                shape: 'circle',
                image: user.avatar,
              }), [[
                Tooltip,
                {
                  value: user.username ? `${user.nickname}（${user.username}）` : user.nickname,
                  autoHide: false,
                },
                undefined,
                { top: true },
              ]]),
            ) || []),
            props.users.length > props.maxCount
              ? h(Avatar, {
                  size: 'small',
                  shape: 'circle',
                  label: `+${props.users.length - props.maxCount}`,
                })
              : null,
          ],
        )
      }

      return null
    }
  },
})

const columns = computed<ProTableColumn<ClientListItem>[]>(() => [
  {
    header: '客户',
    render: (data) => h('span', { class: 'gap-2 whitespace-nowrap flex-center' }, [
      h('span', { class: 'flex flex-col text-sm' }, [
        h('span', { class: 'font-medium' }, data.name),
        h('span', { class: 'text-secondary' }, data.number),
      ]),
    ]),
  },
  {
    field: 'type',
    header: '类型',
  },
  {
    field: 'work_phone',
    header: '联系方式',
    valueType: ProValueType.Text,
  },
  {
    header: '客户地址',
    render: (data) => {
      if (!data.address && !data.country_province && !data.country_province_city) {
        return '-'
      }

      return h('div', [
        h('div', data.address),
        h('div', { class: 'text-secondary text-sm' }, `${data.country_province || ''} ${data.country_province_city || ''}`),
      ])
    },
  },
  {
    header: '关联微信用户',
    render: (data) => {
      if (Array.isArray(data.wechat_users) && data.wechat_users.length > 0) {
        return h(WechatUserAvatars, {
          users: data.wechat_users,
          maxCount: 4,
        })
      }

      return '-'
    },
  },
  {
    header: '客户经理',
    render: (data) => {
      const user = data.user

      if (!user) {
        return '-'
      }

      return h('span', { class: 'gap-2 whitespace-nowrap flex-center' }, [
        h(UserAvatar, {
          avatar: user?.avatar,
          class: 'overflow-hidden',
          username: user?.name,
        }),
        h('span', { class: 'flex flex-col text-sm' }, [
          h('span', { class: 'font-medium' }, user.name),
          h('span', { class: 'text-secondary' }, user.phone ? maskMobile(user.phone) : '-'),
        ]),
      ])
    },
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', { class: 'table-action-group' }, [
      h(PopupMenu, {
        options: [
          {
            label: '详情',
            command: () => {
              activeClientId.value = data.id
            },
          },
        ],
      }, () => h(ProBtnMore, {
        onlyIcon: true,
      })),
    ]),
  },
])
</script>

<template>
  <div>
    <ProTable
      :ref="tableRef"
      :columns="columns"
      :queryKey="queryKeys.client.list()"
      :requestFn="UserService.getClients"
      :toolbarConfig="{
        search: {
          key: 'username',
          placeholder: '搜索客户名称',
        },
      }"
    />

    <Dialog
      class="dialog-half"
      dismissableMask
      :draggable="false"
      header="客户信息"
      modal
      :visible="!!activeClientId"
      @update:visible="activeClientId = undefined"
    >
      <ClientDetail :clientId="activeClientId" />
    </Dialog>
  </div>
</template>
