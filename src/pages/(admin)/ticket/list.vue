<script setup lang="ts">
import { object, safeParse, string } from 'valibot'

import type { TicketType } from '~/enums/ticket'

const route = useRoute()

const ticketType = computed(() => {
  const result = safeParse(object({
    type: string(),
  }), route.query)

  if (result.success) {
    return Number(result.output.type) as TicketType
  }

  return null
})
</script>

<template>
  <div>
    <TicketList
      :filters="ticketType ? { ticketType } : undefined"
      viewIn="page"
    />
  </div>
</template>
