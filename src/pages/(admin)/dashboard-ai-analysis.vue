<script setup lang="ts">
definePageMeta({
  description: 'AI 驱动的安全分析与决策支持平台',
  layoutConfig: {
    customContent: true,
  },
})
</script>

<template>
  <div class="flex h-full flex-col">
    <LayoutAdminContentHeader wrapperClass="!p-0" />

    <div class="flex flex-1 gap-4 overflow-hidden">
      <div class="flex shrink-0 basis-1/4 flex-col gap-admin-layout">
        <CardContainer class="p-card-container">
          <DashAiKnowledgeGraph />
          <DashAiSample />
        </CardContainer>
      </div>

      <div class="flex min-w-0 basis-2/4 flex-col gap-4">
        <CardContainer class="p-card-container">
          <DashAiChat2 />
        </CardContainer>

        <CardContainer class="min-h-[320px] p-card-container">
          <DashAiOrgChart />
        </CardContainer>

        <CardContainer class="max-h-[225px] p-card-container">
          <DashAiSimulation />
        </CardContainer>
      </div>

      <div class="shrink-0 basis-1/4">
        <CardContainer class="p-card-container">
          <DashAiAgent />
        </CardContainer>
      </div>
    </div>
  </div>
</template>
