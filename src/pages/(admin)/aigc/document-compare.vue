<script setup lang="ts">
import {
  BrushIcon,
  FileTextIcon,
  GitCompareIcon,
  GitMergeIcon,
  LayoutGridIcon,
  PercentIcon,
  SearchIcon,
  Wand2Icon,
} from 'lucide-vue-next'
import markdownit from 'markdown-it'

import { useAIGenerator } from '@/composables/useAIGenerator'

definePageMeta({
  title: '智能文本对比分析',
  description: '比较、合并、分析不同文本，智能发现文本之间的关系，为您提供深入的文本分析洞察',
})

const md = markdownit()

interface AnalysisAction {
  id: 'compare' | 'merge' | 'analyze' | 'similarity' | 'summary' | 'expand' | 'style' | 'process'
  label: string
  description: string
  icon: Component
}

const ANALYSIS_ACTIONS: AnalysisAction[] = [
  { id: 'compare', label: '对比差异', description: '比较两段文本的不同之处', icon: GitCompareIcon },
  { id: 'merge', label: '文本合并', description: '将两段文本合并为一个', icon: GitMergeIcon },
  { id: 'analyze', label: '文本分析', description: '分析文本的情感、主题等特征', icon: SearchIcon },
  { id: 'similarity', label: '相似度检测', description: '计算两段文本的相似程度', icon: PercentIcon },
  { id: 'summary', label: '文本摘要', description: '生成文本的简要概述', icon: FileTextIcon },
  { id: 'expand', label: '内容扩展', description: '基于给定文本生成更多内容', icon: LayoutGridIcon },
  { id: 'style', label: '风格统一', description: '统一文本的写作风格', icon: BrushIcon },
  { id: 'process', label: '处理文本', description: '根据右侧文本处理左侧文本', icon: Wand2Icon },
]

const leftContentRef = ref<HTMLTextAreaElement | null>(null)
const rightContentRef = ref<HTMLTextAreaElement | null>(null)
const leftContent = ref('')
const rightContent = ref('')
const selectedAction = ref<AnalysisAction['id']>('compare')

const { generate, stopGeneration, generatedContent, isGenerating } = useAIGenerator()

const renderedContent = computed(() => generatedContent.value ? md.render(generatedContent.value) : '')

const getPromptForAction = (actionId: AnalysisAction['id'], leftText: string, rightText: string): string => {
  switch (actionId) {
    case 'compare':
      return `请详细比较以下两段文本的不同之处：\n\n左侧文本：\n${leftText}\n\n右侧文本：\n${rightText}`

    case 'merge':
      return `请将以下两段文本合并为一个连贯的文本：\n\n文本1：\n${leftText}\n\n文本2：\n${rightText}`

    case 'analyze':
      return `请分析以下两段文本的情感、主题和其他特征：\n\n文本1：\n${leftText}\n\n文本2：\n${rightText}`

    case 'similarity':
      return `请计算以下两段文本的相似程度，并详细说明相似的部分：\n\n文本1：\n${leftText}\n\n文本2：\n${rightText}`

    case 'summary':
      return `请为以下文本生成简要概述：\n\n文本1：\n${leftText}\n\n文本2：\n${rightText}`

    case 'expand':
      return `请基于以下文本生成更多相关内容：\n\n原始文本1：\n${leftText}\n\n原始文本2：\n${rightText}`

    case 'style':
      return `请统一以下两段文本的写作风格，使它们风格一致：\n\n文本1：\n${leftText}\n\n文本2：\n${rightText}`

    case 'process':
      return `请根据第二段文本的指示处理第一段文本：\n\n需处理的文本：\n${leftText}\n\n处理指示：\n${rightText}`
  }
}

const handleAction = async (actionId: AnalysisAction['id']) => {
  if (!leftContent.value.trim() || !rightContent.value.trim()) {
    return
  }

  generatedContent.value = ''

  const prompt = getPromptForAction(actionId, leftContent.value, rightContent.value)
  await generate({ prompt })
}

const cancelGeneration = () => {
  stopGeneration()
  generatedContent.value = ''
}

const startAnalysis = () => {
  handleAction(selectedAction.value)
}
</script>

<template>
  <div>
    <div class="flex h-full flex-col gap-admin-layout overflow-y-auto">
      <div class="grid grid-cols-2 gap-admin-layout">
        <div>
          <div class="mb-2 text-lg font-medium">
            左侧文本
          </div>
          <Textarea
            ref="leftContentRef"
            v-model="leftContent"
            fluid
            placeholder="在此输入左侧文本..."
            rows="15"
          />
        </div>

        <div>
          <div class="mb-2 text-lg font-medium">
            右侧文本
          </div>
          <Textarea
            ref="rightContentRef"
            v-model="rightContent"
            fluid
            placeholder="在此输入右侧文本..."
            rows="15"
          />
        </div>
      </div>

      <div class="grid grid-cols-4 gap-admin-layout">
        <div
          v-for="action of ANALYSIS_ACTIONS"
          :key="action.id"
          class="cursor-pointer flex-col justify-center rounded-xl border border-solid border-divider bg-content p-3 flex-center"
          :class="[
            selectedAction === action.id
              ? '!border-blue-500 !bg-blue-50 !text-blue-500'
              : 'hover:!border-primary',
          ]"
          @click="() => {
            selectedAction = action.id;
          }"
        >
          <Component
            :is="action.icon"
            class="mb-2"
            :size="22"
          />

          <div class="font-medium">
            {{ action.label }}
          </div>

          <div class="mt-1 text-xs opacity-70">
            {{ action.description }}
          </div>
        </div>
      </div>

      <div class="flex gap-3">
        <Button
          v-if="!isGenerating"
          :disabled="isGenerating || !leftContent.trim() || !rightContent.trim()"
          fluid
          :loading="isGenerating"
          @click="startAnalysis()"
        >
          开始分析
        </Button>

        <Button
          v-if="isGenerating"
          fluid
          variant="danger"
          @click="cancelGeneration()"
        >
          停止生成
        </Button>
      </div>
    </div>

    <div
      v-if="renderedContent"
      class="py-5"
    >
      <div
        class="markdown-content"
        v-html="renderedContent"
      />
    </div>
  </div>
</template>
