<script setup lang="ts">
import {
  AlertTriangleIcon,
  CheckIcon,
  CopyIcon,
  FileTextIcon,
  LanguagesIcon,
  MaximizeIcon,
  Minimize2Icon,
  RefreshCwIcon,
  Wand2Icon,
  XIcon,
} from 'lucide-vue-next'
import markdownit from 'markdown-it'

import { useAIGenerator } from '@/composables/useAIGenerator'

definePageMeta({
  layoutConfig: {
    customContent: true,
  },
})

const md = markdownit()

interface PolishAction {
  id: 'polish' | 'expand' | 'error-check' | 'complex' | 'simplify' | 'translate'
  label: string
  description: string
  icon: Component
}

const POLISH_ACTIONS: PolishAction[] = [
  { id: 'polish', label: '一键润色', description: '优化文章表达，提升语言质量', icon: Wand2Icon },
  { id: 'expand', label: '一键扩写', description: '扩展内容，增加细节和例子', icon: MaximizeIcon },
  { id: 'error-check', label: '一键排错', description: '检查并修正语法和用词错误', icon: AlertTriangleIcon },
  { id: 'complex', label: '句子复杂化', description: '增加句子复杂度和专业性', icon: FileTextIcon },
  { id: 'simplify', label: '句子简化', description: '简化句子结构，提高可读性', icon: Minimize2Icon },
  { id: 'translate', label: '一键翻译', description: '将文本翻译成其他语言', icon: LanguagesIcon },
]

const content = ref('')
const selectedAction = ref<PolishAction['id']>('polish')
const copied = ref(false)
const selectionStart = ref(0)
const selectionEnd = ref(0)

const { generate, stopGeneration, generatedContent, isGenerating } = useAIGenerator()

const renderedContent = computed(() => generatedContent.value ? md.render(generatedContent.value) : '')

const getPromptForAction = (actionId: PolishAction['id'], text: string): string => {
  switch (actionId) {
    case 'polish':
      return `请润色优化以下文本，提升语言表达质量：\n\n${text}`

    case 'expand':
      return `请扩写以下文本，增加更多细节和例子：\n\n${text}`

    case 'error-check':
      return `请检查并修正以下文本中的语法和用词错误：\n\n${text}`

    case 'complex':
      return `请将以下文本改写得更加复杂和专业：\n\n${text}`

    case 'simplify':
      return `请将以下文本简化，使其更容易理解：\n\n${text}`

    case 'translate':
      return `请将以下文本翻译成英文：\n\n${text}`
  }
}

const handleAction = async (actionId: PolishAction['id'], text?: string) => {
  const textToProcess = text || content.value

  if (!textToProcess.trim()) {
    return
  }

  const prompt = getPromptForAction(actionId, textToProcess)
  await generate({ prompt })
}

const copy = async () => {
  await copyToClipboard(generatedContent.value)

  copied.value = true

  setTimeout(() => {
    copied.value = false
  }, 2000)
}

const regenerateContent = () => {
  handleAction(selectedAction.value)
}

const cancelGeneration = () => {
  stopGeneration()
  generatedContent.value = ''
}

const replaceContent = () => {
  if (selectionStart.value !== selectionEnd.value) {
    content.value = content.value.slice(0, selectionStart.value) + generatedContent.value + content.value.slice(selectionEnd.value)
  }
  else {
    content.value += '\n\n' + generatedContent.value
  }

  generatedContent.value = ''
}

const furtherOptimize = () => {
  handleAction(selectedAction.value, generatedContent.value)
}
</script>

<template>
  <div class="flex h-full flex-col">
    <LayoutAdminContentHeader
      desc="一键提升报告专业度，AI 智能优化文本表达，让您的内容更具说服力。支持全文处理或选择关键段落进行精准优化"
      title="智能报告润色大师"
      wrapperClass="!p-0"
    />

    <div class="grid h-full gap-admin-layout overflow-y-auto md:grid-cols-2">
      <div class="flex h-full flex-col gap-admin-layout overflow-y-auto">
        <Textarea
          v-model="content"
          fluid
          placeholder="在此输入报告内容..."
          rows="20"
        />

        <div class="grid grid-cols-3 gap-admin-layout">
          <div
            v-for="action of POLISH_ACTIONS"
            :key="action.id"
            class="cursor-pointer flex-col justify-center rounded-xl border border-solid border-divider bg-content p-3 flex-center"
            :class="[
              selectedAction === action.id
                ? '!border-blue-500 !bg-blue-50 !text-blue-500'
                : 'hover:!border-primary',
            ]"
            @click="() => {
              selectedAction = action.id;
            }"
          >
            <Component
              :is="action.icon"
              class="mb-2"
              :size="22"
            />

            <div class="font-medium">
              {{ action.label }}
            </div>

            <div class="mt-1 text-xs opacity-70">
              {{ action.description }}
            </div>
          </div>
        </div>

        <div>
          <Button
            :disabled="!content.trim()"
            fluid
            :loading="isGenerating"
            @click="handleAction(selectedAction)"
          >
            开始生成
          </Button>
        </div>
      </div>

      <CardContainer class="h-full">
        <div class="flex h-full flex-col">
          <div class="flex-1 overflow-y-auto p-card-container !pb-0">
            <div
              v-if="generatedContent"
              class="markdown-content"
              v-html="renderedContent"
            />

            <div
              v-else
              class="h-full justify-center flex-center text-secondary"
            >
              处理结果将显示在这里
            </div>
          </div>

          <div
            v-if="generatedContent"
            class="mt-auto p-card-container pt-0 flex-center"
          >
            <div class="gap-1.5 flex-center">
              <button
                v-tooltip.top="'复制'"
                class="rounded p-2 hover:bg-emphasis"
                @click="copy"
              >
                <CheckIcon
                  v-if="copied"
                  class="size-4 text-success-500"
                />
                <CopyIcon
                  v-else
                  class="size-4"
                />
              </button>

              <button
                v-tooltip.top="'重新生成'"
                class="rounded p-2 hover:bg-emphasis"
                @click="regenerateContent()"
              >
                <RefreshCwIcon class="size-4" />
              </button>
              <button
                v-tooltip.top="'取消'"
                class="rounded p-2 hover:bg-emphasis"
                @click="cancelGeneration"
              >
                <XIcon class="size-4" />
              </button>
            </div>

            <div class="ml-auto flex gap-admin-layout">
              <Button
                size="small"
                variant="outlined"
                @click="replaceContent()"
              >
                替换原文
              </Button>

              <Button
                size="small"
                @click="furtherOptimize()"
              >
                进一步优化
              </Button>
            </div>
          </div>
        </div>
      </CardContainer>
    </div>
  </div>
</template>
