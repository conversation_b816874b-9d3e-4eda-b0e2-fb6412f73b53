<script setup lang="ts">
import { WandSparklesIcon } from 'lucide-vue-next'
import markdownit from 'markdown-it'

import { useAIGenerator } from '~/composables/useAIGenerator'
import type { ERReportFormValues } from '~/types/tw-aigc'

definePageMeta({
  layoutConfig: {
    customContent: true,
  },
})

const md = markdownit()

const { $toast } = useNuxtApp()

const formValues = ref<Partial<ERReportFormValues>>({})

const { generate, stopGeneration, generatedContent, isGenerating } = useAIGenerator({
  onError: (err) => {
    $toast.error({
      summary: '生成报告失败',
      detail: err.message,
    })
  },
})

const renderedReport = computed(() => generatedContent.value ? md.render(generatedContent.value) : '')

const generatePrompt = () => {
  return `
以下是应急响应报告的初始内容：

# ${formValues.value.title || '应急响应报告'}公司/组织名称

## 声明
本报告是${formValues.value.companyName}对${formValues.value.clientName}进行应急响应后分析得出的客观结果，供甲方进行信息系统漏洞修补和安全加固参考，使用时需结合具体情况进行分析。
本报告仅适用于报告中明确提出的安全事件分析范围内的应用系统，且结论仅在安全事件处理期间的时段内有效，安全事件处理结束后如果被测网站系统出现任何变更或出现新的漏洞，本报告结论将不再适用。
在任何情况下，若需引用本报告中的结果或结论都应保持其原有的意义，不得对相关内容擅自进行增加、修改和伪造或掩盖事实。
本报告中出现的任何文字描述、文档格式、插图、照片、方法、过程等内容，除另有特别注明，版权均属${formValues.value.companyName}，受到版权法等有关法律的保护，任何个人、机构未经${formValues.value.companyName}的书面授权许可，不得以任何方式复制或引用本文件的任何片断。

${formValues.value.date}

## 一、应急响应服务
### 1.1 服务简介
${formValues.value.companyName}安全事件应急响应服务，对突发的网站入侵、拒绝服务攻击、大规模的病毒爆发、主机或网站异常事件等紧急安全问题提供全天候的技术支持，控制事态的发展；保护或恢复客户主机、网站服务的正常运行；分析并修补安全漏洞，提取攻击证据，跟踪并追查攻击源，保障系统的安全稳定运行。

### 1.2 应急人员
本次应急响应的参与人员如下表所示：

| 序号 | 姓名 | 日期 | 地点 |
|------|------|------|------|
${formValues.value.responders?.map((r, i) => `| ${i + 1} | ${r.name} | ${r.date} | ${r.location} |`).join('\n')}

### 1.3 问题基本信息
问题基本信息

| 问题名称 | ${formValues.value.problemName} |
|----------|------------------|
| 发生日期 | ${formValues.value.problemTime} |
| 问题概述 | ${formValues.value.problemDescription} |
| 紧急程度 | ${formValues.value.urgencyLevel} |
| 处理要求 | ${formValues.value.requirements} |

## 二、应急响应过程
### 2.1 安全分析过程
#### 2.1.1 攻击时间线分析
攻击时间线需日志提取后进一步分析还原

| 时间 | IP地址 | 黑客痕迹 |
|------|--------|----------|
${formValues.value.attackTimeline?.map((t) => `| ${t.time} | ${t.ip} | ${t.trace} |`).join('\n')}

#### 2.1.2 服务器上机分析
${
  formValues.value.serverAnalysis
    ?.map((s) => `
##### ${s.name}${s.description}
${s.findings}
`)
    .join('\n\n')}

## 三、分析结论
${formValues.value.conclusions}

## 四、后续建议
${formValues.value.recommendations}

请帮我润色和补充以上应急响应报告，保持专业性和完整性，并优化语言表达，使其更加流畅。最终输出的内容需要 markdown 格式，只需要输出完善后的内容，不要输出任何解释，开头不要添加"\`\`\`markdown"，结尾不要添加"\`\`\`"。
`
}

const handleGenerateReport = async () => {
  const prompt = generatePrompt()
  await generate({ prompt })
}

const handleStopGeneration = () => {
  stopGeneration()
}

const handleSetExampleData = () => {
  formValues.value = {
    title: '某公司服务器被入侵应急响应报告',
    companyName: '广东网安科技有限公司',
    clientName: '某科技有限公司',
    date: new Date().toLocaleDateString(),
    responders: [
      {
        name: '张三',
        date: new Date().toLocaleDateString(),
        location: '东莞',
      },
      {
        name: '李四',
        date: new Date().toLocaleDateString(),
        location: '广州',
      },
      {
        name: '王五',
        date: new Date().toLocaleDateString(),
        location: '深圳',
      },
    ],
    problemName: 'Web服务器被植入挖矿木马',
    problemTime: new Date().toLocaleDateString(),
    problemDescription: '客户反馈服务器CPU使用率异常，经排查发现服务器被植入挖矿木马程序',
    urgencyLevel: '紧急',
    requirements: '尽快清除木马，恢复服务器正常运行，并加固系统安全',
    attackTimeline: [
      {
        time: '2024-03-20',
        ip: '*************',
        trace: '发现可疑登录记录',
      },
      {
        time: '2024-03-21',
        ip: '*************',
        trace: '检测到异常进程',
      },
    ],
    serverAnalysis: [
      {
        name: '进程分析',
        description: '对服务器运行进程进行分析',
        findings: '发现异常进程xmrig.exe，该进程为已知的挖矿程序',
      },
      {
        name: '日志分析',
        description: '分析系统及应用日志',
        findings: '在系统日志中发现多次来自境外IP的暴力破解尝试',
      },
    ],
    analysisResults: '通过对服务器的全面分析，发现攻击者利用弱口令成功登录系统，并植入挖矿木马程序',
    conclusions: '本次事件为典型的服务器被入侵挖矿事件，主要原因是系统存在弱口令，且未及时更新安全补丁',
    recommendations: '1. 立即修改所有账户密码，确保密码强度\n2. 及时更新系统安全补丁\n3. 配置防火墙规则，限制远程访问\n4. 部署入侵检测系统',
  }
}
</script>

<template>
  <ProSplitter
    gutterClass="!w-admin-layout-gap"
    hideGutterLine
    :left="{ size: 50, minSize: 30 }"
    leftPanelContentClass="!pr-0"
    :right="{ size: 50, minSize: 30 }"
    rightPanelContentClass="!pl-0"
    wrapperClass="!bg-transparent"
  >
    <template #left>
      <CardContainer>
        <LayoutAdminContentHeader
          title="应急响应报告生成器"
          wrapperClass="!p-card-container !pb-0"
        >
          <template #titleRight>
            <Button
              label="生成报告"
              :loading="isGenerating"
              @click="handleGenerateReport()"
            >
              <template #icon>
                <WandSparklesIcon :size="14" />
              </template>
            </Button>
          </template>

          <template #description>
            <LayoutAdminContentDesc>
              请根据实际情况填写以下信息，生成应急响应报告
            </LayoutAdminContentDesc>

            <Button
              class="mt-0.5"
              label="填充示例数据"
              :pt="{
                root: {
                  class: '!p-0 !rounded-none',
                },
              }"
              severity="info"
              size="small"
              variant="text"
              @click="handleSetExampleData"
            />
          </template>
        </LayoutAdminContentHeader>

        <div class="size-full overflow-y-auto p-card-container pt-0">
          <Form>
            <div class="pb-in-fieldset">
              <Divider>
                <b>报告基本信息</b>
              </Divider>

              <div class="grid w-full grid-cols-2 gap-form-field">
                <FormItem
                  v-slot="{ id }"
                  label="报告标题"
                  name="title"
                >
                  <InputText
                    :id="id"
                    v-model="formValues.title"
                    fluid
                  />
                </FormItem>

                <FormItem
                  v-slot="{ id }"
                  label="公司名称"
                  name="companyName"
                >
                  <InputText
                    :id="id"
                    v-model="formValues.companyName"
                    fluid
                  />
                </FormItem>

                <FormItem
                  v-slot="{ id }"
                  label="客户名称"
                  name="clientName"
                >
                  <InputText
                    :id="id"
                    v-model="formValues.clientName"
                  />
                </FormItem>

                <FormItem
                  v-slot="{ id }"
                  label="报告日期"
                  name="date"
                >
                  <ProDatePicker
                    v-model="formValues.date"
                    :inputId="id"
                  />
                </FormItem>
              </div>
            </div>

            <div class="pb-in-fieldset">
              <Divider>
                <b>应急人员</b>
              </Divider>

              <FormItem>
                <AigcERResponderList v-model="formValues.responders" />
              </FormItem>
            </div>

            <div class="pb-in-fieldset">
              <Divider>
                <b>问题信息</b>
              </Divider>

              <div class="flex flex-col gap-form-field">
                <div class="grid w-full grid-cols-2 gap-form-field">
                  <FormItem
                    v-slot="{ id }"
                    label="问题名称"
                    name="problemName"
                  >
                    <InputText
                      :id="id"
                      v-model="formValues.problemName"
                    />
                  </FormItem>

                  <FormItem
                    v-slot="{ id }"
                    label="问题发生时间"
                    name="problemTime"
                  >
                    <ProDatePicker
                      v-model="formValues.problemTime"
                      :inputId="id"
                    />
                  </FormItem>
                </div>

                <FormItem
                  v-slot="{ id }"
                  label="问题描述"
                  name="problemDescription"
                >
                  <Textarea
                    :id="id"
                    v-model="formValues.problemDescription"
                    fluid
                    placeholder="请描述问题发生的时间、地点、现象、影响等"
                    rows="3"
                  />
                </FormItem>

                <FormItem
                  v-slot="{ id }"
                  label="紧急程度"
                  name="urgencyLevel"
                >
                  <ProSelect
                    v-model="formValues.urgencyLevel"
                    fluid
                    :labelId="id"
                    :options="[
                      { label: '紧急', value: '紧急' },
                      { label: '一般', value: '一般' },
                      { label: '轻微', value: '轻微' },
                    ]"
                    placeholder="请选择紧急程度"
                  />
                </FormItem>

                <FormItem
                  v-slot="{ id }"
                  label="要求"
                  name="requirements"
                >
                  <Textarea
                    :id="id"
                    v-model="formValues.requirements"
                    fluid
                    placeholder="请描述问题发生的时间、地点、现象、影响等"
                    rows="3"
                  />
                </FormItem>
              </div>
            </div>

            <div class="pb-in-fieldset">
              <Divider>
                <b>攻击时间线</b>
              </Divider>

              <FormItem>
                <AigcERAttackTimelineList v-model="formValues.attackTimeline" />
              </FormItem>
            </div>

            <div class="pb-in-fieldset">
              <Divider>
                <b>分析结果</b>
              </Divider>

              <div class="flex flex-col gap-form-field">
                <FormItem label="服务器分析内容">
                  <AigcERServerAnalysisList v-model="formValues.serverAnalysis" />
                </FormItem>

                <FormItem
                  v-slot="{ id }"
                  label="分析结果"
                  name="analysisResults"
                >
                  <Textarea
                    :id="id"
                    v-model="formValues.analysisResults"
                    rows="3"
                  />
                </FormItem>

                <FormItem
                  v-slot="{ id }"
                  label="分析结论"
                  name="conclusions"
                >
                  <Textarea
                    :id="id"
                    v-model="formValues.conclusions"
                    placeholder="请描述分析结果的结论"
                    rows="3"
                  />
                </FormItem>

                <FormItem
                  v-slot="{ id }"
                  label="安全建议"
                  name="recommendations"
                >
                  <Textarea
                    :id="id"
                    v-model="formValues.recommendations"
                    placeholder="请描述安全建议"
                    rows="3"
                  />
                </FormItem>
              </div>
            </div>
          </Form>
        </div>
      </CardContainer>
    </template>

    <template #right>
      <CardContainer>
        <LayoutAdminContentHeader
          title="报告预览"
          wrapperClass="!p-card-container !pb-0"
        >
          <template #titleRight>
            <div
              v-if="isGenerating"
              class="flex items-center gap-2"
            >
              <TextShimmer>
                内容生成中...
              </TextShimmer>

              <Button
                class="!p-0"
                label="停止生成"
                severity="danger"
                variant="text"
                @click="handleStopGeneration()"
              />
            </div>
          </template>
        </LayoutAdminContentHeader>

        <div class="size-full overflow-y-auto p-card-container">
          <div
            v-if="generatedContent"
            class="markdown-content"
            v-html="renderedReport"
          />

          <div
            v-else
            class="h-full justify-center flex-center"
          >
            <div class="text-center text-secondary">
              填写左侧报告信息，生成应急响应报告
            </div>
          </div>
        </div>
      </CardContainer>
    </template>
  </ProSplitter>
</template>
