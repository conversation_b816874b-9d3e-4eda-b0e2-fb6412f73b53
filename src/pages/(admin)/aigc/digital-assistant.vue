<script setup lang="ts">
import Shiki from '@shikijs/markdown-it'
import { ArrowUpIcon, BotIcon, CircleStopIcon, ListXIcon, MessageSquareShareIcon } from 'lucide-vue-next'
import markdownit from 'markdown-it'
import { nanoid } from 'nanoid'
import { Textarea } from 'primevue'

import ScrollGradientContainer from '~/components/ScrollGradientContainer.vue'
import { useAIGenerator } from '~/composables/useAIGenerator'
import { useLocalStorage } from '~/composables/useLocalStorage'
import type { AgentChatHistory, AgentRoleTitle, ChatHistory, ChatMessage } from '~/types/tw-aigc'

import { agentRoles, initialChatHistory } from './mock-data'

definePageMeta({
  title: 'AI 智能助手',
  description: '智能数字助手，为您提供即时、准确的对话式AI体验',
  layoutConfig: {
    customContent: true,
  },
})

// 创建基础的 markdown-it 实例
const md = markdownit({
  linkify: true,
  breaks: true,
})

// 标志 Shiki 是否已加载
const shikiLoaded = ref(false)

// 异步加载 Shiki
const loadShiki = async () => {
  if (!shikiLoaded.value) {
    md.use(await Shiki({
      themes: {
        light: 'material-theme-lighter',
        dark: 'github-dark',
      },
    }))
    shikiLoaded.value = true
  }
}

// 渲染 markdown 的函数，处理 Shiki 尚未加载的情况
const renderMarkdown = (content: string) => {
  return md.render(content)
}

const chatMessages = ref<ChatMessage[]>([])
const userInput = ref('')
const currentChatId = ref<string>(nanoid())
const userHasScrolled = ref(false)

const scrollGradientRef = ref<{ scrollRef: HTMLDivElement }>()
const { ref: inputRef, refKey: inputRefKey } = useRef<InstanceType<typeof Textarea> & { $el: HTMLTextAreaElement }>()

const focusInput = () => {
  inputRef.value?.$el.focus()
}

// 使用 localStorage 存储聊天历史，按助手分类
const agentChatHistories = useLocalStorage<AgentChatHistory>('agent-chat-histories', initialChatHistory)

const selectedAgentRole = ref(agentRoles.at(0))

// 获取当前助手的聊天历史
const currentAgentHistories = computed(() => {
  if (selectedAgentRole.value) {
    return agentChatHistories.value[selectedAgentRole.value.title]
  }

  return []
})

// 聊天历史预览文本（取第一条用户消息的前20个字符）
const getChatPreview = (messages: ChatMessage[]): string => {
  const firstUserMessage = messages.find((msg) => msg.role === 'user')

  if (!firstUserMessage) {
    return '新对话'
  }

  const preview = firstUserMessage.content.substring(0, 20)

  return preview + (firstUserMessage.content.length > 20 ? '...' : '')
}

const showErrorToast = (message: string) => {
  const { $toast } = useNuxtApp()
  $toast.error({
    summary: '错误',
    detail: message,
  })
}

const { generate, stopGeneration, generatedContent, isGenerating } = useAIGenerator({
  onError: (err) => {
    showErrorToast(err.message)
  },
  // 设置最大历史消息数和 token 限制，提高对话上下文连贯性
  maxHistoryMessages: 30,
})

// 添加一条用户消息
const addUserMessage = (content: string) => {
  if (!content.trim()) {
    return
  }

  chatMessages.value.push({
    id: nanoid(),
    content: content.trim(),
    role: 'user',
    timestamp: new Date(),
  })

  userInput.value = ''
}

const scrollToBottom = () => {
  if (!userHasScrolled.value) {
    nextTick(() => {
      if (scrollGradientRef.value?.scrollRef) {
        scrollGradientRef.value.scrollRef.scrollTop = scrollGradientRef.value.scrollRef.scrollHeight
      }
    })
  }
}

// 保存当前聊天记录到历史
const saveChatHistory = (roleOverride?: typeof selectedAgentRole.value, chatIdOverride?: string) => {
  if (chatMessages.value.length === 0) {
    return
  }

  const role = roleOverride || selectedAgentRole.value

  if (!role) {
    return
  }

  const agentTitle = role.title
  const chatId = chatIdOverride || currentChatId.value

  // 确保该助手的历史记录数组已初始化
  if (!agentChatHistories.value[agentTitle]) {
    agentChatHistories.value[agentTitle] = []
  }

  const existingIndex = agentChatHistories.value[agentTitle].findIndex((history) => history.id === chatId)
  const preview = getChatPreview(chatMessages.value)

  if (existingIndex >= 0) {
    // 更新现有历史
    agentChatHistories.value[agentTitle][existingIndex] = {
      ...agentChatHistories.value[agentTitle][existingIndex],
      messages: [...chatMessages.value],
      lastUpdated: new Date(),
      previewMessage: preview,
    }
  }
  else {
    // 创建新历史
    agentChatHistories.value[agentTitle].unshift({
      id: chatId,
      messages: [...chatMessages.value],
      lastUpdated: new Date(),
      previewMessage: preview,
    })
  }

  // 强制刷新历史记录列表，确保更新到 localStorage
  agentChatHistories.value = { ...agentChatHistories.value }
}

// 当前正在生成回复的消息ID，用于跟踪哪条消息应该接收生成内容
const currentGeneratingMessageId = ref<string | null>(null)
// 当前生成内容的会话ID，用于追踪哪个会话正在生成内容
const generatingChatId = ref<string | null>(null)
// 当前生成内容的角色，用于追踪哪个角色的聊天正在生成内容
const generatingRoleTitle = ref<AgentRoleTitle | null>(null)

// 发送消息
const sendMessage = async () => {
  if (!userInput.value.trim() || isGenerating.value || !selectedAgentRole.value) {
    return
  }

  const userMessage = userInput.value
  addUserMessage(userMessage)

  // 保存当前会话和角色信息，用于后续更新生成内容
  generatingChatId.value = currentChatId.value
  generatingRoleTitle.value = selectedAgentRole.value.title

  userHasScrolled.value = false
  scrollToBottom()

  try {
    // 创建一个空的助手消息，将在流式生成过程中更新
    const assistantMsgId = nanoid()
    currentGeneratingMessageId.value = assistantMsgId

    chatMessages.value.push({
      id: assistantMsgId,
      content: '',
      role: 'assistant',
      timestamp: new Date(),
    })

    // 先保存当前状态，确保新的空消息也被保存
    saveChatHistory(selectedAgentRole.value, currentChatId.value)

    // 生成回复，使用角色提示和对话历史
    const rolePrompt = selectedAgentRole.value.rolePrompt

    // 转换聊天历史格式以适应 AI SDK
    // 只保留必要的字段：content 和 role，排除最后一条空的助手消息
    const messageHistory = chatMessages.value
      .slice(0, -1)
      .map((msg) => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
      }))

    // 使用新的基于对话历史的 API
    await generate({
      messages: messageHistory,
      system: rolePrompt, // 将角色提示作为系统提示
    })

    // 生成完成后更新历史记录
    // 即使用户已经切换到其他聊天，也要正确更新生成内容
    if (generatingChatId.value && generatingRoleTitle.value) {
      // 获取生成内容的聊天历史
      const roleTitle = generatingRoleTitle.value as string
      const agentHistories = (agentChatHistories.value as Record<string, ChatHistory[]>)[roleTitle] || []
      const historyIndex = agentHistories.findIndex((history: ChatHistory) => history.id === generatingChatId.value)

      if (historyIndex !== -1) {
        // 找到对应消息并更新内容
        const messages = agentHistories[historyIndex].messages
        const messageIndex = messages.findIndex((msg: ChatMessage) => msg.id === currentGeneratingMessageId.value)

        if (messageIndex !== -1) {
          messages[messageIndex].content = generatedContent.value

          // 更新历史记录
          agentChatHistories.value = { ...agentChatHistories.value }
        }
      }
    }

    // 生成完成后重置状态
    currentGeneratingMessageId.value = null
    generatingChatId.value = null
    generatingRoleTitle.value = null
  }
  catch {
    showErrorToast('生成回复失败')
    currentGeneratingMessageId.value = null
    generatingChatId.value = null
    generatingRoleTitle.value = null
  }
}

// 监听用户滚动事件的函数
const handleScroll = () => {
  if (scrollGradientRef.value?.scrollRef) {
    const { scrollTop, scrollHeight, clientHeight } = scrollGradientRef.value.scrollRef
    // 如果用户向上滚动一定距离，则标记为用户已滚动
    const scrollBottom = scrollHeight - scrollTop - clientHeight

    if (scrollBottom > 50) {
      userHasScrolled.value = true
    }
    else {
      // 如果用户滚动到接近底部，重置标记
      userHasScrolled.value = false
    }
  }
}

// 选择历史聊天记录
const selectChatHistory = (historyId: string) => {
  focusInput()

  if (currentChatId.value === historyId) {
    return
  }

  // 保存当前聊天内容
  if (chatMessages.value.length > 0 && selectedAgentRole.value) {
    saveChatHistory(selectedAgentRole.value, currentChatId.value)
  }

  // 更新当前聊天ID
  currentChatId.value = historyId

  // 加载选中的聊天历史
  if (selectedAgentRole.value) {
    const agentTitle = selectedAgentRole.value.title
    const selected = agentChatHistories.value[agentTitle]?.find((history) => history.id === historyId)

    if (selected) {
      chatMessages.value = [...selected.messages]
    }

    // 强制刷新历史记录列表
    agentChatHistories.value = { ...agentChatHistories.value }
  }

  // 重置滚动控制
  userHasScrolled.value = false
  // 滚动到底部
  scrollToBottom()
}

const createNewChat = () => {
  // 如果正在生成，先停止当前生成
  if (isGenerating.value) {
    stopGeneration()
  }

  // 重置生成状态
  currentGeneratingMessageId.value = null

  if (chatMessages.value.length > 0 && selectedAgentRole.value) {
    saveChatHistory(selectedAgentRole.value, currentChatId.value)
  }

  focusInput()
  // 创建新对话
  currentChatId.value = nanoid()
  chatMessages.value = []

  // 强制刷新历史记录列表
  agentChatHistories.value = { ...agentChatHistories.value }

  // 重置滚动控制
  userHasScrolled.value = false
}

// 复制一个聊天会话并创建副本
const copyChatHistory = (historyId: string) => {
  if (!selectedAgentRole.value) {
    return
  }

  const agentTitle = selectedAgentRole.value.title

  // 查找要复制的聊天历史
  const chatToCopy = agentChatHistories.value[agentTitle]?.find(
    (history) => history.id === historyId,
  )

  if (!chatToCopy) {
    return
  }

  // 生成新的聊天ID
  const newChatId = nanoid()

  // 创建聊天副本（深拷贝消息内容）
  const chatCopy: ChatHistory = {
    id: newChatId,
    messages: JSON.parse(JSON.stringify(chatToCopy.messages)),
    lastUpdated: new Date(),
    previewMessage: `(副本)${chatToCopy.previewMessage} `,
  }

  // 添加到历史记录列表开头
  agentChatHistories.value[agentTitle].unshift(chatCopy)

  // 强制刷新历史记录列表
  agentChatHistories.value = { ...agentChatHistories.value }

  // 切换到新复制的聊天
  selectChatHistory(newChatId)
}

const deleteChatHistory = (historyId: ChatHistory['id']) => {
  if (!selectedAgentRole.value) {
    return
  }

  // 如果删除的是当前正在生成回复的聊天，停止生成
  if (isGenerating.value
    && generatingChatId.value === historyId
    && generatingRoleTitle.value === selectedAgentRole.value.title) {
    stopGeneration()
    currentGeneratingMessageId.value = null
    generatingChatId.value = null
    generatingRoleTitle.value = null
  }

  const agentTitle = selectedAgentRole.value.title

  if (!agentChatHistories.value[agentTitle]) {
    return
  }

  const index = agentChatHistories.value[agentTitle].findIndex(
    (history) => history.id === historyId,
  )

  if (index >= 0) {
    // 如果删除的是当前聊天，先将消息清空
    if (currentChatId.value === historyId) {
      chatMessages.value = []
    }

    // 从历史记录中删除
    agentChatHistories.value[agentTitle].splice(index, 1)

    // 强制触发 localStorage 更新
    agentChatHistories.value = { ...agentChatHistories.value }

    // 如果删除的是当前聊天，创建新聊天
    if (currentChatId.value === historyId) {
      currentChatId.value = nanoid()

      // 如果还有其他聊天记录，加载第一个
      if (agentChatHistories.value[agentTitle] && agentChatHistories.value[agentTitle].length > 0) {
        currentChatId.value = agentChatHistories.value[agentTitle][0].id
        chatMessages.value = [...agentChatHistories.value[agentTitle][0].messages]
      }
    }
  }
}

// 添加重命名聊天历史的函数
const renameChatHistory = (historyId: string, newTitle: string) => {
  if (!selectedAgentRole.value) {
    return
  }

  const agentTitle = selectedAgentRole.value.title

  if (!agentChatHistories.value[agentTitle]) {
    return
  }

  const index = agentChatHistories.value[agentTitle].findIndex(
    (history) => history.id === historyId,
  )

  if (index >= 0) {
    // 更新预览消息而不是标题
    agentChatHistories.value[agentTitle][index].previewMessage = newTitle

    // 强制触发 localStorage 更新
    agentChatHistories.value = { ...agentChatHistories.value }
  }
}

const handleStopGeneration = () => {
  stopGeneration()
  currentGeneratingMessageId.value = null
  generatingChatId.value = null
  generatingRoleTitle.value = null
}

// 清空上下文但保留聊天记录
const clearContext = () => {
  // 如果正在生成当前聊天的内容，则停止生成
  if (isGenerating.value
    && generatingChatId.value === currentChatId.value
    && generatingRoleTitle.value === selectedAgentRole.value?.title) {
    stopGeneration()
    currentGeneratingMessageId.value = null
    generatingChatId.value = null
    generatingRoleTitle.value = null
  }

  // 保存当前聊天历史到一个新的历史记录中
  if (chatMessages.value.length > 0 && selectedAgentRole.value) {
    saveChatHistory(selectedAgentRole.value, currentChatId.value)
  }

  // 清空当前消息但保持相同的会话ID
  chatMessages.value = []

  // 通过更新预览消息来提示用户上下文已清空
  if (selectedAgentRole.value) {
    const agentTitle = selectedAgentRole.value.title
    const existingIndex = agentChatHistories.value[agentTitle].findIndex(
      (history) => history.id === currentChatId.value,
    )

    if (existingIndex >= 0) {
      // 更新预览提示为"上下文已清空"
      agentChatHistories.value[agentTitle][existingIndex].previewMessage = '新对话（上下文已清空）'
      // 强制刷新历史记录列表
      agentChatHistories.value = { ...agentChatHistories.value }
    }
  }

  // 重置滚动控制
  userHasScrolled.value = false

  // 显示清空成功的提示
  const { $toast } = useNuxtApp()
  $toast.info({
    summary: '已清空上下文',
    detail: '您可以继续在当前会话中聊天',
  })
}

// 当生成的内容更新时，更新消息
watch(generatedContent, (newContent) => {
  // 只在有正在生成的消息ID和会话ID时才更新
  if (currentGeneratingMessageId.value && generatingChatId.value && generatingRoleTitle.value) {
    // 检查当前显示的是否就是正在生成内容的会话
    const isCurrentChat
      = currentChatId.value === generatingChatId.value
        && selectedAgentRole.value?.title === generatingRoleTitle.value

    // 如果当前显示的是正在生成内容的会话，则直接更新界面
    if (isCurrentChat) {
      const messageIndex = chatMessages.value.findIndex(
        (msg) => msg.id === currentGeneratingMessageId.value,
      )

      if (messageIndex !== -1) {
        chatMessages.value[messageIndex].content = newContent
        // 根据用户滚动状态决定是否滚动到底部
        scrollToBottom()
      }
    }

    // 同时更新存储中的记录，无论当前显示的是哪个会话
    const roleTitle = generatingRoleTitle.value as string
    const agentHistories = (agentChatHistories.value as Record<string, ChatHistory[]>)[roleTitle] || []
    const historyIndex = agentHistories.findIndex((history: ChatHistory) => history.id === generatingChatId.value)

    if (historyIndex !== -1) {
      const messages = agentHistories[historyIndex].messages
      const messageIndex = messages.findIndex((msg: ChatMessage) => msg.id === currentGeneratingMessageId.value)

      if (messageIndex !== -1) {
        messages[messageIndex].content = newContent

        // 不需要强制刷新历史记录，避免不必要的渲染
        // 最终生成完成后会进行完整更新
      }
    }
  }
})

const handleKeyDown = (ev: KeyboardEvent) => {
  // 检查是否正在使用输入法（composition事件）
  // isComposing 表示正在输入法组合键输入过程中
  if (ev.isComposing) {
    return
  }

  // 按下 Enter 键且没有按下 Shift 键时发送消息
  if (ev.key === 'Enter' && !ev.shiftKey) {
    ev.preventDefault()
    sendMessage()
  }
}

// 组件挂载后自动聚焦输入框
onMounted(() => {
  userHasScrolled.value = false
  scrollToBottom()

  // 在组件挂载后加载 Shiki，不会阻塞初始渲染
  loadShiki()
})

// 在每次添加新消息后滚动到底部
watch(() => chatMessages.value.length, () => {
  scrollToBottom()
})

// 当选择的助手角色变化时，加载该助手的聊天历史
watch(selectedAgentRole, (newRole, oldRole) => {
  if (newRole && oldRole && newRole.title !== oldRole.title) {
    // 保存当前聊天内容到旧角色的历史记录中
    if (chatMessages.value.length > 0) {
      saveChatHistory(oldRole, currentChatId.value)
    }

    // 创建新对话或加载该角色的第一个对话
    if (currentAgentHistories.value.length > 0) {
      // 加载该角色的第一个聊天记录
      const firstHistory = currentAgentHistories.value[0]
      currentChatId.value = firstHistory.id
      chatMessages.value = [...firstHistory.messages]
    }
    else {
      // 创建一个新对话
      currentChatId.value = nanoid()
      chatMessages.value = []
    }

    // 重置滚动控制并滚动到底部
    userHasScrolled.value = false
    scrollToBottom()
  }
}, { deep: true })

// 添加一个用于显示上下文状态的计算属性
const contextIndicator = computed(() => {
  if (chatMessages.value.length <= 2) {
    return null // 初始对话，不显示任何提示
  }

  // 除了最后两条消息（当前用户提问和正在生成的回复）外，有历史消息表示有上下文
  const hasContext = chatMessages.value.length > 2
  const contextCount = Math.max(0, chatMessages.value.length - 2)

  if (hasContext) {
    return {
      messageCount: contextCount,
      text: `AI 助手已使用${contextCount}条历史消息作为上下文`,
    }
  }

  return null
})

// 返回正在进行生成的聊天
const goToGeneratingChat = () => {
  if (generatingChatId.value && generatingRoleTitle.value) {
    // 如果角色不同，需要先切换角色
    const generatingRole = agentRoles.find((role) => role.title === generatingRoleTitle.value)

    if (generatingRole && selectedAgentRole.value?.title !== generatingRoleTitle.value) {
      selectedAgentRole.value = generatingRole
    }

    // 切换到正在生成的聊天
    selectChatHistory(generatingChatId.value)
  }
}

// 判断是否在不同的聊天中生成内容
const isGeneratingInDifferentChat = computed(() => {
  return isGenerating.value && generatingChatId.value && generatingRoleTitle.value
    && (currentChatId.value !== generatingChatId.value || selectedAgentRole.value?.title !== generatingRoleTitle.value)
})
</script>

<template>
  <div class="flex h-full gap-admin-layout">
    <CardContainer wrapperClass="basis-[300px]">
      <div class="flex h-full flex-col gap-2">
        <LayoutAdminContentTitle class="p-card-container pb-0">
          智能助手
        </LayoutAdminContentTitle>

        <div class="flex-1 space-y-2 overflow-y-auto p-card-container">
          <div
            v-for="role of agentRoles"
            :key="role.title"
            class="group/node-content group cursor-pointer rounded-lg px-2.5 py-2 hover:bg-emphasis"
            :class="{
              'bg-emphasis': selectedAgentRole?.title === role.title,
            }"
            @click="selectedAgentRole = role"
          >
            <div class="gap-2 flex-center">
              <div class="flex-center">
                <img
                  class="size-10"
                  :src="role.avatar"
                >
              </div>

              <div class="min-w-0 flex-1">
                <div class="truncate">
                  {{ role.title }}
                </div>

                <div
                  class="mt-1 truncate text-xs text-secondary"
                  :title="role.description"
                >
                  {{ role.description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </CardContainer>

    <CardContainer>
      <div class="flex h-full bg-content">
        <ProSplitter
          leftPanelContentClass="!pr-0"
          rightPanelContentClass="!pl-0"
        >
          <template #left>
            <!-- 聊天历史侧边栏 -->
            <AigcChatHistory
              :currentChatId="currentChatId"
              :histories="currentAgentHistories"
              @copyChat="copyChatHistory"
              @createNewChat="createNewChat"
              @deleteChat="deleteChatHistory"
              @renameChat="renameChatHistory"
              @selectChat="selectChatHistory"
            />
          </template>

          <template #right>
            <!-- 主聊天区域 -->
            <div class="flex h-full flex-col">
              <!-- 头部 -->
              <div class="h-12 border-b border-divider px-4">
                <div class="h-full flex-center">
                  <div
                    v-if="selectedAgentRole"
                    class="gap-2 flex-center"
                  >
                    <div class="flex-center">
                      <img
                        class="size-6"
                        :src="selectedAgentRole.avatar"
                      >
                    </div>

                    <div class="font-semibold">
                      {{ selectedAgentRole.title }}
                    </div>
                  </div>

                  <div class="ml-auto gap-2 flex-center">
                    <PopupMenu
                      :options="[
                        {
                          label: '分享当前聊天',
                          itemIcon: MessageSquareShareIcon,
                        },
                        {
                          label: '清空上下文',
                          itemIcon: ListXIcon,
                          command: () => {
                            clearContext()
                          },
                        },
                      ]"
                    >
                      <ProBtnMore
                        onlyIcon
                        size="small"
                        variant="text"
                      />
                    </PopupMenu>
                  </div>
                </div>
              </div>

              <!-- 聊天区域 -->
              <ScrollGradientContainer
                ref="scrollGradientRef"
                scrollClassName="px-card-container"
                @scroll="handleScroll"
              >
                <div class="mx-auto max-w-3xl py-6">
                  <!-- 欢迎信息 -->
                  <div
                    v-if="chatMessages.length === 0"
                    class="flex flex-col items-center justify-center rounded-lg p-10 text-center"
                  >
                    <BotIcon
                      class="mb-4 text-primary"
                      :size="48"
                    />
                    <h2 class="mb-2 text-xl font-semibold">
                      欢迎使用智能助手
                    </h2>
                    <p class="mb-4 text-secondary">
                      我可以回答问题、提供信息、协助您完成各种任务。请在下方输入您的问题。
                    </p>
                  </div>

                  <!-- 聊天消息列表 -->
                  <div
                    v-else
                    class="space-y-6"
                  >
                    <div
                      v-for="message of chatMessages"
                      :key="message.id"
                      class="group flex"
                      :class="[
                        message.role === 'user' ? 'justify-end' : 'justify-start',
                      ]"
                    >
                      <!-- 消息内容 -->
                      <div
                        :class="[
                          message.role === 'user'
                            ? 'rounded-lg bg-emphasis p-2'
                            : 'markdown-content',
                        ]"
                      >
                        <!-- 用户消息 -->
                        <div v-if="message.role === 'user'">
                          <p class="whitespace-pre-wrap">
                            {{ message.content }}
                          </p>
                        </div>

                        <!-- AI消息 -->
                        <div v-else>
                          <div v-html="renderMarkdown(message.content)" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    v-if="isGenerating && chatMessages.length > 0 && chatMessages[chatMessages.length - 1].role === 'assistant' && !chatMessages[chatMessages.length - 1].content"
                  >
                    <TextShimmer>助手正在输入...</TextShimmer>

                    <!-- 显示上下文提示 -->
                    <div
                      v-if="contextIndicator"
                      class="mt-2 text-xs text-secondary"
                    >
                      {{ contextIndicator.text }}
                    </div>
                  </div>
                </div>
              </ScrollGradientContainer>

              <!-- 输入区域 -->
              <div class="p-card-container pt-0">
                <div class="rounded-xl border border-divider p-2">
                  <!-- 当正在后台生成内容且不在当前聊天时显示提示 -->
                  <div v-if="isGeneratingInDifferentChat">
                    <div class="gap-2 px-1 pb-2 text-sm flex-center">
                      <span>正在后台为另一个聊天生成内容...</span>
                      <Button
                        class="!p-0"
                        label="查看"
                        size="small"
                        variant="text"
                        @click="goToGeneratingChat()"
                      />
                    </div>
                  </div>

                  <div class="rounded-lg border border-divider bg-gradient-to-b from-surface-100 to-transparent dark:from-surface-800">
                    <Textarea
                      :ref="inputRefKey"
                      v-model="userInput"
                      class="!resize-none !border-none !bg-transparent !pb-8 !shadow-none"
                      fluid
                      placeholder="问 AI 任何问题..."
                      rows="2"
                      @keydown="handleKeyDown"
                    />
                  </div>

                  <div class="mt-2 gap-5 px-1.5 text-xs flex-center text-secondary">
                    按 Enter 发送，按 Shift+Enter 换行

                    <div class="ml-auto gap-2">
                      <Button
                        v-if="isGenerating"
                        label="停止生成"
                        size="small"
                        variant="outlined"
                        @click.stop="handleStopGeneration()"
                      >
                        <template #icon>
                          <CircleStopIcon :size="14" />
                        </template>
                      </Button>

                      <Button
                        v-else
                        label="发送消息"
                        size="small"
                        @click.stop="sendMessage()"
                      >
                        <template #icon>
                          <ArrowUpIcon :size="14" />
                        </template>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </ProSplitter>
      </div>
    </CardContainer>
  </div>
</template>
