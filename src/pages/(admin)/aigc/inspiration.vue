<script setup lang="ts">
import { BookIcon, BookOpenIcon, FileTextIcon, GitBranchIcon, GraduationCapIcon, LayoutIcon, MessageSquareIcon, NewspaperIcon, PencilIcon, PresentationIcon, SparklesIcon, TwitterIcon } from 'lucide-vue-next'
import markdownit from 'markdown-it'

import { useAIGenerator } from '~/composables/useAIGenerator'
import type { ProSelectOption } from '~/types/pro'

definePageMeta({
  layoutConfig: {
    customContent: true,
  },
})

const md = markdownit()

const { $toast } = useNuxtApp()

const formValues = ref<InspirationFormValues>({
  inspirationType: '随便写写',
  inspirationContent: '',
  languageStyle: '正式',
  contentLength: '不限',
})

const { generate, stopGeneration, generatedContent, isGenerating } = useAIGenerator({
  onError: (err) => {
    $toast.error({
      summary: '生成内容失败',
      detail: err.message,
    })
  },
})

const renderedContent = computed(() => generatedContent.value ? md.render(generatedContent.value) : '')

const inspirationTips = [
  '没灵感？在左侧选择类型并输入主题，一键开启 AI 创作之旅',
  '灵活运用 AI 工具箱，根据不同创作需求选择合适功能，持续更新中',
  '选中现有文本内容，配合 AI 工具共同探索创作新可能',
]

const inspirationTypes: ProSelectOption[] = [
  { value: '随便写写', label: '随便写写', description: '自由发挥，无限创意', icon: PencilIcon },
  { value: '文档大纲', label: '文档大纲', description: '结构化文档框架', icon: FileTextIcon },
  { value: 'PPT 大纲', label: 'PPT 大纲', description: '演示文稿结构', icon: PresentationIcon },
  { value: '思维导图', label: '思维导图', description: '可视化思维结构', icon: GitBranchIcon },
  { value: '内容完善', label: '内容完善', description: '充实现有内容', icon: LayoutIcon },
  { value: '故事创作', label: '故事创作', description: '创意故事写作', icon: BookIcon },
  { value: '产品描述', label: '产品描述', description: '产品文案创作', icon: SparklesIcon },
  { value: '博客文章', label: '博客文章', description: '博客内容创作', icon: BookOpenIcon },
  { value: '社交媒体', label: '社交媒体', description: '社交平台内容', icon: TwitterIcon },
  { value: '新闻稿', label: '新闻稿', description: '新闻稿件写作', icon: NewspaperIcon },
  { value: '诗歌创作', label: '诗歌创作', description: '诗歌艺术创作', icon: PencilIcon },
  { value: '脚本/对话', label: '脚本/对话', description: '对话脚本创作', icon: MessageSquareIcon },
  { value: '学术论文', label: '学术论文', description: '学术内容写作', icon: GraduationCapIcon },
]

const languageStyleOptions = [
  { value: '正式', label: '正式' },
  { value: '轻松', label: '轻松' },
  { value: '专业', label: '专业' },
  { value: '幽默', label: '幽默' },
]

const contentLengthOptions = [
  { value: '不限', label: '不限' },
  { value: '100字', label: '100字' },
  { value: '300字', label: '300字' },
  { value: '500字', label: '500字' },
  { value: '1000字', label: '1000字' },
  { value: '2000字', label: '2000字' },
]

const generatePrompt = () => {
  return `
请根据以下要求生成内容：

创作主题或想法：${formValues.value.inspirationContent || '请自由发挥'}
类型：${formValues.value.inspirationType}
语言风格：${formValues.value.languageStyle}
内容长度：${formValues.value.contentLength}

请直接生成符合要求的内容，不需要添加额外的解释或说明。如果是文章，需要有合适的标题和段落结构。生成的内容应当有创意、富有洞察力且逻辑清晰。
`
}

const handleGenerate = async () => {
  const prompt = generatePrompt()
  await generate({ prompt })
}

const handleStopGeneration = () => {
  stopGeneration()
}
</script>

<template>
  <ProSplitter
    gutterClass="!w-admin-layout-gap"
    hideGutterLine
    :left="{ size: 40, minSize: 35 }"
    leftPanelContentClass="!pr-0"
    :right="{ size: 60, minSize: 35 }"
    rightPanelContentClass="!pl-0"
    wrapperClass="!bg-transparent"
  >
    <template #left>
      <CardContainer>
        <LayoutAdminContentHeader
          title="灵感中心"
          wrapperClass="!p-card-container !pb-0"
        >
          <template #description>
            <LayoutAdminContentDesc class="pt-2">
              <Message severity="secondary">
                <div class="text-sm font-normal text-secondary">
                  <div class="pb-1.5 font-semibold">
                    如何和小助手一起共创内容？
                  </div>

                  <ul class="!list-inside !list-disc space-y-1">
                    <li
                      v-for="tip of inspirationTips"
                      :key="tip"
                    >
                      {{ tip }}
                    </li>
                  </ul>
                </div>
              </Message>
            </LayoutAdminContentDesc>
          </template>
        </LayoutAdminContentHeader>

        <div class="size-full overflow-y-auto p-card-container pt-0">
          <Form>
            <div class="flex flex-col gap-form-field">
              <FormItem label="选择灵感类型">
                <ProSelect
                  v-model="formValues.inspirationType"
                  fluid
                  :options="inspirationTypes"
                  placeholder="选择灵感类型"
                />
              </FormItem>

              <FormItem label="告诉我你想写什么?">
                <Textarea
                  v-model="formValues.inspirationContent"
                  autoResize
                  placeholder="输入你的创作主题或想法..."
                  rows="5"
                />
              </FormItem>

              <FormItem label="语言风格">
                <ProSelect
                  v-model="formValues.languageStyle"
                  fluid
                  :options="languageStyleOptions"
                  placeholder="选择语言风格"
                />
              </FormItem>

              <FormItem label="文章篇幅">
                <ProSelect
                  v-model="formValues.contentLength"
                  fluid
                  :options="contentLengthOptions"
                  placeholder="选择内容长度"
                />
              </FormItem>

              <RainbowGradient :disabled="isGenerating">
                <Button
                  fluid
                  label="开始创作"
                  :loading="isGenerating"
                  size="large"
                  @click="handleGenerate()"
                >
                  <template #icon>
                    <SparklesIcon :size="16" />
                  </template>
                </Button>
              </RainbowGradient>
            </div>
          </Form>
        </div>
      </CardContainer>
    </template>

    <template #right>
      <CardContainer>
        <LayoutAdminContentHeader
          title="创作区域"
          wrapperClass="!p-card-container !pb-0"
        >
          <template #titleRight>
            <div
              v-if="isGenerating"
              class="flex items-center gap-2"
            >
              <TextShimmer>
                内容生成中...
              </TextShimmer>

              <Button
                class="!p-0"
                label="停止生成"
                severity="danger"
                variant="text"
                @click="handleStopGeneration()"
              />
            </div>
          </template>
        </LayoutAdminContentHeader>

        <div class="size-full overflow-y-auto p-card-container">
          <div
            v-if="generatedContent"
            class="markdown-content"
            v-html="renderedContent"
          />

          <div
            v-else
            class="h-full flex-col justify-center flex-center text-secondary"
          >
            选择灵感类型并输入主题开始创作
          </div>
        </div>
      </CardContainer>
    </template>
  </ProSplitter>
</template>
