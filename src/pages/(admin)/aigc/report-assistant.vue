<script setup lang="ts">
import {
  AwardIcon,
  ClipboardIcon,
  CopyIcon,
  FileSignatureIcon,
  FileTextIcon,
  HeartIcon,
  XIcon,
  ZapIcon,
} from 'lucide-vue-next'
import markdownit from 'markdown-it'
import type { MenuItem as PrimeMenuItem } from 'primevue/menuitem'

const reportMenuItems = ref<PrimeMenuItem[]>([
  {
    key: 'document',
    label: '公文写作',
    data: { icon: FileTextIcon }, // 将图标存储在data中
    items: [
      {
        key: 'situation',
        label: '情况汇报',
      },
      {
        key: 'job',
        label: '述职报告',
      },
    ],
  },
  {
    key: 'report',
    label: '各类报告',
    data: { icon: ClipboardIcon },
    items: [
      {
        key: 'study',
        label: '学习报告',
      },
      {
        key: 'improvement',
        label: '整改报告',
      },
      {
        key: 'selfreview',
        label: '自查报告',
      },
      {
        key: 'inspection',
        label: '考察报告',
      },
    ],
  },
  {
    key: 'thoughts',
    label: '思想与感悟',
    data: { icon: HeartIcon },
    items: [
      {
        key: 'ideological',
        label: '思想感悟',
      },
      {
        key: 'work',
        label: '工作感悟',
      },
      {
        key: 'speech',
        label: '讲话心得',
      },
      {
        key: 'xx',
        label: '学习心得',
      },
      {
        key: 'meeting',
        label: '大会心得',
      },
      {
        key: 'experience',
        label: '心得体会',
      },
    ],
  },
  {
    key: 'achievement',
    label: '事迹材料',
    data: { icon: AwardIcon },
    items: [
      {
        key: 'advanced',
        label: '先进事迹',
      },
      {
        key: 'typical',
        label: '典型事迹',
      },
      {
        key: 'main',
        label: '主要事迹',
      },
      {
        key: 'post',
        label: '岗位事迹',
      },
      {
        key: 'professional',
        label: '敬业事迹',
      },
    ],
  },
  {
    key: 'other',
    label: '其他文档',
    data: { icon: FileSignatureIcon },
    items: [
      {
        key: 'anti_fraud',
        label: '反诈宣传',
      },
      {
        key: 'rectification_plan',
        label: '整改方案',
      },
      {
        key: 'speech_material',
        label: '发言材料',
      },
      {
        key: 'self_criticism',
        label: '检讨书',
      },
      {
        key: 'speech_draft',
        label: '演讲稿',
      },
      {
        key: 'proposal',
        label: '倡议书',
      },
      {
        key: 'thank_letter',
        label: '感谢信',
      },
      {
        key: 'comparison_check',
        label: '对照检查',
      },
      {
        key: 'emergency_plan',
        label: '应急预案',
      },
      {
        key: 'organizational_life',
        label: '组织生活会',
      },
    ],
  },
])

const expandedKeys = ref<Record<string, boolean>>({
  document: true,
  report: true,
  thoughts: true,
})

const formData = ref({
  title: '', // 写作主题
  assistInfo: '', // 辅助信息
  wordCount: 800, // 默认800字
})

// 字数选项
const wordCountOptions = [
  { label: '100字', value: 100 },
  { label: '200字', value: 200 },
  { label: '500字', value: 500 },
  { label: '800字', value: 800 },
  { label: '1000字', value: 1000 },
  { label: '2000字', value: 2000 },
  { label: '4000字', value: 4000 },
]

const selectedReportType = ref('')
const showForm = ref(false)

// 处理菜单项选择
const handleReportTypeSelect = (item: PrimeMenuItem) => {
  if (!item.items) {
    selectedReportType.value = item.key as string
    showForm.value = true

    // 设置表单默认值 - 直接使用菜单项的标签
    formData.value.title = item.label as string
    formData.value.assistInfo = `请为我生成一份${item.label}，内容要求专业、严谨、符合行业标准。`
  }
}

const { generate, stopGeneration, generatedContent, isGenerating } = useAIGenerator()

const md = markdownit()

const renderedContent = computed(() => generatedContent.value ? md.render(generatedContent.value) : '')

const { $toast } = useNuxtApp()

// 提交生成请求
const handleGenerate = async () => {
  if (!formData.value.title.trim()) {
    $toast.warn({
      summary: '提示',
      detail: '请填写写作主题',
    })

    return
  }

  const prompt = `
请生成一篇${formData.value.title}，字数控制在${formData.value.wordCount}字左右。

${formData.value.assistInfo || ''}
  `.trim()

  await generate({ prompt })
}

// 复制内容
const copyContent = async () => {
  await copyToClipboard(generatedContent.value)

  $toast.success({
    summary: '成功',
    detail: '内容已复制到剪贴板',
  })
}
</script>

<template>
  <div class="flex h-full gap-admin-layout-content">
    <!-- 左侧菜单 -->
    <div class="flex h-full min-w-60 flex-col rounded-lg border border-divider">
      <h3 class="mb-4 p-3 pb-0 text-lg font-medium">
        报告类型
      </h3>

      <div class="flex-1 overflow-y-auto p-3 pt-0">
        <SideMenuPanel
          :expandedKeys="expandedKeys"
          :menuItems="reportMenuItems"
          :selectedKey="selectedReportType"
          @select="handleReportTypeSelect"
          @update:expandedKeys="expandedKeys = $event"
        />
      </div>
    </div>

    <!-- 右侧内容区 -->
    <div class="flex-1">
      <div class="flex flex-col gap-form-field">
        <FormItem
          label="写作主题"
          required
        >
          <InputText
            v-model="formData.title"
            class="w-full"
            placeholder="请输入写作主题"
          />
        </FormItem>

        <FormItem label="辅助信息（选填）">
          <Textarea
            v-model="formData.assistInfo"
            class="w-full"
            placeholder="请输入辅助信息，如专业要求、内容要点等"
            rows="4"
          />
        </FormItem>

        <FormItem label="字数">
          <RadioButtonGroup
            v-model="formData.wordCount"
            name="wordCount"
          >
            <div class="flex flex-wrap gap-4">
              <div
                v-for="option of wordCountOptions"
                :key="option.value"
                class="flex items-center"
              >
                <RadioButton
                  :inputId="'wordCount_' + option.value"
                  :value="option.value"
                />
                <label
                  class="ml-2"
                  :for="'wordCount_' + option.value"
                >{{ option.label }}</label>
              </div>
            </div>
          </RadioButtonGroup>
        </FormItem>

        <div class="flex">
          <Button
            class="mr-2"
            :disabled="isGenerating"
            label="开始创作"
            :loading="isGenerating"
            @click="handleGenerate"
          >
            <template #icon>
              <ZapIcon :size="14" />
            </template>
          </Button>

          <Button
            v-if="isGenerating"
            label="停止生成"
            outlined
            severity="secondary"
            @click="stopGeneration"
          >
            <template #icon>
              <XIcon :size="16" />
            </template>
          </Button>
        </div>
      </div>

      <!-- 生成结果区域 -->
      <div
        v-if="generatedContent"
        class="mt-6 rounded-lg border border-divider p-4"
      >
        <div class="mb-2 justify-between flex-center">
          <h3 class="text-lg font-medium">
            生成结果
          </h3>

          <Button
            v-if="generatedContent && !isGenerating"
            rounded
            text
            tooltip="复制内容"
            @click="copyContent"
          >
            <template #icon>
              <CopyIcon :size="16" />
            </template>
          </Button>
        </div>

        <div
          v-if="generatedContent"
          class="markdown-content"
          v-html="renderedContent"
        />
      </div>
    </div>
  </div>
</template>
