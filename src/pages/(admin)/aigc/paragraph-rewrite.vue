<script setup lang="ts">
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CircleAlertIcon,
  CopyIcon,
  HistoryIcon,
  Trash2Icon,
  TrashIcon,
  UndoDotIcon,
  ZapIcon,
} from 'lucide-vue-next'

import { styleOptions, temperatureOptions } from '~/constants-tw/aigc'
import type { RewriteHistoryItem } from '~/types/tw-aigc'

definePageMeta({
  title: '智能文本魔改大师',
  description: '重构文章、论文、书籍或社交媒体帖子中的文字内容，使你的创作独具特色，彻底告别抄袭困扰',
})

const activeStyle = ref('standard')
const activeTemperature = ref('medium')
// 保存先前的风格和温度设置，用于在用户拒绝中断生成时恢复
const previousStyle = ref('standard')
const previousTemperature = ref('medium')
const inputText = ref('')
const isCopySuccess = ref(false)
const showHistoryPanel = ref(false)

const historyGenerations = useLocalStorage<RewriteHistoryItem[]>('paragraph-rewrite-history', [])

const currentHistoryIndex = ref(-1)
const isViewingHistory = ref(false)

/** 是否显示生成提示 */
const showGenerateHint = ref(false)

const { generate, stopGeneration, generatedContent, isGenerating } = useAIGenerator()

// 保存当前生成内容到历史记录
const saveToHistory = () => {
  if (generatedContent.value.trim() && !isViewingHistory.value) {
    historyGenerations.value.unshift({
      style: activeStyle.value,
      temperature: activeTemperature.value,
      content: generatedContent.value,
      originalText: inputText.value,
      timestamp: Date.now(),
    })
  }
}

// 监听生成状态，当生成完成时保存历史记录
watch(isGenerating, (newVal, oldVal) => {
  if (oldVal && !newVal && generatedContent.value.trim()) {
    saveToHistory()
  }
})

const inputTextCount = computed(() => inputText.value.length)

// 当前显示的内容
const displayedContent = computed(() => {
  if (isViewingHistory.value && currentHistoryIndex.value >= 0) {
    return historyGenerations.value[currentHistoryIndex.value].content
  }

  return generatedContent.value
})

// 显示历史内容的标题
const historyTitle = computed(() => {
  if (isViewingHistory.value && currentHistoryIndex.value >= 0) {
    const historyItem = historyGenerations.value[currentHistoryIndex.value]
    const styleName = styleOptions.find((option) => option.value === historyItem.style)?.label || historyItem.style

    return `历史改写结果 - ${styleName}风格`
  }

  return '改写后的文本'
})

const getStyleLabel = (styleValue: string) => {
  return styleOptions.find((option) => option.value === styleValue)?.label || styleValue
}

const getTemperatureLabel = (tempValue: string) => {
  return temperatureOptions.find((option) => option.value === tempValue)?.label || tempValue
}

// 用于处理选项变更（风格或创意程度）的通用方法
const handleOptionChange = (optionType: 'style' | 'temperature', newValue: string, previousValue: Ref<string>) => {
  // 保存当前选项值，以便在需要时恢复
  previousValue.value = newValue

  // 如果正在生成内容，停止当前生成
  if (isGenerating.value) {
    stopGeneration()

    // 设置提示信息，告知用户需要重新生成
    showGenerateHint.value = true

    return true
  }

  // 处理选项变更后的状态
  if (generatedContent.value && !isGenerating.value && !isViewingHistory.value) {
    generatedContent.value = ''
    showGenerateHint.value = true
  }
  else if (inputText.value.trim() && !generatedContent.value) {
    showGenerateHint.value = true
  }

  isViewingHistory.value = false

  return false
}

// 监听风格变化
watch(activeStyle, (newValue) => {
  handleOptionChange('style', newValue, previousStyle)
})

// 监听创意程度变化
watch(activeTemperature, (newValue) => {
  handleOptionChange('temperature', newValue, previousTemperature)
})

// 生成文本
const rewriteText = async () => {
  if (!inputText.value.trim() || isGenerating.value) {
    return
  }

  isViewingHistory.value = false
  showGenerateHint.value = false

  const currentStyleOption = styleOptions.find((option) => option.value === activeStyle.value)
  const stylePrompt = currentStyleOption?.prompt || ''

  const currentTemperature = temperatureOptions.find((option) => option.value === activeTemperature.value)?.temperature || 0.7

  const prompt = `
请将以下文本改写成${activeStyle.value}风格，确保改写后内容流畅自然，表达清晰，能绕过AI检测:

${stylePrompt}

原文:
${inputText.value}

====

直接输出改写后的内容，不要输出任何解释或说明。
`

  await generate({
    prompt,
    modelName: 'deepseek-chat',
    temperature: currentTemperature,
  })

  // 生成完成后的保存逻辑已通过 watch isGenerating 处理
}

const copy = async () => {
  // 复制显示的内容（可能是当前生成内容或历史内容）
  await copyToClipboard(displayedContent.value)

  isCopySuccess.value = true

  setTimeout(() => {
    isCopySuccess.value = false
  }, 2000)
}

const handleStopGeneration = () => {
  stopGeneration()

  // 停止生成后，无论内容多少都保存到历史记录
  // 即使内容很少，也可能是用户需要的
  if (generatedContent.value) {
    historyGenerations.value.unshift({
      style: activeStyle.value,
      temperature: activeTemperature.value,
      content: generatedContent.value,
      originalText: inputText.value,
      timestamp: Date.now(),
    })
  }
}

// 历史内容浏览功能
const viewHistory = (index: number) => {
  if (historyGenerations.value.length > 0) {
    isViewingHistory.value = true
    currentHistoryIndex.value = index
    // 将原文设置到左侧输入框
    inputText.value = historyGenerations.value[index].originalText
    showHistoryPanel.value = false
  }
}

// 退出历史查看模式
const exitHistoryView = () => {
  isViewingHistory.value = false
}

const hasHistory = computed(() => historyGenerations.value.length > 0)

// 历史记录展开状态
const expandedHistoryItems = ref<Set<number>>(new Set())

// 切换历史记录的展开状态
const toggleHistoryItemExpand = (index: number, event: Event) => {
  event.stopPropagation() // 阻止冒泡，防止触发viewHistory

  if (expandedHistoryItems.value.has(index)) {
    expandedHistoryItems.value.delete(index)
  }
  else {
    expandedHistoryItems.value.add(index)
  }
}

// 删除单个历史记录
const removeHistoryItem = (index: number) => {
  // 如果正在查看被删除的历史记录，退出历史查看模式
  if (isViewingHistory.value && currentHistoryIndex.value === index) {
    isViewingHistory.value = false
  }
  else if (isViewingHistory.value && currentHistoryIndex.value > index) {
    // 如果删除的是当前查看的历史记录之前的记录，需要调整当前索引
    currentHistoryIndex.value--
  }

  // 从展开集合中移除
  if (expandedHistoryItems.value.has(index)) {
    expandedHistoryItems.value.delete(index)
  }

  // 更新展开状态的索引
  const newExpandedItems = new Set<number>()
  expandedHistoryItems.value.forEach((itemIndex) => {
    if (itemIndex < index) {
      newExpandedItems.add(itemIndex)
    }
    else if (itemIndex > index) {
      newExpandedItems.add(itemIndex - 1)
    }
  })
  expandedHistoryItems.value = newExpandedItems

  // 移除历史记录
  historyGenerations.value.splice(index, 1)
}

// 清空所有历史记录
const clearAllHistory = () => {
  // 如果正在查看历史记录，退出历史查看模式
  if (isViewingHistory.value) {
    isViewingHistory.value = false
  }

  // 清空展开状态
  expandedHistoryItems.value.clear()

  // 清空历史记录
  historyGenerations.value = []
}
</script>

<template>
  <div class="w-[clamp(400px,100%,1400px)]">
    <div class="flex flex-col gap-form-field">
      <FormItem
        label="风格选项"
        layout="horizontal"
      >
        <ProSelectButton
          v-model="activeStyle"
          :options="styleOptions"
        >
          <template #option="{ option }">
            <div class="gap-2 flex-center">
              <Component
                :is="option.icon"
                class="shrink-0"
                :size="15"
              />
              <span>{{ option.label }}</span>
            </div>
          </template>
        </ProSelectButton>
      </FormItem>

      <FormItem
        label="创意程度"
        layout="horizontal"
      >
        <ProSelectButton
          v-model="activeTemperature"
          :options="temperatureOptions"
        >
          <template #option="{ option }">
            <div class="gap-2 flex-center">
              <Component
                :is="option.icon"
                class="shrink-0"
                :size="15"
              />
              <span>{{ option.label }}</span>
            </div>
          </template>
        </ProSelectButton>
      </FormItem>

      <div class="grid grid-cols-2 gap-form-field">
        <!-- 当前风格的提示内容 -->
        <div class="rounded-lg border border-primary/20 bg-primary/5 p-3">
          <div class="mb-1 flex items-center gap-2">
            <Component
              :is="styleOptions.find(option => option.value === activeStyle)?.icon"
              class="text-primary"
              :size="16"
            />
            <strong class="text-primary">{{ getStyleLabel(activeStyle) }}风格</strong>
          </div>
          <p class="text-sm text-secondary">
            {{ styleOptions.find(option => option.value === activeStyle)?.prompt }}
          </p>
        </div>

        <!-- 当前温度选项的提示内容 -->
        <div class="rounded-lg border border-primary/20 bg-primary/5 p-3">
          <div class="mb-1 flex items-center gap-2">
            <Component
              :is="temperatureOptions.find(option => option.value === activeTemperature)?.icon"
              class="text-primary"
              :size="16"
            />
            <strong class="text-primary">{{ temperatureOptions.find(option => option.value === activeTemperature)?.label }}模式</strong>
          </div>
          <p class="text-sm text-secondary">
            {{ temperatureOptions.find(option => option.value === activeTemperature)?.description }}
          </p>
        </div>
      </div>

      <!-- 生成提示 -->
      <Message
        v-if="showGenerateHint && inputText.trim() && !isGenerating"
        severity="warn"
      >
        <template #icon>
          <CircleAlertIcon :size="16" />
        </template>

        <span v-if="generatedContent">已将设置更改为<strong>{{ getStyleLabel(activeStyle) }}</strong>风格、<strong>{{ getTemperatureLabel(activeTemperature) }}</strong>模式，点击「开始改写」按钮重新生成内容</span>
        <span v-else>已设置为<strong>{{ getStyleLabel(activeStyle) }}</strong>风格、<strong>{{ getTemperatureLabel(activeTemperature) }}</strong>模式，点击「开始改写」按钮生成内容</span>
      </Message>

      <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div class="relative rounded-xl border border-divider">
          <div class="p-card-container text-lg font-semibold">
            要改写的文本
          </div>

          <Textarea
            v-model="inputText"
            class="w-full resize-none !border-none !bg-transparent !p-0 !px-card-container !shadow-none !outline-none placeholder:text-base"
            placeholder="在此输入文本..."
            rows="14"
          />

          <div class="p-card-container flex-center">
            <div class="text-secondary">
              {{ inputTextCount }} 字
            </div>

            <div class="ml-auto gap-1 flex-center">
              <Button
                v-tooltip.top="'清空文本'"
                severity="contrast"
                variant="text"
                @click="inputText = ''"
              >
                <template #icon>
                  <Trash2Icon :size="14" />
                </template>
              </Button>

              <Button
                v-if="!isGenerating"
                :disabled="!inputText.trim()"
                label="开始改写"
                @click="rewriteText()"
              >
                <template #icon>
                  <ZapIcon :size="14" />
                </template>
              </Button>

              <Button
                v-else
                label="停止生成"
                variant="outlined"
                @click="handleStopGeneration()"
              />
            </div>
          </div>
        </div>

        <div class="relative flex flex-col rounded-xl border border-divider">
          <div class="justify-between p-card-container flex-center">
            <span class="text-lg font-semibold">
              {{ historyTitle }}
            </span>
          </div>

          <div class="flex-1 overflow-y-auto px-card-container">
            <div
              v-if="displayedContent"
              class="whitespace-pre-wrap"
            >
              {{ displayedContent }}
            </div>

            <div
              v-else
              class="cursor-default text-secondary"
            >
              改写后的内容将显示在这里...
            </div>
          </div>

          <div class="justify-end gap-1 p-card-container flex-center">
            <Button
              v-if="displayedContent && !isGenerating"
              class="flex items-center gap-1"
              :label="isCopySuccess ? '已复制' : '全文复制'"
              variant="text"
              @click="copy()"
            >
              <template #icon>
                <CopyIcon
                  v-if="!isCopySuccess"
                  :size="14"
                />
                <CheckIcon
                  v-else
                  :size="14"
                />
              </template>
            </Button>

            <Button
              v-if="isViewingHistory"
              label="返回编辑"
              variant="text"
              @click="exitHistoryView()"
            >
              <template #icon>
                <UndoDotIcon :size="14" />
              </template>
            </Button>
          </div>
        </div>
      </div>

      <!-- 历史记录面板 -->
      <div
        v-if="hasHistory"
        id="history-section"
        class="rounded-xl border border-divider p-card-container"
      >
        <div class="justify-between pb-card-container flex-center">
          <h3 class="gap-2 text-lg font-semibold flex-center">
            <HistoryIcon :size="18" />
            历史生成记录
          </h3>

          <Button
            v-if="historyGenerations.length > 0"
            label="清空全部"
            variant="text"
            @click="clearAllHistory()"
          >
            <template #icon>
              <TrashIcon :size="14" />
            </template>
          </Button>
        </div>

        <div class="grid grid-cols-1 gap-card-container overflow-y-auto md:grid-cols-2 lg:grid-cols-3">
          <div
            v-for="(item, index) of historyGenerations"
            :key="index"
            class="cursor-pointer rounded-lg border border-divider p-3 hover:border-primary/50 hover:bg-primary/5"
            @click="viewHistory(index)"
          >
            <div class="justify-between flex-center">
              <div class="flex items-center gap-3">
                <span class="flex items-center gap-1 font-medium">
                  <Component
                    :is="styleOptions.find(style => style.value === item.style)?.icon"
                    :size="14"
                  />
                  {{ getStyleLabel(item.style) }}风格
                </span>

                <span class="flex items-center gap-1 text-sm text-secondary">
                  <Component
                    :is="temperatureOptions.find(temp => temp.value === item.temperature)?.icon"
                    :size="13"
                  />
                  {{ getTemperatureLabel(item.temperature) }}
                </span>
              </div>

              <PopupMenu
                :options="[
                  {
                    label: '删除此记录',
                    command: () => {
                      removeHistoryItem(index)
                    },
                  },
                ]"
              >
                <ProBtnMore
                  class="!p-0.5"
                  onlyIcon
                />
              </PopupMenu>
            </div>

            <!-- 改写后内容 -->
            <p class="my-2 line-clamp-2 text-sm text-secondary">
              {{ item.content }}
            </p>

            <div class="flex-center">
              <span class="text-sm text-secondary">
                {{ formatRelativeTime(item.timestamp) }}
              </span>

              <Button
                class="ml-auto !px-1.5 !py-1"
                variant="text"
                @click="toggleHistoryItemExpand(index, $event)"
              >
                <div class="gap-1 text-xs flex-center">
                  <span>{{ expandedHistoryItems.has(index) ? '收起原文' : '查看原文' }}</span>
                  <Component
                    :is="expandedHistoryItems.has(index) ? ChevronUpIcon : ChevronDownIcon"
                    :size="14"
                  />
                </div>
              </Button>
            </div>

            <!-- 原文内容 (展开时显示) -->
            <div v-if="expandedHistoryItems.has(index)">
              <Divider class="!my-1">
                <span class="px-1 text-sm">原文</span>
              </Divider>

              <p class="text-sm opacity-80 text-secondary">
                {{ item.originalText }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
