<script setup lang="ts">
import { RefreshCwIcon } from 'lucide-vue-next'

definePageMeta({
  layoutConfig: {
    customContent: true,
    noContainerPadding: true,
  },
})

const theme = useTheme()

const workspaceStatsStore = useWorkspaceStatsStore()

onMounted(() => {
  workspaceStatsStore.fetchStats()
})
</script>

<template>
  <div
    class="size-full overflow-auto p-admin-layout-container"
    :class="{ '!pl-0': theme.themeState.value.layout.startsWith('admin-horizontal') }"
  >
    <LayoutAdminContentHeader
      title="智能体运行监控"
      wrapperClass="!p-0"
    >
      <template #titleRight>
        <ProBtn
          label="刷新数据"
          severity="secondary"
          size="small"
          type="button"
          variant="text"
          @click="() => workspaceStatsStore.fetchStats()"
        >
          <template #icon="{ size }">
            <RefreshCwIcon :size="size" />
          </template>
        </ProBtn>
      </template>
    </LayoutAdminContentHeader>

    <div class="flex flex-col gap-admin-layout">
      <CardContainer class="p-card-container">
        <WorkStatsList />
      </CardContainer>

      <div class="grid grid-cols-2 gap-admin-layout">
        <CardContainer
          class="p-card-container"
          title="工作流使用榜"
        >
          <WorkExecutionTimeRanking />
        </CardContainer>

        <CardContainer
          class="p-card-container"
          title="工作流执行状态统计"
        >
          <div class="size-full justify-center flex-center">
            <WorkExecutionStatus />
          </div>
        </CardContainer>

        <CardContainer
          class="p-card-container"
          title="流程节点数"
        >
          <div class="size-full justify-center flex-center">
            <WorkStatsNodeCount />
          </div>
        </CardContainer>

        <CardContainer
          class="p-card-container"
          title="工作流执行次数统计"
        >
          <div class="size-full justify-center flex-center">
            <WorkStatsExecutionCount />
          </div>
        </CardContainer>
      </div>

      <CardContainer
        class="p-card-container"
        title="工作流执行记录"
      >
        <WorkStatsExecutionRecord />
      </CardContainer>
    </div>
  </div>
</template>
