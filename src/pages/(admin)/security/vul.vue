<script setup lang="ts">
import { Tag } from 'primevue'

import ProBtnDetail from '~/components/pro/btn/ProBtnDetail.vue'
import TicketListTrigger from '~/components/ticket/TicketListTrigger.vue'
import { ProValueType } from '~/enums/pro'
import type { VulThreatLevel } from '~/enums/security'
import { getThreatLevelLabel, getThreatLevelSeverity, threatLevelConfig } from '~/enums/security'
import { TicketType } from '~/enums/ticket'

const searchValue = ref('')
const threatLevel = ref<VulThreatLevel>()

const activeRecord = ref<Vul>()

const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    activeRecord.value = undefined
  }
}

const columns: ProTableColumn<Vul>[] = [
  {
    field: 'server.name',
    header: '终端设备名称',
    render: (row) => row.server?.name || row.server?.ip_list.at(0)?.ipv4 || '-',
  },
  {
    field: 'name',
    header: '漏洞名称',
  },
  {
    field: 'code',
    header: '漏洞编号',
  },
  {
    field: 'type',
    header: '类型',
  },
  {
    field: 'ip',
    header: 'IP',
  },
  {
    field: 'port',
    header: '端口',
  },
  {
    field: 'level',
    header: '威胁等级',
    valueType: ProValueType.Select,
    valueEnum: toValueEnum(threatLevelConfig),
    render: (row) => h(Tag, {
      class: 'text-xs',
      severity: getThreatLevelSeverity(row.level),
      value: getThreatLevelLabel(row.level),
    }),
  },
  {
    field: 'create_time',
    header: '发现时间',
    valueType: ProValueType.DateTime,
  },
  {
    class: 'min-w-table-actions-wide',
    frozen: true,
    alignFrozen: 'right',
    valueType: ProValueType.ActionGroup,
    render: (row) => h('div', { class: 'table-action-group' }, [
      h(TicketListTrigger, {
        objectId: row.id,
        ticketCount: row.work_order.total,
        ticketType: TicketType.Vul,
      }),
      h(ProBtnDetail, {
        label: '详情',
        size: 'small',
        onClick: () => activeRecord.value = row,
      }),
    ]),
  },
]
</script>

<template>
  <div class="flex h-full flex-col gap-4">
    <div>
      <StatsVulCount :clickable="false" />
    </div>

    <div class="min-h-0 flex-1">
      <ProTable
        :columns="columns"
        :queryParams="{ name: searchValue, level: threatLevel }"
        :requestFn="(queryParams) => {
          return SecurityService.getVulList(queryParams)
        }"
        scrollable
        scrollHeight="flex"
      >
        <template #toolbarRight>
          <div class="flex-center">
            <div class="ml-auto gap-2 flex-center">
              <SearchInput
                placeholder="搜索漏洞"
                @update:debounceChange="searchValue = $event"
              />

              <FormItemThreatLevelSelect
                v-model="threatLevel"
              />
            </div>
          </div>
        </template>
      </ProTable>
    </div>

    <Dialog
      class="dialog-full"
      dismissableMask
      header="漏洞详情"
      modal
      :visible="!!activeRecord"
      @update:visible="handleVisibleChange"
    >
      <VulDetail :vulId="activeRecord?.id" />
    </Dialog>
  </div>
</template>
