<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import arrayToTree from 'array-to-tree'
import { cloneDeep } from 'lodash-es'
import { ChevronDownIcon, ListFilterIcon, SearchIcon, XIcon } from 'lucide-vue-next'

import { DateFormat, TooltipShowDelay } from '~/enums/common'
import { queryKeys } from '~/enums/query-key'
import { RouteKey } from '~/enums/route'
import { getThreatLevelLabel, getThreatLevelSeverity, VulThreatLevel } from '~/enums/security'

const pageInfo = ref<PageInfo>({
  page: 1,
  page_size: 20,
  page_sum: 1,
  total: 0,
})

const searchText = ref<SecurityAlert['name']>()

const initialFilter: VulFilterParams = {
  level: undefined,
  vendor_id: undefined,
  vul_type_id: undefined,
  publish_time: [],
  update_time: [],
}

const filter = ref<VulFilterParams>(cloneDeep(initialFilter))

watch([searchText, filter], () => {
  pageInfo.value.page = 1
})

const { data: advisories, isLoading } = useQuery({
  queryKey: queryKeys.security.vulLib.all({ query: { pageInfo, searchText, filter } }),
  queryFn: () => {
    const publishTime = formatDateRange(filter.value.publish_time)
    const updateTime = formatDateRange(filter.value.update_time)

    return SecurityService.getVulLibList({
      page: pageInfo.value.page,
      page_size: pageInfo.value.page_size,
      name: searchText.value,
      level: filter.value.level,
      vendor_id: filter.value.vendor_id,
      vul_type_id: filter.value.vul_type_id,
      publish_time_start: publishTime.start,
      publish_time_end: publishTime.end || publishTime.start,
      update_time_start: updateTime.start,
      update_time_end: updateTime.end || updateTime.start,
    })
  },
  select: (data) => {
    pageInfo.value.total = data.total || 0
    pageInfo.value.page_sum = data.page_sum || 0

    return data.list
  },
})

const searchVendor = ref<SecurityAlert['vendor_name']>()

const { data: vendors, isLoading: isVendorsLoading } = useQuery({
  queryKey: queryKeys.security.vulLib.vendor,
  queryFn: () => SecurityService.getVendorList({
    page: 1,
    page_size: 50,
    name: searchVendor.value,
  }),
})

const { data: vulTypes, isLoading: isVulTypesLoading } = useQuery({
  queryKey: queryKeys.security.vulLib.type,
  queryFn: SecurityService.getVulTypes,
})

const vulTypeOptions = computed(() => {
  if (vulTypes.value) {
    const vulTypesTree = arrayToTree(vulTypes.value, {
      customID: 'cnnvd_id',
      parentProperty: 'pid',
      childrenProperty: 'children',
    })

    return vulTypesTree
  }

  return []
})

const handlePageChange = (pInfo: PrimeVuePageInfo) => {
  const newPage = pInfo.page + 1
  const newPageSize = pInfo.rows

  const shouldTriggerChange = newPage !== pageInfo.value.page || newPageSize !== pageInfo.value.page_size

  if (shouldTriggerChange) {
    pageInfo.value.page = newPage
    pageInfo.value.page_size = newPageSize
  }
}

const selectedVul = ref<SecurityAlert>()

const handleViewDetail = (alert: SecurityAlert) => {
  selectedVul.value = alert
}

const handleViewDetailInNewPage = async (alert: SecurityAlert) => {
  await navigateTo(`${getRoutePath(RouteKey.漏洞详情, { vulDetailId: alert.cnnvd_id })}`)
}

const handleViewDetailInNewTab = (alert: SecurityAlert) => {
  window.open(`${getRoutePath(RouteKey.漏洞详情, { vulDetailId: alert.cnnvd_id })}`, '_blank')
}

const skeletonCount = 8

const activeFilterCount = computed(() =>
  Object.values(filter.value).filter((value) =>
    value !== undefined && value !== null && value !== '' && (Array.isArray(value) ? value.length > 0 : true),
  ).length,
)

const tempFilter = ref<VulFilterParams>(cloneDeep(initialFilter))

const { popoverRef, popover } = usePopover()

/** 清除筛选条件 */
const handleResetFilter = () => {
  tempFilter.value = cloneDeep(initialFilter)
  filter.value = cloneDeep(tempFilter.value)

  popover.value?.hide()
}

const handleFilterClick = (event: MouseEvent) => {
  popover.value?.show(event)
}

const handleSearch = () => {
  if (tempFilter.value) {
    filter.value = cloneDeep(tempFilter.value)
  }

  popover.value?.hide()
}
</script>

<template>
  <div class="flex h-full flex-col gap-8">
    <div class="flex min-h-0 flex-1 gap-6">
      <div class="flex flex-1 flex-col">
        <div class="flex-wrap gap-2 flex-center">
          <div class="ml-auto gap-2 flex-center">
            <SearchInput
              class="h-[32px]"
              fluid
              :modelValue="searchText"
              placeholder="搜索漏洞名称、描述或编号"
              size="small"
              @update:debounceChange="searchText = $event"
            />

            <Button
              outlined
              :severity="activeFilterCount > 0 ? 'primary' : 'secondary'"
              @click="handleFilterClick"
            >
              <ListFilterIcon class="size-4 shrink-0" />

              <span class="whitespace-nowrap text-sm font-medium">筛选条件</span>

              <span
                v-if="activeFilterCount > 0"
                class="group/badge relative ml-2 inline-block aspect-auto"
              >
                <Badge
                  class="group-hover/badge:opacity-0"
                  :value="activeFilterCount"
                />
                <span
                  v-tooltip.top="{ value: '清除筛选条件', showDelay: TooltipShowDelay.Fast }"
                  class="absolute inset-0 z-10 justify-center rounded-full bg-danger-100 opacity-0 inline-flex-center group-hover/badge:opacity-100"
                  @click.stop="handleResetFilter()"
                >
                  <XIcon class="size-3 text-danger-500" />
                </span>
              </span>
            </Button>
          </div>
        </div>

        <Divider class="!mb-0" />

        <div class="flex-1 overflow-auto">
          <div
            v-if="isLoading"
            class="space-y-4 py-in-tab"
          >
            <div
              v-for="i of skeletonCount"
              :key="i"
            >
              <div class="space-y-3">
                <div class="gap-2 flex-center">
                  <Skeleton
                    class="basis-10"
                    height="1.5rem"
                  />
                  <Skeleton
                    class="basis-1/3"
                    height="1.5rem"
                  />
                </div>

                <div class="flex gap-2 opacity-70">
                  <Skeleton
                    v-for="j of 6"
                    :key="j"
                    class="flex-1"
                  />
                </div>
              </div>
              <Divider v-if="i !== skeletonCount" />
            </div>
          </div>

          <DataView
            v-else
            data-key="cnnvd_id"
            layout="list"
            :value="advisories"
          >
            <template #list="{ items }: { items: SecurityAlert[] }">
              <template
                v-for="(alert, idx) of items"
                :key="alert.cnnvd_id"
              >
                <div
                  class="px-4 py-5"
                  :class="{
                    'border-b border-divider': idx !== items.length - 1,
                  }"
                >
                  <div class="gap-2 flex-center">
                    <Tag
                      class="text-xs"
                      :severity="getThreatLevelSeverity(alert.level)"
                      :value="getThreatLevelLabel(alert.level)"
                    />
                    <span
                      class="line-clamp-4 flex-1 text-lg font-semibold"
                      :class="{ 'text-danger-500 dark:text-danger-400': alert.level === VulThreatLevel.Super }"
                    >
                      {{ alert.name }}
                    </span>
                  </div>

                  <div class="flex-wrap gap-2 pt-2 text-sm flex-center text-secondary">
                    <div class="flex-1 flex-wrap gap-x-4 gap-y-0.5 flex-center">
                      <span class="whitespace-nowrap">
                        CVE 编号: {{ alert.cve_code }}
                      </span>
                      <span class="whitespace-nowrap">
                        CNNVD 编号: {{ alert.cnnvd_code }}
                      </span>
                      <span class="whitespace-nowrap">
                        漏洞类型: {{ alert.vul_type_name }}
                      </span>
                      <span class="whitespace-nowrap">
                        责任厂商: {{ alert.vendor_name }}
                      </span>
                      <span class="whitespace-nowrap">
                        披露时间: {{ formatDate(alert.publish_time, DateFormat.YYYY_MM_DD) }}
                      </span>
                      <span class="whitespace-nowrap">
                        最后更新: {{ alert.update_time ? formatDate(alert.update_time, DateFormat.YYYY_MM_DD) : '-' }}
                      </span>
                    </div>

                    <SplitButton
                      class="ml-auto h-8"
                      label="查看详情"
                      :model="[
                        {
                          label: '弹窗中查看',
                          command: () => handleViewDetail(alert),
                        },
                        {
                          label: '新页面中查看',
                          command: () => handleViewDetailInNewPage(alert),
                        },
                        {
                          label: '新页签中查看',
                          command: () => handleViewDetailInNewTab(alert),
                        },
                      ]"
                      severity="secondary"
                      size="small"
                      @click="handleViewDetail(alert)"
                    >
                      <template #dropdownicon>
                        <ChevronDownIcon :size="16" />
                      </template>
                    </SplitButton>
                  </div>
                </div>
              </template>
            </template>

            <template #empty>
              <ProTableEmpty />
            </template>
          </DataView>
        </div>

        <template v-if="pageInfo.total > 0">
          <Divider class="!mt-0" />

          <ProPaginator
            :pageInfo="pageInfo"
            :pageSizeOptions="[10, 20, 50, 100]"
            @pageChange="handlePageChange($event)"
          />
        </template>
      </div>

      <div class="flex basis-96 flex-col gap-4">
        <StatsVulLibLevel
          :vendorId="filter.vendor_id"
          :vulTypeId="filter.vul_type_id"
        />
      </div>
    </div>

    <Dialog
      class="dialog-half"
      dismissableMask
      header="漏洞详情"
      modal
      :visible="!!selectedVul"
      @update:visible="(visible: boolean) => {
        if (!visible) {
          selectedVul = undefined
        }
      }"
    >
      <SecVulDetail :cveKey="selectedVul?.cnnvd_id" />
    </Dialog>

    <Popover
      :ref="popoverRef"
      @show="tempFilter = cloneDeep(filter)"
    >
      <div class="w-[280px] px-2 pb-1 pt-4">
        <div class="flex flex-col gap-form-field">
          <FormItem label="风险等级">
            <FormItemThreatLevelSelect
              v-model="tempFilter.level"
            />
          </FormItem>

          <FormItem label="厂商">
            <ProSelect
              v-model="tempFilter.vendor_id"
              v-model:searchValue="searchVendor"
              class="w-full"
              :loading="isVendorsLoading"
              :options="vendors?.list.map(item => ({ label: item.name, value: item.id }))"
              placeholder="搜索或选择漏洞相关厂商"
              :search="{
                enable: true,
                placeholder: '搜索厂商',
              }"
              showClear
            />
          </FormItem>

          <FormItem label="漏洞类型">
            <CascadeSelect
              v-model="tempFilter.vul_type_id"
              class="w-full"
              :loading="isVulTypesLoading"
              :optionGroupChildren="['children']"
              optionGroupLabel="name"
              optionLabel="name"
              :options="vulTypeOptions"
              optionValue="cnnvd_id"
              placeholder="搜索或选择漏洞类型"
              showClear
            />
          </FormItem>

          <FormItem label="漏洞披露时间">
            <ProDatePicker
              v-model="tempFilter.publish_time"
              class="w-full"
              dateFormat="yy-mm-dd"
              :numberOfMonths="2"
              placeholder="选择漏洞首次披露的时间范围"
              selectionMode="range"
              showIcon
              showShortcuts
            />
          </FormItem>

          <FormItem label="漏洞更新时间">
            <ProDatePicker
              v-model="tempFilter.update_time"
              class="w-full"
              dateFormat="yy-mm-dd"
              :numberOfMonths="2"
              placeholder="选择漏洞信息更新的时间范围"
              selectionMode="range"
              showIcon
              showShortcuts
            />
          </FormItem>

          <div class="flex justify-end gap-2">
            <Button
              label="清除条件"
              severity="secondary"
              @click="handleResetFilter()"
            />
            <Button
              label="搜索"
              @click="handleSearch()"
            >
              <template #icon>
                <SearchIcon class="size-4" />
              </template>
            </Button>
          </div>
        </div>
      </div>
    </Popover>
  </div>
</template>
