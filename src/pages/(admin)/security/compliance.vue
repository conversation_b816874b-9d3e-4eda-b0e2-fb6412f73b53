<script setup lang="ts">
import ProBtnDetail from '~/components/pro/btn/ProBtnDetail.vue'
import TicketListTrigger from '~/components/ticket/TicketListTrigger.vue'
import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'
import { TicketType } from '~/enums/ticket'

const sortField = ref<string>()

const handleSortFieldChange = (sort: string) => {
  sortField.value = sort
}

const activeRecord = ref<ComplianceListItem>()

const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    activeRecord.value = undefined
  }
}

const columns: ProTableColumn<ComplianceListItem>[] = [
  {
    field: 'server.name',
    header: '终端设备名称',
    render: (data) => data.server?.name || '-',
  },
  {
    field: 'template_baseline.name',
    header: '基线模板',
  },
  {
    field: 'template_baseline.system_name',
    header: '模板系统类型',
  },
  {
    field: 'create_time',
    header: '报告时间',
    valueType: ProValueType.DateTime,
    sortable: true,
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('span', { class: 'table-action-group' }, [
      h(TicketListTrigger, {
        objectId: data.id,
        ticketCount: data.work_order.total,
        ticketType: TicketType.Compliance,
      }),
      h(ProBtnDetail, {
        label: '详情',
        onClick: () => activeRecord.value = data,
      }),
    ]),
  },
]
</script>

<template>
  <div>
    <div class="mb-8">
      <StatsComplianceCount />
    </div>

    <ProTable
      :columns="columns"
      :queryKey="queryKeys.security.compliance.all()"
      :requestFn="SecurityService.getComplianceList"
      @update:sortField="handleSortFieldChange"
    />

    <Dialog
      class="dialog-full"
      dismissableMask
      header="基线详情"
      maximizable
      modal
      :visible="!!activeRecord"
      @update:visible="handleVisibleChange"
    >
      <BaselineResultContent
        v-if="activeRecord"
        :complianceId="activeRecord.id"
      />
    </Dialog>
  </div>
</template>
