<script setup lang="ts">
import { RouteKey } from '~/enums/route'
import { useSpaceStore } from '~/features/space/stores/space'

const spaceStore = useSpaceStore()
const { activeSpaceId } = storeToRefs(spaceStore)

watch(activeSpaceId, () => {
  if (activeSpaceId.value) {
    navigateTo({
      path: getRoutePath(RouteKey.TW_工作空间_应用, {
        spaceId: activeSpaceId.value,
      }),
      replace: true,
    })
  }
}, { immediate: true })
</script>

<template>
  <div />
</template>
