<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { ArrowLeftIcon, UploadIcon } from 'lucide-vue-next'

import KnowledgeFileList from '~/features/knowledge/components/file/KnowledgeFileList.vue'
import KnowledgeFileUpload from '~/features/knowledge/components/file/KnowledgeFileUpload.vue'
import { useKnowledgeStore } from '~/features/knowledge/stores/knowledge'

definePageMeta({
  layout: 'space',
})

const route = useRoute()
const spaceId = route.params.spaceId as string
const knowledgeBaseId = route.params.knowledgeBaseId as string

const knowledgeStore = useKnowledgeStore()

const showUpload = ref(false)

// 获取知识库详情
const { data: knowledgeBase, isLoading } = useQuery({
  queryKey: ['knowledge-base', knowledgeBaseId],
  queryFn: async () => {
    const { KnowledgeBaseService } = await import('~/features/knowledge/services/knowledgeBase')
    const base = await KnowledgeBaseService.getKnowledgeBase(knowledgeBaseId)

    if (base) {
      knowledgeStore.setActiveKnowledgeBase(base)
    }

    return base
  },
  enabled: computed(() => !!knowledgeBaseId),
})

const handleGoBack = () => {
  navigateTo(`/space/${spaceId}/knowledge-base`)
}

const handleToggleUpload = () => {
  showUpload.value = !showUpload.value
}
</script>

<template>
  <div class="h-full">
    <!-- 头部导航 -->
    <div class="mb-6 flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <Button
          class="!p-2"
          severity="contrast"
          variant="text"
          @click="handleGoBack"
        >
          <ArrowLeftIcon :size="20" />
        </Button>

        <div>
          <h1 class="text-2xl font-semibold text-surface-900 dark:text-surface-50">
            {{ knowledgeBase?.name || '知识库详情' }}
          </h1>
          <p
            v-if="knowledgeBase?.description"
            class="mt-1 text-sm text-surface-600 dark:text-surface-400"
          >
            {{ knowledgeBase.description }}
          </p>
        </div>
      </div>

      <Button
        :label="showUpload ? '隐藏上传' : '上传文件'"
        @click="handleToggleUpload"
      >
        <template #icon>
          <UploadIcon :size="16" />
        </template>
      </Button>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="isLoading"
      class="flex h-64 items-center justify-center"
    >
      <ProgressSpinner />
    </div>

    <!-- 知识库不存在 -->
    <div
      v-else-if="!knowledgeBase"
      class="flex h-64 flex-col items-center justify-center"
    >
      <h3 class="mb-2 text-lg font-medium text-surface-900 dark:text-surface-50">
        知识库不存在
      </h3>
      <p class="mb-4 text-sm text-surface-600 dark:text-surface-400">
        该知识库可能已被删除或您没有访问权限
      </p>
      <Button
        label="返回知识库列表"
        @click="handleGoBack"
      />
    </div>

    <!-- 知识库内容 -->
    <div
      v-else
      class="space-y-6"
    >
      <!-- 文件上传区域 -->
      <Card
        v-if="showUpload"
        class="transition-all duration-200"
      >
        <template #header>
          <div class="px-6 py-4">
            <h2 class="text-lg font-semibold text-surface-900 dark:text-surface-50">
              上传文件
            </h2>
          </div>
        </template>

        <template #content>
          <div class="px-6 pb-6">
            <KnowledgeFileUpload :knowledgeBaseId="knowledgeBaseId" />
          </div>
        </template>
      </Card>

      <!-- 文件列表 -->
      <Card>
        <template #content>
          <div class="p-6">
            <KnowledgeFileList :knowledgeBaseId="knowledgeBaseId" />
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>
