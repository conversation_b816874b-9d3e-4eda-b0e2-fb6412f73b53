<script setup lang="ts">
import { GlobalEvent } from '~/enums/event'
import KnowledgeBaseDialog from '~/features/knowledge/components/base/KnowledgeBaseDialog.vue'
import KnowledgeBaseList from '~/features/knowledge/components/base/KnowledgeBaseList.vue'

const dialogRef = ref<InstanceType<typeof KnowledgeBaseDialog>>()

const { on } = useEmitter()

onMounted(() => {
  on(GlobalEvent.KnowledgeBaseCreate, () => {
    dialogRef.value?.openCreateModal()
  })

  on(GlobalEvent.KnowledgeBaseUpdate, (defaultValues) => {
    if (defaultValues) {
      dialogRef.value?.openUpdateModal(defaultValues)
    }
  })
})
</script>

<template>
  <div class="h-full">
    <KnowledgeBaseList />

    <KnowledgeBaseDialog ref="dialogRef" />
  </div>
</template>
