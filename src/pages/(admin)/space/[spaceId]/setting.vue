<script setup lang="ts">
import { PencilLineIcon } from 'lucide-vue-next'

import { GlobalEvent } from '~/enums/event'
import SpaceManage from '~/features/space/components/space/setting/SpaceManage.vue'
import SpaceMemberTable from '~/features/space/components/space/setting/SpaceMemberTable.vue'
import SpaceIcon from '~/features/space/components/space/SpaceIcon.vue'
import { useSpaceStore } from '~/features/space/stores/space'

const spaceStore = useSpaceStore()
const { activeSpace } = storeToRefs(spaceStore)

const handleEditSpace = () => {
  if (activeSpace.value) {
    emitter.emit(GlobalEvent.SpaceUpdate, activeSpace.value)
  }
}

const enum SpaceSettingTab {
  Member,
  Space,
}

const activeTab = ref<SpaceSettingTab>(SpaceSettingTab.Member)
</script>

<template>
  <div
    v-if="activeSpace"
    class="flex h-full flex-col"
  >
    <div class="gap-4 flex-center">
      <SpaceIcon :size="25" />

      <div>
        <div class="gap-2 flex-center">
          <div class="text-lg font-semibold">
            {{ activeSpace.name }}
          </div>

          <ProBtn
            size="small"
            :tooltip="{ value: '编辑信息' }"
            @click="handleEditSpace"
          >
            <template #icon="{ size }">
              <PencilLineIcon
                :size="size"
                :strokeWidth="2.5"
              />
            </template>
          </ProBtn>
        </div>
      </div>
    </div>

    <div class="mt-4 flex-1">
      <Tabs
        v-model:value="activeTab"
        class="h-full"
        :dt="{
          tab: {
            padding: '0.5rem 0.8rem',
          },
        }"
      >
        <TabList>
          <Tab :value="SpaceSettingTab.Member">
            成员管理
          </Tab>
          <Tab :value="SpaceSettingTab.Space">
            空间管理
          </Tab>
        </TabList>

        <TabPanels class="h-full min-h-0 flex-1 !p-0 !pt-in-tab">
          <TabPanel
            class="h-full"
            :value="SpaceSettingTab.Member"
          >
            <template v-if="activeTab === SpaceSettingTab.Member">
              <SpaceMemberTable />
            </template>
          </TabPanel>

          <TabPanel
            class="h-full"
            :value="SpaceSettingTab.Space"
          >
            <template v-if="activeTab === SpaceSettingTab.Space">
              <SpaceManage />
            </template>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </div>
  </div>
</template>
