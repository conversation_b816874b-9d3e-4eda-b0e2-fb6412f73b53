<script setup lang="ts">
import { CheckIcon } from 'lucide-vue-next'
</script>

<template>
  <div class="size-full bg-content">
    <div class="size-full flex-col justify-center gap-5 flex-center">
      <div class="rounded-full bg-success-100 p-2">
        <div class="rounded-full border border-success-300 bg-success-200 p-2">
          <div class="rounded-full bg-success-500 p-2 flex-center">
            <CheckIcon class="size-8 text-surface-0" />
          </div>
        </div>
      </div>

      <h1 class="text-xl font-bold">
        您已注册成功，欢迎加入
      </h1>

      <NuxtLink to="/">
        <Button label="前往首页" />
      </NuxtLink>
    </div>
  </div>
</template>
