<script setup lang="ts">
import { string } from 'valibot'

definePageMeta({
  title: '资产信息采集',
  description: '填报和完善 IT 资产信息，支持资产管理和安全评估。',
})

const collectionKey = useRouteParam({
  name: 'collectionKey',
  schema: string(),
})

const store = useAssetInfoStore()

watchEffect(() => {
  if (collectionKey.value) {
    store.setAssetCollectionKey(collectionKey.value)
  }
})
</script>

<template>
  <div class="flex h-full flex-col bg-content">
    <LayoutAdminContentHeader />

    <div class="flex-1 overflow-auto p-admin-layout-content pt-0">
      <AssetDetail />
    </div>
  </div>
</template>
