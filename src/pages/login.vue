<script setup lang="ts">
import { valibotResolver } from '@primevue/forms/resolvers/valibot'
import { useQuery } from '@tanstack/vue-query'
import { createWWLoginPanel, WWLoginPanelSizeType, WWLoginRedirectType, WWLoginType } from '@wecom/jssdk'
import { consola } from 'consola'
import { ChevronLeftIcon } from 'lucide-vue-next'
import { object } from 'valibot'

const runtimeConfig = useRuntimeConfig()
const {
  siteName,
  siteLogo,
  enableMock,
  wecomAppId,
  wecomAgentId,
} = runtimeConfig.public

const { $toast } = useNuxtApp()

const resolver = valibotResolver(
  object({
    username: schemaNotEmptyString('请输入账号'),
    password: schemaNotEmptyString('请输入密码'),
    captcha: schemaNotEmptyString('请输入验证码'),
  }),
)

// 控制验证码显示的状态
const showCaptcha = ref(false)

const { data: verifyData, refetch: refreshCaptcha } = useQuery({
  queryKey: ['captcha', showCaptcha],
  queryFn: () => {
    if (showCaptcha.value) {
      return UserService.getCaptcha()
    }

    return null
  },
})

const loginPayload = ref<LoginFormValues | null>(null)

const { refetch: login, isLoading: isLoginLoading } = useQuery({
  queryKey: ['login'],
  queryFn: async () => {
    const { public_key: pemKey } = await UserService.getPublicKey()

    if (loginPayload.value && pemKey) {
      try {
        if (!enableMock) {
          const publicKey = await loadPublicKey(pemKey)
          const encryptedUsername = await encryptMessage(loginPayload.value.username, publicKey)
          const encryptedPassword = await encryptMessage(loginPayload.value.password, publicKey)

          return UserService.login({
            ...loginPayload.value,
            username: encryptedUsername,
            password: encryptedPassword,
          })
        }
        else {
          return UserService.login(loginPayload.value)
        }
      }
      catch (err) {
        consola.error(err)
      }
    }

    return null
  },
  enabled: false,
})

const handleLogin = async ({ valid, states }: FormSubmitOptions<LoginFormValidValues>) => {
  if (valid) {
    loginPayload.value = {
      username: states.username.value,
      password: states.password.value,
      // 仅在显示验证码时才添加验证码相关信息
      ...(showCaptcha.value && verifyData.value
        ? {
            captcha: states.captcha?.value,
            uuid: verifyData.value.uuid,
          }
        : {}),
    }

    const { data, error } = await login()

    if (error) {
      // 登录失败时显示验证码
      showCaptcha.value = true
      refreshCaptcha()
    }
    else if (data) {
      handleLoginSuccess(data)
    }
  }
}

const captchaInput = useTemplateRef<{ $el: HTMLInputElement }>('captcha-input')

const handleRefreshCaptcha = () => {
  captchaInput.value?.$el.select()
  refreshCaptcha()
}

const showWecomLogin = ref(false)
const { refKey, ref: wwLoginRef } = useRef<HTMLElement>()

const handleWecomLogin = () => {
  if (wecomAppId && wecomAgentId) {
    showWecomLogin.value = true

    nextTick(() => {
      if (wwLoginRef.value) {
        createWWLoginPanel({
          el: wwLoginRef.value,
          params: {
            appid: wecomAppId,
            agentid: wecomAgentId,
            redirect_uri: window.location.href,
            login_type: WWLoginType.corpApp,
            redirect_type: WWLoginRedirectType.callback,
            panel_size: WWLoginPanelSizeType.small,
          },
          onLoginSuccess: ({ code }) => {
            handleThirdPartyLogin(code)
          },
          onLoginFail: (err) => {
            consola.error('企业微信登录失败', err)
            $toast.error({
              summary: '企业微信登录失败',
              detail: err instanceof Error ? err.message : '未知错误',
            })
          },
        })
      }
    })
  }
}
</script>

<template>
  <div class="bg-grid-small-black size-full justify-center flex-center md:p-5">
    <div class="size-full overflow-hidden bg-content shadow-surface-300 md:size-max md:rounded-3xl md:border md:border-divider md:shadow-[0_4px_0_0_var(--tw-shadow-color)]">
      <div class="h-full flex-col justify-center overflow-auto flex-center md:min-w-[380px]">
        <div
          v-if="!showWecomLogin"
          class="size-full p-4 md:p-12"
        >
          <div class="mb-10 flex flex-col justify-center gap-3">
            <img
              v-if="siteLogo"
              alt="网站图标"
              class="size-16 overflow-hidden object-cover"
              :src="siteLogo"
            >

            <h1 class="text-2xl font-bold">
              欢迎登录{{ siteName }}
            </h1>
          </div>

          <Form
            v-if="!isFuYao()"
            :resolver="resolver"
            @submit="handleLogin"
          >
            <div class="flex flex-col gap-5">
              <FormItem
                v-slot="{ id }"
                label="账号"
                name="username"
              >
                <InputText
                  :id="id"
                  fluid
                  placeholder="请输入邮箱或手机号"
                  type="text"
                />
              </FormItem>

              <FormItem
                v-slot="{ id }"
                label="密码"
                name="password"
              >
                <Password
                  :feedback="false"
                  fluid
                  :inputId="id"
                  placeholder="请输入密码"
                  toggleMask
                  type="password"
                />
              </FormItem>

              <FormItem
                v-if="showCaptcha"
                v-slot="{ id }"
                label="验证码"
                name="captcha"
              >
                <div class="flex h-8 gap-2">
                  <InputText
                    :id="id"
                    ref="captcha-input"
                    class="flex-1"
                    placeholder="验证码不区分大小写"
                    type="text"
                  />

                  <div
                    class="min-w-11 cursor-pointer overflow-hidden rounded-lg bg-surface-100"
                    title="点击重新获取验证码"
                    @click="handleRefreshCaptcha"
                  >
                    <img
                      v-if="verifyData?.image"
                      alt="图形验证码"
                      class="size-full object-contain"
                      :src="verifyData.image"
                    >
                  </div>
                </div>
              </FormItem>

              <Button
                class="mt-2"
                label="确认登录"
                :loading="isLoginLoading"
                size="large"
                type="submit"
              />
            </div>
          </Form>

          <template v-if="isFuYao()">
            <LoginForm />

            <Divider align="center">
              <span class="text-sm text-secondary">
                更多登录方式
              </span>
            </Divider>

            <div class="flex flex-col gap-2">
              <Button
                label="企业微信登录"
                size="large"
                type="button"
                variant="outlined"
                @click="handleWecomLogin()"
              >
                <template #icon>
                  <img
                    alt="企业微信"
                    class="size-6"
                    src="/images/wecom.svg"
                  >
                </template>
              </Button>
            </div>
          </template>
        </div>

        <div
          v-if="showWecomLogin"
          class="flex size-full flex-col gap-2 p-4"
        >
          <div>
            <Button
              label="使用账号登录"
              type="button"
              variant="text"
              @click="showWecomLogin = false"
            >
              <template #icon>
                <ChevronLeftIcon :size="18" />
              </template>
            </Button>
          </div>

          <div
            :ref="refKey"
            class="min-h-0 flex-1 justify-center flex-center"
          />
        </div>
      </div>
    </div>
  </div>
</template>
