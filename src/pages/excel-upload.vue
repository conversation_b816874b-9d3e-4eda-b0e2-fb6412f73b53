<!-- eslint-disable -->
<script setup lang="ts">
import * as XLSX from 'xlsx'
import type { FileUploadSelectEvent } from 'primevue'

interface ColumnDef {
  field: string
  header: string
}

interface SheetData {
  name: string
  data: Record<string, UnsafeAny>[]
  columns: ColumnDef[]
  duplicateStockNos: string[]
}

// 定义表格数据和列
const allSheets = ref<SheetData[]>([])
const currentSheetIndex = ref(0)
const tableData = computed(() => currentSheet.value?.data || [])
const columns = computed(() => currentSheet.value?.columns || [])
const currentSheet = computed(() => allSheets.value[currentSheetIndex.value] || null)
const isLoading = ref(false)
const fileUploaded = ref(false)
const fileName = ref('')
const errorMessage = ref('')
const successMessage = ref('')
const globalFilterValue = ref('')
// 添加重复项相关状态
const duplicateStockNos = computed(() => currentSheet.value?.duplicateStockNos || [])
const hasDuplicates = computed(() => duplicateStockNos.value.length > 0)
const duplicateCount = computed(() => duplicateStockNos.value.length)
const sheetNames = computed(() => allSheets.value.map((sheet) => sheet.name))
const hasMultipleSheets = computed(() => allSheets.value.length > 1)

// 可能的日期列名称
const possibleDateColumns = ['RequestDate', 'Date', 'CreateDate', 'UpdateDate', 'CreatedAt', 'UpdatedAt']

// 检查值是否可能是Excel日期数字
const isExcelDateNumber = (value: any): boolean => {
  // Excel日期通常是大于等于1的数字（1对应1900年1月1日）
  return !isNaN(Number(value)) && Number(value) >= 1
}

// 将Excel日期数字转换为格式化的日期字符串
const formatExcelDate = (excelDateValue: number): string => {
  try {
    const jsDate = XLSX.SSF.parse_date_code(excelDateValue)

    return `${jsDate.y}-${String(jsDate.m).padStart(2, '0')}-${String(jsDate.d).padStart(2, '0')}`
  } catch (error) {
    console.error('日期转换失败:', error)

    return String(excelDateValue) // 如果转换失败，返回原始值
  }
}

// 处理数据行中的所有可能日期
const processRowDates = (row: Record<string, UnsafeAny>): Record<string, UnsafeAny> => {
  const newRow = { ...row }

  // 遍历所有列
  Object.keys(newRow).forEach((key) => {
    // 检查列名是否可能是日期列，或者值是否看起来像日期数字
    if (possibleDateColumns.includes(key) || isExcelDateNumber(newRow[key])) {
      if (isExcelDateNumber(newRow[key])) {
        newRow[key] = formatExcelDate(Number(newRow[key]))
      }
    }
  })

  return newRow
}

// 查找重复的 StockNo
const findDuplicateStockNos = (sheetData: Record<string, UnsafeAny>[]): string[] => {
  // 检查是否存在 StockNo 列
  if (!sheetData.length || !sheetData[0].hasOwnProperty('StockNo')) {
    return []
  }

  // 统计每个 StockNo 出现的次数
  const stockNoCounts = new Map<string, number>()

  sheetData.forEach((row) => {
    const stockNo = row.StockNo?.toString() || ''

    if (stockNo) {
      stockNoCounts.set(stockNo, (stockNoCounts.get(stockNo) || 0) + 1)
    }
  })

  // 找出重复的 StockNo（出现次数大于1的）
  return Array.from(stockNoCounts.entries())
    .filter(([_, count]) => count > 1)
    .map(([stockNo]) => stockNo)
}

// 处理文件上传
const handleFileUpload = (ev: FileUploadSelectEvent) => {
  const file = ev.files[0]

  if (!file) {
    return
  }

  // 检查文件类型
  const fileExtension = file.name.split('.').pop()?.toLowerCase()

  if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
    errorMessage.value = '请上传 Excel 文件 (.xlsx 或 .xls 格式)'

    return
  }

  fileName.value = file.name
  isLoading.value = true
  fileUploaded.value = true
  errorMessage.value = ''
  successMessage.value = ''
  allSheets.value = []

  const reader = new FileReader()

  reader.onload = (e) => {
    try {
      const data = e.target?.result
      const workbook = XLSX.read(data, { type: 'array' })

      // 处理所有工作表
      for (const sheetName of workbook.SheetNames) {
        const worksheet = workbook.Sheets[sheetName]

        // 将 Excel 数据转换为 JSON，添加日期转换选项
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          raw: false, // 不使用原始值
          dateNF: 'yyyy-mm-dd', // 设置日期格式
        })

        // 如果有数据，设置列和数据
        if (jsonData.length > 0) {
          // 从第一行数据提取列名
          const firstRow = jsonData[0] as Record<string, UnsafeAny>
          const sheetColumns = Object.keys(firstRow).map((key) => ({
            field: key,
            header: key,
          }))

          // 处理所有行的日期格式
          const processedData = (jsonData as Record<string, UnsafeAny>[]).map(processRowDates)

          // 查找重复项
          const duplicates = findDuplicateStockNos(processedData)

          // 添加到工作表列表
          allSheets.value.push({
            name: sheetName,
            data: processedData,
            columns: sheetColumns,
            duplicateStockNos: duplicates,
          })
        }
      }

      // 如果没有找到任何有效的工作表数据
      if (allSheets.value.length === 0) {
        errorMessage.value = '上传的 Excel 文件中没有数据'
      } else {
        // 设置当前工作表为第一个
        currentSheetIndex.value = 0

        // 显示工作表数量信息
        successMessage.value = `成功加载 ${allSheets.value.length} 个工作表`
      }
    } catch (error) {
      console.error('Excel解析错误:', error)
      errorMessage.value = '解析 Excel 文件时出错，请确保文件格式正确'
    } finally {
      isLoading.value = false
    }
  }

  reader.onerror = () => {
    errorMessage.value = '读取文件时出错，请重试'
    isLoading.value = false
  }

  reader.readAsArrayBuffer(file)
}

// 切换当前工作表
const changeSheet = (index: number) => {
  if (index >= 0 && index < allSheets.value.length) {
    currentSheetIndex.value = index
  }
}

// 判断行是否包含重复的 StockNo
const isRowDuplicate = (row: Record<string, UnsafeAny>) => {
  return row.StockNo && duplicateStockNos.value.includes(row.StockNo.toString())
}

// 获取行的样式
const getRowClass = (row: Record<string, UnsafeAny>) => {
  return isRowDuplicate(row) ? 'bg-yellow-100 dark:bg-yellow-900' : ''
}

// 清除上传的文件
const clearUpload = () => {
  allSheets.value = []
  currentSheetIndex.value = 0
  fileUploaded.value = false
  fileName.value = ''
  errorMessage.value = ''
  successMessage.value = ''
  globalFilterValue.value = ''
}

// 计算表格是否有数据
const hasTableData = computed(() => tableData.value.length > 0)

// 移除当前工作表中重复的StockNo行，只保留RequestDate最新的记录
const removeDuplicatesKeepLatest = () => {
  if (!currentSheet.value || !hasDuplicates.value || !tableData.value.length) {
    return
  }

  // 重置消息
  errorMessage.value = ''
  successMessage.value = ''

  // 检查是否存在RequestDate列
  const hasRequestDate = tableData.value.some((row) => row.hasOwnProperty('RequestDate'))

  if (!hasRequestDate) {
    errorMessage.value = '无法执行操作：数据中没有RequestDate列'

    return
  }

  // 按StockNo分组
  const groupedByStockNo = new Map<string, Record<string, UnsafeAny>[]>()

  tableData.value.forEach((row) => {
    const stockNo = row.StockNo?.toString() || ''

    if (stockNo) {
      if (!groupedByStockNo.has(stockNo)) {
        groupedByStockNo.set(stockNo, [])
      }

      groupedByStockNo.get(stockNo)?.push(row)
    }
  })

  // 记录原始数据长度
  const originalLength = tableData.value.length

  // 对于每个重复的StockNo，只保留RequestDate最新的记录
  const newTableData: Record<string, UnsafeAny>[] = []

  groupedByStockNo.forEach((rows, stockNo) => {
    if (rows.length === 1) {
      // 如果只有一条记录，直接保留
      newTableData.push(rows[0])
    } else {
      // 对于重复记录，按RequestDate排序并保留最新的
      const sortedRows = [...rows].sort((a, b) => {
        const dateA = a.RequestDate ? new Date(a.RequestDate).getTime() : 0
        const dateB = b.RequestDate ? new Date(b.RequestDate).getTime() : 0

        return dateB - dateA // 降序排列，最新的在前
      })

      // 只保留第一条（最新的）记录
      newTableData.push(sortedRows[0])
    }
  })

  // 更新表格数据
  if (currentSheet.value) {
    currentSheet.value.data = newTableData

    // 重新计算重复项
    currentSheet.value.duplicateStockNos = findDuplicateStockNos(newTableData)
  }

  // 计算移除的行数
  const removedCount = originalLength - newTableData.length

  // 显示成功消息
  successMessage.value = `已成功移除工作表"${currentSheet.value.name}"中的 ${removedCount} 行重复数据，只保留了每个StockNo的最新记录`
}

// 移除所有工作表中的重复项
const removeAllSheetsDuplicates = () => {
  // 保存当前工作表索引
  const originalSheetIndex = currentSheetIndex.value
  let totalRemoved = 0

  // 遍历所有工作表并处理重复项
  for (let i = 0; i < allSheets.value.length; i++) {
    // 切换到当前工作表
    currentSheetIndex.value = i

    // 如果有重复项，移除它们
    if (hasDuplicates.value) {
      const originalLength = tableData.value.length
      removeDuplicatesKeepLatest()
      totalRemoved += originalLength - tableData.value.length
    }
  }

  // 恢复原始工作表索引
  currentSheetIndex.value = originalSheetIndex

  // 显示总体成功消息
  successMessage.value = `已处理所有工作表，共移除 ${totalRemoved} 行重复数据`
}

// 移除重复项并导出
const removeDuplicatesAndExport = () => {
  // 先移除当前工作表的重复项
  removeDuplicatesKeepLatest()

  // 如果没有错误消息，则导出
  if (!errorMessage.value) {
    // 短暂延迟确保数据已更新
    setTimeout(() => {
      exportExcel()
    }, 100)
  }
}

// 移除所有工作表的重复项并导出
const removeAllDuplicatesAndExport = () => {
  // 先移除所有工作表的重复项
  removeAllSheetsDuplicates()

  // 如果没有错误消息，则导出
  if (!errorMessage.value) {
    // 短暂延迟确保数据已更新
    setTimeout(() => {
      exportExcel()
    }, 100)
  }
}

// 导出为 Excel 文件
const exportExcel = () => {
  if (allSheets.value.length === 0) {
    return
  }

  try {
    // 创建工作簿
    const workbook = XLSX.utils.book_new()

    // 为每个工作表创建工作表并添加到工作簿
    allSheets.value.forEach((sheet) => {
      // 创建工作表
      const worksheet = XLSX.utils.json_to_sheet(sheet.data)

      // 设置所有可能的日期列的格式
      sheet.columns.forEach((col, index) => {
        if (possibleDateColumns.includes(col.field)) {
          // 获取列的字母标识（A, B, C等）
          const colLetter = XLSX.utils.encode_col(index)

          // 设置列范围（从第2行开始，第1行是标题）
          const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')

          // 为日期列设置格式
          for (let row = range.s.r + 1; row <= range.e.r; row++) {
            const cellRef = colLetter + (row + 1)

            if (worksheet[cellRef]) {
              worksheet[cellRef].z = 'yyyy-mm-dd'
            }
          }
        }
      })

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, sheet.name)
    })

    // 生成 Excel 文件并下载
    XLSX.writeFile(workbook, `${fileName.value.split('.')[0]}_导出.xlsx`)
  } catch (error) {
    console.error('导出Excel错误:', error)
    errorMessage.value = '导出 Excel 文件时出错，请重试'
  }
}
</script>

<template>
  <div class="bg-content p-6">
    <div class="mb-6 flex gap-2">
      <FileUpload
        accept=".xlsx,.xls"
        auto
        chooseLabel="选择 Excel 文件"
        customUpload
        mode="basic"
        @select="handleFileUpload($event)"
      />

      <Button
        v-if="fileUploaded"
        class="flex-none"
        icon="pi pi-times"
        label="清除"
        severity="danger"
        @click="clearUpload"
      />

      <Button
        v-if="hasTableData"
        class="flex-none"
        label="导出 Excel"
        severity="success"
        @click="exportExcel"
      />

      <Button
        v-if="hasDuplicates"
        class="flex-none"
        icon="pi pi-filter-slash"
        label="移除当前表重复项"
        severity="warning"
        @click="removeDuplicatesKeepLatest"
      />

      <Button
        v-if="hasMultipleSheets"
        class="flex-none"
        icon="pi pi-filter-slash"
        label="移除所有表重复项"
        severity="warning"
        @click="removeAllSheetsDuplicates"
      />

      <Button
        v-if="hasDuplicates"
        class="flex-none"
        icon="pi pi-download"
        label="移除当前表重复并导出"
        severity="info"
        @click="removeDuplicatesAndExport"
      />

      <Button
        v-if="hasMultipleSheets"
        class="flex-none"
        icon="pi pi-download"
        label="移除所有表重复并导出"
        severity="info"
        @click="removeAllDuplicatesAndExport"
      />
    </div>

    <!-- 工作表选择器 -->
    <div
      v-if="hasMultipleSheets"
      class="mb-4"
    >
      <div class="mb-2 text-lg font-medium">
        选择工作表:
      </div>
      <div class="flex flex-wrap gap-2">
        <Button
          v-for="(name, index) of sheetNames"
          :key="index"
          :class="{ 'p-button-outlined': index !== currentSheetIndex }"
          :label="name"
          :severity="index === currentSheetIndex ? 'primary' : 'secondary'"
          @click="changeSheet(index)"
        />
      </div>
    </div>

    <!-- 显示重复项统计信息 -->
    <div
      v-if="hasDuplicates"
      class="mb-4"
    >
      <Message severity="warn">
        <div class="flex items-center gap-2">
          <i class="pi pi-exclamation-triangle" />
          <span>在工作表"{{ currentSheet?.name }}"中发现 <strong>{{ duplicateCount }}</strong> 个重复的 StockNo，重复行已用黄色标记</span>
        </div>
      </Message>
    </div>

    <!-- 显示成功消息 -->
    <Message
      v-if="successMessage"
      class="mb-4"
      :closable="false"
      severity="success"
    >
      {{ successMessage }}
    </Message>

    <!-- 显示错误消息 -->
    <Message
      v-if="errorMessage"
      class="mb-4"
      :closable="false"
      severity="error"
    >
      {{ errorMessage }}
    </Message>

    <DataTable
      v-if="hasTableData"
      columnResizeMode="fit"
      paginator
      resizableColumns
      :rowClass="getRowClass"
      :rows="10000"
      showGridlines
      :value="tableData"
    >
      <Column
        v-for="col of columns"
        :key="col.field"
        v-bind="col"
      />
    </DataTable>
  </div>
</template>
