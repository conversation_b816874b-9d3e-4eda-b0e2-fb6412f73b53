<script setup lang="ts">
import { ArrowLeftIcon, HouseIcon } from 'lucide-vue-next'

definePageMeta({
  layout: false,
  title: '未找到页面',
})

const router = useRouter()

const handleBack = () => {
  router.back()
}
</script>

<template>
  <div class="h-screen justify-center overflow-auto bg-content p-8 flex-center">
    <div class="w-full max-w-md flex-col gap-8 flex-center">
      <div class="flex-col gap-10 flex-center">
        <div class="text-7xl font-bold">
          <img
            alt="404"
            src="/images/not-found.webp"
          >
        </div>

        <h2 class="text-4xl font-semibold">
          未找到页面
        </h2>

        <div class="flex-col gap-2 flex-center">
          <Message
            severity="secondary"
            variant="simple"
          >
            抱歉，您要访问的页面不存在或已被移除。
          </Message>

          <Message
            severity="secondary"
            variant="simple"
          >
            不用担心，这可能是因为 URL 输入错误或页面已过期。
          </Message>
        </div>
      </div>

      <div class="flex justify-center gap-4">
        <Button
          label="返回上一页"
          variant="outlined"
          @click="handleBack"
        >
          <template #icon>
            <ArrowLeftIcon class="mr-1 size-5" />
          </template>
        </Button>

        <NuxtLink to="/">
          <Button label="返回首页">
            <template #icon>
              <HouseIcon class="mr-1 size-5" />
            </template>
          </Button>
        </NuxtLink>
      </div>
    </div>
  </div>
</template>
