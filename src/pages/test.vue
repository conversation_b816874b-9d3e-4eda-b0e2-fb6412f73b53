<script setup lang="ts">
import { ApiStatus } from '~/enums/common'
</script>

<template>
  <div class="flex flex-col gap-4 p-admin-layout">
    <Button
      @click="() => {
        throwCustomError({
          message: '测试错误',
          statusCode: ApiStatus.LicenseInvalid,
        })
      }"
    >
      触发错误：证书过期
    </Button>

    <Button
      @click="() => {
        throwCustomError({
          message: '测试错误',
          statusCode: ApiStatus.Unauthorized,
        })
      }"
    >
      触发错误：未授权
    </Button>

    <Button
      @click="() => {
        throwCustomError({
          message: '测试错误',
          statusCode: ApiStatus.LinkExpiredOrInvalid,
        })
      }"
    >
      触发错误：分享采集链接失效
    </Button>
  </div>
</template>
