<script setup lang="ts">
import { CheckCircleIcon, StarIcon } from 'lucide-vue-next'
import { string } from 'valibot'

import { RouteKey } from '~/enums/route'

definePageMeta({
  layout: 'client-view',
})

const projectId = useRouteParam({
  name: 'projectId',
  schema: string(),
})

const handleBackToProject = async () => {
  if (projectId.value) {
    await navigateTo(
      getRoutePath(RouteKey.TW_客户_项目详情, { projectId: projectId.value }),
    )
  }
}
</script>

<template>
  <div class="flex min-h-full flex-col items-center justify-center p-client-layout-page px-4">
    <div class="mb-8 flex flex-col items-center">
      <div class="mb-6 text-success-500">
        <CheckCircleIcon :size="40" />
      </div>

      <h1 class="mb-2 text-center text-2xl font-bold">
        感谢您的评价！
      </h1>

      <p class="text-center">
        您的反馈对我们非常重要，这将帮助我们不断改进服务质量
      </p>
    </div>

    <div class="mb-8 w-full max-w-md rounded-lg bg-content p-6 shadow-md">
      <div class="mb-4 text-center text-sm font-medium">
        您的评价已经成功提交
      </div>

      <div class="flex items-center justify-center space-x-2">
        <span
          v-for="i of 5"
          :key="i"
          class="text-warning-500"
        >
          <StarIcon :size="20" />
        </span>
      </div>
    </div>

    <VanButton
      type="primary"
      @click="handleBackToProject"
    >
      返回项目详情
    </VanButton>
  </div>
</template>
