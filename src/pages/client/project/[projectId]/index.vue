<script setup lang="ts">
import { number } from 'valibot'

definePageMeta({
  layout: 'client-view',
})

const projectId = useRouteParam({
  name: 'projectId',
  schema: number(),
})
</script>

<template>
  <div class="size-full bg-content p-client-layout-page">
    <ClientProjectDetail
      v-if="projectId"
      :projectId="projectId"
    />

    <VanDivider />

    <ClientProjectFeedback
      v-if="projectId"
      :projectId="projectId"
    />
  </div>
</template>
