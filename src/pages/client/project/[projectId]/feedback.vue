<script setup lang="ts">
import { number } from 'valibot'
import type { FormInstance } from 'vant'

import { RouteKey } from '~/enums/route'
import { ProjectService } from '~/services-tw/project'
import type { FeedbackFormValues } from '~/types-tw/client'

definePageMeta({
  layout: 'client-view',
})

const userStore = useUserStore()

const projectId = useRouteParam({
  name: 'projectId',
  schema: number(),
})

const canEdit = ref(true)

const loading = ref(false)

const formRef = ref<FormInstance>()

const defaultForm: Partial<FeedbackFormValues> = {
  response_score: undefined,
  professional_score: undefined,
  communication_score: undefined,
}

const tags = {
  one: ['表达模糊', '消极怠工', '毫无经验', '延期交付'],
  two: ['表达简略', '经常拖延', '能力欠佳', '交互不便'],
  three: ['表意明确', '反应较快', '业务熟练', '如期交付'],
  four: ['交流高效', '响应迅速', '经验丰富', '倾心服务'],
  five: ['耐心贴心', '分秒必争', '行业顶尖', '无可挑剔'],
}

const form = reactive<Partial<FeedbackFormValues>>({
  ...defaultForm,
  tags: [],
})

const rateColor = '#ffd21e'

const isAllRated = computed(() => {
  return form.response_score !== undefined
    && form.professional_score !== undefined
    && form.communication_score !== undefined
})

const getAverageRating = () => {
  const ratings = [
    form.response_score,
    form.professional_score,
    form.communication_score,
  ].filter((r) => r !== undefined) as number[]

  if (ratings.length === 0) {
    return 0
  }

  return ratings.reduce((a, b) => a + b, 0) / ratings.length
}

const availableTags = computed(() => {
  if (!isAllRated.value) {
    return []
  }

  const avgRating = getAverageRating()
  const rating = Math.ceil(avgRating)

  switch (rating) {
    case 1:
      return tags.one

    case 2:
      return tags.two

    case 3:
      return tags.three

    case 4:
      return tags.four

    case 5:
      return tags.five

    default:
      return []
  }
})

const toggleTag = (tag: string) => {
  if (!form.tags) {
    form.tags = []
  }

  const index = form.tags.indexOf(tag)

  if (index > -1) {
    form.tags.splice(index, 1)
  }
  else {
    form.tags.push(tag)
  }
}

const errMsg = ref<Record<keyof Omit<FeedbackFormValues, 'comment' | 'tags'>, string>>({
  response_score: '',
  professional_score: '',
  communication_score: '',
})

const validateRate = async () => {
  let hasError = false

  if (form.response_score === undefined) {
    errMsg.value.response_score = '请评价响应速度'
    hasError = true
  }

  if (form.professional_score === undefined) {
    errMsg.value.professional_score = '请评价专业能力'
    hasError = true
  }

  if (form.communication_score === undefined) {
    errMsg.value.communication_score = '请评价沟通效果'
    hasError = true
  }

  if (hasError) {
    throw new Error('评分未完成')
  }
}

// 监听评分变化，清除对应错误信息
watch(() => form.response_score, () => {
  if (form.response_score !== undefined) {
    errMsg.value.response_score = ''
  }
})

watch(() => form.professional_score, () => {
  if (form.professional_score !== undefined) {
    errMsg.value.professional_score = ''
  }
})

watch(() => form.communication_score, () => {
  if (form.communication_score !== undefined) {
    errMsg.value.communication_score = ''
  }
})

const handleSubmit = async () => {
  const openid = userStore.TW_clientInfo?.openid

  if (!formRef.value || !projectId.value || !openid) {
    return
  }

  try {
    await validateRate()
    await formRef.value.validate()

    const formValues = form as FeedbackFormValues

    loading.value = true

    await ProjectService.submitFeedback({
      ...formValues,
      project_id: projectId.value,
      openid,
    })

    formRef.value.resetValidation()

    Object.assign(form, defaultForm)

    if (projectId.value) {
      await navigateTo(
        getRoutePath(RouteKey.TW_客户_项目评价_成功, { projectId: projectId.value }),
      )
    }
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="min-h-full p-client-layout-page">
    <div class="flex flex-col gap-client-layout-page">
      <div class="rounded-lg bg-content p-5">
        <h2 class="mb-2 text-xl font-bold">
          项目信息
        </h2>

        <ClientProjectDetail
          v-if="projectId"
          :projectId="projectId"
        />
      </div>

      <div class="rounded-lg bg-content p-5">
        <h2 class="mb-2 text-xl font-bold">
          项目评价
        </h2>

        <VanNoticeBar>
          此项目已完成，诚邀您留下宝贵评价
        </VanNoticeBar>

        <VanForm
          ref="formRef"
          :model="form"
          :readonly="!canEdit"
          @submit="handleSubmit"
        >
          <div class="space-y-5 [&>.van-field]:!px-2">
            <VanField
              :errorMessage="errMsg?.response_score"
              label="响应速度"
              required
            >
              <template #input>
                <VanRate
                  v-model="form.response_score"
                  :color="rateColor"
                  :readonly="!canEdit"
                />
              </template>
            </VanField>

            <VanField
              :errorMessage="errMsg?.professional_score"
              label="专业能力"
              required
            >
              <template #input>
                <VanRate
                  v-model="form.professional_score"
                  allowHalf
                  :color="rateColor"
                  :readonly="!canEdit"
                />
              </template>
            </VanField>

            <VanField
              :errorMessage="errMsg?.communication_score"
              label="沟通效果"
              required
            >
              <template #input>
                <VanRate
                  v-model="form.communication_score"
                  :color="rateColor"
                  :readonly="!canEdit"
                />
              </template>
            </VanField>

            <VanField
              v-if="availableTags.length > 0"
              label="选择标签"
            >
              <template #input>
                <div class="flex flex-wrap gap-2">
                  <VanTag
                    v-for="tag of availableTags"
                    :key="tag"
                    :plain="!form.tags?.includes(tag)"
                    size="large"
                    :type="form.tags?.includes(tag) ? 'primary' : 'default'"
                    @click="toggleTag(tag)"
                  >
                    {{ tag }}
                  </VanTag>
                </div>
              </template>
            </VanField>

            <VanField
              v-model="form.comment"
              label="评价内容"
              placeholder="请详细描述您的使用体验"
              :readonly="!canEdit"
              rows="4"
              type="textarea"
            />

            <div>
              <VanButton
                block
                :loading="loading"
                loadingText="提交中..."
                nativeType="submit"
                type="primary"
              >
                完成评价
              </VanButton>
            </div>
          </div>
        </VanForm>
      </div>
    </div>
  </div>
</template>
