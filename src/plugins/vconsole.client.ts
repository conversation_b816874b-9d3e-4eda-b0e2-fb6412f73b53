/**
 * vConsole 插件
 *
 * 该插件用于在开发和测试环境中提供移动端调试工具
 * 当 URL 中包含 debug=true 参数时，会自动加载 vConsole
 *
 * 使用方法：
 * 1. 在浏览器地址栏添加 ?debug=true 参数
 * 2. 页面将自动加载 vConsole 调试面板
 */

export default defineNuxtPlugin({
  name: 'vconsole',

  enforce: 'post', // 在其他插件之后执行

  async setup() {
    // 获取当前 URL 的查询参数
    const route = useRoute()

    // 检查 URL 中是否包含 debug=true 参数
    if (route.query.debug === 'true') {
      // 动态导入 vConsole，节省打包体积
      const { default: VConsole } = await import('vconsole')

      const vConsole = new VConsole()

      // 在组件卸载时销毁 vConsole 实例
      onUnmounted(() => {
        if (vConsole) {
          vConsole.destroy()
        }
      })
    }
  },
})
