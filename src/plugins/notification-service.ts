import { merge } from 'lodash-es'
import type { ToastMessageOptions as PrimeVueToastMessageOptions } from 'primevue'
import type { ConfirmationOptions } from 'primevue/confirmationoptions'

interface ToastMessageOptions extends PrimeVueToastMessageOptions {
  /**
   * 是否保持消息可见，直到用户手动关闭
   * @defaultValue false
   */
  keepAlive?: boolean
}

interface ConfirmService {
  dialog: (options?: ConfirmationOptions) => void
  popup: (options?: ConfirmationOptions) => void
}

type ToastServiceFn = (options?: ToastMessageOptions) => void
type ToastSeverity = NonNullable<ToastMessageOptions['severity']>
type ToastService = Record<ToastSeverity, ToastServiceFn>

type DialogService = ReturnType<typeof useDialog>
type DialogOptions = Parameters<DialogService['open']>[1]

/**
 * 通知服务插件
 *
 * 该文件提供了应用程序中使用的各种通知服务，包括：
 * - 确认对话框服务 (ConfirmService) - 用于显示确认操作的对话框
 * - 提示消息服务 (ToastService) - 用于显示不同类型的提示消息
 * - 动态对话框服务 (DialogService) - 用于动态创建和显示自定义内容的对话框
 *
 * 封装的好处：
 * - 统一接口 - 提供一致的 API，简化调用方式，降低使用复杂度
 * - 默认配置 - 预设常用配置，减少重复代码，保持UI风格一致性
 * - 可扩展性 - 可以方便地添加新功能或修改现有功能，而不影响使用方
 * - 关注点分离 - 将通知逻辑与业务逻辑分离，提高代码可维护性
 *
 * 这些服务通过 Nuxt 插件系统提供，可以在应用程序中通过 useNuxtApp() 访问
 */
export default defineNuxtPlugin({
  name: 'notification-service',

  setup() {
    // Confirmation 相关配置和方法 =============================================
    const confirmService = useConfirm()

    const DEFAULT_CONFIRM_OPTIONS: ConfirmationOptions = {
      header: '谨慎操作',
      rejectLabel: '取消',
      rejectProps: {
        severity: 'secondary',
      },
      acceptLabel: '删除',
      acceptProps: {
        severity: 'danger',
      },
    }

    const createConfirmService = (): ConfirmService => {
      return {
        dialog: (options) => {
          confirmService.require(merge({}, DEFAULT_CONFIRM_OPTIONS, options))
        },
        popup: (options) => {
          confirmService.require(merge({}, DEFAULT_CONFIRM_OPTIONS, options))
        },
      }
    }

    // Toast 相关配置和方法 =============================================
    const toastService = useToast()

    const DEFAULT_TOAST_OPTIONS: ToastMessageOptions = {
      life: 3000,
    }

    const createToastService = (): ToastService => {
      const createToastWithSeverity = (severity?: ToastSeverity) => {
        return (options: ToastMessageOptions = DEFAULT_TOAST_OPTIONS) => {
          const { keepAlive, ...restOptions } = options

          if (!isMobile()) {
            const mergedOptions = merge(
              {},
              DEFAULT_TOAST_OPTIONS,
              restOptions,
              { severity },
            )

            toastService.add({
              ...mergedOptions,
              life: keepAlive ? 0 : mergedOptions.life,
            })
          }
          else {
            showToast({
              type: severity === 'success'
                ? 'success'
                : severity === 'error'
                  ? 'fail'
                  : 'text',
              message: options?.detail,
            })
          }
        }
      }

      return {
        success: createToastWithSeverity('success'),
        info: createToastWithSeverity('info'),
        warn: createToastWithSeverity('warn'),
        error: createToastWithSeverity('error'),
        secondary: createToastWithSeverity('secondary'),
        contrast: createToastWithSeverity('contrast'),
        removeAllGroups: toastService.removeAllGroups,
      }
    }

    // Dialog 相关配置和方法 =============================================
    const dialogService = useDialog()

    const DEFAULT_DIALOG_OPTIONS: DialogOptions = {
      props: {
        modal: true,
        closable: true,
        closeOnEscape: true,
        dismissableMask: true,
        pt: {
          root: 'dialog-half',
        },
      },
    }

    const createDialogService = (): DialogService => {
      return {
        open: (component, options) => {
          const mergedOptions = merge({}, DEFAULT_DIALOG_OPTIONS, options)

          return dialogService.open(component, mergedOptions)
        },
      }
    }

    const confirm = createConfirmService()
    const toast = createToastService()
    const dialog = createDialogService()

    return {
      provide: {
        confirm,
        toast,
        dialog,
      },
    }
  },
})
