import { ApiStatus } from '~/enums/common'

interface HandleApiErrorParams {
  errorTitle?: string
  errorMessage?: string
}

function handleApiError(params: HandleApiErrorParams) {
  const { errorTitle, errorMessage } = params

  const { $toast } = useNuxtApp()

  $toast.error({
    summary: errorTitle ?? '请求失败',
    detail: errorMessage,
  })
}

/**
 * Nuxt API 请求插件
 *
 * 该插件创建了一个预配置的 Fetch 实例，用于处理所有的 API 请求。
 *
 * 主要功能：
 * - 配置统一的 API 基础URL
 * - 支持请求拦截（可配置认证token）
 * - 处理响应错误（如401未授权自动跳转）
 * - 支持自定义响应适配器处理第三方 API
 *
 * 使用方式：
 * 在组件中通过 useNuxtApp().$api 访问
 *
 * @example
 * const { $api } = useNuxtApp()
 * const data = await $api('/endpoint')
 *
 * // 使用适配器处理第三方 API
 * const thirdPartyData = await $api('https://api.example.com/data', {
 *   responseAdapter: (data, response) => {
 *     // 对响应进行转换或处理
 *     return transformedData
 *   }
 * })
 */
export default defineNuxtPlugin((nuxtApp) => {
  const runtimeConfig = useRuntimeConfig()
  const { apiBase } = runtimeConfig.public

  const appConfig = useAppConfig()
  const publicApis = appConfig.auth.publicApis

  const userStore = useUserStore()

  const api = $fetch.create({
    baseURL: apiBase,

    timeout: 10 * 1000,

    onRequest({ request, options }) {
      const token = userStore.token

      if (token) {
        const isPublicApi = publicApis.some((path) => typeof request === 'string' && request.includes(path))

        if (!isPublicApi) {
          options.headers.set('Authorization', token)
        }
      }
    },

    async onRequestError({ error }) {
      if (error instanceof Error) {
        const isTimeoutError = error.message.toLocaleLowerCase().includes('timeout')

        if (isTimeoutError) {
          handleApiError({
            errorTitle: '请求超时',
            errorMessage: '服务器响应时间过长，请稍后再试',
          })
        }
      }
    },

    async onResponse({ response, options }) {
      const res = options.responseAdapter
        ? options.responseAdapter(response._data, response)
        : response._data as ApiResponse

      const { status, message, result } = res

      // 如果后端返回的状态码不是成功状态，则抛出错误
      if (status !== ApiStatus.Success) {
        if (!options.ignoreResponseError) {
          if (status !== ApiStatus.TokenExpired) {
          // TODO: 看是否能在下面的 onResponseError 统一做错误处理
            handleApiError({ errorMessage: message })
          }
        }

        // 根据不同的 HTTP 状态码定制错误页面展示逻辑
        if ([
          ApiStatus.Unauthorized,
          ApiStatus.LinkExpiredOrInvalid,
          ApiStatus.LicenseInvalid,
        ].includes(status)) {
          throwCustomError({
            statusCode: status,
            message,
          })
        }

        if (status === ApiStatus.TokenExpired) {
          await nuxtApp.runWithContext(() => {
            handleLogout()
          })
        }

        throw new Error(message, {
          cause: {
            status,
            message,
          },
        })
      }

      if (options.raw) {
        const dataWithRaw: RawResult = { ...(result || {}), __raw: res }
        response._data = dataWithRaw
      }
      else {
        response._data = result
      }
    },

    async onResponseError({ response, options }) {
      if (!options.ignoreResponseError) {
        if (response._data?.status !== ApiStatus.TokenExpired) {
          handleApiError({ errorMessage: response._data?.message })
        }
      }
    },
  })

  return {
    provide: {
      api,
    },
  }
})
