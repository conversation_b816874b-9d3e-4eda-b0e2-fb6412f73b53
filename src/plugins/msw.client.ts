/**
 * MSW (Mock Service Worker) 插件
 *
 * 作用：
 * - 在开发环境中模拟 API 响应，实现前后端并行开发
 * - 拦截实际的网络请求，返回预设的模拟数据
 * - 无需修改实际的 API 调用代码，便于后续切换到真实接口
 *
 * Mock 数据的必要性：
 * - 提高开发效率：
 *    - 前端开发不依赖后端 API 完成度
 *    - 可以并行开发，加快项目进度
 * - 测试场景模拟：
 *    - 模拟各种 API 响应状态（成功、失败、加载中）
 *    - 模拟网络错误、超时等异常情况
 * - 离线开发：
 *    - 在后端服务不可用时仍可继续开发
 *    - 适合本地开发和演示环境
 */
export default defineNuxtPlugin(async () => {
  const runtimeConfig = useRuntimeConfig()
  const { enableMock } = runtimeConfig.public

  if (enableMock) {
    // 动态导入 MSW worker 实例
    // 这样可以避免在生产环境中包含 mock 相关代码
    const { worker } = await import('~/mocks/browser')

    worker.start({
      // 对未处理的请求采取 bypass 策略
      // bypass: 表示未匹配的请求将直接发送到实际服务器
      onUnhandledRequest: 'bypass',

      // 配置 Service Worker
      serviceWorker: {
        // 指定 Service Worker 文件的路径
        // 这个文件需要通过 `npx msw init public/` 命令生成
        url: '/mockServiceWorker.js',
      },
    })
  }
})
