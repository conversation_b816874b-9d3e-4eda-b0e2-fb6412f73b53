import { ApiStatus } from '~/enums/common'
import { throwCustomError } from '~/utils/error'

/**
 * 环境变量检查插件
 * 用于在项目启动时检查必要的环境变量是否已设置
 */
export default defineNuxtPlugin(() => {
  const runtimeConfig = useRuntimeConfig()

  const requiredEnvVars: Record<string, { name: string, value?: string | boolean, required: boolean }> = {
    NUXT_PUBLIC_RELEASE_ENV: {
      name: '发布环境标识',
      value: runtimeConfig.public.releaseEnv,
      required: true,
    },
    NUXT_PUBLIC_SITE_NAME: {
      name: '站点名称',
      value: runtimeConfig.public.siteName,
      required: true,
    },
    NUXT_PUBLIC_API_HOST: {
      name: '接口服务地址',
      value: runtimeConfig.public.apiHost,
      required: false,
    },
    NUXT_PUBLIC_ENABLE_MOCK: {
      name: 'Mock 数据开关',
      value: runtimeConfig.public.enableMock,
      required: false,
    },
    NUXT_PUBLIC_IS_TEAMWORK: {
      name: '是否为扶摇环境',
      value: isFuYao(),
      required: false,
    },
    NUXT_PUBLIC_WECOM_APP_ID: {
      name: '企业微信 App ID',
      value: runtimeConfig.public.wecomAppId,
      required: false,
    },
    NUXT_PUBLIC_DEEPSEEK_BASE_URL: {
      name: '深度求索 API 地址',
      value: runtimeConfig.public.aiBaseUrl,
      required: isFuYao(),
    },
    NUXT_PUBLIC_DEEPSEEK_API_KEY: {
      name: '深度求索 API 密钥',
      value: runtimeConfig.public.aiApiKey,
      required: isFuYao(),
    },
    NUXT_PUBLIC_N8N_HOST: {
      name: 'N8N 地址',
      value: runtimeConfig.public.n8nHost,
      required: isFuYao(),
    },
  }

  const missingRequiredEnvVars: string[] = []
  const missingOptionalEnvVars: string[] = []

  // 检查环境变量
  for (const [key, info] of Object.entries(requiredEnvVars)) {
    if (typeof info.value === 'undefined') {
      if (info.required) {
        missingRequiredEnvVars.push(`${info.name}(${key})`)
      }
      else {
        missingOptionalEnvVars.push(`${info.name}(${key})`)
      }
    }
  }

  if (import.meta.dev) {
    // 在开发环境中，如果缺少必要的环境变量，跳转到错误页面
    if (missingRequiredEnvVars.length > 0) {
      throwCustomError({
        statusCode: ApiStatus.MissingEnvVars,
        message: '环境变量配置不完整',
        data: {
          missingVars: missingRequiredEnvVars,
        },
      })
    }
  }
})
