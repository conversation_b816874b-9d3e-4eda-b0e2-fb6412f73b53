import {
  dehydrate,
  type DehydratedState,
  hydrate,
  QueryClient,
  VueQueryPlugin,
  type VueQueryPluginOptions,
} from '@tanstack/vue-query'

/**
 * Vue Query 插件
 *
 * 该插件为应用提供数据请求和缓存管理功能。
 *
 * 主要功能:
 * - 配置全局的 Vue Query 客户端实例
 * - 处理服务端状态的序列化/反序列化
 * - 提供统一的数据请求接口
 *
 * 使用方式:
 * 在组件中通过 useQuery 访问
 *
 * @example
 * const { data } = useQuery({
 *   queryKey: ['todos'],
 *   queryFn: () => fetch('/api/todos')
 * })
 */
export default defineNuxtPlugin((nuxt) => {
  const vueQueryState = useState<DehydratedState | null>('vue-query')

  // 创建查询客户端实例，配置默认选项
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  const options: VueQueryPluginOptions = { queryClient }

  // 注册 Vue Query 插件
  nuxt.vueApp.use(VueQueryPlugin, options)

  // 服务端渲染时序列化状态
  if (import.meta.server) {
    nuxt.hooks.hook('app:rendered', () => {
      vueQueryState.value = dehydrate(queryClient)
    })
  }

  // 客户端渲染时还原状态
  if (import.meta.client) {
    nuxt.hooks.hook('app:created', () => {
      hydrate(queryClient, vueQueryState.value)
    })
  }
})
