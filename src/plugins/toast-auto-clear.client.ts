/**
 * 路由变化自动清除 Toast 插件
 *
 * 该插件用于在路由变化时自动清除所有 Toast 消息， 即使是设置了 keepAlive: true 需要用户手动关闭的消息。
 *
 * 使用场景：
 * - 用户在查看某个错误消息后切换路由，无需手动关闭消息
 * - 避免消息在不相关页面持续显示，提升用户体验
 * - 保持界面整洁，减少干扰
 */
export default defineNuxtPlugin({
  name: 'toast-auto-clear',

  dependsOn: ['notification-service'],

  setup() {
    const router = useRouter()

    const { $toast } = useNuxtApp()

    // 在每次路由变化后清除所有 Toast 消息
    router.afterEach(() => {
      // 使用 nextTick 确保在 DOM 更新后执行清理，避免在路由变化过程中可能出现的时序问题
      nextTick(() => {
        $toast.removeAllGroups()
      })
    })
  },
})
