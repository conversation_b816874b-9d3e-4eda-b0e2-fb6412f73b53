import { vPermission } from '~/directives/permission'

/**
 * 权限控制插件
 *
 * 这个插件注册了一个全局的 v-permission 指令，用于控制页面元素的访问权限。
 *
 * 使用场景：
 * - 在需要进行权限控制的 DOM 元素上使用 v-permission 指令
 * - 可以传入单个权限标识或权限标识数组
 * - 如果用户没有对应权限，该元素会被从 DOM 中移除
 *
 * 权限标识格式：
 * - 使用 `~/enums/user.ts` 中的 `PermissionFlag` 枚举类型
 *
 * 示例：
 * ```vue
 * <!-- 单个权限控制 -->
 * <button v-permission="'perm1'">创建用户</button>
 *
 * <!-- 多个权限控制（满足其中之一即可） -->
 * <button v-permission="['perm1', 'perm2']">管理用户</button>
 * ```
 */
export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive('permission', vPermission)
})
