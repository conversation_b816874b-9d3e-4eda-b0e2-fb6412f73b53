import { array, safeParse, string } from 'valibot'

import AutoflowList from '~/components/AutoflowList.vue'
import { ProjectService } from '~/services-tw/project'
import type { TW_AutoFlow, TW_Project } from '~/types-tw/project'

export default defineNuxtPlugin({
  name: 'helpers',

  dependsOn: ['notification-service'],

  setup() {
    const { $dialog, $toast } = useNuxtApp()

    const runtimeConfig = useRuntimeConfig()
    const { n8nHost } = runtimeConfig.public

    /**
     * 打开流程选择对话框
     *
     * @param projectData 项目数据
     * @param initialSelection 初始选中的流程ID数组
     */
    const openFlowSelectionDialog = (projectData: Pick<TW_Project, 'id'>, options?: {
      initialSelection?: TW_AutoFlow['id'][]
      onDone?: (flowId: TW_AutoFlow['id']) => void
    }) => {
      $dialog.open(h(AutoflowList, {
        selection: options?.initialSelection,
        selectionMode: 'single',
      }), {
        props: {
          header: '选择关联工作流',
        },
        onClose: (opt) => {
          const res = safeParse(array(string()), opt?.data)

          if (res.success) {
            const flowId = res.output.at(0)

            if (flowId) {
              ProjectService.associateAutoFlow(projectData.id, flowId).then(() => {
                options?.onDone?.(flowId)

                $toast.success({
                  summary: '成功关联工作流',
                })
              })
            }
          }
        },
      })
    }

    /**
     * 打开流程详情对话框
     *
     * @param flowData 流程数据
     */
    const openFlowDetailDialog = (flowData: TW_AutoFlow) => {
      $dialog.open(h('iframe', {
        src: `${n8nHost}/workflow/${flowData.id}`,
        class: 'size-full',
      }), {
        props: {
          header: `工作流：${flowData.name}`,
          pt: {
            root: 'p-dialog-maximized overflow-hidden',
            content: '!p-0',
          },
        },
      })
    }

    return {
      provide: {
        openFlowSelectionDialog,
        openFlowDetailDialog,
      },
    }
  },
})
