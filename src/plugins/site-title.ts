/**
 * 网站标题自动设置插件
 *
 * 该插件会自动监听路由变化并设置相应的网页标题
 *
 * 标题的设置优先级为：
 * 1. 页面组件中通过 definePageMeta 定义的 title
 * 2. `~/enums/route/@routeConfig` 配置的标题
 * 3. 仅展示「站点名称」
 */
export default defineNuxtPlugin(() => {
  const runtimeConfig = useRuntimeConfig()
  const { siteName } = runtimeConfig.public

  const { pageTitle } = usePageInfo()

  watchEffect(() => {
    /**
     * 组合最终的网页标题：
     * - 如果有页面标题，格式为：页面标题 - 站点名称
     * - 如果没有页面标题，则只显示站点名称
     */
    const siteTitle = `${pageTitle.value ? `${pageTitle.value} - ` : ''}${siteName}`

    // 设置网页标题
    useHead({ title: siteTitle })
  })
})
