<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { get } from 'lodash-es'

const { themeMode, textColor } = useTheme()

const fadeColor = computed(() => {
  return get($dt('surface.400').value, `${themeMode.value}.value`)
})
</script>

<template>
  <span
    class="text-shimmer"
    :style="{
      '--color-from': fadeColor,
      '--color-via': textColor,
      '--color-to': fadeColor,
    }"
  >
    <slot />
  </span>
</template>

<style scoped>
.text-shimmer {
  font-weight: 500;
  background: linear-gradient(
    to right,
    var(--color-from),
    var(--color-via) 35%,
    var(--color-via) 65%,
    var(--color-to) 100%
  );
  background-clip: text;
  background-size: 200% auto;
  animation: animated-text-gradient 1s linear infinite;
  -webkit-text-fill-color: transparent;
}

@keyframes animated-text-gradient {
  from {
    background-position: 200% center;
  }
}
</style>
