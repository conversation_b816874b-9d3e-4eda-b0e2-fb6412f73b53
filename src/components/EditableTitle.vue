<script setup lang="ts">
import { PencilLineIcon } from 'lucide-vue-next'

import { TooltipShowDelay } from '~/enums/common'

interface Props {
  disabled?: boolean
  maxLength?: number
  hideEditIcon?: boolean
  tooltipConfig?: {
    value?: string
    showDelay?: TooltipShowDelay
  }
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  hideEditIcon: false,
})

const model = defineModel<string>()

const { refKey, ref: inputRef } = useRef<HTMLInputElement>()

const tempValue = ref('')

const isEditing = ref(false)
const isCanceling = ref(false)

const startEditing = () => {
  if (!props.disabled) {
    tempValue.value = model.value ?? ''
    isEditing.value = true
    isCanceling.value = false

    nextTick(() => {
      if (inputRef.value) {
        inputRef.value.focus()
        inputRef.value.select()
      }
    })
  }
}

const cancelEditing = () => {
  isCanceling.value = true
  isEditing.value = false
}

const saveValue = () => {
  if (isCanceling.value) {
    return
  }

  const newValue = tempValue.value.trim()

  if (newValue && newValue !== model.value) {
    model.value = newValue
  }

  isEditing.value = false
}

const handleKeyDown = (ev: KeyboardEvent) => {
  ev.stopPropagation()

  if (ev.key === 'Enter' && !ev.isComposing) {
    saveValue()
  }
  else if (ev.key === 'Escape') {
    cancelEditing()
  }
}
</script>

<template>
  <div class="inline-block font-semibold">
    <div
      v-if="!isEditing"
      v-tooltip="{ value: '点击编辑', showDelay: TooltipShowDelay.Slower, ...tooltipConfig }"
      class="group w-full min-w-0 cursor-text rounded-sm leading-[1.2] inline-flex-center hover:bg-emphasis hover:outline hover:outline-2 hover:outline-emphasis"
      @click="startEditing()"
    >
      <span class="truncate">
        {{ model }}
      </span>

      <span
        v-if="!hideEditIcon"
        class="hidden shrink-0 px-1 group-hover:inline-block"
      >
        <PencilLineIcon class="size-[0.9em]" />
      </span>
    </div>

    <input
      v-else
      :ref="refKey"
      v-model="tempValue"
      class="!rounded-sm !leading-none !outline !outline-1 !outline-offset-1 !outline-surface-500"
      :maxlength="maxLength"
      @blur="saveValue()"
      @keydown="handleKeyDown"
    >
  </div>
</template>
