<script setup lang="ts">
interface Props {
  /** 骨架屏列数 */
  columns?: number
  /** 骨架屏行数 */
  rows?: number
}

withDefaults(defineProps<Props>(), {
  columns: 6,
  rows: 5,
})
</script>

<template>
  <div class="pointer-events-none flex flex-col gap-6 py-5">
    <template
      v-for="i of rows"
      :key="i"
    >
      <div class="gap-6 flex-center">
        <Skeleton
          v-for="j of columns"
          :key="j"
          height="1.5rem"
          :style="{ opacity: 1 - (i - 1) * 0.12 }"
        />
      </div>
    </template>
  </div>
</template>
