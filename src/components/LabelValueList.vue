<script setup lang="ts">
import type { ProValueType } from '~/enums/pro'

defineProps<{
  data?: {
    label: string
    value?: AnyType
    valueType?: ProValueType
  }[]
}>()
</script>

<template>
  <div v-if="data">
    <div
      v-for="(item, rowIndex) of data"
      :key="rowIndex"
      class="grid grid-cols-[150px_1fr] border-b border-divider last:border-b-0"
    >
      <div
        class="border-x border-divider bg-emphasis p-3 font-semibold text-surface-600 flex-center dark:text-surface-300"
        :class="{ 'border-t': rowIndex === 0, 'border-b': rowIndex === data.length - 1 }"
      >
        {{ item.label }}
      </div>

      <div
        class="min-h-full break-all border-r border-divider p-3 flex-center"
        :class="{ 'border-t': rowIndex === 0, 'border-b': rowIndex === data.length - 1 }"
      >
        <Component
          :is="item.value"
          v-if="typeof item.value === 'function'"
        />

        <template v-else>
          {{ item.value || '-' }}
        </template>
      </div>
    </div>
  </div>
</template>
