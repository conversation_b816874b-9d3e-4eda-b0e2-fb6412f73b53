<script setup lang="ts">
import IconSwitchBlue from '~/assets/icons/topo/switch-blue.svg'
import IconSwitchBlueHover from '~/assets/icons/topo/switch-blue-hover.svg'
import IconSwitchRed from '~/assets/icons/topo/switch-red.svg'
import IconSwitchRedHover from '~/assets/icons/topo/switch-red-hover.svg'
import IconSwitchYellow from '~/assets/icons/topo/switch-yellow.svg'
import IconSwitchYellowHover from '~/assets/icons/topo/switch-yellow-hover.svg'
import { TopoElementTypes } from '~/enums/topo-elements'

const props = defineProps<{
  id: string
  data: {
    label: string
    eleType: TopoElementTypes
    // ... 其他数据
  }
}>()

const isHover = ref(false)

const nodeData = toRef(props, 'data')
const eleType = computed(() => nodeData.value.eleType)

const handleMouseEnter = () => {
  isHover.value = true
}

const handleMouseLeave = () => {
  isHover.value = false
}
</script>

<template>
  <div
    class="cursor-pointer flex-col justify-center px-2 pb-2 text-8xl flex-center"
    :data-id="id"
    @mouseenter="handleMouseEnter()"
    @mouseleave="handleMouseLeave()"
  >
    <div v-if="eleType === TopoElementTypes.SwitchBlue">
      <IconSwitchBlue
        v-if="!isHover"
        filled
      />
      <IconSwitchBlueHover
        v-else
        filled
      />
    </div>

    <div v-else-if="eleType === TopoElementTypes.SwitchRed">
      <IconSwitchRed
        v-if="!isHover"
        filled
      />
      <IconSwitchRedHover
        v-else
        filled
      />
    </div>

    <div v-else-if="eleType === TopoElementTypes.SwitchYellow">
      <IconSwitchYellow
        v-if="!isHover"
        filled
      />
      <IconSwitchYellowHover
        v-else
        filled
      />
    </div>

    <div class="text-center text-xs">
      {{ nodeData.label || 'Node' }}
    </div>
  </div>
</template>
