<script setup lang="ts">
import { ChevronDownIcon, ChevronUpIcon } from 'lucide-vue-next'
import { Tag } from 'primevue'

import type TicketEditForm from '~/components/ticket/TicketEditForm.vue'
import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'
import type { TicketType } from '~/enums/ticket'
import { ticketGradeConfig, TicketStatus, ticketTypeConfig } from '~/enums/ticket'

import ProBtnEdit from '../pro/btn/ProBtnEdit.vue'
import ProBtnInfo from '../pro/btn/ProBtnInfo.vue'
import UserSelectorChip from '../user/selector/UserSelectorChip.vue'

import TicketStatusTag from './TicketStatusTag.vue'

const props = withDefaults(defineProps<{
  filters?: {
    objectId?: TicketListItem['object_id']
    ticketType?: TicketType
  }
  /** 是否可以创建工单 */
  canCreate?: boolean
  /** 工单列表的展示位置 */
  viewIn?: 'page' | 'dialog' | 'assigned'
}>(), {
  viewIn: 'page',
})

const filters = toRef(props, 'filters')

const { $toast } = useNuxtApp()
const { tableRef, tableAction } = useTableAction()

const sortField = ref<string>()

const handleSortFieldChange = (sort: string) => {
  sortField.value = sort
}

const { ref: ticketForm, refKey: ticketFormRefKey } = useRef<InstanceType<typeof TicketEditForm>>()

const handleCreateTicket = () => {
  const { objectId, ticketType } = filters.value || {}

  if (objectId && ticketType) {
    ticketForm.value?.openToCreate({
      object_id: objectId,
      object_type: ticketType,
    })
  }
  else {
    $toast.error({ summary: '请先选择对象和工单类型' })
  }
}

const handleUpdateTicket = (ticket: TicketListItem) => {
  ticketForm.value?.openToUpdate(ticket)
}

const activeTicket = ref<TicketListItem>()

const handleCloseDrawer = () => {
  activeTicket.value = undefined
  tableAction.value?.reload()
}

const dataSources = ref<TicketListItem[]>([])

const activeTicketIndex = computed(() => {
  return dataSources.value?.findIndex((it) => it.id === activeTicket.value?.id)
})

const handleViewPrevTicket = () => {
  if (typeof activeTicketIndex.value === 'number') {
    if (activeTicketIndex.value > 0) {
      activeTicket.value = dataSources.value[activeTicketIndex.value - 1]
    }
  }
}

const handleViewNextTicket = () => {
  if (typeof activeTicketIndex.value === 'number') {
    if (activeTicketIndex.value < dataSources.value.length - 1) {
      activeTicket.value = dataSources.value[activeTicketIndex.value + 1]
    }
  }
}

const prevEnabled = computed(() => {
  return typeof activeTicketIndex.value === 'number' && activeTicketIndex.value > 0
})

const nextEnabled = computed(() => {
  return typeof activeTicketIndex.value === 'number' && activeTicketIndex.value < dataSources.value.length - 1
})

const columns = computed<ProTableColumn<TicketListItem>[]>(() => [
  {
    field: 'name',
    header: '工单标题',
    hideInFilter: true,
  },
  {
    field: 'grade',
    header: '重要度',
    valueType: ProValueType.Select,
    valueEnum: toValueEnum(ticketGradeConfig),
    render: (data) => h(Tag, {
      severity: ticketGradeConfig[data.grade].severity,
      value: ticketGradeConfig[data.grade].label,
    }),
  },
  {
    field: 'object_type',
    header: '工单类型',
    valueType: ProValueType.Select,
    valueEnum: toValueEnum(ticketTypeConfig),
    visible: props.viewIn === 'page' || props.viewIn === 'assigned',
    render: (data) => h('span', {
      class: 'text-nowrap',
    }, ticketTypeConfig[data.object_type].label),
  },
  {
    field: 'status',
    header: '状态',
    render: (data) => h(TicketStatusTag, {
      status: data.status,
    }),
  },
  {
    field: 'create_user',
    header: '发起人',
    render: (data) => h(UserSelectorChip, {
      enablePopoverCard: true,
      value: data.create_user,
    }),
  },
  {
    field: 'create_time',
    header: '创建时间',
    valueType: ProValueType.DateTime,
  },
  {
    field: 'schedule_time',
    header: '计划处理时间',
    valueType: ProValueType.DateTime,
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', {
      class: 'table-action-group',
    }, [
      h(ProBtnInfo, {
        onClick: () => activeTicket.value = data,
      }),
      h(ProBtnEdit, {
        class: {
          invisible: data.status !== TicketStatus.Draft,
        },
        onlyIcon: true,
        onClick: () => handleUpdateTicket(data),
      }),
    ]),
  },
])
</script>

<template>
  <div>
    <ProTable
      :ref="tableRef"
      :columns="columns"
      filterDisplay="menu"
      :initialFilters="filters?.ticketType ? { object_type: filters.ticketType } : undefined"
      :queryKey="queryKeys.tickets.all"
      :queryParams="{
        object_id: filters?.objectId,
      }"
      :requestFn="viewIn === 'assigned' ? TicketService.getAssignedTicketList : TicketService.getTickets"
      :toolbarConfig="{
        search: {
          key: 'name',
          placeholder: '搜索工单标题',
        },
      }"
      @update:dataSource="dataSources = $event || []"
      @update:sortField="handleSortFieldChange"
    >
      <template #toolbarRight>
        <div class="gap-2 py-1 flex-center">
          <div class="ml-auto gap-4 flex-center">
            <Button
              v-if="canCreate"
              label="创建工单"
              @click="handleCreateTicket()"
            />
          </div>
        </div>
      </template>
    </ProTable>

    <TicketEditForm
      :ref="ticketFormRefKey"
      typeDisabled
      @done="tableAction?.reload()"
    />

    <Drawer
      class="!w-[clamp(500px,_50vw,_770px)]"
      position="right"
      :visible="!!activeTicket"
      @update:visible="(visible: boolean) => !visible ? handleCloseDrawer() : undefined"
    >
      <template #header>
        <div class="gap-2 flex-center">
          <Button
            v-tooltip.bottom="prevEnabled ? '上一个工单' : undefined"
            class="!size-7 !p-0"
            :disabled="!prevEnabled"
            severity="secondary"
            size="small"
            variant="outlined"
            @click="handleViewPrevTicket()"
          >
            <template #icon>
              <ChevronUpIcon :size="16" />
            </template>
          </Button>

          <Button
            v-tooltip.bottom="nextEnabled ? '下一个工单' : undefined"
            class="!size-7 !p-0"
            :disabled="!nextEnabled"
            severity="secondary"
            size="small"
            variant="outlined"
            @click="handleViewNextTicket()"
          >
            <template #icon>
              <ChevronDownIcon :size="16" />
            </template>
          </Button>
        </div>
      </template>

      <TicketDetail
        :ticketId="activeTicket?.id"
      />
    </Drawer>
  </div>
</template>
