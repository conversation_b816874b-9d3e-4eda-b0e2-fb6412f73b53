<script setup lang="ts">
import { FilePenIcon } from 'lucide-vue-next'
import { number, object } from 'valibot'

import { ticketGradeConfig, TicketStatus, ticketTypeConfig } from '~/enums/ticket'

defineProps<{
  typeDisabled?: boolean
}>()

const emit = defineEmits<{
  done: []
}>()

const { $toast } = useNuxtApp()

const ticketStatus = ref<TicketStatus>()

const {
  loading,
  isFormCreate,
  isFormUpdate,
  modalVisible,
  formValues,
  updatingItem: updateTicket,
  formResolver,
  handleClose,
  handleModalVisibleChange,
  handleSubmit,
  openCreateModal,
  openUpdateModal,
  formTitleText,
  confirmBtnLabel,
} = useFormControl<TicketFormValues, TicketListItem>({
  resolver: object({
    name: schemaNotEmptyString('请填写工单名称'),
    desc: schemaNotEmptyString('请填写工单详情'),
    grade: number('请选择工单等级'),
    object_id: number(),
    object_type: number('请选择工单类型'),
    // HACK: 校验 user_list 时 value 不知道为什么会出现布尔值，导致校验失败，所以暂时关闭校验
    // user_list: schemaNotEmptyArray('请选择工单处理人'),
  }),
  btnLabel: {
    create: '确认创建',
    update: '创建工单',
  },
  formTitle: {
    create: '创建工单',
    update: '编辑工单',
  },
  fetchDetail: (ticket) => TicketService.getTicket(ticket.id),
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const userIds = formValues.value.user_list?.map((it) => it.id)

      const payload: TicketFormUpdateValues = {
        name: states.name.value,
        desc: states.desc.value,
        grade: states.grade.value,
        object_id: states.object_id.value,
        object_type: states.object_type.value,
        user_list: userIds,
        schedule_time: formValues.value.schedule_time,
        status: ticketStatus.value ?? TicketStatus.Pending,
      }

      if (isFormCreate.value) {
        await TicketService.createTicket(payload)
        $toast.success({ summary: '创建工单成功' })
      }
      else if (isFormUpdate.value && updateTicket.value?.id) {
        await TicketService.updateTicket(updateTicket.value.id, payload)
        $toast.success({ summary: '更新工单成功' })
      }

      // 每次请求完成后，都重置为 undefined，防止表单提交时 status 被保留
      ticketStatus.value = undefined
    }
  },
  onSubmitSuccess: () => {
    emit('done')
  },
})

const ticketTypeOptions = computed(() => {
  return Object.entries(ticketTypeConfig).map(([, it]) => ({
    value: it.value,
    label: it.label,
  }))
})

const ticketGradeOptions = computed(() => {
  return Object.entries(ticketGradeConfig).map(([, it]) => ({
    value: it.value,
    label: it.label,
  }))
})

const handleScheduleTimeChange = (date: Date) => {
  formValues.value.schedule_time = formatDate(date)
}

defineExpose({
  openToCreate: openCreateModal,
  openToUpdate: openUpdateModal,
})
</script>

<template>
  <ProDialogForm
    :dialogProps="{
      dismissableMask: false,
      class: 'dialog-form-wider',
    }"
    :formActionGroupProps="{
      confirmButtonProps: {
        label: confirmBtnLabel,
        loading: ticketStatus !== TicketStatus.Draft && loading,
      },
    }"
    :initialValues="formValues"
    :loading="loading"
    :resolver="formResolver"
    :title="formTitleText"
    :visible="modalVisible"
    @cancel="handleClose"
    @submit="handleSubmit"
    @update:visible="handleModalVisibleChange"
  >
    <FormItem
      v-slot="{ id }"
      :hidden="typeDisabled"
      label="工单类型"
      name="object_type"
      required
    >
      <ProSelect
        v-model="formValues.object_type"
        :disabled="typeDisabled"
        :labelId="id"
        :options="ticketTypeOptions"
        placeholder="选择工单类型"
      />
    </FormItem>

    <FormItem
      hidden
      label="工单对象"
      name="object_id"
    />

    <FormItem
      v-slot="{ id }"
      label="工单名称"
      name="name"
      required
    >
      <InputText
        :id="id"
        v-model="formValues.name"
        placeholder="输入工单名称"
      />
    </FormItem>

    <FormItem
      label="工单描述"
      name="desc"
      required
    >
      <Editor
        v-model="formValues.desc"
        editorStyle="height: 320px"
      />
    </FormItem>

    <FormItem
      v-slot="{ id }"
      label="工单等级"
      name="grade"
      required
    >
      <ProSelect
        v-model="formValues.grade"
        :labelId="id"
        :options="ticketGradeOptions"
        placeholder="选择工单等级"
      />
    </FormItem>

    <FormItem
      v-slot="{ id }"
      label="工单处理人"
      name="user_list"
      required
    >
      <UserSelector
        v-model="formValues.user_list"
        :inputId="id"
      />
    </FormItem>

    <FormItem
      v-slot="{ id }"
      label="计划处理时间"
      name="schedule_time"
    >
      <ProDatePicker
        :inputId="id"
        :modelValue="formValues.schedule_time ? new Date(formValues.schedule_time) : undefined"
        placeholder="选择计划处理时间"
        showIcon
        showTime
        @update:modelValue="handleScheduleTimeChange"
      />
    </FormItem>

    <template #extraButtons>
      <Button
        label="存为草稿"
        :loading="ticketStatus === TicketStatus.Draft && loading"
        severity="secondary"
        type="submit"
        @click="ticketStatus = TicketStatus.Draft"
      >
        <template #icon>
          <FilePenIcon :size="16" />
        </template>
      </Button>
    </template>
  </ProDialogForm>
</template>
