<script setup lang="ts">
import type { TicketStatus } from '~/enums/ticket'
import { ticketStatusConfig } from '~/enums/ticket'

defineProps<{
  status: TicketStatus
}>()
</script>

<template>
  <Tag
    :severity="ticketStatusConfig[status].severity"
    :value="ticketStatusConfig[status].label"
  >
    <template #icon>
      <Component
        :is="ticketStatusConfig[status].icon"
        class="size-4 shrink-0"
      />
    </template>
  </Tag>
</template>
