<script setup lang="ts">
import { TicketIcon } from 'lucide-vue-next'

import { TicketType } from '~/enums/ticket'

defineProps<{
  objectId?: TicketListItem['object_id']
  ticketType: TicketType
  ticketCount?: number
}>()

const modalVisible = ref(false)

const handleOpenModal = () => {
  modalVisible.value = true
}

const handleModalVisibleChange = (visible: boolean) => {
  modalVisible.value = visible
}
</script>

<template>
  <ProBtn
    :label="`相关工单${ticketCount ? ` (${ticketCount})` : ''}`"
    severity="info"
    size="small"
    @click="handleOpenModal()"
  >
    <template #icon="{ size }">
      <TicketIcon :size="size" />
    </template>
  </ProBtn>

  <Drawer
    class="!md:w-[850px] !w-[90vw]"
    :header="`工单列表（${ticketType === TicketType.Vul ? '漏洞' : ticketType === TicketType.Compliance ? '基线检查' : '工单'}）`"
    position="right"
    :visible="modalVisible"
    @update:visible="handleModalVisibleChange"
  >
    <TicketList
      canCreate
      :filters="{
        objectId,
        ticketType,
      }"
      viewIn="dialog"
    />
  </Drawer>
</template>
