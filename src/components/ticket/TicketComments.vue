<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import DOMPurify from 'dompurify'
import { Undo2Icon } from 'lucide-vue-next'

import { queryKeys } from '~/enums/query-key'
import { TicketCommentActionStatus, ticketCommentActionStatusConfig, TicketStatus } from '~/enums/ticket'

const props = defineProps<{
  ticketDetail?: Ticket
  ticketId?: Ticket['id']
  onPublish?: (params: Omit<TicketCommentFormValues, 'work_order_id'>) => Promise<void>
  onUpdateComment?: (commentId: TicketComment['id'], params: Omit<TicketCommentFormValues, 'work_order_id'>) => Promise<void>
}>()

const userStore = useUserStore()
const user = computed(() => userStore.user)

const ticketId = toRef(props, 'ticketId')

const { data: ticketComments, refetch: refetchTicketComments } = useQuery({
  queryKey: queryKeys.tickets.comments(ticketId),
  queryFn: () => TicketService.getTicketComments({ work_order_id: ticketId.value! }),
  enabled: !!ticketId.value,
})

const commentValue = ref<string>()

const handlePublishComment = async () => {
  if (ticketId.value) {
    if (typeof props.onPublish === 'function') {
      await props.onPublish({
        content: commentValue.value,
      })

      commentValue.value = undefined

      refetchTicketComments()
    }
  }
}

const handleUpdateComment = async (commentId: TicketComment['id'], params: Omit<TicketCommentFormValues, 'work_order_id'>) => {
  if (ticketId.value) {
    commentValue.value = undefined

    if (typeof props.onUpdateComment === 'function') {
      await props.onUpdateComment(commentId, {
        comment: commentValue.value,
        status: params.status,
      })

      refetchTicketComments()
    }
  }
}

const lastComment = computed(() => {
  return ticketComments.value?.at(-1)
})

/** 是否为创建人 */
const isTicketCreator = computed(() => {
  return props.ticketDetail?.create_user.id === user.value?.id
})

/** 是否为处理人 */
const isTicketHandler = computed(() => {
  return props.ticketDetail?.user_list?.some(({ id }) => id === user.value?.id)
})

/** 是否显示提交审核按钮 */
const showSubmitButton = computed(() => {
  return (
    (props.ticketDetail?.status === TicketStatus.Pending
      || lastComment.value?.status === TicketCommentActionStatus.Withdraw
      || lastComment.value?.status === TicketCommentActionStatus.Reject)
    && isTicketHandler.value
  )
})

/** 是否显示审批按钮（同意/驳回） */
const showApprovalButtons = computed(() => {
  return lastComment.value?.status === TicketCommentActionStatus.Process && isTicketCreator.value
})

const showEditor = computed(() => {
  return (isTicketCreator.value || isTicketHandler.value) && (showSubmitButton.value || showApprovalButtons.value)
})
</script>

<template>
  <div>
    <Divider />

    <template v-if="Array.isArray(ticketComments) && ticketComments.length > 0">
      <div class="text-base font-bold">
        处理记录
      </div>

      <div class="py-2">
        <Timeline
          :pt="{
            eventOpposite: '!hidden',
          }"
          :value="ticketComments"
        >
          <template #marker="{ item }: { item: TicketComment }">
            <span
              class="z-10 size-8 justify-center rounded-full p-1 flex-center"
              :style="{ color: ticketCommentActionStatusConfig[item.status].frontColor }"
            >
              <Component
                :is="ticketCommentActionStatusConfig[item.status].icon"
                :size="18"
              />
            </span>
          </template>

          <template #content="{ item }: { item: TicketComment }">
            <div class="flex flex-col gap-3 py-2">
              <div
                class="text-[13px] font-medium"
                :style="{ color: ticketCommentActionStatusConfig[item.status].frontColor }"
              >
                {{ ticketCommentActionStatusConfig[item.status].label }}
              </div>

              <div class="gap-2 flex-center">
                <UserAvatar
                  :avatar="item.user.avatar"
                  size="normal"
                  :username="item.user.username"
                />

                <div class="flex flex-col gap-0.5">
                  <div class="text-[13px] font-medium">
                    {{ item.user.username }}
                  </div>
                  <div
                    class="text-xs text-secondary"
                    :title="item.create_time"
                  >
                    {{ formatRelativeTime(item.create_time) }}
                  </div>
                </div>

                <div class="ml-auto">
                  <Button
                    v-if="item.status === TicketCommentActionStatus.Process && item.user.id === user?.id"
                    label="撤回提交"
                    severity="secondary"
                    size="small"
                    @click="handleUpdateComment(item.id, { status: TicketCommentActionStatus.Withdraw })"
                  >
                    <template #icon>
                      <Undo2Icon :size="14" />
                    </template>
                  </Button>
                </div>
              </div>

              <p
                v-if="item.content"
                class="inline-block w-auto rounded-md bg-emphasis p-2.5"
                v-html="DOMPurify.sanitize(item.content || '')"
              />

              <p
                v-if="item.comment"
                class="inline-block w-auto rounded-md bg-emphasis p-2.5"
                v-html="DOMPurify.sanitize(item.comment || '')"
              />
            </div>
          </template>
        </Timeline>
      </div>
    </template>

    <div v-if="ticketDetail?.status && ![TicketStatus.Draft, TicketStatus.Approved, TicketStatus.Terminated].includes(ticketDetail.status)">
      <Editor
        v-if="showEditor"
        v-model="commentValue"
        placeholder="输入提交内容"
      />

      <div class="justify-end gap-2 pt-2 flex-center">
        <template v-if="showSubmitButton">
          <Button
            label="提交审核"
            size="small"
            @click="handlePublishComment()"
          />
        </template>

        <template v-if="showApprovalButtons">
          <Button
            label="驳回"
            severity="danger"
            size="small"
            variant="outlined"
            @click="handleUpdateComment(lastComment!.id, { status: TicketCommentActionStatus.Reject })"
          />

          <Button
            label="同意"
            size="small"
            @click="handleUpdateComment(lastComment!.id, { status: TicketCommentActionStatus.Approve })"
          />
        </template>
      </div>
    </div>
  </div>
</template>
