<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import DOMPurify from 'dompurify'
import { CalendarCheckIcon, CircleFadingArrowUpIcon, ClockIcon, LinkIcon, TextIcon, TicketIcon, UserPlusIcon, UsersIcon } from 'lucide-vue-next'

import { DateFormat } from '~/enums/common'
import { queryKeys } from '~/enums/query-key'
import { ticketStatusConfig, TicketType } from '~/enums/ticket'

const props = defineProps<{
  ticketId?: Ticket['id']
}>()

const ticketId = toRef(props, 'ticketId')

const { data: ticket, refetch: refetchTicketDetail } = useQuery({
  queryKey: queryKeys.tickets.detail(ticketId),
  queryFn: () => {
    if (ticketId.value) {
      return TicketService.getTicket(ticketId.value)
    }

    return null
  },
})

const ticketInfo = computed(() => [
  {
    label: '工单类型',
    value: ticket.value?.object_type_name,
    icon: TicketIcon,
    displayType: 'text',
  },
  {
    label: '关联对象',
    value: ticket.value?.object_id,
    icon: LinkIcon,
  },
  {
    label: '状态',
    value: ticket.value?.status,
    icon: CircleFadingArrowUpIcon,
  },
  {
    label: '发起人',
    value: ticket.value?.create_user,
    icon: UserPlusIcon,
    displayType: 'text',
  },
  {
    label: '处理人',
    value: ticket.value?.user_list,
    icon: UsersIcon,
    displayType: 'text',
  },
  {
    label: '创建时间',
    value: ticket.value?.create_time,
    icon: ClockIcon,
    displayType: 'text',
  },
  {
    label: '计划处理时间',
    value: ticket.value?.schedule_time,
    icon: CalendarCheckIcon,
    displayType: 'text',
  },
  {
    label: '工单描述',
    value: ticket.value?.desc,
    icon: TextIcon,
    displayType: 'text',
  },
] as const)

const ticketObjectType = computed(() => ticket.value?.object_type)

const handlePublishComment = async (params: TicketCommentFormValues) => {
  if (ticketId.value) {
    await TicketService.createTicketComment({
      work_order_id: ticketId.value,
      content: params.content,
    })

    refetchTicketDetail()
  }
}

const handleUpdateComment = async (commentId: TicketComment['id'], params: TicketCommentFormValues) => {
  if (ticketId.value) {
    await TicketService.updateTicketComment(commentId, {
      work_order_id: ticketId.value,
      comment: params.comment,
      status: params.status,
    })

    refetchTicketDetail()
  }
}
</script>

<script lang="ts">
const dialogVisible = ref(false)

const ticketStatusOptions = computed(() => {
  return Object.entries(ticketStatusConfig).map(([, it]) => ({
    value: it.value,
    label: it.label,
  }))
})
</script>

<template>
  <div v-if="ticket">
    <div class="pb-4">
      <div class="text-xl font-bold">
        {{ ticket.name }}
      </div>
    </div>

    <div class="flex flex-col">
      <div
        v-for="item of ticketInfo"
        :key="item.label"
      >
        <div class="flex gap-4">
          <div class="h-10 min-w-[140px] gap-2 py-1 inline-flex-center text-secondary">
            <Component
              :is="item.icon"
              :size="15"
            />
            {{ item.label }}
          </div>

          <div
            class="min-h-10 min-w-0 flex-1 p-1"
            :class="{ 'flex-center': 'displayType' in item && item.displayType === 'text' }"
          >
            <div v-if="item.label === '关联对象'">
              <ProBtnInfo
                label="查看"
                @click="dialogVisible = true"
              />
            </div>

            <div v-else-if="item.label === '状态'">
              <ProSelect
                v-model="ticket.status"
                disabled
                fluid
                :options="ticketStatusOptions"
                size="small"
                unstyled
              >
                <template #value="{ value }">
                  <TicketStatusTag :status="value" />
                </template>

                <template #dropdownicon>
                  {{ '' }}
                </template>
              </ProSelect>
            </div>

            <div v-else-if="item.label === '发起人'">
              <UserSelectorChip
                :key="ticket.create_user.id"
                enablePopoverCard
                :value="ticket.create_user"
              />
            </div>

            <div v-else-if="item.label === '处理人'">
              <div class="flex-wrap gap-2 inline-flex-center">
                <UserSelectorChip
                  v-for="user of item.value"
                  :key="user.id"
                  enablePopoverCard
                  :value="user"
                />
              </div>
            </div>

            <div v-else-if="item.label === '创建时间' || item.label === '计划处理时间'">
              {{ formatDate(item.value, DateFormat.YYYY年MM月DD日HH时MM分) || '--' }}
            </div>

            <div
              v-else-if="item.label === '工单描述'"
              class="-m-2 max-h-52 w-full overflow-auto whitespace-pre rounded-lg p-2 hover:bg-surface-50"
              v-html="DOMPurify.sanitize(item.value || '')"
            />

            <div v-else>
              {{ item.value }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <TicketComments
      :ticketDetail="ticket"
      :ticketId="ticket.id"
      @publish="handlePublishComment($event)"
      @updateComment="handleUpdateComment"
    />

    <Dialog
      v-if="ticket"
      v-model:visible="dialogVisible"
      class="w-[clamp(300px,90vw,600px)]"
      dismissableMask
      header="关联对象"
      modal
    >
      <VulDetail
        v-if="ticketObjectType === TicketType.Vul"
        :vulId="ticket.object_id"
      />

      <BaselineResultContent
        v-if="ticketObjectType === TicketType.Compliance"
        :complianceId="ticket.object_id"
      />
    </Dialog>
  </div>
</template>
