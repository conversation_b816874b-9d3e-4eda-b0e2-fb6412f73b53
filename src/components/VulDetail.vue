<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import DOMPurify from 'dompurify'

import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'
import { getThreatLevelLabel } from '~/enums/security'

const props = defineProps<{
  vulId?: Vul['id']
}>()

const { vulId } = toRefs(props)

const { data: vulDetail, isLoading } = useQuery({
  queryKey: queryKeys.security.vul.detail(vulId),
  queryFn: () => {
    if (vulId.value) {
      return SecurityService.getVulDetail(vulId.value)
    }

    return null
  },
})

const tableData = computed(() => {
  if (vulDetail.value) {
    return [
      { label: '漏洞名称', value: vulDetail.value.name },
      { label: '漏洞编号', value: vulDetail.value.code },
      { label: '威胁等级', value: getThreatLevelLabel(vulDetail.value.level) },
      { label: '发现时间', value: vulDetail.value.create_time, valueType: ProValueType.DateTime },
      { label: '类型', value: vulDetail.value.type },
      { label: 'IP', value: vulDetail.value.ip },
      { label: '端口', value: vulDetail.value.port },
      { label: '标签', value: vulDetail.value.tags?.map((tag) => ({ label: tag, value: tag })), valueType: ProValueType.Tag },
      { label: '所在服务器', value: vulDetail.value.server?.name ?? '-' },
    ]
  }

  return []
})
</script>

<template>
  <article>
    <div
      v-if="isLoading"
      class="space-y-8"
    >
      <div
        v-for="i of 3"
        :key="i"
      >
        <h2 class="mb-4">
          <Skeleton
            height="2rem"
            width="8rem"
          />
        </h2>

        <div class="space-y-3 opacity-90">
          <Skeleton
            v-for="j of 4"
            :key="j"
            height="1.2rem"
            :width="j === 4 ? '50%' : '100%'"
          />
        </div>
      </div>
    </div>

    <template v-else-if="!isLoading && vulDetail">
      <h2 class="mb-4 text-xl font-bold">
        基本信息
      </h2>

      <ProFormLabelValue
        :data="tableData"
        layout="two-column-table"
        readOnly
      />

      <template v-if="vulDetail.desc">
        <h2 class="mb-4 mt-8 text-xl font-bold">
          漏洞描述
        </h2>

        <pre
          class="whitespace-pre-wrap text-base"
          v-html="DOMPurify.sanitize(vulDetail.desc)"
        />
      </template>

      <template v-if="vulDetail.impact">
        <h2 class="mb-4 mt-8 text-xl font-bold">
          涉及影响
        </h2>

        <div>
          <p class="text-base">
            {{ vulDetail.impact }}
          </p>
        </div>
      </template>

      <template v-if="vulDetail.remediation">
        <h2 class="mb-4 mt-8 text-xl font-bold">
          修复建议
        </h2>

        <pre
          class="whitespace-pre-wrap text-base"
          v-html="DOMPurify.sanitize(vulDetail.remediation || '')"
        />
      </template>

      <template v-if="vulDetail.refer_url">
        <h2 class="mb-4 mt-8 text-xl font-bold">
          参考链接
        </h2>

        <CollapsibleContent>
          <div class="space-y-2">
            <div
              v-for="(it, idx) of parseKeyValueText(vulDetail.refer_url)"
              :key="idx"
              class="gap-2 flex-center"
            >
              <span
                v-if="it.label"
                class="whitespace-nowrap"
              >
                {{ it.label }}:
              </span>

              <span class="font-medium">
                <a
                  v-if="it.type === 'link'"
                  class="break-all underline-offset-2 text-secondary hover:underline"
                  :href="it.value"
                  target="_blank"
                >
                  {{ it.value }}
                </a>
                <span v-else>{{ it.value }}</span>
              </span>
            </div>
          </div>
        </CollapsibleContent>
      </template>

      <template v-if="vulDetail.curl_command">
        <h2 class="mb-4 mt-8 text-xl font-bold">
          漏洞利用命令
        </h2>

        <CodeBlock
          :code="vulDetail.curl_command"
          lang="shell"
          maxHeight="400px"
        />
      </template>

      <template v-if="vulDetail.request_body">
        <h2 class="mb-4 mt-8 text-xl font-bold">
          漏洞利用请求体
        </h2>

        <CodeBlock
          :code="vulDetail.request_body"
          lang="shell"
          maxHeight="400px"
        />
      </template>

      <template v-if="vulDetail.response_body">
        <h2 class="mb-4 mt-8 text-xl font-bold">
          漏洞利用响应体
        </h2>

        <CodeBlock
          :code="vulDetail.response_body"
          lang="shell"
          maxHeight="400px"
        />
      </template>
    </template>
  </article>
</template>
