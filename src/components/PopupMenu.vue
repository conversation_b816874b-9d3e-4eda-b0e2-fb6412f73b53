<script setup lang="ts">
import type { ProMenuProps, ProMenuSlots } from '~/components/pro/menu/ProMenu.vue'
import ProMenu from '~/components/pro/menu/ProMenu.vue'

const { ref: menuRef, refKey } = useRef<InstanceType<typeof ProMenu>>()

defineProps<{
  /** 弹出菜单的选项，适配 PrimeVue 的 MenuItem 类型 */
  options: ProMenuProps['model']
  wrapperClass?: string
}>()

const emit = defineEmits<{
  open: []
  hide: []
}>()

const handleClick = (ev: MouseEvent) => {
  menuRef.value?.popMenu?.toggle(ev)
}

const isMenuShow = ref(false)

const handleShow = () => {
  isMenuShow.value = true
  emit('open')
}

const handleHide = () => {
  isMenuShow.value = false
  emit('hide')
}

defineSlots<ProMenuSlots & {
  default: () => VNode[]
}>()
</script>

<template>
  <div
    class="inline-block"
    :class="wrapperClass"
    @click.stop="handleClick($event)"
  >
    <slot :isMenuShow="isMenuShow" />
  </div>

  <ProMenu
    :ref="refKey"
    :model="options"
    popup
    @hide="handleHide"
    @show="handleShow"
  />
</template>
