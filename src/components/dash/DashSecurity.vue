<script setup lang="ts">
import { TaskType } from '~/enums/asset'

const value = ref('vul')
</script>

<template>
  <div class="flex flex-col gap-4">
    <ProSelectButton
      v-model="value"
      :options="[
        { label: '漏洞扫描', value: 'vul' },
        { label: '合规检查', value: 'compliance' },
      ]"
    />

    <template v-if="value === 'vul'">
      <StatsVulCount />

      <div class="flex gap-4">
        <Card class="min-h-10 flex-1 border border-divider">
          <template #content>
            <StatsVulLevel />
          </template>
        </Card>

        <Card class="min-h-10 w-1/2 border border-divider">
          <template #content>
            <StatsTaskAsset :taskType="TaskType.VulScan" />
          </template>
        </Card>
      </div>

      <div class="flex gap-4">
        <Card class="min-h-10 flex-1 border border-divider">
          <template #content>
            <StatsVulType />
          </template>
        </Card>
      </div>

      <div class="flex gap-4">
        <div class="basis-80">
          <StatsVulLibLevel title="漏洞库等级分布" />
        </div>

        <div class="flex-1">
          <Card class="min-h-10 flex-1 border border-divider">
            <template #content>
              <StatsVulVendor />
            </template>
          </Card>
        </div>
      </div>
    </template>

    <template v-else-if="value === 'compliance'">
      <StatsComplianceCount />

      <div class="flex gap-4">
        <Card class="min-h-10 flex-1 border border-divider">
          <template #content>
            <StatsTaskAsset :taskType="TaskType.BaselineScan" />
          </template>
        </Card>
      </div>
    </template>
  </div>
</template>
