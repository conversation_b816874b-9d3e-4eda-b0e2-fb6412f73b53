<script setup lang="ts">
interface Strategy {
  id: string
  name: string
  description: string
  riskMitigation: number
  businessInterruption: '低' | '中等' | '高'
  businessInterruptionHours: number
  resourceConsumption: '低' | '中低' | '中等' | '高'
  recommended?: boolean
  color: string
}

const strategies = ref<Strategy[]>([
  {
    id: 'patch',
    name: '立即应用官方补丁',
    description: '直接应用官方发布的安全补丁修复漏洞',
    riskMitigation: 100,
    businessInterruption: '中等',
    businessInterruptionHours: 2,
    resourceConsumption: '中等',
    color: 'bg-blue-500 hover:bg-blue-600',
  },
  {
    id: 'isolation',
    name: '临时网络隔离限制',
    description: '通过网络隔离和访问控制临时缓解风险',
    riskMitigation: 80,
    businessInterruption: '低',
    businessInterruptionHours: 0.5,
    resourceConsumption: '低',
    color: 'bg-cyan-500 hover:bg-cyan-600',
  },
  {
    id: 'combined',
    name: '组合策略(推荐)',
    description: '先实施临时隔离，再分批应用补丁',
    riskMitigation: 100,
    businessInterruption: '低',
    businessInterruptionHours: 0.5,
    resourceConsumption: '中低',
    recommended: true,
    color: 'bg-amber-500 hover:bg-amber-600',
  },
])

const selectedStrategy = ref<Strategy | null>(null)

const selectStrategy = (strategy: Strategy) => {
  selectedStrategy.value = strategy
}
</script>

<template>
  <div class="w-full">
    <h2 class="mb-2 text-xl font-bold">
      情境模拟器
    </h2>
    <p class="mb-4 text-sm">
      模拟不同安全策略的效果评估
    </p>

    <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
      <div
        v-for="strategy of strategies"
        :key="strategy.id"
        class="overflow-hidden rounded-lg border border-divider shadow-sm"
      >
        <Button
          :class="`w-full p-4 text-white text-center font-medium ${strategy.color}`"
          @click="selectStrategy(strategy)"
        >
          {{ strategy.name }}
        </Button>

        <div class="space-y-1 p-4">
          <div class="flex items-center justify-between">
            <span>风险缓解率:</span>
            <span class="font-semibold">{{ strategy.riskMitigation }}%</span>
          </div>

          <div class="flex items-center justify-between">
            <span>业务中断:</span>
            <span>{{ strategy.businessInterruption }} ({{ strategy.businessInterruptionHours }}小时)</span>
          </div>

          <div class="flex items-center justify-between">
            <span>资源消耗:</span>
            <span>{{ strategy.resourceConsumption }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
