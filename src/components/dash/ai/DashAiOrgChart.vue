<script setup lang="ts">
import { ChevronDownIcon, ChevronUpIcon } from 'lucide-vue-next'
import type { OrganizationChartNode } from 'primevue'

// 创建决策树数据
const decisionTreeData = ref<OrganizationChartNode>({
  key: 'root',
  label: '漏洞影响评估',
  type: 'question',
  styleClass: 'bg-primary p-3 rounded-md',
  children: [
    {
      key: '1',
      label: '关键级别资产?',
      type: 'question',
      styleClass: 'bg-primary-600 text-white p-3 rounded-md',
      children: [
        {
          key: '1-1',
          label: '是 (5台)',
          type: 'answer',
          styleClass: 'bg-green-600 text-white p-3 rounded-md',
        },
        {
          key: '1-2',
          label: '否 (15台)',
          type: 'answer',
          styleClass: 'bg-red-600 text-white p-3 rounded-md',
        },
      ],
    },
    {
      key: '2',
      label: '存在外部访问?',
      type: 'question',
      styleClass: 'bg-primary-600 text-white p-3 rounded-md',
      children: [
        {
          key: '2-1',
          label: '是 (8台)',
          type: 'answer',
          styleClass: 'bg-red-600 text-white p-3 rounded-md',
        },
        {
          key: '2-2',
          label: '否 (12台)',
          type: 'answer',
          styleClass: 'bg-green-600 text-white p-3 rounded-md',
        },
      ],
    },
    {
      key: '3',
      label: '有可用补丁了?',
      type: 'question',
      styleClass: 'bg-primary-600 text-white p-3 rounded-md',
      children: [
        {
          key: '3-1',
          label: '是 (10台)',
          type: 'answer',
          styleClass: 'bg-green-600 text-white p-3 rounded-md',
        },
        {
          key: '3-2',
          label: '否 (10台)',
          type: 'answer',
          styleClass: 'bg-red-600 text-white p-3 rounded-md',
        },
      ],
    },
  ],
})

// 选中的节点
const selectedNodes = ref({})
</script>

<template>
  <div>
    <h2 class="mb-4 text-lg font-bold">
      决策树可视化
    </h2>

    <div>
      <OrganizationChart
        v-model:selectionKeys="selectedNodes"
        collapsible
        :pt="{
          node: { class: '!p-0' },
          nodeContent: { class: 'border-0 bg-transparent p-0' },
          lines: { class: 'text-slate-500' },
        }"
        :value="decisionTreeData"
      >
        <template #default="{ node }">
          <div
            class="min-w-20 rounded-md p-2 transition-all duration-200"
            :class="[
              node.styleClass,
            ]"
          >
            {{ node.label }}
          </div>
        </template>

        <template #toggleicon="{ expanded }">
          <span class="flex size-full items-center justify-center">
            <Component
              :is="expanded ? ChevronDownIcon : ChevronUpIcon"
              :size="16"
            />
          </span>
        </template>
      </OrganizationChart>
    </div>
  </div>
</template>
