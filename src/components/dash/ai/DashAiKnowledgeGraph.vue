<script setup lang="ts">
import * as d3 from 'd3'

// 定义知识图谱数据接口
interface KnowledgeNode {
  id: string
  name: string
  category?: string
  value?: number
  x?: number
  y?: number
  fx?: number | null
  fy?: number | null
}

interface KnowledgeLink {
  source: string | KnowledgeNode
  target: string | KnowledgeNode
  label?: string
}

// 模拟数据 - 类似图片中的CVE漏洞关系图
const nodes: KnowledgeNode[] = [
  { id: 'cve-2024-1234', name: 'CVE-2024-1234', category: 'vulnerability', value: 40 },
  { id: 'web-service', name: 'Web服务器', category: 'component', value: 25 },
  { id: 'api-endpoint', name: 'API接口', category: 'component', value: 25 },
  { id: 'https-service', name: 'HTTPS服务', category: 'component', value: 25 },
  { id: 'exploit-code', name: '近期代码执行漏洞', category: 'exploit', value: 30 },
  { id: 'rights-escalation', name: '权限提升', category: 'impact', value: 25 },
  { id: 'data-leakage', name: '数据泄露', category: 'impact', value: 25 },
]

const links: KnowledgeLink[] = [
  { source: 'cve-2024-1234', target: 'web-service' },
  { source: 'cve-2024-1234', target: 'api-endpoint' },
  { source: 'cve-2024-1234', target: 'https-service' },
  { source: 'cve-2024-1234', target: 'exploit-code' },
  { source: 'exploit-code', target: 'rights-escalation' },
  { source: 'exploit-code', target: 'data-leakage' },
]

// 颜色映射
const colorMap: Record<string, string> = {
  vulnerability: '#4169E1', // 蓝色
  component: '#6495ED', // 浅蓝色
  exploit: '#1E90FF', // 深蓝色
  impact: '#87CEEB', // 天蓝色
}

const graphRef = ref<HTMLElement | null>(null)
let simulation: d3.Simulation<KnowledgeNode, d3.SimulationLinkDatum<KnowledgeNode>> | null = null

// 初始化知识图谱
const initGraph = () => {
  if (!graphRef.value) {
    return
  }

  // 清除之前的图表
  d3.select(graphRef.value).selectAll('*').remove()

  const width = graphRef.value.clientWidth
  const height = graphRef.value.clientHeight

  // 创建SVG容器
  const svg = d3.select(graphRef.value)
    .append('svg')
    .attr('width', width)
    .attr('height', height)
    .attr('viewBox', [0, 0, width, height])

  // 创建图形容器
  const g = svg.append('g')

  // 添加缩放功能
  const zoom = d3.zoom<SVGSVGElement, unknown>()
    .extent([[0, 0], [width, height]])
    .scaleExtent([0.1, 8])
    .on('zoom', (event: d3.D3ZoomEvent<SVGSVGElement, unknown>) => {
      g.attr('transform', event.transform.toString())
    })

  svg.call(zoom as UnsafeAny)

  // 拖拽事件处理函数
  function dragstarted(event: d3.D3DragEvent<SVGGElement, KnowledgeNode, unknown>, d: KnowledgeNode) {
    if (!event.active && simulation) {
      simulation.alphaTarget(0.3).restart()
    }

    d.fx = d.x
    d.fy = d.y
  }

  function dragged(event: d3.D3DragEvent<SVGGElement, KnowledgeNode, unknown>, d: KnowledgeNode) {
    d.fx = event.x
    d.fy = event.y
  }

  function dragended(event: d3.D3DragEvent<SVGGElement, KnowledgeNode, unknown>, d: KnowledgeNode) {
    if (!event.active && simulation) {
      simulation.alphaTarget(0)
    }

    d.fx = null
    d.fy = null
  }

  // 创建力导向图
  simulation = d3.forceSimulation<KnowledgeNode>()
    .nodes(nodes)
    .force('link', d3.forceLink<KnowledgeNode, d3.SimulationLinkDatum<KnowledgeNode>>()
      .id((d) => d.id)
      .links(links as d3.SimulationLinkDatum<KnowledgeNode>[])
      .distance(100))
    .force('charge', d3.forceManyBody().strength(-300))
    .force('center', d3.forceCenter(width / 2, height / 2))
    .force('collision', d3.forceCollide<KnowledgeNode>().radius((d) => (d.value || 20) + 10))

  // 创建连线
  const link = g.append('g')
    .attr('class', 'links')
    .selectAll('line')
    .data(links)
    .enter()
    .append('line')
    .attr('class', 'stroke-gray-300 stroke-[1.5px]')

  // 创建节点组
  const nodeGroup = g.append('g')
    .attr('class', 'nodes')
    .selectAll('g')
    .data(nodes)
    .enter()
    .append('g')
    .call(d3.drag<SVGGElement, KnowledgeNode>()
      .on('start', dragstarted)
      .on('drag', dragged)
      .on('end', dragended))

  // 添加节点圆圈
  nodeGroup.append('circle')
    .attr('r', (d) => d.value || 20)
    .attr('fill', (d) => colorMap[d.category || 'default'] || '#6495ED')
    .attr('class', 'opacity-80 cursor-pointer hover:opacity-100 transition-opacity duration-300')

  // 添加节点文本
  nodeGroup.append('text')
    .text((d) => d.name)
    .attr('dy', 4)
    .attr('dx', (d) => (d.value || 20) + 5)
    .attr('class', 'text-xs text-gray-700 font-medium pointer-events-none')

  // 更新力导向图
  simulation.on('tick', () => {
    link
      .attr('x1', (d: UnsafeAny) => (d.source as KnowledgeNode).x || 0)
      .attr('y1', (d: UnsafeAny) => (d.source as KnowledgeNode).y || 0)
      .attr('x2', (d: UnsafeAny) => (d.target as KnowledgeNode).x || 0)
      .attr('y2', (d: UnsafeAny) => (d.target as KnowledgeNode).y || 0)

    nodeGroup.attr('transform', (d) => `translate(${d.x || 0},${d.y || 0})`)
  })
}

// 监听窗口大小变化，重新渲染图表
const handleResize = () => {
  initGraph()
}

onMounted(() => {
  initGraph()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)

  if (simulation) {
    simulation.stop()
  }
})
</script>

<template>
  <div class="flex h-full flex-col">
    <h2 class="mb-1 text-lg font-bold">
      安全知识图谱
    </h2>
    <p class="mb-2 text-sm text-surface-500">
      实时展示相关安全概念与关系
    </p>

    <div
      ref="graphRef"
      class="min-h-[300px] flex-1"
    />
  </div>
</template>
