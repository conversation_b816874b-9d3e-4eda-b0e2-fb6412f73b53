<script setup lang="ts">
import type { ApexOptions } from 'apexcharts'

// 模拟数据 - 根据图片中的数据
const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月']

// 图表数据 - 根据图片中的数据
const chartData = {
  modelRecognition: [10, 25, 20, 30, 35, 45, 55, 65, 68], // 模型识别能力提升
}

// 图表配置
const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: 'light', // 固定为浅色主题
  },
  chart: {
    type: 'line',
    height: 200,
    toolbar: {
      show: false,
    },
    background: 'transparent',
  },
  colors: ['#3b82f6'], // 蓝色线条
  series: [
    {
      name: '模型识别能力',
      data: chartData.modelRecognition,
    },
  ],
  xaxis: {
    categories: months,
    labels: {
      show: false, // 隐藏 x 轴标签
    },
    axisBorder: {
      show: false, // 隐藏 x 轴边框
    },
    axisTicks: {
      show: false, // 隐藏 x 轴刻度
    },
  },
  yaxis: {
    labels: {
      show: false, // 隐藏 y 轴标签
    },
    axisBorder: {
      show: false, // 隐藏 y 轴边框
    },
    axisTicks: {
      show: false, // 隐藏 y 轴刻度
    },
  },
  stroke: {
    curve: 'smooth',
    width: 3,
  },
  grid: {
    show: false, // 隐藏网格线
  },
  markers: {
    size: 6,
    colors: ['#3b82f6'],
    strokeColors: '#ffffff',
    strokeWidth: 1,
  },
  dataLabels: {
    enabled: false,
  },
  tooltip: {
    enabled: true,
    theme: 'light',
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div>
    <div ref="chartContainer" />
  </div>
</template>
