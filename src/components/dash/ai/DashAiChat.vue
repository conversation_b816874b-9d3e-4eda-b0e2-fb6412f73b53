<script setup lang="ts">
import { MessageSquarePlus, MicIcon, PaperclipIcon, SendIcon } from 'lucide-vue-next'

// 定义消息类型
interface ChatMessage {
  id: string
  content: string
  isUser: boolean
  timestamp: Date
}

// 示例消息数据
const messages = ref<ChatMessage[]>([
  {
    id: '1',
    content: '您好，我是安全AI专家助手。我可以帮您分析安全风险、解答安全问题、提供专业建议。\n请问有什么可以帮助您的？',
    isUser: false,
    timestamp: new Date(Date.now() - 60000 * 30),
  },
  {
    id: '2',
    content: '分析当前最严重的安全风险是什么？',
    isUser: true,
    timestamp: new Date(Date.now() - 60000 * 25),
  },
  {
    id: '3',
    content: '基于当前资产和漏洞数据分析，最严重的安全风险是5台关键业务服务器存在的未修复高危漏洞 CVE-2024-1234，该漏洞可能导致远程代码执行。建议优先处理这些服务器的漏洞修复工作，临时缓解措施包括限制这些服务器的外部访问权限。',
    isUser: false,
    timestamp: new Date(Date.now() - 60000 * 24),
  },
  {
    id: '4',
    content: '查询CVE-2024-1234漏洞的影响范围和修复建议',
    isUser: true,
    timestamp: new Date(Date.now() - 60000 * 15),
  },
  {
    id: '5',
    content: '漏洞CVE-2024-1234影响范围：\n- 5台关键业务服务器\n- 3台数据库服务器\n\n修复建议：\n1. 立即应用官方补丁\n2. 如无法立即修复，建议限制访问IP\n3. 加强网络监控，检测可能的攻击行为',
    isUser: false,
    timestamp: new Date(Date.now() - 60000 * 14),
  },
])

// 当前输入的消息
const inputMessage = ref('')
// 是否正在加载（等待AI回复）
const isLoading = ref(false)
// 聊天容器引用
const chatContainerRef = ref<HTMLElement | null>(null)

// 预设问题列表
const suggestedQuestions = [
  '分析当前最严重的安全风险是什么？',
  '查询CVE-2024-1234漏洞的影响范围和修复建议',
  '如何优化我们的漏洞修复流程？',
  '生成上个月的安全态势报告',
  '分析我们的关键资产安全状况',
]

// 滚动到底部
const scrollToBottom = () => {
  setTimeout(() => {
    if (chatContainerRef.value) {
      chatContainerRef.value.scrollTop = chatContainerRef.value.scrollHeight
    }
  }, 100)
}

// 发送消息
const sendMessage = () => {
  if (!inputMessage.value.trim()) {
    return
  }

  // 添加用户消息
  const userMessage: ChatMessage = {
    id: Date.now().toString(),
    content: inputMessage.value,
    isUser: true,
    timestamp: new Date(),
  }
  messages.value.push(userMessage)

  // 清空输入框
  const userInput = inputMessage.value
  inputMessage.value = ''

  // 模拟AI回复
  isLoading.value = true
  setTimeout(() => {
    // 这里是模拟的AI回复，实际应用中应该调用API
    let aiResponse = ''

    if (userInput.includes('风险')) {
      aiResponse = '基于当前资产和漏洞数据分析，最严重的安全风险是5台关键业务服务器存在的未修复高危漏洞 CVE-2024-1234，该漏洞可能导致远程代码执行。建议优先处理这些服务器的漏洞修复工作，临时缓解措施包括限制这些服务器的外部访问权限。'
    }
    else if (userInput.includes('CVE-2024-1234')) {
      aiResponse = '漏洞CVE-2024-1234影响范围：\n- 5台关键业务服务器\n- 3台数据库服务器\n\n修复建议：\n1. 立即应用官方补丁\n2. 如无法立即修复，建议限制访问IP\n3. 加强网络监控，检测可能的攻击行为'
    }
    else {
      aiResponse = '我已收到您的问题，正在分析中。基于当前信息，我建议您进一步提供更多细节，以便我能提供更准确的安全建议。您可以描述具体的系统环境、安全需求或者特定的安全事件。'
    }

    // 添加AI回复
    messages.value.push({
      id: Date.now().toString(),
      content: aiResponse,
      isUser: false,
      timestamp: new Date(),
    })

    isLoading.value = false

    // 滚动到底部
    scrollToBottom()
  }, 1500)
}

// 选择预设问题
const selectSuggestedQuestion = (question: string) => {
  inputMessage.value = question
  sendMessage()
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
}

// 监听消息变化，自动滚动到底部
onMounted(() => {
  scrollToBottom()
})

// 处理按键事件
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 重新开始对话
const restartConversation = () => {
  messages.value = [
    {
      id: '1',
      content: '您好，我是安全AI专家助手。我可以帮您分析安全风险、解答安全问题、提供专业建议。\n请问有什么可以帮助您的？',
      isUser: false,
      timestamp: new Date(),
    },
  ]
}
</script>

<template>
  <div class="flex h-full flex-col overflow-hidden rounded-lg bg-content">
    <!-- 聊天头部 -->
    <div class="flex items-center justify-between border-b border-divider pb-4">
      <h2 class="text-lg font-bold">
        安全 AI 助手
      </h2>
      <Button
        v-tooltip.top="'重新开始对话'"
        aria-label="重新开始对话"
        rounded
        severity="secondary"
        text
        @click="restartConversation"
      >
        <template #icon>
          <MessageSquarePlus :size="16" />
        </template>
      </Button>
    </div>

    <!-- 聊天内容区域 -->
    <div
      ref="chatContainerRef"
      class="flex-1 space-y-4 overflow-y-auto p-4"
    >
      <div
        v-for="message of messages"
        :key="message.id"
        class="flex"
        :class="message.isUser ? 'justify-end' : 'justify-start'"
      >
        <div
          class="max-w-[85%] space-y-1 rounded-lg p-3"
          :class="message.isUser
            ? 'bg-primary-500 text-white rounded-tr-none'
            : 'bg-emphasis rounded-tl-none'"
        >
          <div class="whitespace-pre-wrap">
            {{ message.content }}
          </div>
          <div
            class="text-right text-xs opacity-70"
            :class="message.isUser ? 'text-white/70' : 'text-secondary'"
          >
            {{ formatTime(message.timestamp) }}
          </div>
        </div>
      </div>

      <!-- 加载指示器 -->
      <div
        v-if="isLoading"
        class="flex justify-start"
      >
        <div class="rounded-lg rounded-tl-none bg-emphasis p-3">
          <ProgressSpinner
            animationDuration=".5s"
            fill="var(--surface-ground)"
            strokeWidth="4"
            style="width: 20px; height: 20px"
          />
        </div>
      </div>
    </div>

    <!-- 预设问题区域 -->
    <div
      v-if="messages.length <= 2"
      class="border-t border-divider px-4 py-3"
    >
      <div class="mb-2 text-sm font-medium text-secondary">
        您可能想问：
      </div>
      <div class="flex flex-wrap gap-2">
        <Button
          v-for="(question, index) of suggestedQuestions"
          :key="index"
          class="text-left"
          outlined
          severity="secondary"
          size="small"
          @click="selectSuggestedQuestion(question)"
        >
          {{ question }}
        </Button>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="border-t border-divider p-3 pb-0">
      <div class="flex gap-2">
        <Textarea
          v-model="inputMessage"
          autoResize
          class="flex-1"
          placeholder="输入您的问题或需求..."
          rows="1"
          @keydown="handleKeyDown"
        />
        <div class="flex gap-1">
          <Button
            v-tooltip.top="'上传附件'"
            aria-label="附件"
            icon="pi pi-paperclip"
            rounded
            severity="secondary"
            text
          >
            <template #icon>
              <PaperclipIcon :size="18" />
            </template>
          </Button>
          <Button
            v-tooltip.top="'语音输入'"
            aria-label="语音输入"
            icon="pi pi-microphone"
            rounded
            severity="secondary"
            text
          >
            <template #icon>
              <MicIcon :size="18" />
            </template>
          </Button>
          <Button
            aria-label="发送"
            :disabled="!inputMessage.trim()"
            icon="pi pi-send"
            rounded
            @click="sendMessage"
          >
            <template #icon>
              <SendIcon :size="18" />
            </template>
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
