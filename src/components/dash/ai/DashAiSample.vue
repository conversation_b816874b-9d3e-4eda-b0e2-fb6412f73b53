<script setup lang="ts">
interface Tag {
  text: string
  type: 'success' | 'warning' | 'danger' | 'info'
}

interface SimilarCase {
  id: string
  similarity: number
  fixTime: number
  tags?: Tag[]
}

// 模拟数据
const similarCases = ref<SimilarCase[]>([
  {
    id: 'CVE-2023-8127',
    similarity: 89,
    fixTime: 4,
  },
  {
    id: 'CVE-2024-0472',
    similarity: 76,
    fixTime: 6,
    tags: [{ text: '是(5台)', type: 'success' }],
  },
  {
    id: 'CVE-2023-9186',
    similarity: 72,
    fixTime: 5,
  },
])
</script>

<template>
  <div>
    <h2 class="mb-1 text-lg font-bold">
      相似案例分析
    </h2>
    <p class="mb-2 text-sm text-surface-500">
      基于历史数据的相关案例
    </p>

    <div class="space-y-3">
      <!-- 案例卡片 -->
      <div
        v-for="(item, index) of similarCases"
        :key="index"
        class="rounded-lg border border-gray-200 bg-gray-50 p-4 transition-shadow hover:shadow-md"
      >
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-bold text-gray-800">
            {{ item.id }}
          </h3>
        </div>

        <div class="mt-2 text-gray-600">
          <span>相似度: {{ item.similarity }}% | </span>
          <span>修复时间: {{ item.fixTime }}小时</span>
        </div>
      </div>
    </div>
  </div>
</template>
