<script setup lang="ts">
interface AiCapability {
  name: string
  value: number
}

interface OptimizationSuggestion {
  id: number
  title: string
  description: string
  riskReduction: number
}

// AI 能力指标数据
const capabilities = ref<AiCapability[]>([
  { name: '威胁识别准确率', value: 96 },
  { name: '漏洞分析深度', value: 92 },
  { name: '修复建议准确性', value: 94 },
  { name: '知识库覆盖范围', value: 93 },
])

// AI 学习进度数据
const learningProgress = ref({
  modelRecognitionImprovement: 32,
  securitySuggestionAccuracyImprovement: 26,
  predictionReliabilityImprovement: 18,
})

// 资源优化建议
const optimizationSuggestions = ref<OptimizationSuggestion[]>([
  {
    id: 1,
    title: '优先修复服务器',
    description: 'SRV-DB01, SRV-APP03',
    riskReduction: 70,
  },
  {
    id: 2,
    title: '并行修复策略',
    description: '同步实施临时防护措施',
    riskReduction: 40,
  },
])
</script>

<template>
  <div class="w-full">
    <div class="mb-6 flex items-center">
      <div class="mr-4 flex size-16 items-center justify-center rounded-full bg-info-500">
        <span class="text-4xl font-bold text-white">AI</span>
      </div>
      <div>
        <h2 class="text-xl font-bold">
          安全专家助理
        </h2>
        <p class="text-sm">
          已连接到您的安全系统
        </p>
      </div>
    </div>

    <!-- AI 能力指标 -->
    <div class="mb-6">
      <h3 class="mb-3 text-lg font-bold">
        AI 能力指标
      </h3>
      <div class="space-y-4">
        <div
          v-for="capability of capabilities"
          :key="capability.name"
          class="w-full"
        >
          <div class="mb-1 flex justify-between">
            <span class="text-sm">{{ capability.name }}</span>
            <span class="text-sm font-medium">{{ capability.value }}%</span>
          </div>
          <ProgressBar
            class="h-2"
            :class="`${capability.value >= 95 ? 'bg-info-500' : 'bg-info-400'}`"
            :value="capability.value"
          />
        </div>
      </div>
    </div>

    <!-- AI 学习进度 -->
    <div class="relative mb-6">
      <div class="relative z-50 rounded-lg border border-divider p-4">
        <h3 class="mb-3 text-lg font-bold">
          AI 学习进度
        </h3>

        <div class="mb-4">
          <div class="mb-1 text-sm font-medium">
            模型识别能力提升
          </div>
          <div class="text-sm">
            过去90天误报率下降{{ learningProgress.modelRecognitionImprovement }}%
          </div>
        </div>
        <div class="mb-4">
          <div class="mb-1 text-sm font-medium">
            安全建议采纳率提升
          </div>
          <div class="text-sm">
            {{ learningProgress.securitySuggestionAccuracyImprovement }}%
          </div>
        </div>
        <div>
          <div class="mb-1 text-sm font-medium">
            预测准确性提升
          </div>
          <div class="text-sm">
            {{ learningProgress.predictionReliabilityImprovement }}%
          </div>
        </div>
      </div>

      <div class="absolute inset-0">
        <DashAiPowerChart />
      </div>
    </div>

    <div>
      <h3 class="mb-3 text-lg font-bold">
        资源优化建议
      </h3>
      <div class="space-y-3">
        <div
          v-for="suggestion of optimizationSuggestions"
          :key="suggestion.id"
          class="rounded-lg border border-divider p-4"
        >
          <div class="flex items-center">
            <div class="mr-4 flex size-10 items-center justify-center rounded-full bg-success-500">
              <span class="text-lg font-bold text-white">{{ suggestion.id }}</span>
            </div>
            <div>
              <h4 class="font-medium">
                {{ suggestion.title }}
              </h4>
              <p class="text-sm">
                {{ suggestion.description }}
              </p>
              <p class="mt-1 text-sm font-medium">
                预计节省{{ suggestion.riskReduction }}%风险值
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
