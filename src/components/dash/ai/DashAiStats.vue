<script setup lang="ts">
import type { ApexOptions } from 'apexcharts'
import {
  AlertCircleIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  TrendingDownIcon,
  TrendingUpIcon,
} from 'lucide-vue-next'

// 模拟数据
const securityStats = ref([
  {
    id: 'security-score',
    label: '安全评分',
    value: 85,
    change: 5,
    trend: 'up',
    icon: CheckCircleIcon,
    bgColor: 'bg-info-50',
    iconBgColor: 'bg-info-500',
    textColor: 'text-info-500',
  },
  {
    id: 'unfixed-vulnerabilities',
    label: '未修复漏洞',
    value: 24,
    change: 8,
    trend: 'down',
    icon: AlertCircleIcon,
    bgColor: 'bg-danger-50',
    iconBgColor: 'bg-danger-500',
    textColor: 'text-danger-500',
  },
  {
    id: 'asset-risk-index',
    label: '资产风险指数',
    value: 42,
    change: 6,
    trend: 'up',
    icon: AlertTriangleIcon,
    bgColor: 'bg-warning-50',
    iconBgColor: 'bg-warning-500',
    textColor: 'text-warning-500',
  },
  {
    id: 'fix-completion-rate',
    label: '修复完成率',
    value: '86%',
    change: '7%',
    trend: 'up',
    icon: CheckCircleIcon,
    bgColor: 'bg-success-50',
    iconBgColor: 'bg-success-500',
    textColor: 'text-success-500',
  },
])

// 月份数据
const months = ['一月', '二月', '三月', '四月', '五月', '六月']

// 图表数据
const chartData = {
  discoveredVulnerabilities: [30, 40, 35, 38, 45, 50],
  fixedVulnerabilities: [15, 20, 22, 25, 30, 35],
  assetRisk: [25, 30, 28, 32, 35, 40],
}

const { isDarkMode } = useTheme()

// 图表配置
const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  chart: {
    type: 'line',
    height: 200,
    toolbar: {
      show: false,
    },
  },
  colors: ['#ef4444', '#22c55e', '#eab308'],
  series: [
    {
      name: '发现漏洞',
      data: chartData.discoveredVulnerabilities,
    },
    {
      name: '已修复漏洞',
      data: chartData.fixedVulnerabilities,
    },
    {
      name: '资产风险',
      data: chartData.assetRisk,
    },
  ],
  xaxis: {
    categories: months,
  },
  stroke: {
    curve: 'smooth',
    width: 2,
  },
  grid: {
    borderColor: isDarkMode.value ? '#374151' : '#e5e7eb',
    row: {
      colors: [isDarkMode.value ? '#1f2937' : '#f9fafb', 'transparent'],
      opacity: 0.5,
    },
  },
  markers: {
    size: 4,
  },
  legend: {
    position: 'bottom',
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div class="flex flex-col">
    <div>
      <h2 class="mb-4 text-lg font-bold">
        安全态势概览
      </h2>

      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div
          v-for="stat of securityStats"
          :key="stat.id"
          class="rounded-lg p-4"
          :class="[stat.bgColor]"
        >
          <div class="flex items-center gap-3">
            <div
              class="justify-center rounded-full p-1.5 flex-center"
              :class="[stat.iconBgColor]"
            >
              <Component
                :is="stat.icon"
                class="text-white"
                :size="15"
              />
            </div>
            <span class="text-secondary">{{ stat.label }}</span>
          </div>

          <div class="mt-3 flex items-end gap-2">
            <span class="text-3xl font-bold">{{ stat.value }}</span>
            <div
              class="flex items-center gap-0.5 pb-1"
              :class="[stat.textColor]"
            >
              <Component
                :is="stat.trend === 'up' ? TrendingUpIcon : TrendingDownIcon"
                :size="16"
              />
              <span class="text-sm font-medium">{{ stat.change }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <Divider />

    <div>
      <div ref="chartContainer" />
    </div>
  </div>
</template>
