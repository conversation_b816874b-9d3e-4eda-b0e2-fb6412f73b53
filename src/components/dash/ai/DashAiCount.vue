<script setup lang="ts">
interface AssetStatus {
  total: number
  good: number
  risk: number
}

// 模拟数据，实际应用中可能从API获取
const assetStatus = ref<AssetStatus>({
  total: 27,
  good: 22,
  risk: 5,
})

// 计算良好资产的百分比
const goodPercentage = computed(() => {
  return Math.round((assetStatus.value.good / assetStatus.value.total) * 100)
})

// 计算风险资产的百分比
const riskPercentage = computed(() => {
  return Math.round((assetStatus.value.risk / assetStatus.value.total) * 100)
})
</script>

<template>
  <div class="w-full">
    <h2 class="mb-4 text-lg font-medium">
      上下文信息
    </h2>

    <div class="mb-4">
      <div class="mb-2 flex items-center">
        <div class="mr-2 size-3 rounded-full bg-info-500" />
        <span class="text-base font-medium">关键资产安全状况</span>
      </div>

      <div class="mt-3">
        <div class="mb-2 text-base">
          关键资产总数: {{ assetStatus.total }}
        </div>

        <div class="mb-2 h-6 w-full overflow-hidden rounded-md bg-surface-100">
          <div class="flex h-full">
            <div
              class="h-full bg-green-500"
              :style="`width: ${goodPercentage}%`"
            />
            <div
              class="h-full bg-red-500"
              :style="`width: ${riskPercentage}%`"
            />
          </div>
        </div>

        <div class="flex justify-between text-sm">
          <div>
            良好: {{ assetStatus.good }}
          </div>
          <div>
            风险: {{ assetStatus.risk }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
