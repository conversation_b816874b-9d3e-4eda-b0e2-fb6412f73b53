<script setup lang="ts">
interface AiAction {
  id: string
  title: string
  priority: 'high' | 'medium' | 'low'
  createdAt: string
}

// 模拟数据，实际应用中可能从API获取
const aiActions = ref<AiAction[]>([
  {
    id: '1',
    title: '建议对5台关键服务器进行安全配置加固',
    priority: 'high',
    createdAt: '今天',
  },
  {
    id: '2',
    title: '发现高危漏洞CVE-2024-1234影响20台服务器',
    priority: 'high',
    createdAt: '昨天',
  },
  {
    id: '3',
    title: '优化漏洞修复流程可提高30%的修复效率',
    priority: 'medium',
    createdAt: '3天前',
  },
  {
    id: '4',
    title: '4台数据库服务器配置不符合合规要求',
    priority: 'medium',
    createdAt: '4天前',
  },
])

// 根据优先级获取颜色
const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'bg-red-100 border-l-4 border-l-red-500'

    case 'medium':
      return 'bg-amber-50 border-l-4 border-l-amber-400'

    default:
      return 'bg-gray-100 border-l-4 border-l-gray-400'
  }
}

// 根据优先级获取标签文本和样式
const getPriorityLabel = (priority: string) => {
  switch (priority) {
    case 'high':
      return {
        text: '高优先级',
        class: 'bg-red-100 text-red-600',
      }

    case 'medium':
      return {
        text: '中优先级',
        class: 'bg-amber-100 text-amber-600',
      }

    default:
      return {
        text: '低优先级',
        class: 'bg-gray-100 text-gray-600',
      }
  }
}
</script>

<template>
  <div class="w-full">
    <h2 class="mb-4 text-lg font-bold">
      AI 推荐行动项
    </h2>

    <div class="space-y-3">
      <div
        v-for="action of aiActions"
        :key="action.id"
        class="overflow-hidden rounded-lg shadow-sm transition-all duration-200 hover:shadow-md"
        :class="[
          getPriorityColor(action.priority),
        ]"
      >
        <div class="p-4">
          <p class="mb-2 font-medium text-surface-600">
            {{ action.title }}
          </p>
          <div class="flex items-center justify-between">
            <span
              class="rounded-full px-3 py-1 text-xs"
              :class="[
                getPriorityLabel(action.priority).class,
              ]"
            >
              {{ getPriorityLabel(action.priority).text }}
            </span>
            <span class="text-xs text-gray-500">{{ action.createdAt }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
