<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { TaskType } from '~/enums/asset'
import { queryKeys } from '~/enums/query-key'

const selectedNetwork = ref<Network['id']>()
const selectedNetworkDeviceId = ref<NetworkDevice['id']>()

const { data: networkListData } = useQuery({
  queryKey: queryKeys.asset.network.all(),
  queryFn: () => AssetService.getNetworkList(),
})

const { data: networkDeviceList } = useQuery({
  queryKey: queryKeys.asset.networkDevice.all(selectedNetwork),
  queryFn: () => {
    if (selectedNetwork.value) {
      return AssetService.getNetDeviceList({ network_id: selectedNetwork.value })
    }

    return null
  },
})

const activeTab = ref('ipStatus')
</script>

<template>
  <div class="flex flex-col gap-4">
    <StatsMetrics />

    <div class="grid grid-cols-2 gap-4">
      <CardContainer class="p-card-container">
        <StatsAssetImportance />
      </CardContainer>

      <CardContainer class="p-card-container">
        <StatsTaskAsset :taskType="TaskType.AssetScan" />
      </CardContainer>
    </div>

    <CardContainer class="p-card-container">
      <StatsAssetIpLog />
    </CardContainer>

    <CardContainer class="p-card-container">
      <div class="gap-2 flex-center">
        <div class="ml-auto gap-2 flex-center">
          <ProSelect
            v-model="selectedNetwork"
            :options="networkListData?.map(it => ({
              label: it.name,
              value: it.id,
            }))"
            placeholder="网络"
            showClear
            size="small"
          />

          <ProSelect
            v-model="selectedNetworkDeviceId"
            :options="networkDeviceList?.list?.map(it => ({
              label: it.asset?.name || '未确认',
              value: it.id,
            }))"
            placeholder="网络设备"
            showClear
            size="small"
          />
        </div>
      </div>

      <Tabs v-model:value="activeTab">
        <TabList>
          <Tab value="ipStatus">
            IP 资产在线情况
          </Tab>
          <Tab value="location">
            IP 资产位置分布
          </Tab>
        </TabList>

        <TabPanels>
          <TabPanel value="ipStatus">
            <StatsAssetIpStatus
              :selectedNetwork="selectedNetwork"
              :selectedNetworkDeviceId="selectedNetworkDeviceId"
            />
          </TabPanel>
          <TabPanel value="location">
            <StatsAssetLocation
              :selectedNetwork="selectedNetwork"
              :selectedNetworkDeviceId="selectedNetworkDeviceId"
            />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </CardContainer>

    <CardContainer class="p-card-container">
      <StatsAssetChange />
    </CardContainer>
  </div>
</template>
