<script setup lang="ts">
import { SearchIcon } from 'lucide-vue-next'
</script>

<template>
  <div class="flex-col justify-center overflow-hidden p-12 flex-center">
    <div class="pointer-events-none relative">
      <div class="justify-center rounded-lg border border-divider bg-content p-3 shadow-[inset_0_-2px_0_0_var(--p-surface-100),_0_1px_2px_0_var(--p-surface-200)] flex-center dark:shadow-[inset_0_-2px_0_0_var(--p-surface-800),_0_1px_2px_0_var(--p-surface-600)]">
        <SearchIcon :size="22" />
      </div>

      <div class="absolute left-1/2 top-1/2 size-[480px] -translate-x-1/2 -translate-y-1/2">
        <BackgroundPattern />
      </div>
    </div>

    <div class="z-10 flex flex-col items-center">
      <div class="mb-2 mt-6 text-lg font-medium">
        <slot name="title">
          暂无匹配内容
        </slot>
      </div>

      <Message
        severity="secondary"
        size="small"
        variant="simple"
      >
        <slot name="message">
          尝试调整搜索关键词或检查拼写
        </slot>
      </Message>
    </div>
  </div>
</template>
