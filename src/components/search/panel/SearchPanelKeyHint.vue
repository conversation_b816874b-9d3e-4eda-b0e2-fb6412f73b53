<script setup lang="ts">
const props = defineProps<{
  shortcut: string
  useMetaKey?: boolean
  keyName?: string
}>()

const kbd = computed(() => {
  if (props.useMetaKey && props.keyName) {
    return getKbd(props.keyName)
  }

  return props.shortcut
})
</script>

<template>
  <kbd
    class="rounded bg-emphasis px-1 py-0.5 font-sans text-sm font-medium capitalize tracking-wide outline outline-1 outline-surface-300 flex-center dark:outline-surface-800"
  >
    {{ kbd }}
  </kbd>
</template>
