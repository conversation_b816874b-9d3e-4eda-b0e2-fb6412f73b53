<script setup lang="ts">
import type { RouteRecordName } from 'vue-router'

import type { IFuseOptions } from 'fuse.js'
import Fuse from 'fuse.js'
import { LogOutIcon, SettingsIcon, StickyNoteIcon } from 'lucide-vue-next'

import { RouteKey } from '~/enums/route'

import type SearchInput from '../SearchInput.vue'

const visible = defineModel<boolean>('visible')
const searchQuery = ref('')

const userStore = useUserStore()
const adminNavMenuStore = useAdminNavMenuStore()

const router = useRouter()
const routes = router.getRoutes()

/**
 * 搜索项
 */
interface SearchItem {
  path: string
  name: RouteRecordName
  title: string
  parentTitle?: string
}

// 添加 Fuse.js 配置
const fuseOptions: IFuseOptions<SearchItem> = {
  keys: ['path', 'name', 'title'],
  threshold: 0.6,
  includeScore: true,
}

// 创建搜索索引
const fuse = computed(() => {
  // 创建路由项的缓存 Map
  const routeItemsMap = new Map(
    routes.map((route) => [route.path, getRouteByPath(route.path)]),
  )

  // 创建菜单项的查找 Map
  const menuItemsMap = new Map(
    adminNavMenuStore.flatNavMenuItems.map((item) => [item.route, item]),
  )
  const menuKeyMap = new Map(
    adminNavMenuStore.flatNavMenuItems.map((item) => [item.key, item]),
  )

  const searchItems = routes.reduce<SearchItem[]>((acc, route) => {
    const routeItem = routeItemsMap.get(route.path)
    const routeName = routeItem?.title

    if (!routeName) {
      return acc
    }

    // 如果是动态路由，则不添加到搜索结果中
    if (isDynamicRoute(route.path)) {
      return acc
    }

    const permissions = route.meta?.permissions ?? routeItem?.permissions

    if (userStore.hasPermission(permissions)) {
      const menuItem = menuItemsMap.get(route.path)

      const title = menuItem?.label ?? routeName ?? route.meta?.title ?? route.path

      const parentTitle = menuItem?.parentKey
        ? menuKeyMap.get(menuItem.parentKey)?.label
        : undefined

      acc.push({
        path: route.path,
        name: route.name,
        title,
        parentTitle,
      })
    }

    return acc
  }, [])

  return new Fuse(searchItems, fuseOptions)
})

const filteredRoutes = computed(() => {
  if (!searchQuery.value) {
    return []
  }

  const results = fuse.value.search(searchQuery.value)

  return results.map(({ item }) => ({
    name: item.name,
    title: item.title,
    path: item.path,
    description: item.parentTitle
      ? `${item.parentTitle} / ${item.title}`
      : undefined,
  }))
})

type Command = {
  id: string
  icon: Component
  title: string
  desc?: string
  shortcut: string
  action: () => Promise<void> | void
}

const enum CommandId {
  SystemSettings = 'system-settings',
  Logout = 'logout',
}

const commandList = ref<Command[]>([
  {
    id: CommandId.SystemSettings,
    icon: SettingsIcon,
    title: '系统设置',
    desc: '更改你的系统设置和偏好设置',
    shortcut: getKbd('S'),
    action: async () => {
      visible.value = false
      searchQuery.value = ''

      await navigateTo(getRoutePath(RouteKey.系统设置))
    },
  },
  {
    id: CommandId.Logout,
    icon: LogOutIcon,
    title: '退出系统',
    desc: '清除登录信息并退出账号',
    shortcut: getKbd('Q'),
    action: () => {
      visible.value = false
      searchQuery.value = ''

      handleLogout()
    },
  },
])

const handleClick = async (path: string) => {
  visible.value = false
  searchQuery.value = ''

  await navigateTo(path)
}

const executeCommand = async (commandId: string) => {
  const command = commandList.value.find((cmd) => cmd.id === commandId)

  if (command) {
    await command.action()
  }
}

const isKeyMatch = (ev: KeyboardEvent, key: string) => {
  return isMac()
    ? ev.metaKey && ev.key.toLowerCase() === key
    : ev.ctrlKey && ev.key.toLowerCase() === key
}

const { ref: searchInputRef, refKey } = useRef<InstanceType<typeof SearchInput>>()

const handleKeydown = async (ev: KeyboardEvent) => {
  const isSettingsShortcut = isKeyMatch(ev, 's')

  if (isSettingsShortcut) {
    ev.preventDefault()
    await executeCommand(CommandId.SystemSettings)
  }
  else if (isKeyMatch(ev, 'q')) {
    ev.preventDefault()
    await executeCommand(CommandId.Logout)
  }
  else if (isKeyMatch(ev, '/')) {
    ev.preventDefault()
    searchInputRef.value?.focus()
  }
}

watch(visible, (newValue) => {
  if (newValue) {
    // 当面板显示时添加事件监听
    window.addEventListener('keydown', handleKeydown)
  }
  else {
    // 当面板隐藏时移除事件监听
    window.removeEventListener('keydown', handleKeydown)
  }
}, { immediate: true })

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <Dialog
    v-model:visible="visible"
    dismissableMask
    modal
    :pt="{
      content: { class: '!p-0 !flex !flex-col !overflow-hidden' },
    }"
    :showHeader="false"
    :style="{ width: '550px' }"
  >
    <div class="flex h-full min-h-0 flex-col overflow-hidden">
      <div class="shrink-0">
        <div class="gap-0.5 flex-center">
          <SearchInput
            :ref="refKey"
            autofocus
            class="flex-1"
            fluid
            placeholder="搜索页面名称、菜单名称、访问路径"
            :pt="{
              root: {
                class: '!border-none !shadow-none h-14',
              },
            }"
            @update:debounceChange="searchQuery = $event"
          />

          <div class="pr-6">
            <SearchPanelKeyHint :shortcut="getKbd('/')" />
          </div>
        </div>

        <Divider class="!m-0" />
      </div>

      <div class="min-h-0 flex-1 overflow-auto p-4">
        <div v-if="searchQuery === ''">
          <div class="space-y-2">
            <div
              v-for="cmd of commandList"
              :key="cmd.id"
              class="cursor-pointer gap-3 rounded-md p-2 flex-center hover:bg-emphasis hover:outline hover:outline-1 hover:outline-surface-200 dark:hover:outline-surface-700"
              @click="executeCommand(cmd.id)"
            >
              <div class="justify-center rounded-full bg-emphasis p-3 flex-center">
                <Component
                  :is="cmd.icon"
                  :size="16"
                />
              </div>

              <div class="flex-1">
                <div class="font-medium">
                  {{ cmd.title }}
                </div>
                <div class="mt-1 text-sm text-secondary">
                  {{ cmd.desc }}
                </div>
              </div>

              <SearchPanelKeyHint
                v-if="cmd.shortcut"
                :shortcut="cmd.shortcut"
              />
            </div>
          </div>
        </div>

        <div v-else>
          <div
            v-if="filteredRoutes.length > 0"
            class="space-y-2"
          >
            <div
              v-for="routeItem of filteredRoutes"
              :key="routeItem.path"
              class="cursor-pointer gap-3 rounded-md p-2.5 flex-center hover:bg-emphasis"
              @click="handleClick(routeItem.path)"
            >
              <div class="size-5">
                <Component
                  :is="getRouteIcon(routeItem.name as RouteKey) || StickyNoteIcon"
                  :size="16"
                />
              </div>

              <div class="min-w-0 flex-1 space-y-0.5">
                <div class="font-medium">
                  {{ routeItem.title }}
                </div>
                <div
                  v-if="routeItem.description"
                  class="truncate text-sm text-secondary"
                >
                  {{ routeItem.description }}
                </div>
              </div>
            </div>
          </div>

          <div v-else>
            <SearchPanelEmpty />
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>
