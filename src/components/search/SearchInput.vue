<script setup lang="ts">
import { debounce } from 'lodash-es'
import { LoaderIcon, SearchIcon, XIcon } from 'lucide-vue-next'
import type { InputTextProps } from 'primevue'
import { InputText } from 'primevue'

// HACK: 使用 vue-ignore 忽略 InputTextProps 的类型检查
interface SearchInputProps extends /* @vue-ignore */ InputTextProps {
  /** 是否显示搜索图标 */
  icon?: boolean
  /** 输入防抖时间，单位：ms */
  delay?: number
  /** 是否处于加载状态 */
  loading?: boolean
  /** 是否显示清除图标 */
  showClear?: boolean
}

const props = withDefaults(defineProps<SearchInputProps>(), {
  icon: true,
  delay: 300,
  loading: false,
  showClear: false,
})

const defaultIconSize = 16

const modelValue = defineModel<InputTextProps['modelValue']>('modelValue')

const emit = defineEmits<{
  'update:debounceChange': [value: SearchInputProps['modelValue']]
  enter: []
}>()

defineSlots<{
  searchIcon?: (props: { iconSize: number, loading: boolean }) => VNode
  clearIcon?: (props: { iconSize: number }) => VNode
}>()

const debouncedEmit = debounce((value: SearchInputProps['modelValue']) => {
  modelValue.value = value
  emit('update:debounceChange', value)
}, props.delay)

const onInput = (event: Event) => {
  const target = event.target as HTMLInputElement

  // 实时更新本地 modelValue，同时 debounce 外部事件
  modelValue.value = target.value
  debouncedEmit(target.value)
}

const handleEnter = () => {
  emit('enter')
}

const { refKey: inputRefKey, ref: inputRef } = useRef<
InstanceType<typeof InputText> & { $el: HTMLInputElement }
>()

const handleFocus = () => {
  inputRef.value?.$el.focus()
}

const handleClear = () => {
  modelValue.value = ''
  handleFocus()
  debouncedEmit('')
}

defineExpose({
  /** 聚焦输入框 */
  focus: () => {
    handleFocus()
  },
})
</script>

<template>
  <IconField>
    <InputIcon
      v-if="icon"
      class="inline-flex-center"
    >
      <slot
        :iconSize="defaultIconSize"
        :loading="loading"
        name="searchIcon"
      >
        <LoaderIcon
          v-if="loading"
          class="animate-spin"
          :size="defaultIconSize"
        />
        <SearchIcon
          v-else
          :size="defaultIconSize"
        />
      </slot>
    </InputIcon>

    <InputText
      :ref="inputRefKey"
      v-bind="$attrs"
      :value="modelValue"
      @input="onInput"
      @keydown.enter="handleEnter"
    />

    <InputIcon
      v-if="showClear && modelValue"
      class="!mt-0 -translate-y-1/2 inline-flex-center"
    >
      <ProBtn
        mini
        size="small"
        variant="text"
        @click="handleClear()"
      >
        <slot
          :iconSize="defaultIconSize"
          name="clearIcon"
        >
          <XIcon :size="defaultIconSize" />
        </slot>
      </ProBtn>
    </InputIcon>
  </IconField>
</template>
