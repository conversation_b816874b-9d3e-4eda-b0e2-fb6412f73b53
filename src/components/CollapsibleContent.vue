<script setup lang="ts">
import { ChevronDownIcon, ChevronUpIcon } from 'lucide-vue-next'

const props = withDefaults(defineProps<{
  /** 折叠时的最大高度 */
  maxHeight?: number | string
  /** 展开按钮文本 */
  expandText?: string
  /** 收起按钮文本 */
  collapseText?: string
  /** 是否默认展开 */
  defaultExpanded?: boolean
  /** 按钮尺寸 */
  buttonSize?: 'small' | 'normal'
}>(), {
  maxHeight: 200,
  expandText: '展开全文',
  collapseText: '收起',
  defaultExpanded: false,
  buttonSize: 'small',
})

const emit = defineEmits<{
  toggle: [expanded: boolean]
}>()

const { refKey, ref: contentRef } = useRef<HTMLDivElement>()
const isExpanded = ref(props.defaultExpanded)
const hasOverflow = ref(false)

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
  emit('toggle', isExpanded.value)
}

const checkOverflow = () => {
  if (contentRef.value) {
    const container = contentRef.value
    // 获取内容的真实高度
    const scrollHeight = container.scrollHeight
    // 获取最大高度（如果是数字，则按照px计算）
    const maxHeightValue = typeof props.maxHeight === 'number'
      ? props.maxHeight
      : parseFloat(props.maxHeight.toString())

    // 判断内容是否超出最大高度
    hasOverflow.value = scrollHeight > maxHeightValue
  }
}

// 内容渲染完后检查是否需要显示展开/收起按钮
onMounted(() => {
  nextTick(checkOverflow)
})

// 当内容变化时重新检查
watch(() => props.maxHeight, () => {
  nextTick(checkOverflow)
})

// 计算实际的最大高度样式
const maxHeightStyle = computed(() => {
  if (isExpanded.value) {
    return 'none'
  }

  return typeof props.maxHeight === 'number'
    ? `${props.maxHeight}px`
    : props.maxHeight
})
</script>

<template>
  <div class="relative">
    <!-- 内容容器 -->
    <div
      :ref="refKey"
      class="overflow-hidden"
      :style="{ maxHeight: maxHeightStyle }"
    >
      <slot />
    </div>

    <!-- 展开/收起按钮 -->
    <div
      v-if="hasOverflow"
      class="pointer-events-none absolute inset-0 z-50"
      :class="{
        'bg-gradient-to-b from-transparent to-content': !isExpanded,
      }"
    >
      <div
        class="pointer-events-auto absolute bottom-0 left-1/2 -translate-x-1/2"
        :class="{
          'translate-y-full': isExpanded,
        }"
      >
        <Button
          :size="buttonSize"
          variant="text"
          @click="toggleExpanded"
        >
          <span class="gap-1 flex-center">
            {{ isExpanded ? collapseText : expandText }}
            <Component
              :is="isExpanded ? ChevronUpIcon : ChevronDownIcon"
              class="size-4"
            />
          </span>
        </Button>
      </div>
    </div>
  </div>
</template>
