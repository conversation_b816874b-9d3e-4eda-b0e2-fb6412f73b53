<script setup lang="ts">
import type { SelectButtonProps, SelectButtonSlots } from 'primevue'

type Props = Pick<SelectButtonProps, 'options' | 'optionLabel' | 'optionValue' | 'dataKey' | 'allowEmpty' | 'disabled' | 'size'>

const props = withDefaults(defineProps<Props>(), {
  optionLabel: 'label',
  optionValue: 'value',
  allowEmpty: false,
})
</script>

<template>
  <SelectButton v-bind="props">
    <template
      v-for="(_, name) of $slots"
      :key="name"
      #[name]="slotData"
    >
      <slot
        :name="name as keyof SelectButtonSlots"
        v-bind="slotData"
      />
    </template>
  </SelectButton>
</template>
