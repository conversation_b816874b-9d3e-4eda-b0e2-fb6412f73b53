<script setup lang="ts" generic="SelectValue = (string | number)[]">
import type { MultiSelectProps, MultiSelectSlots } from 'primevue'

export interface ProMultiSelectOption<V = string | number> {
  label: string
  value: V
  [key: string]: UnsafeAny
}

export interface ProMultiSelectProps<V = string | number> extends Pick<MultiSelectProps, 'optionLabel' | 'optionValue' | 'loading' | 'placeholder' | 'showClear' | 'fluid' | 'maxSelectedLabels'> {
  options?: ProMultiSelectOption<V>[]
}

const selectProps = withDefaults(defineProps<ProMultiSelectProps<SelectValue>>(), {
  optionLabel: 'label',
  optionValue: 'value',
})

defineSlots<MultiSelectSlots>()

const selectedValue = defineModel<SelectValue>()
</script>

<template>
  <MultiSelect
    v-bind="selectProps"
    v-model="selectedValue"
  >
    <!-- 转发所有插槽 -->
    <template
      v-for="(_, name) of $slots"
      :key="name"
      #[name]="slotData"
    >
      <slot
        :name="name as keyof MultiSelectSlots"
        v-bind="slotData"
      />
    </template>
  </MultiSelect>
</template>
