<script setup lang="ts">
import { PenLineIcon } from 'lucide-vue-next'

import type { ProBtnProps } from './ProBtn.vue'

const props = withDefaults(defineProps<ProBtnProps>(), {
  label: '编辑',
  showIcon: true,
  onlyIcon: false,
  size: 'small',
  severity: 'secondary',
  variant: 'text',
  tooltip: undefined,
})
</script>

<template>
  <ProBtn v-bind="props">
    <template #icon="{ size }">
      <PenLineIcon :size="size" />
    </template>
  </ProBtn>
</template>
