<script setup lang="ts">
import type { AnchorHTMLAttributes } from 'vue'

import type { ButtonProps } from 'primevue'

import { TooltipShowDelay } from '~/enums/common'

export interface ProBtnProps {
  label?: ButtonProps['label']
  severity?: ButtonProps['severity']
  variant?: ButtonProps['variant'] | 'default'
  size?: ButtonProps['size']
  href?: AnchorHTMLAttributes['href']
  target?: AnchorHTMLAttributes['target']
  /** 是否显示图标 */
  showIcon?: boolean
  /** 是否只显示图标 */
  onlyIcon?: boolean
  /** 是否只显示文字 */
  onlyLabel?: boolean
  tooltip?: {
    value?: string
    showDelay?: number
  } | false
  /** 图标大小 */
  iconSize?: number

  /** 是否高亮 */
  highlight?: boolean
  /** 是否小型 */
  mini?: boolean
}

const props = withDefaults(defineProps<ProBtnProps>(), {
  severity: 'secondary',
  variant: 'text',
  showIcon: true,
  onlyIcon: false,
  onlyLabel: false,
  tooltip: () => ({
    showDelay: TooltipShowDelay.Fast,
  }),
  mini: false,
})

const tooltip = toRef(props, 'tooltip')
const hasIcon = !props.onlyLabel && props.showIcon
const hasLabel = !props.onlyIcon && props.label

const tooltipConfig = computed(() => {
  if (!hasLabel) {
    if (tooltip.value === false) {
      return undefined
    }
    else if (tooltip.value) {
      const tooltipValue = (tooltip.value.value ?? props.label) ?? props.label
      const tooltipShowDelay = tooltip.value.showDelay ?? TooltipShowDelay.Fast

      if (tooltipValue) {
        return {
          value: tooltipValue,
          showDelay: tooltipShowDelay,
        }
      }
    }
  }

  return undefined
})

defineSlots<{
  default?: () => VNode
  icon?: (props: { size: number }) => VNode
  label?: VNode[]
}>()
</script>

<template>
  <Button
    v-tooltip.top="tooltipConfig"
    v-bind="props"
    :class="{
      '!bg-emphasis': highlight,
    }"
    :dt="{
      sm: {
        padding: {
          x: props.mini
            ? onlyIcon || !hasLabel
              ? '0.15rem'
              : '0.4rem'
            : '0.3rem',
          y: props.mini ? '0.15rem' : '0.4rem',
        },
      },
    }"
    :variant="props.variant === 'default' ? undefined : props.variant"
  >
    <span
      class="font-medium flex-center"
      :class="{
        'gap-1': size === 'small' || !size,
        'gap-1.5': size === 'large',
      }"
    >
      <slot name="default" />

      <span
        v-if="$slots.icon"
        class="shrink-0"
      >
        <slot
          v-if="hasIcon"
          name="icon"
          :size="size === 'small' ? 13.5 : size === 'large' ? 16 : 14.5"
        />
      </span>

      <span
        v-if="hasLabel"
        class="shrink-0"
      >
        <slot name="label">
          {{ label }}
        </slot>
      </span>
    </span>
  </Button>
</template>
