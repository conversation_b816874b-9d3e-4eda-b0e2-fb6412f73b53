<script setup lang="ts">
import { TrashIcon } from 'lucide-vue-next'

import type { ProBtnProps } from './ProBtn.vue'

const { $confirm, $toast } = useNuxtApp()

interface Props extends ProBtnProps {
  /** 是否显示确认删除的弹窗 */
  showConfirm?: boolean
  /** 是否显示删除成功的提示 */
  showDeleteSuccess?: boolean
  /** 确认删除的提示信息 */
  confirmMessage?: string
  /** 确认删除的弹窗配置 */
  confirmPopupProps?: NonNullable<Parameters<typeof $confirm.popup>[0]>
  /** 确认删除的回调 */
  onConfirm?: () => Promise<void>
  /** 删除成功后的提示信息 */
  deleteSuccessMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  label: '删除',
  showIcon: true,
  size: 'small',
  severity: 'danger',
  variant: 'text',
  showConfirm: true,
  confirmMessage: '确定要删除吗？',
  showDeleteSuccess: true,
  tooltip: undefined,
})

const emit = defineEmits<{
  click: [MouseEvent]
}>()

const handleDelete = (ev: MouseEvent) => {
  if (props.showConfirm) {
    if (ev.currentTarget instanceof HTMLElement) {
      $confirm.popup({
        target: ev.currentTarget,
        ...props.confirmPopupProps,
        message: props.confirmPopupProps?.message ?? props.confirmMessage,
        accept: async () => {
          await props.onConfirm?.()

          if (props.showDeleteSuccess) {
            $toast.info({
              summary: '删除成功',
              detail: props.deleteSuccessMessage,
            })
          }
        },
      })
    }
  }
  else {
    emit('click', ev)
  }
}
</script>

<template>
  <ProBtn
    v-bind="props"
    @click="handleDelete($event)"
  >
    <template #icon="{ size }">
      <TrashIcon :size="size" />
    </template>
  </ProBtn>
</template>
