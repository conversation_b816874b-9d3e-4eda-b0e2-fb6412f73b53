<script setup lang="ts">
import { ChevronsUpDownIcon } from 'lucide-vue-next'

import type { ProBtnProps } from './ProBtn.vue'

const props = withDefaults(defineProps<ProBtnProps>(), {
  label: '详情',
  showIcon: true,
  size: 'small',
  severity: 'secondary',
  variant: 'text',
  tooltip: undefined,
})
</script>

<template>
  <ProBtn v-bind="props">
    <template #icon="{ size }">
      <ChevronsUpDownIcon :size="size" />
    </template>
  </ProBtn>
</template>
