<script setup lang="ts">
import { EllipsisVerticalIcon } from 'lucide-vue-next'

import type { ProBtnProps } from './ProBtn.vue'

const props = withDefaults(defineProps<ProBtnProps>(), {
  label: '更多',
  showIcon: true,
  size: 'small',
  severity: 'secondary',
  variant: 'text',
  tooltip: undefined,
})
</script>

<template>
  <ProBtn v-bind="props">
    <template #icon="{ size }">
      <EllipsisVerticalIcon :size="props.iconSize ?? size" />
    </template>
  </ProBtn>
</template>
