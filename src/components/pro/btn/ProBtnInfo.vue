<script setup lang="ts">
import { EyeIcon } from 'lucide-vue-next'

import type { ProBtnProps } from './ProBtn.vue'

const props = withDefaults(defineProps<ProBtnProps>(), {
  label: '查看',
  showIcon: true,
  size: 'small',
  severity: 'secondary',
  variant: 'text',
  tooltip: undefined,
})
</script>

<template>
  <ProBtn v-bind="props">
    <template #icon="{ size }">
      <EyeIcon :size="size" />
    </template>
  </ProBtn>
</template>
