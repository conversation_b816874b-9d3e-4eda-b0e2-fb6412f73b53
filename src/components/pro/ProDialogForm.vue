<script setup lang="ts">
import type { FormProps } from '@primevue/forms'
import { omit } from 'lodash-es'
import type { DialogProps, DialogSlots } from 'primevue'

interface DialogFormProps<FormValues = Record<string, unknown>> {
  /** 弹窗是否可见 */
  visible?: boolean
  /** 弹窗标题 */
  title?: string
  /** 弹窗内容是否加载中 */
  loading?: boolean
  /** 表单初始值 */
  initialValues?: FormValues
  /** 表单 schema 校验器 */
  resolver?: FormProps['resolver']
  dialogProps?: DialogProps & {
    class?: string
  }
  formProps?: FormProps
  formActionGroupProps?: FormActionGroupProps
  onCancel?: () => void
  onSubmit?: (params: FormSubmitOptions) => Promise<void> | void
}

const props = defineProps<DialogFormProps>()

const {
  visible,
  title,
  resolver,
  loading,
  initialValues,
  dialogProps,
  formProps,
  formActionGroupProps,
  onCancel,
  onSubmit,
} = toRefs(props)

const emit = defineEmits<{
  'update:visible': [visible: boolean]
}>()

const slots = defineSlots<DialogSlots & {
  default?: []
  extraButtons?: []
}>()

const forwardedSlots = omit(slots, 'default')

const finalDialogProps = computed(() => ({
  dismissableMask: true,
  modal: true,
  ...dialogProps.value,
}))

const forceUpdateKey = ref(0)

const initValues = computed(() => {
  return props.formProps?.initialValues || initialValues.value
})

watch(initValues, () => {
  forceUpdateKey.value++
})
</script>

<template>
  <Dialog
    v-bind="finalDialogProps"
    class="dialog-form"
    :class="dialogProps?.class"
    :header="dialogProps?.header || title"
    :visible="dialogProps?.visible || visible"
    @update:visible="emit('update:visible', $event)"
  >
    <Form
      :key="forceUpdateKey"
      :initialValues="toRaw(initValues)"
      :resolver="formProps?.resolver || resolver"
      @submit="onSubmit"
    >
      <div class="flex flex-col gap-form-field">
        <slot name="default" />

        <FormActionGroup
          v-bind="formActionGroupProps"
          :loading="loading"
          @cancel="onCancel"
        >
          <template #extra>
            <slot name="extraButtons" />
          </template>
        </FormActionGroup>
      </div>
    </Form>

    <template
      v-for="(_, name) of forwardedSlots"
      :key="name"
      #[name]="slotData"
    >
      <slot
        :key="name"
        :name="name as keyof DialogSlots"
        v-bind="slotData"
      />
    </template>
  </Dialog>
</template>
