<script lang="ts">
import { type QueryOptions, useQuery } from '@tanstack/vue-query'
import { get, merge } from 'lodash-es'
import type { DataTableProps, DataTableSlots, PaginatorProps } from 'primevue'

import type { ProTableToolbarConfig } from '~/components/pro/table/ProTableToolbar.vue'
import TimeDisplay from '~/components/TimeDisplay.vue'
import { useDebounceState } from '~/composables/useDebounceState'
import type { TablePersistConfig } from '~/composables/useTablePersist'
import { useTablePersist } from '~/composables/useTablePersist'
import { DateFormat } from '~/enums/common'
import { ProTableSortOrder, ProValueType } from '~/enums/pro'
</script>

<script setup lang="ts" generic="DataRecord, QueryParams">
/**
 * 高级数据表格组件 (ProTable)
 *
 * 基于 PrimeVue DataTable 封装的企业级表格组件，提供丰富的数据展示和操作功能：
 *
 * 核心功能：
 * - 数据加载与展示：支持本地数据源和远程数据请求，自动处理加载状态
 * - 分页系统：内置分页逻辑，支持自定义页码和每页条数
 * - 排序与过滤：支持多字段排序和自定义过滤条件
 * - 列配置：支持列宽度、隐藏列、自定义渲染等丰富配置
 * - 工具栏：集成搜索、列设置、表格尺寸调整等功能
 * - 数据分组：支持按字段分组展示数据
 *
 * 高级特性：
 * - 状态持久化：支持将表格配置（如列设置、排序、筛选等）持久化到本地存储
 * - 时间格式化：自动识别并格式化日期时间类型的列
 * - 自定义渲染：支持单元格和过滤器的自定义渲染
 * - 响应式设计：适配不同屏幕尺寸
 * - TypeScript 支持：完整的类型定义，提供良好的开发体验
 */

export interface ProTableProps<T, Q = Record<string, unknown>> extends Pick<DataTableProps, 'dataKey' | 'removableSort' | 'showGridlines' | 'scrollable' | 'scrollHeight' | 'stripedRows'> {
  /** 请求函数 */
  requestFn?: (queryParams: Q, queryOptions?: QueryOptions) => Promise<PageResult<T>> | null
  /** 表格数据 */
  dataSource?: T[] | null
  /** 添加 columns 配置 */
  columns?: ProTableColumn<T>[]
  /** 加载状态 */
  loading?: boolean
  /** 分页配置 */
  pagination?: {
    defaultPageSize?: PageInfo['page_size']
    pageSizeOptions?: PaginatorProps['rowsPerPageOptions']
    position?: 'top' | 'bottom' | 'both'
    /** 是否显示分页器 */
    show?: boolean
  }
  /** 是否显示工具栏 */
  toolbar?: boolean
  /** 分页器配置 */
  paginatorProps?: PaginatorProps
  /** 初始化过滤条件 */
  initialFilters?: ProTableFilterValues
  /** 查询参数，会结合分页参数一起传递给 requestFn */
  queryParams?: Q
  /** 传递给 useQuery 的 queryKey */
  queryKey?: QueryOptions['queryKey']
  /** 当 queryKey 变化时，是否重置分页 */
  resetPageOnQueryKeyChange?: boolean
  /**
   * 表格持久化配置选项，用于配置表格状态的本地持久化存储，包括列设置、排序、筛选等状态。
   * 例如，当你想在刷新页面后，表格的列设置、排序、筛选状态仍然存在，则可以配置该选项
   */
  persistConfig?: Partial<TablePersistConfig>
  /** 工具栏配置 */
  toolbarConfig?: ProTableToolbarConfig
}

const defaultPersistConfig: TablePersistConfig = {
  enabled: false,
  keyPrefix: 'pro-table',
  features: ['search', 'sorts', 'filters', 'hiddenColumns', 'size'],
}

const props = withDefaults(defineProps<ProTableProps<DataRecord>>(), {
  dataKey: 'id',
  toolbar: true,
  removableSort: true,
  resetPageOnQueryKeyChange: true,
})

const {
  requestFn,
  dataSource: dataSourcex,
  pagination,
  loading: loadingx,
  paginatorProps,
  ...restDataTableProps
} = props

const queryParams = toRef(props, 'queryParams')
const queryKey = toRef(props, 'queryKey')
const loading = toRef(props, 'loading')
const dataSource = toRef(props, 'dataSource')
const columns = toRef(props, 'columns')

const emit = defineEmits<{
  'update:queryParams': [params: ProTableProps<DataRecord>['queryParams']]
  'update:dataSource': [dataSource?: DataRecord[]]
}>()

const slots = defineSlots<DataTableSlots & {
  headerLeft: VNode[]
  toolbarRight: VNode[]
}>()

const { $toast } = useNuxtApp()

const pageInfo = ref<PageInfo>({
  page: 1,
  page_size: pagination?.defaultPageSize || 10,
  total: 0,
  page_sum: 0,
})

// 监听 queryKey 变化，如果变化则重置分页，防止查询参数变化时，分页信息不更新
watch(queryKey, () => {
  if (props.resetPageOnQueryKeyChange) {
    pageInfo.value.page = 1
  }
}, { immediate: false, deep: true })

const search = ref<ProTableSearchValue>()
const filters = ref<ProTableFilterValues | undefined>(props.initialFilters)
const sorts = ref<ProTableSort[]>()
const hiddenColumnFields = ref<ProTableColumn<DataRecord>['field'][]>()

const tableSize = defineModel<DataTableProps['size']>('size')
const groupConfig = defineModel<ProTableGroupConfig>('groupConfig')

// 使用持久化逻辑
useTablePersist({
  search,
  sorts,
  filters,
  hiddenColumnFields,
  size: tableSize,
  persistConfig: merge({}, defaultPersistConfig, props.persistConfig),
})

const orderBy = computed(() => {
  const firstSort = sorts.value?.at(0)

  if (firstSort) {
    return getOrderBy(firstSort.order === ProTableSortOrder.ASC ? 1 : -1, firstSort.field)
  }

  return null
})

/**
 * 合并后的查询参数对象，包含：
 *
 * @property page - 当前页码
 * @property page_size - 每页显示条数
 * @property order_by - 排序规则
 * @property filters - 过滤条件
 * @property queryParams - 用户自定义查询参数
 *
 * 注意: queryParams 会覆盖之前的同名参数
 */
const mergedQueryParams = computed(() => ({
  page: pageInfo.value.page,
  page_size: pageInfo.value.page_size,
  order_by: orderBy.value,
  [props.toolbarConfig?.search?.key ?? 'search']: search.value,
  ...filters.value,
  ...queryParams.value,
}))

/**
 * 使用 VueQuery 获取表格数据
 *
 * - 当 queryKey 或查询参数变化时会自动重新请求
 * - 通过 requestFn 发起实际的数据请求
 */
const { data: tableData, isLoading, refetch } = useQuery({
  queryKey: ['dataSource', queryKey.value, mergedQueryParams],
  queryFn: () => {
    if (requestFn) {
      return requestFn(mergedQueryParams.value)
    }

    return null
  },
  enabled: !dataSource.value && !!requestFn,
})

const handleReloadTableData = async () => {
  await refetch()
}

const handleReloadFromToolbar = async () => {
  await handleReloadTableData()

  $toast.success({
    summary: '刷新成功',
  })
}

defineExpose<TableActionType>({
  reload: handleReloadTableData,
  reloadAndRest: () => {
    pageInfo.value.page = 1
    handleReloadTableData()
  },
  reset: () => {
    pageInfo.value = {
      page: 1,
      page_size: pagination?.defaultPageSize ?? 10,
      total: 0,
      page_sum: 0,
    }

    emit('update:queryParams', {})
  },
})

watchEffect(() => {
  if (tableData.value) {
    pageInfo.value.total = tableData.value.total
    pageInfo.value.page_sum = tableData.value.page_sum
  }

  emit('update:dataSource', tableData.value?.list)
})

const handlePageChange = (pInfo: PrimeVuePageInfo) => {
  const newPage = pInfo.page + 1
  const newPageSize = pInfo.rows

  const shouldTriggerChange = newPage !== pageInfo.value.page || newPageSize !== pageInfo.value.page_size

  if (shouldTriggerChange) {
    pageInfo.value.page = newPage
    pageInfo.value.page_size = newPageSize
  }
}

const isLoadingData = computed(() => isLoading.value || loading.value)
const isEmptyData = computed(() => !isLoadingData.value && !tableData.value?.list.length)

const { debouncedState: debouncedLoadingState } = useDebounceState(isLoadingData, {
  delay: 100,
})

const internalDataSource = computed(() => dataSource.value || tableData.value?.list)

const isTimeColumn = (column: ProTableColumn<DataRecord>) => {
  return column.valueType === ProValueType.DateTime || column.valueType === ProValueType.Date || column.valueType === ProValueType.Time
}

// 处理自定义渲染
const renderCell = (column: ProTableColumn<DataRecord>, rowData: DataRecord, rowIndex: number) => {
  if (typeof column.render === 'function') {
    const rendered = column.render(rowData, rowIndex)

    // 如果返回的是字符串，直接显示文本内容
    if (typeof rendered === 'string') {
      return h('span', rendered)
    }

    // 否则返回 VNode
    return rendered
  }

  const cellValue = get(rowData, getFieldName(column) || '')

  if (isTimeColumn(column)) {
    if (typeof cellValue === 'string') {
      const format = column.valueType === ProValueType.DateTime
        ? DateFormat.YYYY_MM_DD_HH_MM
        : column.valueType === ProValueType.Date
          ? DateFormat.YYYY_MM_DD
          : DateFormat.HH时MM分

      return h(TimeDisplay, {
        format,
        time: cellValue,
      })
    }
  }

  return h('span', String(cellValue ?? '-'))
}

// 处理自定义过滤器
const renderFilter = (column: ProTableColumn<DataRecord>, filterModel: UnsafeAny) => {
  if (column.filterRender) {
    return column.filterRender(filterModel)
  }

  return null
}

const mergedPaginatorProps = computed(() => ({
  show: true,
  ...props.paginatorProps,
}))

watch(columns, (columns) => {
  if (Array.isArray(columns)) {
    hiddenColumnFields.value = columns
      .filter((col) => col.hideInTable === true)
      .map((col) => getFieldName(col))
  }
}, { immediate: true, deep: true })

const computedColumns = computed(() => {
  if (columns.value) {
    return columns.value?.map((col) => {
      return {
        ...col,
        hideInTable: hiddenColumnFields.value?.includes(getFieldName(col)),
      }
    })
  }
  else {
    if ('default' in slots && typeof slots.default === 'function') {
      const defaultSlotChildren: VNode[] = slots.default()

      return defaultSlotChildren
        .filter((vnode) => {
          if (typeof vnode.type === 'object' && 'name' in vnode.type) {
            return vnode.type.name === 'Column'
          }

          return false
        })
        .map<ProTableColumn<DataRecord>>(({ props }) => props as ProTableColumn<DataRecord>)
    }
  }

  return []
})

const renderColumns = computed(() => {
  return toValue(computedColumns)?.reduce<ProTableColumn<DataRecord>[]>((acc, col) => {
    let colCopy = col

    if (col.sortable) {
      // 如果排序字段为空，则不显示排序按钮
      colCopy = {
        ...colCopy,
        sortable: false,
      }
    }

    if (col.hideInTable !== true) {
      acc.push(colCopy)
    }

    return acc
  }, [])
})

const getColumnByField = (field: string) => {
  return computedColumns.value?.find((col) => getFieldName(col) === field)
}
</script>

<template>
  <DataTable
    v-bind="restDataTableProps"
    :dt="{
      footer: {
        border: {
          width: '0',
        },
      },
    }"
    :groupRowsBy="groupConfig?.by"
    :loading="isLoadingData"
    :pt="{
      header: {
        class: '!px-0 !pt-0',
      },
    }"
    :rowGroupMode="groupConfig?.by ? 'subheader' : undefined"
    :size="tableSize"
    :value="internalDataSource"
  >
    <!-- 转发所有插槽 -->
    <template
      v-for="(_, name) of $slots"
      :key="name"
      #[name]="slotData"
    >
      <slot
        :key="name"
        :name="name as keyof DataTableSlots"
        v-bind="slotData"
      />
    </template>

    <!-- 工具栏 -->
    <template
      v-if="toolbar || $slots.toolbarRight"
      #header
    >
      <slot name="header">
        <div class="gap-4 flex-center">
          <slot name="headerLeft" />

          <div class="ml-auto gap-4 flex-center">
            <ProTableToolbar
              v-if="toolbar && computedColumns.length > 0"
              v-model:columns="computedColumns"
              v-model:filters="filters"
              v-model:groupConfig="groupConfig"
              v-model:hiddenColumnFields="hiddenColumnFields"
              v-model:search="search"
              v-model:size="tableSize"
              v-model:sorts="sorts"
              :config="toolbarConfig"
              @reload="handleReloadFromToolbar()"
            />

            <slot name="toolbarRight" />
          </div>
        </div>
      </slot>
    </template>

    <!-- 根据 columns 配置自动生成列 -->
    <template v-if="Array.isArray(renderColumns) && renderColumns.length > 0">
      <Column
        v-for="(col, idx) of renderColumns"
        :key="getFieldName(col) ?? idx"
        v-bind="col"
        :filterField="col.filterField ?? col.field"
        :style="{ width: col.width }"
      >
        <!-- 自定义单元格渲染 -->
        <template
          v-if="col.render || isTimeColumn(col)"
          #body="{ data }: { data: DataRecord }"
        >
          <Component
            :is="renderCell(col, data, idx)"
            :data="data"
          />
        </template>

        <template
          v-else-if="col.valueType"
          #body="{ data }: { data: DataRecord }"
        >
          {{ get(data, getFieldName(col) ?? '') || '-' }}
        </template>

        <!-- 自定义过滤器渲染 -->
        <template
          v-if="col.filterRender"
          #filter="{ filterModel }"
        >
          <Component :is="renderFilter(col, filterModel)" />
        </template>
      </Column>
    </template>

    <!-- 分组头部 -->
    <template #groupheader="slotProps: { data: DataRecord, index: number }">
      <div class="flex items-center gap-2">
        <Component
          :is="renderCell(
            getColumnByField(groupConfig.by)!,
            slotProps.data,
            slotProps.index,
          )"
          v-if="groupConfig?.by && getColumnByField(groupConfig.by)"
        />
      </div>
    </template>

    <template
      v-if="!isEmptyData && pagination?.show !== false"
      #footer
    >
      <ProPaginator
        :pageInfo="pageInfo"
        :pageSizeOptions="pagination?.pageSizeOptions"
        :paginatorProps="mergedPaginatorProps"
        @pageChange="handlePageChange($event)"
      />
    </template>

    <!-- 加载中展示 -->
    <template #loading>
      <div class="pointer-events-none size-full" />
    </template>

    <!-- 空数据展示 -->
    <template #empty>
      <LoadingSkeleton
        v-if="debouncedLoadingState"
        :columns="renderColumns.length < 6 ? renderColumns.length : 6"
        :rows="5"
      />

      <ProTableEmpty v-else-if="!isLoadingData" />
    </template>
  </DataTable>
</template>
