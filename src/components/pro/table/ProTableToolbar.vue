<script lang="ts">
import { AlignVerticalSpaceAroundIcon, ArrowDownUpIcon, BetweenHorizontalStartIcon, EllipsisIcon, FilterIcon, RotateCwIcon, SearchIcon, TablePropertiesIcon } from 'lucide-vue-next'
import type { DataTableProps } from 'primevue'

import type { ProBtnProps } from '~/components/pro/btn/ProBtn.vue'
import type ProPrimePopover from '~/components/pro/ProPrimePopover.vue'
import type SearchInput from '~/components/search/SearchInput.vue'
import { ProTableSortOrder, ProValueType } from '~/enums/pro'

const toolbarButtonProp: ProBtnProps = {
  variant: 'text',
  size: 'small',
  severity: 'secondary',
  onlyIcon: true,
}

export interface ProTableToolbarConfig {
  search?: {
    /** 搜索框的 key 值，用于在请求参数中添加搜索参数，默认值为 `search` */
    key?: string
    /** 搜索框的 placeholder */
    placeholder?: string
  }
}
</script>

<script setup lang="ts" generic="DataRecord">
const props = defineProps<{
  config?: ProTableToolbarConfig
}>()

const search = defineModel<string>('search')
const columns = defineModel<ProTableColumn<DataRecord>[]>('columns')
const filters = defineModel<ProTableFilterValues>('filters')
const tableSize = defineModel<DataTableProps['size']>('size')

const emit = defineEmits<{
  reload: []
}>()

const internalFilterValues = ref<ProTableFilterValues | undefined>(filters.value)

const { refKey: popoverRefKey, ref: popoverRef } = useRef<InstanceType<typeof ProPrimePopover>>()

const handleResetFilter = () => {
  filters.value = undefined
  internalFilterValues.value = undefined
  popoverRef.value?.hide()
}

const handleSubmitFilter = () => {
  filters.value = internalFilterValues.value
  popoverRef.value?.hide()
}

const sorts = defineModel<ProTableSort[]>('sorts', {
  default: [{ field: '', order: ProTableSortOrder.ASC }],
})

const hiddenColumnFields = defineModel<ProTableColumn<DataRecord>['field'][]>('hiddenColumnFields')

const filterCount = computed(() => {
  const filterKeys = Object.keys(filters.value || {})

  return filterKeys.filter((key) => {
    const filterValue = filters.value?.[key]

    if (typeof filterValue === 'string') {
      return filterValue.trim() !== ''
    }

    if (Array.isArray(filterValue)) {
      return filterValue.length > 0
    }

    if (typeof filterValue === 'object') {
      return Object.keys(filterValue || {}).length > 0
    }

    return true
  }).length
})

const groupConfig = defineModel<ProTableGroupConfig>('groupConfig')

const sortCount = computed(() => sorts.value.filter((it) => it.field).length)

const groupableColumns = computed(() => columns.value?.filter((col) => {
  return col.groupable !== false && col.valueType !== ProValueType.ActionGroup && !!col.header && !!col.field
}))

const moreMenuItems = computed<ProMenuItem[]>(() => {
  const groupMenuItems = groupableColumns.value?.map((col) => {
    return {
      label: col.header,
      command: () => {
        groupConfig.value = { by: getFieldName(col)! }
      },
      active: groupConfig.value?.by === getFieldName(col),
    }
  })

  groupMenuItems?.unshift({
    label: '无',
    command: () => {
      groupConfig.value = undefined
    },
    active: !groupConfig.value?.by,
  })

  return [
    {
      label: '分组',
      itemIcon: BetweenHorizontalStartIcon,
      items: groupMenuItems,
    },
    {
      label: '尺寸大小',
      itemIcon: AlignVerticalSpaceAroundIcon,
      items: [
        {
          label: '大',
          command: () => {
            tableSize.value = 'large'
          },
          active: tableSize.value === 'large',
        },
        {
          label: '中',
          command: () => {
            tableSize.value = undefined
          },
          active: tableSize.value === undefined,
        },
        {
          label: '小',
          command: () => {
            tableSize.value = 'small'
          },
          active: tableSize.value === 'small',
        },
      ],
    },
    {
      label: '刷新列表',
      itemIcon: RotateCwIcon,
      command: () => {
        emit('reload')
      },
    },
  ]
})

const { refKey: searchRefKey, ref: searchRef } = useRef<InstanceType<typeof SearchInput>>()
const searchActive = ref(false)
const searchValue = ref<string | undefined>(search.value)

watch(searchActive, (value) => {
  if (value) {
    nextTick(() => {
      searchRef.value?.focus()
    })
  }
})
</script>

<template>
  <div class="flex-wrap gap-2 flex-center">
    <SearchInput
      v-if="search || searchActive"
      :ref="searchRefKey"
      v-model="searchValue"
      :placeholder="props.config?.search?.placeholder || '输入并搜索'"
      size="small"
      @blur="searchActive = false"
      @update:debounceChange="search = $event"
    />

    <ProBtn
      v-else
      label="搜索"
      v-bind="toolbarButtonProp"
      @click="searchActive = true"
    >
      <template #icon="{ size }">
        <SearchIcon :size="size" />
      </template>
    </ProBtn>

    <ProPrimePopover :ref="popoverRefKey">
      <ProBtn
        :highlight="filterCount > 0"
        :label="filterCount > 0 ? `筛选 (${filterCount})` : '筛选'"
        v-bind="toolbarButtonProp"
      >
        <template #icon="{ size }">
          <FilterIcon :size="size" />
        </template>
      </ProBtn>

      <template #content>
        <FormFilters
          v-model="internalFilterValues"
          :columns="columns"
          @reset="handleResetFilter()"
          @submit="handleSubmitFilter()"
        />
      </template>
    </ProPrimePopover>

    <ProPrimePopover>
      <ProBtn
        :highlight="sortCount > 0"
        :label="sortCount > 0 ? `排序 (${sortCount})` : '排序'"
        v-bind="toolbarButtonProp"
      >
        <template #icon="{ size }">
          <ArrowDownUpIcon :size="size" />
        </template>
      </ProBtn>

      <template #content>
        <ProTableConfigSort
          v-model:sorts="sorts"
          :columns="columns"
        />
      </template>
    </ProPrimePopover>

    <ProPrimePopover>
      <ProBtn
        label="列属性"
        v-bind="toolbarButtonProp"
      >
        <template #icon="{ size }">
          <TablePropertiesIcon :size="size" />
        </template>
      </ProBtn>

      <template #content>
        <ProTableConfigColumn
          v-model:hiddenColumnFields="hiddenColumnFields"
          :columns="columns"
        />
      </template>
    </ProPrimePopover>

    <ProPrimePopover
      :popoverProps="{
        pt: {
          content: '!p-1',
        },
      }"
    >
      <ProBtn
        label="编辑布局、分组等..."
        v-bind="toolbarButtonProp"
      >
        <template #icon="{ size }">
          <EllipsisIcon :size="size" />
        </template>
      </ProBtn>

      <template #content>
        <TieredMenu
          class="!border-none"
          :model="moreMenuItems"
          :pt="{
            rootList: {
              class: '!p-0',
            },
          }"
        >
          <template #item="{ item, props: p }: { item: ProMenuItem, props: { action: AnyType, icon: AnyType, label: AnyType } }">
            <ProMenuItem
              :active="item.active"
              :item="item"
              :itemProps="p"
            />
          </template>
        </TieredMenu>
      </template>
    </ProPrimePopover>
  </div>
</template>
