<script setup lang="ts" generic="DataRecord">
import { ListXIcon } from 'lucide-vue-next'

import type { ProSelectProps } from '~/components/pro/select/ProSelect.vue'
import { ProTableSortOrder } from '~/enums/pro'

const props = defineProps<{
  columns?: ProTableColumn<DataRecord>[]
}>()

const defaultSorts = [{ field: '', order: ProTableSortOrder.ASC }]

const sorts = defineModel<ProTableSort[]>('sorts', {
  default: [{ field: '', order: ProTableSortOrder.ASC }],
})

const columnOptions = computed(() => props.columns?.reduce<NonNullable<ProSelectProps['options']>>((acc, col) => {
  if (typeof col.field === 'string' && col.header && col.sortable) {
    acc.push({
      label: col.header,
      value: col.field,
    })
  }

  return acc
}, []))

const updateSortField = (idx: number, newField: string) => {
  const newSorts = [...sorts.value]
  newSorts[idx] = { ...newSorts[idx], field: newField }
  sorts.value = newSorts
}

const updateSortOrder = (idx: number, newOrder: ProTableSortOrder) => {
  const newSorts = [...sorts.value]
  newSorts[idx] = { ...newSorts[idx], order: newOrder }
  sorts.value = newSorts
}
</script>

<template>
  <div>
    <div
      v-for="(sort, index) of sorts"
      :key="index"
    >
      <div class="gap-2 flex-center">
        <ProSelect
          fluid
          :modelValue="sort.field"
          :options="columnOptions"
          placeholder="选择列"
          size="small"
          @update:modelValue="(value) => updateSortField(index, value as string)"
        />

        <ProSelect
          fluid
          :modelValue="sort.order"
          :options="[
            { label: '升序', value: ProTableSortOrder.ASC },
            { label: '降序', value: ProTableSortOrder.DESC },
          ]"
          size="small"
          @update:modelValue="(value) => updateSortOrder(index, value as ProTableSortOrder)"
        />
      </div>
    </div>

    <div class="pt-2">
      <Menu
        class="!border-none"
        :model="[{
          label: '移除排序',
          itemIcon: ListXIcon,
          command: () => {
            sorts = defaultSorts
          },
        }]"
        :pt="{
          list: {
            class: '!p-0',
          },
          item: {
            class: '!text-sm',
          },
        }"
      >
        <template #itemicon="{ item }: { item: PrimeVueMenuItem }">
          <Component
            :is="item.itemIcon"
            v-if="item.itemIcon"
            :size="15"
          />
        </template>
      </Menu>
    </div>
  </div>
</template>
