<script setup lang="ts" generic="DataRecord">
import { EyeClosedIcon, EyeIcon } from 'lucide-vue-next'

defineProps<{
  columns?: ProTableColumn<DataRecord>[]
}>()

const hiddenColumnFields = defineModel<ProTableColumn<DataRecord>['field'][]>('hiddenColumnFields', {
  default: [],
})

const handleToggleColumnVisible = (col: ProTableColumn<DataRecord>) => {
  if (col.field) {
    const isHidden = col.hideInTable === true

    if (isHidden) {
      hiddenColumnFields.value = hiddenColumnFields.value.filter((field) => field !== col.field)
    }
    else {
      hiddenColumnFields.value = [...hiddenColumnFields.value, col.field]
    }
  }
}
</script>

<template>
  <div class="max-h-80 space-y-1 overflow-y-auto">
    <template
      v-for="(col, idx) of columns"
      :key="idx"
    >
      <div
        v-if="col.field && col.header"
        class="gap-2 flex-center"
      >
        <div class="min-w-36 flex-1 truncate">
          {{ col.header }}
        </div>

        <div>
          <ProBtn
            size="small"
            variant="text"
            @click.stop="handleToggleColumnVisible(col)"
          >
            <template #icon="{ size }">
              <Component
                :is="col.hideInTable ? EyeClosedIcon : EyeIcon"
                :size="size"
              />
            </template>
          </ProBtn>
        </div>
      </div>
    </template>
  </div>
</template>
