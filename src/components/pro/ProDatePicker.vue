<script setup lang="ts">
import { CalendarDaysIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-vue-next'
import type { DatePickerProps } from 'primevue'

import type { DateFormat } from '~/enums/common'
import type { AnyType } from '~/types/common'

interface ShortcutOption {
  label: string
  days: number
}

type DateValue = Date | string | undefined | null
type DateRangeValue = [DateValue, DateValue] | DateValue[] | undefined

// 修改 modelValue 类型定义，支持 Date 和 string
type PickDatePickerProps = Omit<Pick<DatePickerProps, 'modelValue' | 'fluid' | 'showButtonBar' | 'dateFormat' | 'placeholder' | 'selectionMode' | 'showIcon' | 'view' | 'numberOfMonths' | 'showTime' | 'timeOnly' | 'inline' | 'minDate' | 'maxDate'>, 'modelValue'> & {
  modelValue?: DateValue | DateRangeValue
}

export interface ProDatePickerProps extends PickDatePickerProps {
  /** 是否显示快捷选择 */
  showShortcuts?: boolean
  /** 快捷选择选项，不传则不显示快捷选择 */
  shortcuts?: {
    options?: ShortcutOption[]
    onSelect?: (option: ShortcutOption) => void
  }
  format?: DateFormat
}

const props = withDefaults(defineProps<ProDatePickerProps>(), {
  fluid: false,
  showButtonBar: true,
  dateFormat: 'yy-mm-dd',
  showIcon: true,
  showShortcuts: false,
  shortcuts: () => ({
    options: [
      { label: '近三天', days: 3 },
      { label: '近七天', days: 7 },
    ],
  }),
})

const emit = defineEmits<{
  'update:modelValue': [date: DateValue | DateRangeValue]
}>()

// 将字符串日期转换为 Date 对象
const parseDate = (value: DateValue): Date | undefined => {
  if (!value) {
    return undefined
  }

  return typeof value === 'string' ? new Date(value) : value
}

// 处理日期值，确保传递给 DatePicker 的是 Date 对象
const dateValue = computed(() => {
  const val = props.modelValue

  if (!val) {
    return undefined
  }

  if (Array.isArray(val)) {
    // 过滤掉 undefined 值并转换为 Date 类型
    return val.map(parseDate).filter((d): d is Date => d !== undefined)
  }

  return parseDate(val)
})

const handleShortcutClick = (days: number) => {
  const endDate = new Date()
  const startDate = new Date()

  startDate.setDate(startDate.getDate() - (days - 1))
  startDate.setHours(0, 0, 0, 0)
  endDate.setHours(23, 59, 59, 999)

  emit('update:modelValue', [startDate, endDate])
}

// 处理日期选择事件
const handleDateChange = (event: AnyType) => {
  emit('update:modelValue', event)
}
</script>

<template>
  <DatePicker
    v-bind="props"
    :modelValue="dateValue"
    @update:modelValue="handleDateChange"
  >
    <template #dropdownicon>
      <CalendarDaysIcon :size="16" />
    </template>

    <template #incrementicon>
      <ChevronUpIcon :size="16" />
    </template>

    <template #decrementicon>
      <ChevronDownIcon :size="16" />
    </template>

    <template #footer>
      <span
        v-if="selectionMode === 'range' && showShortcuts"
        class="flex-center"
      >
        <Button
          v-for="option of shortcuts.options"
          :key="option.days"
          :label="option.label"
          severity="secondary"
          size="small"
          variant="text"
          @click="handleShortcutClick(option.days)"
        />
      </span>
    </template>
  </DatePicker>
</template>
