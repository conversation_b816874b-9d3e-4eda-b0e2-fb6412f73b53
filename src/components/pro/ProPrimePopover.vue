<script setup lang="ts">
import type { PopoverProps } from 'primevue'

const { popoverRef, popover } = usePopover()

interface Props {
  visible?: boolean
  popoverProps?: PopoverProps
  rootClass?: string
  triggerClass?: string
  disabled?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  show: [MouseEvent]
  hide: []
}>()

const isPopoverShow = ref(false)

const handleAfterShow = (ev: MouseEvent) => {
  isPopoverShow.value = true
  emit('show', ev)
}

const handleAfterHide = () => {
  isPopoverShow.value = false
  emit('hide')
}

const handleShow = (ev: MouseEvent) => {
  if (!props.disabled) {
    popover.value?.show(ev)
    handleAfterShow(ev)
  }
}

const handleHide = () => {
  popover.value?.hide()
  handleAfterHide()
}

const handleToggle = (ev: MouseEvent) => {
  if (isPopoverShow.value) {
    handleHide()
  }
  else {
    handleShow(ev)
  }
}

watch(() => props.visible, (visible) => {
  if (visible) {
    // TODO: 需要支持通过 visible 的变化控制 popover 的显示隐藏
    // handleShow(new MouseEvent('click'))
  }
  else {
    handleHide()
  }
})

defineExpose({
  show: (ev: MouseEvent) => handleShow(ev),
  hide: () => handleHide(),
})
</script>

<template>
  <div
    class="inline-block"
    :class="rootClass"
  >
    <!-- 触发元素 -->
    <div
      class="inline-block"
      :class="triggerClass"
      @click="handleToggle($event)"
    >
      <slot :isPopoverShow="isPopoverShow" />
    </div>

    <!-- 弹出内容 -->
    <Popover
      v-bind="popoverProps"
      :ref="popoverRef"
      @hide="handleAfterHide"
      @show="handleAfterShow"
    >
      <slot name="content" />
    </Popover>
  </div>
</template>
