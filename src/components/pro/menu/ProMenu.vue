<script setup lang="ts">
import type { PassThrough } from '@primevue/core'
import type { MenuPassThroughOptions, MenuProps, MenuSlots } from 'primevue'

import { ProMenuActionType } from '~/enums/pro'
import { mergePT } from '~/utils/primevue'

export interface ProMenuProps extends Omit<MenuProps, 'model'> {
  /** 菜单项 */
  model: ProMenuItem[]
  /** 是否嵌入到其他 UI 中 */
  isEmbed?: boolean
}

export type ProMenuSlots = MenuSlots

const props = defineProps<ProMenuProps>()

const slots = defineSlots<ProMenuSlots>()

const emit = defineEmits<{
  /** 菜单显示时触发 */
  show: []
  /** 菜单隐藏时触发 */
  hide: []
}>()

const handleShow = () => {
  emit('show')
}

const handleHide = () => {
  emit('hide')
}

const { popMenuRef, popMenu } = usePopMenu()

defineExpose({
  popMenu,
})

// 嵌入式菜单的基础样式
const embedStyle: PassThrough<MenuPassThroughOptions> = {
  root: '!border-none',
  list: '!p-0',
  itemContent: '[&:has(.p-menu-item-active)]:!bg-primary [&:has(.p-menu-item-active)]:!text-primary-contrast',
}

const passThroughOptions = computed(() => {
  if (props.isEmbed) {
    return mergePT(embedStyle, props.pt)
  }

  return props.pt || {}
})
</script>

<template>
  <Menu
    v-bind="props"
    :ref="popMenuRef"
    :pt="passThroughOptions"
    @hide="handleHide()"
    @show="handleShow()"
  >
    <template #item="{ item, props: p }: { item: ProMenuItem, props: { action: AnyType, icon: AnyType, label: AnyType } }">
      <Divider
        v-if="item.actionType === ProMenuActionType.Separator"
        class="!my-1"
      />

      <ProMenuItem
        v-else-if="!item.hide"
        :active="item.active"
        :item="item"
        :itemProps="p"
      />
    </template>

    <!-- 转发所有插槽 -->
    <template
      v-for="(_, name) of slots"
      :key="name"
      #[name]="slotData"
    >
      <slot
        :key="name"
        :name="name as keyof MenuSlots"
        v-bind="slotData"
      />
    </template>
  </Menu>
</template>
