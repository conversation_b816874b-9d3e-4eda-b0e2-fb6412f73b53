<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { ChevronRightIcon } from 'lucide-vue-next'

import { getMenuIcon, ProMenuActionType } from '~/enums/pro'

defineProps<{
  item: ProMenuItem
  itemProps?: { action: AnyType, icon: AnyType, label: AnyType }
  active?: boolean
}>()

const { isDarkMode } = useTheme()

const dangerColor = computed(() => {
  return isDarkMode.value ? $dt('primitive.red.400').value : $dt('primitive.red.500').value
})

const cssVarMenuItemIconFocusColor = $dt('menu.item.icon.focus.color').name
const cssVarContextMenuItemIconFocusColor = $dt('contextmenu.item.icon.focus.color').name

const getActionStyles = (item: ProMenuItem) => {
  if (item.actionType === ProMenuActionType.Delete) {
    // 使用危险色覆盖默认的 CSS 变量色
    return {
      [cssVarMenuItemIconFocusColor]: dangerColor.value,
      [cssVarContextMenuItemIconFocusColor]: dangerColor.value,
    }
  }
}
</script>

<template>
  <a
    v-bind="itemProps?.action"
    class="text-sm flex-center"
    :class="{
      'p-menu-item-active pointer-events-none !cursor-default': active,
      'hover:!text-danger-500': item.actionType === ProMenuActionType.Delete,
    }"
    :style="{
      padding: $dt('menu.item.padding').value,
      ...getActionStyles(item),
    }"
  >
    <Component
      v-bind="itemProps?.icon"
      :is="getMenuIcon(item)"
      v-if="getMenuIcon(item)"
      :size="15"
    />

    <span
      v-bind="itemProps?.label"
      class="flex-1"
    >
      {{ item.label }}
    </span>

    <span
      v-if="Array.isArray(item.items) && item.items.length > 0"
      class="ml-auto"
    >
      <ChevronRightIcon :size="14" />
    </span>
  </a>
</template>
