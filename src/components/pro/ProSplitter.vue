<script setup lang="ts">
import type { SplitterPanelProps, SplitterProps } from 'primevue'

interface Props extends SplitterProps {
  left?: SplitterPanelProps
  right?: SplitterPanelProps
  wrapperClass?: string
  leftPanelContentClass?: string
  rightPanelContentClass?: string
  gutterClass?: string
  hideGutterLine?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  left: () => ({
    size: 20,
    minSize: 15,
  }),
  right: () => ({
    size: 100 - 20,
    minSize: 50,
  }),
})

const rightPanelSize = computed(() => props.right.size ?? 100 - (props.left.size ?? 20))

const isResizing = ref(false)

defineSlots<{
  left: () => VNode[]
  right: () => VNode[]
}>()
</script>

<template>
  <Splitter
    :gutterSize="1"
    layout="horizontal"
    :pt="{
      root: {
        class: `!border-none size-full ${wrapperClass || ''}`,
      },
      gutter: {
        class: `${gutterClass || ''} ${hideGutterLine ? '!bg-transparent opacity-0 hover:opacity-100' : ''}`,
      },
      gutterHandle: {
        class: hideGutterLine ? `!h-[98%] !w-0.5 ${isResizing ? '!bg-primary' : '!bg-primary/20'}` : undefined,
      },
    }"
    @resizeend="isResizing = false"
    @resizestart="isResizing = true"
  >
    <SplitterPanel
      :minSize="left.minSize"
      :size="left.size"
    >
      <div
        class="h-full overflow-auto pr-in-splitter"
        :class="leftPanelContentClass"
      >
        <slot name="left" />
      </div>
    </SplitterPanel>

    <SplitterPanel
      :minSize="right.minSize"
      :size="rightPanelSize"
    >
      <div
        class="h-full overflow-auto pl-in-splitter"
        :class="rightPanelContentClass"
      >
        <slot name="right" />
      </div>
    </SplitterPanel>
  </Splitter>
</template>
