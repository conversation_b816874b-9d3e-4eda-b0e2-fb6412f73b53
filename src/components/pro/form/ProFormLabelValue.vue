<script setup lang="ts">
import { ProValueType } from '~/enums/pro'

withDefaults(defineProps<{
  data?: ProFormLabelValueItem[]
  readOnly?: boolean
  layout?: 'horizontal' | 'vertical' | 'table' | 'two-column-table'
  wrapperClass?: string
  labelValueClass?: string
  labelClass?: string
  valueClass?: string
  labelColon?: boolean | string
}>(), {
  layout: 'table',
  labelColon: false,
})
</script>

<template>
  <div
    v-if="data"
    :class="[
      {
        'grid grid-cols-4 gap-4': layout === 'horizontal',
        'flex flex-col gap-3': layout === 'vertical',
        'grid grid-cols-2': layout === 'two-column-table',
      },
      wrapperClass,
    ]"
  >
    <div
      v-for="(item, rowIdx) of data"
      :key="rowIdx"
      :class="[
        {
          'flex flex-col gap-1': layout === 'horizontal',
          'flex gap-5': layout === 'vertical',
          'grid grid-cols-[150px_1fr] border-b border-divider last:border-b-0': layout === 'table',
          'grid grid-cols-[120px_1fr] border-divider': layout === 'two-column-table',
        },
        labelValueClass,
      ]"
    >
      <template v-if="item.valueType === ProValueType.Divider">
        <Divider />
      </template>

      <template v-else>
        <!-- Label -->
        <div
          :class="[
            {
              'text-sm text-surface-500': layout === 'horizontal',
              'basis-28': layout === 'vertical',
            },

            layout === 'table' && [
              'border-x border-divider bg-emphasis p-3 font-semibold flex-center',
              {
                'border-t': rowIdx === 0,
                'border-b': rowIdx === data.length - 1,
              },
            ],

            layout === 'two-column-table' && [
              'border-l border-t border-divider bg-emphasis p-3 font-semibold flex-center',
              {
                'border-b':
                  rowIdx === data.length - 1
                  || rowIdx === data.length - 2
                  || (rowIdx ===data.length - 2 && (data.length - 2) % 2 === 1),
              },
            ],

            labelClass,
          ]"
        >
          {{ item.label }}{{ typeof labelColon === 'string' ? labelColon : labelColon ? '：' : '' }}
        </div>

        <!-- Value -->
        <div
          :class="[
            {
              'flex-1': layout === 'vertical',

              'min-h-full break-all border-r p-3 flex-center': layout === 'table',
              'border-t': layout === 'table' && rowIdx === 0,
              'border-b': layout === 'table' && rowIdx === data.length - 1,
            },

            layout === 'two-column-table' && [
              'min-h-full break-all border-l border-t border-divider p-3 flex-center',
              {
                'border-r': rowIdx % 2 === 1 || rowIdx === data.length - 1,
                'border-b':
                  rowIdx === data.length - 1
                  || rowIdx === data.length - 2
                  || (rowIdx ===data.length - 2 && (data.length - 2) % 2 === 1),
              },
            ],

            valueClass,
          ]"
        >
          <Component
            :is="item.value"
            v-if="typeof item.value === 'function'"
          />

          <template v-else>
            <ProFormField
              v-if="item.valueType"
              :fieldType="item.valueType"
              :modelValue="item.value"
              :readOnly="readOnly"
            />

            <template v-else>
              {{ item.value || '-' }}
            </template>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>
