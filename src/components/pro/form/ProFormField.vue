/**
 * ProFormField 表单字段组件
 *
 * 这是一个通用的表单字段组件，可以根据字段类型渲染不同的表单控件。
 * 支持各种表单元素类型，如文本框、下拉选择、日期选择器等。
 */
<script setup lang="ts">
import type { ProDatePickerProps } from '~/components/pro/ProDatePicker.vue'
import type { ProSelectProps } from '~/components/pro/select/ProSelect.vue'
import { ProValueType } from '~/enums/pro'

interface Props {
  fieldType: ProValueType
  fieldProps?: Record<string, UnsafeAny>
  options?: {
    label?: string
    value: string
  }[]
  placeholder?: string
  inputId?: string

  /** 是否只读 */
  readOnly?: boolean

  /** 空值时的默认文本，默认值为 '-' */
  emptyText?: string
}

const props = withDefaults(defineProps<Props>(), {
  readOnly: false,
  emptyText: '-',
})

const { fieldType, fieldProps, placeholder, inputId, readOnly, emptyText } = toRefs(props)

defineSlots<{
  fallback: (props: Props) => VNode
}>()

type FieldValue = AnyType

const fieldValue = defineModel<FieldValue>()

const commonProps = computed(() => ({
  modelValue: fieldValue.value,
  placeholder: placeholder.value,
  id: inputId.value,
  inputId: inputId.value,
  disabled: readOnly.value,
  'onUpdate:modelValue': (val: FieldValue) => fieldValue.value = val,
  ...fieldProps.value,
}))

const selectProps = computed<ProSelectProps>(() => ({
  ...commonProps.value,
  showClear: true,
}))

const datePickerProps = computed<ProDatePickerProps>(() => ({
  ...commonProps.value,
  showIcon: true,
}))

const selectOptions = computed(() => props.options?.map((opt) => ({ label: opt.label ?? opt.value, value: opt.value })))
</script>

<template>
  <template v-if="readOnly">
    <div>
      <template v-if="fieldType === ProValueType.Text || fieldType === ProValueType.Textarea || fieldType === ProValueType.Digit">
        <div class="line-clamp-1">
          {{ fieldValue || emptyText }}
        </div>
      </template>

      <template v-else-if="fieldType === ProValueType.Select || fieldType === ProValueType.Checkbox || fieldType === ProValueType.Radio">
        {{ options?.find(opt => opt.value === fieldValue)?.label || fieldValue || emptyText }}
      </template>

      <template v-else-if="fieldType === ProValueType.Switch">
        {{ fieldValue || emptyText }}
      </template>

      <template v-else-if="fieldType === ProValueType.Time || fieldType === ProValueType.Date || fieldType === ProValueType.DateTime">
        {{ fieldValue || emptyText }}
      </template>

      <template v-else-if="fieldType === ProValueType.Tag">
        <div
          v-if="Array.isArray(fieldValue)"
          class="flex flex-wrap gap-2"
        >
          <Tag
            v-for="(tag, idx) of fieldValue"
            :key="idx"
            severity="secondary"
            :value="typeof tag === 'string' ? tag : tag.label || tag.value || emptyText"
          />
        </div>

        <template v-else>
          <Tag
            severity="secondary"
            :value="fieldValue || emptyText"
          />
        </template>
      </template>

      <template v-else-if="fieldType === ProValueType.Image">
        <Image
          :src="fieldValue"
          width="100"
        />
      </template>

      <template v-else-if="fieldType === ProValueType.Divider">
        <Divider />
      </template>

      <slot
        v-else
        name="fallback"
        v-bind="props"
      >
        <Message severity="secondary">
          暂不支持该字段类型：{{ fieldType }}
        </Message>
      </slot>
    </div>
  </template>

  <template v-else>
    <InputText
      v-if="fieldType === ProValueType.Text || fieldType === ProValueType.Image"
      fluid
      v-bind="commonProps"
    />

    <Textarea
      v-else-if="fieldType === ProValueType.Textarea"
      fluid
      v-bind="commonProps"
    />

    <InputNumber
      v-else-if="fieldType === ProValueType.Digit"
      fluid
      v-bind="commonProps"
      :useGrouping="false"
    />

    <ProSelect
      v-else-if="fieldType === ProValueType.Select || fieldType === ProValueType.Tag"
      v-bind="selectProps"
      :options="selectOptions"
    />

    <CheckboxGroup
      v-else-if="fieldType === ProValueType.Checkbox"
      class="flex flex-wrap gap-4"
      v-bind="commonProps"
    >
      <template v-if="selectOptions?.length">
        <div
          v-for="opt of selectOptions"
          :key="opt.value"
          class="gap-2 flex-center"
        >
          <Checkbox
            :inputId="opt.value"
            :value="opt.value"
          />
          <label :for="opt.value">
            {{ opt.label }}
          </label>
        </div>
      </template>

      <div
        v-else
        class="py-1 text-sm"
      >
        暂无选项
      </div>
    </CheckboxGroup>

    <RadioButtonGroup
      v-else-if="fieldType === ProValueType.Radio"
      class="flex flex-wrap gap-4"
      v-bind="commonProps"
    >
      <template v-if="selectOptions?.length">
        <div
          v-for="opt of selectOptions"
          :key="opt.value"
          class="gap-2 flex-center"
        >
          <RadioButton
            :inputId="opt.value"
            :value="opt.value"
          />
          <label :for="opt.value">
            {{ opt.label }}
          </label>
        </div>
      </template>

      <div
        v-else
        class="py-1 text-sm"
      >
        暂无选项
      </div>
    </RadioButtonGroup>

    <ToggleSwitch
      v-else-if="fieldType === ProValueType.Switch"
      v-bind="commonProps"
    />

    <ProDatePicker
      v-else-if="fieldType === ProValueType.Time"
      v-bind="datePickerProps"
      timeOnly
    />

    <ProDatePicker
      v-else-if="fieldType === ProValueType.Date"
      v-bind="datePickerProps"
    />

    <ProDatePicker
      v-else-if="fieldType === ProValueType.DateTime"
      v-bind="datePickerProps"
      showTime
    />

    <Divider v-else-if="fieldType === ProValueType.Divider" />

    <slot
      v-else
      name="fallback"
      v-bind="$props"
    >
      <Message
        severity="secondary"
      >
        暂不支持该字段类型：{{ fieldType }}
      </Message>
    </slot>
  </template>
</template>
