<script setup lang="ts">
import { flip, offset, type Placement, shift, useFloating } from '@floating-ui/vue'
import { $dt } from '@primevue/themes'

const props = withDefaults(
  defineProps<{
    /** 触发方式，默认 click */
    trigger?: 'click' | 'hover'

    /** 弹出位置，默认 bottom */
    position?: Placement

    /** hover 模式下展示延迟时间(ms)，默认 300 */
    showDelay?: number

    /** hover 模式下隐藏延迟时间(ms)，默认 100 */
    hideDelay?: number

    /** 弹出内容 z-index，默认 999 */
    zIndex?: number

    /** 弹出内容样式 */
    contentClass?: string
    /** 弹出内容样式 */
    contentStyle?: Record<string, string>
  }>(),
  {
    trigger: 'click',
    position: 'bottom',
    showDelay: 300,
    hideDelay: 100,
    zIndex: 999,
  },
)

const isOpen = ref(false)

const { ref: reference, refKey: referenceRefKey } = useRef<HTMLElement | null>()
const { ref: floating, refKey: floatingRefKey } = useRef<HTMLElement | null>()

const targetRef = ref<HTMLElement | null>(null)

watch(reference, (newRef) => {
  if (newRef) {
    targetRef.value = newRef
  }
}, { immediate: true })

const { floatingStyles } = useFloating(targetRef, floating, {
  placement: props.position,
  middleware: [
    offset(4),
    flip(),
    shift(),
  ],
})

const showTimer = ref<NodeJS.Timeout>()
const hideTimer = ref<NodeJS.Timeout>()

const show = (outterTarget?: HTMLElement | null) => {
  if (showTimer.value) {
    clearTimeout(showTimer.value)
  }

  if (hideTimer.value) {
    clearTimeout(hideTimer.value)
  }

  if (outterTarget) {
    targetRef.value = outterTarget
  }

  if (props.trigger === 'hover' && props.showDelay > 0) {
    showTimer.value = setTimeout(() => {
      isOpen.value = true
    }, props.showDelay)
  }
  else {
    isOpen.value = true
  }
}

const hide = () => {
  if (showTimer.value) {
    clearTimeout(showTimer.value)
  }

  isOpen.value = false
}

const toggle = () => {
  if (isOpen.value) {
    hide()
  }
  else {
    show()
  }
}

const handleTrigger = () => {
  if (props.trigger === 'click') {
    toggle()
  }
}

const handleMouseEnter = () => {
  if (props.trigger === 'hover') {
    show()
  }
}

const handleHoverLeave = (ev: MouseEvent, oppositeElement: HTMLElement | null) => {
  if (props.trigger === 'hover') {
    const toElement = ev.relatedTarget

    if (!(toElement instanceof HTMLElement)) {
      return
    }

    // 如果离开的元素是弹出框的子元素，则不隐藏弹出框
    if (oppositeElement?.contains(toElement)) {
      return
    }

    hideTimer.value = setTimeout(() => {
      if (oppositeElement?.matches(':hover')) {
        return
      }

      hide()
    }, props.hideDelay)
  }
}

const handleMouseLeave = (ev: MouseEvent) => {
  handleHoverLeave(ev, floating.value)
}

const handleContentMouseLeave = (ev: MouseEvent) => {
  handleHoverLeave(ev, targetRef.value)
}

const handleClickOutside = (event: MouseEvent) => {
  if (!isOpen.value) {
    return
  }

  const target = event.target as HTMLElement

  if (!target) {
    return
  }

  const isClickOutside = !(
    targetRef.value?.contains(target)
    || floating.value?.contains(target)
  )

  if (isClickOutside) {
    hide()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)

  if (showTimer.value) {
    clearTimeout(showTimer.value)
  }

  if (hideTimer.value) {
    clearTimeout(hideTimer.value)
  }
})

defineSlots<{
  default: VNode[]
  content: VNode[]
}>()

defineExpose({
  show,
  hide,
  toggle,
  hoverLeave: (ev: MouseEvent) => {
    handleHoverLeave(ev, floating.value)
  },
})

const popoverBorderColor = $dt('popover.border.color').value
const popoverBorderRadius = $dt('popover.border.radius').value
const popoverShadow = $dt('popover.shadow').value
const popoverContentPadding = $dt('popover.content.padding').value
</script>

<template>
  <div class="inline-block">
    <!-- 触发元素 -->
    <div
      :ref="referenceRefKey"
      class="inline-block"
      @click="handleTrigger"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    >
      <slot />
    </div>

    <!-- 弹出内容 -->
    <Teleport to="body">
      <div
        v-if="$slots.content && isOpen"
        :ref="floatingRefKey"
        class="absolute left-0 top-0"
        :style="{ ...floatingStyles, zIndex }"
        @mouseleave="handleContentMouseLeave"
      >
        <div
          class="border bg-content"
          :class="contentClass"
          :style="{
            borderColor: popoverBorderColor,
            borderRadius: popoverBorderRadius,
            boxShadow: popoverShadow,
            padding: popoverContentPadding,
            ...contentStyle,
          }"
        >
          <slot name="content" />
        </div>
      </div>
    </Teleport>
  </div>
</template>
