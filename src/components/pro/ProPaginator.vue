<script setup lang="ts">
import type { PaginatorProps } from 'primevue'

const { isDarkMode } = useTheme()

const props = defineProps<{
  /** 分页信息，包括当前页码、每页条数、总条数 */
  pageInfo: PageInfo
  pageSizeOptions?: number[]
  paginatorProps?: PaginatorProps
  hideStart?: boolean
  hideEnd?: boolean
} & PaginatorProps>()

const emit = defineEmits<{
  pageChange: [pageState: PrimeVuePageInfo]
}>()

const notEmpty = computed(() => {
  return props.pageInfo.total > 0
})
</script>

<template>
  <Paginator
    v-bind="paginatorProps"
    :dt="{
      nav: {
        button: {
          width: '1.8rem',
          height: '1.8rem',
          selected: {
            color: isDarkMode ? '{surface-400}' : '{surface-500}',
            background: isDarkMode ? '{surface-700}' : '{surface-100}',
          },
          border: {
            radius: '0.5rem',
          },
        },
      },
    }"
    :first="(pageInfo.page - 1) * pageInfo.page_size"
    :pt="{
      root: {
        class: '!p-0',
      },
      page: {
        class: '!px-1',
      },
    }"
    :rows="pageInfo.page_size"
    :rowsPerPageOptions="pageSizeOptions"
    :template="pageInfo.page > 100000
      ? 'FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink RowsPerPageDropdown'
      : undefined"
    :totalRecords="pageInfo.total"
    @page="emit('pageChange', $event)"
  >
    <template
      v-if="notEmpty && !hideStart"
      #start
    >
      <span class="text-sm text-secondary">
        共 {{ pageInfo.total > 1000 ? new Intl.NumberFormat().format(pageInfo.total) : pageInfo.total }} 条记录
      </span>
    </template>

    <template
      v-if="notEmpty && !hideEnd"
      #end
    >
      <!-- 为了让分页器居中，这里需要一个空模板 -->
    </template>
  </Paginator>
</template>
