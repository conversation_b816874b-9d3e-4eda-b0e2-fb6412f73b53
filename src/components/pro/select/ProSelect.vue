<script setup lang="ts" generic="SelectValue = string | number">
import { $dt } from '@primevue/themes'
import { ChevronDownIcon, LoaderIcon, SearchIcon, XIcon } from 'lucide-vue-next'
import type { SelectProps, SelectSlots } from 'primevue'

import type { ProSelectOption } from '~/types/pro'

export interface ProSelectProps<V = string | number> extends Pick<SelectProps, 'optionLabel' | 'optionValue' | 'loading' | 'placeholder' | 'labelId' | 'showClear' | 'fluid' | 'disabled'> {
  options?: ProSelectOption<V>[]
  /** 搜索配置 */
  search?: {
    /** 是否启用搜索 */
    enable?: boolean
    /** 搜索框的 placeholder */
    placeholder?: string
  }
}

const selectProps = withDefaults(defineProps<ProSelectProps<SelectValue>>(), {
  optionLabel: 'label',
  optionValue: 'value',
})

const slots = defineSlots<SelectSlots>()

const selectedValue = defineModel<SelectValue>()
const searchValue = defineModel<string>('searchValue', {
  default: '',
})
</script>

<template>
  <Select
    v-bind="selectProps"
    v-model="selectedValue"
  >
    <template
      v-if="search?.enable"
      #header
    >
      <div class="pb-1">
        <div class="py-0.5">
          <IconField>
            <InputIcon class="inline-flex-center">
              <SearchIcon :size="16" />
            </InputIcon>

            <InputText
              v-model="searchValue"
              :dt="{
                border: {
                  color: 'transparent',
                },
                hover: {
                  border: {
                    color: 'transparent',
                  },
                },
                focus: {
                  border: {
                    color: 'transparent',
                  },
                },
                shadow: 'none',
              }"
              fluid
              :placeholder="search?.placeholder"
            />
          </IconField>
        </div>

        <Divider class="!m-0" />
      </div>
    </template>

    <template #option="{ option } : {option: ProSelectOption<string | number> }">
      <ProSelectOption :option="option" />
    </template>

    <!-- 转发所有插槽 -->
    <template
      v-for="(_, name) of slots"
      :key="name"
      #[name]="slotData"
    >
      <slot
        :name="name as keyof SelectSlots"
        v-bind="slotData"
      />
    </template>

    <template #dropdownicon>
      <ChevronDownIcon :size="15" />
    </template>

    <template #clearicon="{ clearCallback }">
      <span
        class="justify-center flex-center"
        :style="{ color: $dt('select.dropdown.color').variable }"
      >
        <XIcon
          :size="15"
          @click="clearCallback"
        />
      </span>
    </template>

    <template #loadingicon>
      <span
        class="justify-center flex-center"
        :style="{ color: $dt('select.dropdown.color').variable }"
      >
        <LoaderIcon
          class="animate-spin"
          :size="15"
        />
      </span>
    </template>
  </Select>
</template>
