<script setup lang="ts">
import type { ProSelectOption } from '~/types/pro'

defineProps<{
  option: ProSelectOption
}>()
</script>

<template>
  <div class="w-full overflow-hidden">
    <div class="gap-2.5 flex-center">
      <Component
        :is="option.icon"
        v-if="option.icon"
        :size="15"
      />

      {{ option.label }}
    </div>

    <div
      v-if="option.description"
      class="w-max gap-2 truncate pt-0.5 text-sm opacity-70 flex-center"
    >
      {{ option.description }}
    </div>
  </div>
</template>
