<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { useVueFlow } from '@vue-flow/core'
import arrayToTree from 'array-to-tree'
import { cloneDeep, omit, uniqBy } from 'lodash-es'
import { BoltIcon, ChevronDownIcon, ChevronUpIcon, LoaderIcon, PlusIcon, WorkflowIcon } from 'lucide-vue-next'
import { nanoid } from 'nanoid'
import type { OrganizationChartNode } from 'primevue'

import { assetDetailConfig, TopologyNodeType } from '~/enums/asset'
import { queryKeys } from '~/enums/query-key'

const { fitView } = useVueFlow()

/** 默认每页加载的节点数 */
const defaultNodesPerPage = 10
const debug = ref(false)

const rootNode: TopologyNode = {
  id: 'root',
  key: 'root',
  topology_type: TopologyNodeType.Root,
  nodeData: {
    name: '网络',
  },
}

const loadParams = ref(new Map<string, {
  /** 当前已加载出来的页数 */
  page: number
  pageSum: number
  level?: number
  remain?: number
  subNodeData?: SubNode
}>())

// const levels = computed(() => loadParams.value.size)
const shouldFitView = ref(false)

/** 加载更多（同级或子级）节点时，该加载节点的父节点 */
const requestParentId = ref<Topology['id']>(rootNode.id)

/** 加载更多（同级或子级）节点时，该加载节点的页码 */
const requestPage = ref(1)

const requestKey = computed(() => {
  return `${requestParentId.value}-${requestPage.value}`
})

const subNodeData = ref<Required<SubNode>>({
  type: TopologyNodeType.Network,
  data: {},
})

/** 资产拓扑节点 */
const assetTopoNodes = ref<TopologyNode[]>([])

/** 「加载更多同级」节点 */
const loadMoreNodes = ref<TopologyNode[]>([])

const { isLoading } = useQuery({
  queryKey: queryKeys.asset.topo.all({ requestKey, subNodeData }),
  queryFn: () => {
    return AssetService.getTopology({
      type: subNodeData.value.type,
      data: {
        ...subNodeData.value.data,
        page: requestPage.value,
        page_size: defaultNodesPerPage,
      },
    })
  },
  select: (data) => {
    if (data) {
      // 当前请求得到的新节点的父节点
      const thisParentId = requestParentId.value

      const newTopologyNodes = data.list.map((item) => {
        const nodeId = nanoid(4)

        return {
          ...item,
          key: nodeId,
          id: nodeId,
          parent_id: thisParentId, // 在获取到加载出的更多子节点时，手动设置其 parent_id
          nodeData: omit(item, ['id', 'parent_id', 'key', 'node', 'topology_type']) as UnsafeAny,
        }
      })

      // 计算当前节点的层级
      const parentLevel = thisParentId === rootNode.id
        ? 0
        : loadParams.value.get(thisParentId)?.level ?? 0

      const hasMoreSiblingNodes = data.page < data.page_sum

      if (hasMoreSiblingNodes) {
        // 如果还有同级节点未加载，则记录「加载节点」的信息，在下次加载同级节点时，会使用到这些信息
        loadParams.value.set(thisParentId, {
          page: data.page,
          pageSum: data.page_sum,
          level: parentLevel + 1,
          remain: data.total - data.page * defaultNodesPerPage,
          subNodeData: toRaw(subNodeData.value),
        })
      }
      else {
        // 如果已经加载完所有页，则应该删除「加载节点」的信息
        loadParams.value.delete(thisParentId)
      }

      const loadMoreEntries = Array.from(loadParams.value.entries())

      loadMoreNodes.value = loadMoreEntries.reduce<TopologyNode[]>((acc, [parentId, { remain = 0 }]) => {
        const nodeId = `${parentId}-more`

        acc.push({
          key: nodeId,
          id: nodeId,
          parent_id: parentId,
          topology_type: TopologyNodeType.LoadMore,
          nodeData: {
            name: `加载更多 (${remain})`,
          },
        })

        return acc
      }, [])

      // 将新加载出的子节点与已加载的子节点合并，并去重
      assetTopoNodes.value = uniqBy([cloneDeep(rootNode), ...assetTopoNodes.value, ...newTopologyNodes], 'id')

      if (shouldFitView.value) {
        // HACK: 延迟执行 fitView 以避免在加载更多时触发
        setTimeout(() => {
          fitView({ maxZoom: 1 })
          shouldFitView.value = false
        }, 50)
      }
    }

    return null
  },
})

// watchEffect(() => {
//   if (levels.value < 2) {
//     if (!isLoading.value) {
//       // 获取当前已加载的节点
//       const currentNodes = assetTopoNodes.value

//       // 找到第一个具有子节点的节点
//       const firstNodeWithChildren = currentNodes.find(({ node }) =>
//         node?.has_more,
//       )

//       // 如果找到了这样的节点，自动触发加载其子节点
//       if (firstNodeWithChildren?.node?.sub_node) {
//         requestParentId.value = firstNodeWithChildren.id
//         subNodeData.value = firstNodeWithChildren.node.sub_node
//       }

//       shouldFitView.value = true
//     }
//   }
// })

const topologyTree = computed<TopologyTreeNode[]>(() => {
  if (Array.isArray(assetTopoNodes.value)) {
    return arrayToTree([...assetTopoNodes.value, ...loadMoreNodes.value], {
      customID: 'id',
      parentProperty: 'parent_id',
      childrenProperty: 'children',
    })
  }

  return []
})

const chartNode = computed<OrganizationChartNode | undefined>(() => {
  return topologyTree.value.at(0)
})

const selection = ref({})

// MARK: 加载同级节点
const handleLoadMoreSibling = (selectedNode: TopologyNode) => {
  if (selectedNode.topology_type === TopologyNodeType.LoadMore) {
    const parentId = selectedNode.parent_id

    if (parentId) {
      const loadParam = loadParams.value.get(parentId)

      if (loadParam) {
        requestParentId.value = parentId

        if (loadParam?.subNodeData) {
          subNodeData.value = loadParam.subNodeData
        }

        requestPage.value = loadParam.page + 1
      }
    }
  }
}

// MARK: 加载子节点
const handleLoadSubNodes = (node: TopologyNode) => {
  const subNode = node.node?.sub_node

  if (subNode) {
    requestParentId.value = node.id
    subNodeData.value = subNode
    requestPage.value = 1
  }
}

const hasLoadedSubNodes = (node: TopologyTreeNode): node is TopologyTreeNode => {
  return Array.isArray(node.children) && node.children.length > 0
}
</script>

<template>
  <OrganizationChart
    v-if="chartNode"
    v-model:selectionKeys="selection"
    collapsible
    :pt="{
      node: {
        class: '!p-0',
      },
    }"
    :value="chartNode"
  >
    <template #default="{ node }: { node: TopologyTreeNode }">
      <div
        class="relative min-w-32 p-3"
        @click.stop
      >
        <!-- DEBUG -->
        <div v-if="debug">
          <div>
            id: {{ node.id }}
          </div>
          <div>
            parent: {{ node.parent_id }}
          </div>
        </div>

        <div v-if="node.topology_type === TopologyNodeType.LoadMore">
          <Button
            :disabled="isLoading"
            :label="node.nodeData.name"
            :loading="isLoading && requestParentId === node.parent_id"
            severity="secondary"
            size="small"
            @click="handleLoadMoreSibling(node)"
          />
        </div>

        <!-- 以下是节点类型 -->
        <template v-else>
          <div
            v-if="(node.topology_type === TopologyNodeType.NetworkDevice || node.topology_type === TopologyNodeType.Server || node.topology_type === TopologyNodeType.Database || node.topology_type === TopologyNodeType.Web || node.topology_type === TopologyNodeType.Other) && node.nodeData.template?.name"
            class="pb-4"
          >
            <span class="absolute left-0 top-0 rounded-br bg-emphasis p-1 text-xs">
              {{ node.nodeData.template.name }}
            </span>
          </div>

          <div class="flex-col gap-2 flex-center">
            <div
              v-if="(node.topology_type === TopologyNodeType.NetworkDevice || node.topology_type === TopologyNodeType.Server || node.topology_type === TopologyNodeType.Database || node.topology_type === TopologyNodeType.Web || node.topology_type === TopologyNodeType.Other) && node.nodeData.template?.icon"
              class="size-[22px]"
            >
              <IconRender :iconVal="node.nodeData.template.icon">
                <template #fallback>
                  <BoltIcon class="opacity-85" />
                </template>
              </IconRender>
            </div>

            <div
              v-else-if="node.topology_type === TopologyNodeType.ServerTemplateType"
              class="size-[22px]"
            >
              <IconRender :iconVal="node.nodeData.icon">
                <template #fallback>
                  <BoltIcon class="opacity-85" />
                </template>
              </IconRender>
            </div>

            <Component
              :is="assetDetailConfig[node.nodeData.asset_type].icon"
              v-else-if="node.topology_type === TopologyNodeType.ServerAssetType"
              class="shrink-0"
              :size="22"
            />

            <Component
              :is="getTopologyNodeIcon(node.topology_type)"
              v-else
              class="shrink-0"
              :size="22"
            />

            <div class="text-sm">
              <div v-if="node.topology_type === TopologyNodeType.Root">
                {{ node.nodeData.name }}
              </div>

              <div v-else-if="node.topology_type === TopologyNodeType.Network">
                {{ node.nodeData.name }}
              </div>

              <div v-else-if="node.topology_type === TopologyNodeType.NetworkDevice">
                {{ node.nodeData.asset?.name }}
              </div>

              <div v-else-if="node.topology_type === TopologyNodeType.ServerTemplateType || node.topology_type === TopologyNodeType.ServerAssetType">
                {{ node.nodeData.name }}
              </div>

              <div
                v-else-if="node.topology_type === TopologyNodeType.Server"
                class="space-y-1"
              >
                <div>
                  {{ node.nodeData?.asset?.name }}
                </div>
                <div>
                  <Tag
                    v-if="node.nodeData.ip_list?.at(0)?.ipv4"
                    severity="secondary"
                    size="small"
                  >
                    IP: {{ node.nodeData.ip_list?.at(0)?.ipv4 }}
                  </Tag>
                </div>
              </div>

              <div
                v-else-if="node.topology_type === TopologyNodeType.Database || node.topology_type === TopologyNodeType.Web || node.topology_type === TopologyNodeType.Other"
                class="space-y-1"
              >
                <div>
                  {{ node.nodeData?.name || node.nodeData?.asset?.name || '未确认' }}
                </div>
                <div>
                  <Tag
                    v-if="node.nodeData.port?.port"
                    severity="secondary"
                    size="small"
                  >
                    端口：{{ node.nodeData.port?.port }}
                  </Tag>
                </div>
              </div>

              <div v-else>
                未命名设备
              </div>
            </div>

            <ProBtn
              v-if="node.node?.has_more"
              :disabled="hasLoadedSubNodes(node)"
              :label="String(node.node?.count)"
              :pt="{
                root: {
                  class: '!p-1 !shrink-0',
                },
              }"
              severity="secondary"
              size="small"
              :title="hasLoadedSubNodes(node) ? `共有 ${node.node?.count} 个子节点` : '加载更多资产'"
              variant="outlined"
              @click.stop="handleLoadSubNodes(node)"
            >
              <template #icon>
                <Component
                  :is="isLoading && requestParentId === node.id ? LoaderIcon : hasLoadedSubNodes(node) ? WorkflowIcon : PlusIcon"
                  :class="{
                    'animate-spin': isLoading && requestParentId === node.id,
                  }"
                  :size="13"
                />
              </template>
            </ProBtn>
          </div>
        </template>
      </div>
    </template>

    <template #toggleicon="{ expanded }: { expanded: boolean }">
      <span class="size-full justify-center flex-center">
        <Component
          :is="expanded ? ChevronDownIcon : ChevronUpIcon"
          :size="16"
        />
      </span>
    </template>
  </OrganizationChart>
</template>
