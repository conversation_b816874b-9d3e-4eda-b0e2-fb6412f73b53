<script setup>
import { useVueFlow, VueFlow } from '@vue-flow/core'

import CustomNode from '~/components/CustomNode.vue'
import { useLayout } from '~/composables/useLayout'
import { initialEdges, initialNodes } from '~/enums/topo-elements'

// 注册自定义节点类型
const nodeTypes = {
  custom: markRaw(CustomNode),
}

const nodes = ref(initialNodes)

const edges = ref(initialEdges)

const { layout } = useLayout()

const { fitView } = useVueFlow()

async function layoutGraph(direction) {
  nodes.value = layout(nodes.value, edges.value, direction)

  nextTick(() => {
    fitView()
  })
}
</script>

<template>
  <div class="size-full">
    <VueFlow
      :edges="edges"
      :nodeTypes="nodeTypes"
      :nodes="nodes"
      :nodesDraggable="false"
      @nodesInitialized="layoutGraph('TB')"
    >
      <!-- <Background /> -->
    </VueFlow>
  </div>
</template>
