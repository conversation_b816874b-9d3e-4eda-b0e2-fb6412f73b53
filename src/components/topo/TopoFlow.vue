<script setup>
import { useVueFlow, VueFlow } from '@vue-flow/core'

import TopoView from '~/components/topo/TopoView.vue'

const nodeTypes = {
  custom: markRaw(TopoView),
}

const { fitView } = useVueFlow()

async function layoutGraph() {
  nextTick(() => {
    fitView({ maxZoom: 1 })
  })
}

const position = { x: 0, y: 0 }
</script>

<template>
  <div class="size-full">
    <VueFlow
      :nodeTypes="nodeTypes"
      :nodes="[{
        id: '1',
        position,
        type: 'custom',
      }]"
      :nodesDraggable="false"
      @nodesInitialized="layoutGraph('TB')"
    >
      <!-- <Background /> -->
    </VueFlow>
  </div>
</template>
