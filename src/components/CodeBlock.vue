<script setup lang="ts">
import { codeToHtml } from 'shiki'

type Options = Parameters<typeof codeToHtml>[1]

interface Props {
  code?: string
  maxHeight?: string
  lang?: Options['lang']
  showLineNumbers?: boolean
}

const props = defineProps<Props>()

const highlightedCode = ref('')
const { ref: codeBlockRef, refKey } = useRef<HTMLElement>()

const { isDarkMode } = useTheme()

const codeLang = computed(() => {
  return props.lang || 'text'
})

const highlightCode = async () => {
  if (!props.code) {
    return ''
  }

  try {
    const html = await codeToHtml(props.code, {
      lang: codeLang.value,
      theme: isDarkMode.value ? 'github-dark' : 'material-theme-lighter',
      transformers: [
        {
          pre(node) {
            const existingStyle = node.properties.style || ''

            const newStyles = ['height: 100%', 'overflow: auto', 'padding: 1rem']

            if (props.maxHeight) {
              newStyles.push(`max-height: ${props.maxHeight}`)
            }

            node.properties.style = `${existingStyle}${existingStyle ? '; ' : ''}${newStyles.join(';')}`

            if (props.showLineNumbers) {
              node.properties.class = `${node.properties.class || ''} line-numbers`.trim()
            }
          },
          line(node, line) {
            if (props.showLineNumbers) {
              node.properties['data-line'] = line
            }
          },
        },
      ],
    })
    highlightedCode.value = html
  }
  catch {
    // 降级处理 - 返回未高亮的代码
    highlightedCode.value = `<pre><code>${props.code}</code></pre>`
  }
}

const emit = defineEmits<{
  afterRender: [preElement: HTMLPreElement]
}>()

watchEffect(async () => {
  await highlightCode()

  if (codeBlockRef.value instanceof HTMLElement) {
    const preElement = codeBlockRef.value.querySelector('pre')

    if (preElement) {
      emit('afterRender', preElement)
    }
  }
})
</script>

<template>
  <div
    :ref="refKey"
    v-html="highlightedCode"
  />
</template>

<style>
/* 行号样式 */
.line-numbers {
  counter-reset: line;
  position: relative;
  padding-left: 3.5rem !important;
}

.line-numbers .line {
  position: relative;
}

.line-numbers .line::before {
  content: counter(line);
  counter-increment: line;
  position: absolute;
  left: -2.5rem;
  width: 1.5rem;
  color: var(--p-surface-400);
  text-align: right;
}
</style>
