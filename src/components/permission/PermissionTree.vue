<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { ChevronDownIcon, ChevronRightIcon } from 'lucide-vue-next'
import type { TreeNode } from 'primevue/treenode'

import { queryKeys } from '~/enums/query-key'
import { UserService } from '~/services-tw/org'
import type { PermissionItem } from '~/types-tw/user'

const menuItemIds = defineModel<PermissionItem['id'][]>()

const { data: permissionListData, isLoading } = useQuery({
  queryKey: queryKeys.user.permission.all(),
  queryFn: () => UserService.getPermissions(),
})

const transformNode = (item: PermissionItem): TreeNode => {
  return {
    key: String(item.id),
    label: item.name,
    children: permissionListData.value
      ?.filter((child) => child.parent_id === item.id)
      ?.map(transformNode) || [],
  }
}

const menuNodes = computed(() => {
  if (!permissionListData.value) {
    return []
  }

  // 只获取顶级权限项（没有 parent_id 的项）
  return permissionListData.value
    .filter((item) => !item.parent_id)
    .map(transformNode)
})

const handleSelectionKeysChange = (keys: PrimeVueTreeSelectionKeys | undefined) => {
  // 将 PrimeVue 的选择键值对象转换为选中的菜单 key 数组
  const selectedMenuKeys = keys
    ? Object.entries(keys).reduce<PermissionItem['id'][]>((acc, [key, val]) => {
        if (val.checked || val.partialChecked) {
          acc.push(Number(key))
        }

        return acc
      }, [])
    : []

  menuItemIds.value = selectedMenuKeys
}

const selectionKeys = computed<PrimeVueTreeSelectionKeys>(() => {
  if (!menuItemIds.value?.length || !permissionListData.value) {
    return {} as PrimeVueTreeSelectionKeys
  }

  const result = {} as PrimeVueTreeSelectionKeys

  // 创建一个 parent_id 映射，方便查找父节点
  const parentMap = new Map<number, number>()
  permissionListData.value.forEach((item) => {
    if (item.parent_id) {
      parentMap.set(item.id, item.parent_id)
    }
  })

  // 创建一个子节点映射，方便查找子节点
  const childrenMap = new Map<number, number[]>()
  permissionListData.value.forEach((item) => {
    if (item.parent_id) {
      const children = childrenMap.get(item.parent_id) || []
      children.push(item.id)
      childrenMap.set(item.parent_id, children)
    }
  })

  // 处理选中状态
  menuItemIds.value.forEach((id) => {
    result[String(id) as NavMenuItem['key']] = { checked: true }
  })

  // 处理部分选中状态
  permissionListData.value.forEach((item) => {
    if (!item.parent_id) {
      const children = childrenMap.get(item.id) || []

      if (children.length > 0) {
        const selectedChildren = children.filter((childId) =>
          menuItemIds.value?.includes(childId),
        )

        if (selectedChildren.length > 0 && selectedChildren.length < children.length) {
          result[String(item.id) as NavMenuItem['key']] = {
            checked: false,
            partialChecked: true,
          }
        }
      }
    }
  })

  return result
})

/** 计算需要展开的节点（当子节点被选中时，自动展开父节点） */
const expandedKeys = computed(() => {
  if (!menuItemIds.value?.length || !permissionListData.value) {
    return {}
  }

  const result: Record<string, boolean> = {}

  // 遍历所有选中的节点
  menuItemIds.value.forEach((id) => {
    // 查找当前节点的所有父节点
    const currentId = id
    const item = permissionListData.value?.find((item) => item.id === currentId)

    if (item?.parent_id) {
      result[String(item.parent_id)] = true
    }
  })

  return result
})
</script>

<template>
  <Tree
    class="!p-0"
    :expandedKeys="expandedKeys"
    :loading="isLoading"
    :selectionKeys="selectionKeys"
    selectionMode="checkbox"
    :value="menuNodes"
    @update:selectionKeys="handleSelectionKeysChange"
  >
    <template #nodetoggleicon="{ expanded }: {expanded: boolean}">
      <Component
        :is="expanded ? ChevronDownIcon : ChevronRightIcon"
        :size="15"
      />
    </template>
  </Tree>
</template>
