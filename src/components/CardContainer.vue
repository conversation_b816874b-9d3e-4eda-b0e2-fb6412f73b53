<script setup lang="ts">
/**
 * 卡片容器组件，用于包裹卡片内容，并提供标题和样式
 */

defineProps<{
  /** 最外层容器的类名 */
  wrapperClass?: string
  /** 卡片标题 */
  title?: string
}>()

defineSlots<{
  default: () => VNode
  title: () => VNode
}>()
</script>

<template>
  <div
    class="flex size-full flex-col overflow-auto rounded-admin-layout-content border border-solid border-divider bg-content shadow-sm"
    :class="wrapperClass"
  >
    <slot name="title">
      <CardContainerTitle
        v-if="title"
        class="pb-card-container"
      >
        {{ title }}
      </CardContainerTitle>
    </slot>

    <slot name="default" />
  </div>
</template>
