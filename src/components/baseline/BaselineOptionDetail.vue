<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/enums/query-key'

const props = defineProps<{
  optionId?: ComplianceResult['id']
}>()

const optionId = toRef(props, 'optionId')

const { data: result, isLoading } = useQuery({
  queryKey: queryKeys.security.compliance.option.result(optionId),
  queryFn: () => {
    if (optionId.value) {
      return SecurityService.getComplianceCheckOptionResult(optionId.value)
    }

    return null
  },
})
</script>

<template>
  <div>
    <div
      v-if="isLoading"
      class="p-4"
    >
      <div
        v-for="i of 2"
        :key="i"
        class="space-y-3 pb-10"
      >
        <div class="pb-1">
          <Skeleton
            height="2rem"
            width="20%"
          />
        </div>
        <Skeleton
          height="1.5rem"
          width="100%"
        />
        <Skeleton
          height="1.5rem"
          width="100%"
        />
        <Skeleton
          height="1.5rem"
          width="100%"
        />
        <Skeleton
          height="1.5rem"
          width="50%"
        />
      </div>
    </div>

    <div
      v-else
      class="space-y-6"
    >
      <div class="space-y-2">
        <strong>描述</strong>

        <div>
          {{ result?.desc }}
        </div>
      </div>

      <div class="space-y-2">
        <strong>建议</strong>

        <div>
          {{ result?.suggest }}
        </div>
      </div>

      <div class="space-y-2">
        <strong>检查命令</strong>

        <CodeBlock
          :code="result?.command"
          lang="shell"
        />
      </div>
    </div>
  </div>
</template>
