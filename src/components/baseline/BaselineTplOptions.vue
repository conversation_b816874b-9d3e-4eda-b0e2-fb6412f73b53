<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { number, object } from 'valibot'

import ProBtnDelete from '~/components/pro/btn/ProBtnDelete.vue'
import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'

interface AssetTemplateFieldsProps {
  /** 当前查看的基线模板 */
  baselineTemplate: BaselineTplListItem
}

const props = defineProps<AssetTemplateFieldsProps>()

const tplId = computed(() => props.baselineTemplate.id)
const systemId = computed(() => props.baselineTemplate.system_id)

const { tableRef, tableAction } = useTableAction()
const baselineOptions = ref<BaselineOption[]>()

const { data: systemOptionData, isLoading: isLoadingSystemOptions } = useQuery({
  queryKey: queryKeys.security.compliance.systemPolicy.options(systemId),
  queryFn: () => ConfigService.getBaselinePolicyOptions(systemId.value),
  enabled: !!systemId.value,
})

const systemOptions = computed(() => {
  const existingOptionIds = new Set(baselineOptions.value?.map((it) => it.system_option_id))

  return systemOptionData.value?.list.map((it) => ({
    label: it.name,
    value: it.id,
  })).filter((it) => !existingOptionIds.has(it.value))
})

const handleUpdateDataSource = (dataSource?: BaselineOption[]) => {
  baselineOptions.value = dataSource
}

const {
  loading,
  isFormCreate,
  formValues,
  formResolver,
  confirmBtnLabel,
  formTitleText,
  openCreateModal,
  modalVisible,
  handleClose,
  handleSubmit,
  handleModalVisibleChange,
} = useFormControl<BaselineOptionFormValues, BaselineOption>({
  resolver: object({
    system_option_id: number('请选择系统检查项'),
  }),
  btnLabel: {
    create: '添加检查项',
    update: '保存检查项',
  },
  formTitle: {
    create: '添加检查项',
    update: '编辑检查项',
  },
  fetchDetail: (it) => toRaw(it),
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      if (isFormCreate.value) {
        await ConfigService.createSystemOption(tplId.value, {
          system_option_id: states.system_option_id.value,
        })
      }
    }
  },
  onSubmitSuccess: () => {
    tableAction.value?.reload()
  },
})

const formSubmitButton = useTemplateRef<HTMLButtonElement>('form-submit')

const handleSubmitButtonClick = () => {
  formSubmitButton.value?.click()
}

const handleDelete = async (currentBaselineOption: BaselineOption) => {
  await ConfigService.deleteSystemOption(currentBaselineOption.id)

  tableAction.value?.reload()
}

const columns: ProTableColumn<BaselineOption>[] = [
  {
    header: '系统检查项',
    field: 'system_option.name',
    render: (data) => data.system_option?.name || '-',
  },
  {
    field: 'actions',
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', { class: 'table-action-group' }, [
      h(ProBtnDelete, {
        confirmMessage: `确定要删除检查项「${data.system_option?.name}」吗？`,
        deleteSuccessMessage: `检查项「${data.system_option?.name}」已删除`,
        onlyIcon: true,
        onConfirm: () => handleDelete(data),
      }),
    ]),
  },
]
</script>

<template>
  <div>
    <ProTable
      :ref="tableRef"
      :columns="columns"
      :queryKey="queryKeys.security.compliance.option.all(tplId)"
      :queryParams="{ template_baseline_id: tplId }"
      :requestFn="(queryParams) => {
        if (tplId) {
          return ConfigService.getBaselineSystemOptions(queryParams)
        }

        return null
      }"
      @update:dataSource="handleUpdateDataSource($event)"
    >
      <template #toolbarRight>
        <div class="flex-center">
          <div class="ml-auto">
            <Button
              label="添加检查项"
              @click="openCreateModal()"
            />
          </div>
        </div>
      </template>
    </ProTable>

    <Drawer
      class="!w-[650px]"
      :header="formTitleText"
      position="right"
      :visible="modalVisible"
      @update:visible="handleModalVisibleChange"
    >
      <Form
        class="h-full"
        :initialValues="formValues"
        :resolver="formResolver"
        @submit="handleSubmit"
      >
        <div class="flex h-full flex-col gap-form-field">
          <FormItem
            label="系统检查项"
            name="system_option_id"
            required
          >
            <ProSelect
              :loading="isLoadingSystemOptions"
              :options="systemOptions"
              placeholder="请选择系统检查项"
            />
          </FormItem>
        </div>

        <button
          v-show="false"
          ref="form-submit"
          type="submit"
        />
      </Form>

      <template #footer>
        <Divider />
        <FormActionGroup
          :confirmButtonProps="{
            label: confirmBtnLabel,
          }"
          :loading="loading"
          @cancel="handleClose"
          @confirm="handleSubmitButtonClick"
        />
      </template>
    </Drawer>
  </div>
</template>
