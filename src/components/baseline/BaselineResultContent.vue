<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { CheckIcon, XIcon } from 'lucide-vue-next'
import { Tag } from 'primevue'

import { queryKeys } from '~/enums/query-key'
import { ComplianceCheckResultStatus, complianceCheckResultStatusConfig, threatLevelConfig } from '~/enums/security'

const props = defineProps<{
  complianceId?: ComplianceCheck['id']
}>()

const complianceId = toRef(props, 'complianceId')

const selectedOption = ref<ComplianceResult>()
const selectedOptionId = computed(() => selectedOption.value?.system_option.id)

const { data: detail } = useQuery({
  queryKey: queryKeys.security.compliance.detail(complianceId),
  queryFn: () => {
    if (complianceId.value) {
      return SecurityService.getComplianceDetail(complianceId.value)
    }

    return null
  },
})

const columns = computed<ProTableColumn<ComplianceResult>[]>(() => [
  {
    field: 'system_option',
    header: '系统检查项',
    render: (data) => data.system_option.name,
  },
  {
    field: 'level',
    header: '级别',
    render: (data) => h(Tag, {
      severity: threatLevelConfig[data.system_option.level].severity,
      value: threatLevelConfig[data.system_option.level].label,
    }),
  },
  {
    field: 'status',
    header: '状态',
    render: (data) => h(Tag, {
      severity: complianceCheckResultStatusConfig[data.status].severity,
    }, () => [
      h(data.status === ComplianceCheckResultStatus.Pass ? CheckIcon : XIcon, {
        size: 15,
      }),
      complianceCheckResultStatusConfig[data.status].label,
    ]),
  },
])
</script>

<template>
  <div>
    <div class="space-y-5">
      <p>
        <strong>检查服务器：</strong>{{ detail?.server?.name || '-' }}
      </p>

      <p>
        <strong>基线模板：</strong>{{ detail?.template_baseline.name }}
      </p>

      <p>
        <strong>模板系统类型：</strong>{{ detail?.template_baseline.system_name }}
      </p>

      <p>
        <strong>检查时间：</strong>{{ detail?.create_time }}
      </p>
    </div>

    <Divider />

    <ProSplitter
      :left="{ size: 60, minSize: 40 }"
      :right="{ minSize: 30 }"
    >
      <template #left>
        <ProTable
          v-model:selection="selectedOption"
          :columns="columns"
          metaKeySelection
          :queryKey="queryKeys.security.compliance.result(complianceId)"
          :requestFn="() => {
            if (complianceId) {
              return SecurityService.getComplianceCheckResultList(complianceId)
            }

            return null
          }"
          selectionMode="single"
          @update:dataSource="(dataSource) => {
            if (Array.isArray(dataSource) && dataSource.length > 0) {
              selectedOption = dataSource.at(0)
            }
          }"
        />
      </template>

      <template #right>
        <BaselineOptionDetail :optionId="selectedOptionId" />
      </template>
    </ProSplitter>
  </div>
</template>
