<script setup lang="ts">
import { number, object } from 'valibot'

import ProBtnDelete from '~/components/pro/btn/ProBtnDelete.vue'
import ProBtnDetail from '~/components/pro/btn/ProBtnDetail.vue'
import ProBtnEdit from '~/components/pro/btn/ProBtnEdit.vue'
import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'
import { threatLevelConfig } from '~/enums/security'

interface Props {
  /** 当前查看的基线策略 */
  baselinePolicy: BaselineSystem
}

const props = defineProps<Props>()

const policyId = computed(() => props.baselinePolicy.id)

const { tableRef, tableAction } = useTableAction()

const {
  loading,
  isFormCreate,
  isFormUpdate,
  formValues,
  forceUpdateKey,
  formResolver,
  updatingItem,
  confirmBtnLabel,
  formTitleText,
  openCreateModal,
  openUpdateModal,
  modalVisible,
  handleClose,
  handleSubmit,
  handleModalVisibleChange,
} = useFormControl<BaselinePolicyFormValues, BaselinePolicyOptionListItem>({
  resolver: object({
    name: schemaNotEmptyString('请输入检查项名称'),
    level: number('请选择检查项级别'),
    desc: schemaNotEmptyString('请输入检查项描述'),
    suggest: schemaNotEmptyString('请输入检查项建议'),
    command: schemaNotEmptyString('请输入检查项命令'),
  }),
  btnLabel: {
    create: '添加检查项',
    update: '保存检查项',
  },
  formTitle: {
    create: '添加检查项',
    update: '编辑检查项',
  },
  fetchDetail: (it) => ConfigService.getBaselinePolicyOption(it.id),
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const payload: BaselinePolicyFormValues = {
        system_id: policyId.value,
        name: states.name.value,
        level: states.level.value,
        suggest: states.suggest.value,
        desc: states.desc.value,
        command: states.command.value,
      }

      if (isFormCreate.value) {
        await ConfigService.createBaselinePolicyOption(payload)
      }
      else if (isFormUpdate.value) {
        if (updatingItem.value?.id) {
          await ConfigService.updateBaselinePolicyOption(updatingItem.value.id, payload)
        }
      }
    }
  },
  onSubmitSuccess: () => {
    tableAction.value?.reload()
  },
})

const formSubmitButton = useTemplateRef<HTMLButtonElement>('form-submit')

const handleSubmitButtonClick = () => {
  formSubmitButton.value?.click()
}

const activeRecord = ref<BaselinePolicyOptionListItem>()

const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    activeRecord.value = undefined
  }
}

const columns: ProTableColumn<BaselinePolicyOptionListItem>[] = [
  {
    field: 'name',
    header: '系统检查项',
  },
  {
    field: 'level',
    header: '对应级别',
    render: (data) => threatLevelConfig[data.level].label,
  },
  {
    field: 'action',
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', {
      class: 'table-action-group',
    }, [
      data.is_update
        ? h(ProBtnEdit, {
            onlyIcon: true,
            onClick: () => openUpdateModal(data),
          })
        : h(ProBtnDetail, {
            onClick: () => {
              activeRecord.value = data
            },
          }),
      data.is_update && h(ProBtnDelete, {
        onlyIcon: true,
        confirmMessage: `确定要删除检查项「${data.name}」吗？`,
        deleteSuccessMessage: `检查项「${data.name}」已删除`,
        onConfirm: async () => {
          await ConfigService.deleteBaselinePolicyOption(data.id)

          tableAction.value?.reload()
        },
      }),
    ]),
  },
]
</script>

<template>
  <div>
    <ProTable
      :ref="tableRef"
      :columns="columns"
      :queryKey="queryKeys.security.compliance.systemPolicy.options(policyId)"
      :queryParams="{
        policyId,
      }"
      :requestFn="(queryParams) => {
        const { policyId, ...restQueryParams } = queryParams

        if (typeof policyId === 'number') {
          return ConfigService.getBaselinePolicyOptions(policyId, restQueryParams)
        }

        return null
      }"
    >
      <template #toolbarRight>
        <div class="flex-center">
          <div class="ml-auto">
            <Button
              label="添加检查项"
              @click="openCreateModal()"
            />
          </div>
        </div>
      </template>
    </ProTable>

    <Dialog
      class="dialog-half"
      dismissableMask
      header="检查项详情"
      modal
      :visible="!!activeRecord"
      @update:visible="handleVisibleChange"
    >
      <BaselineOptionDetail
        :optionId="activeRecord?.id"
      />
    </Dialog>

    <Drawer
      class="!w-[650px]"
      :header="formTitleText"
      position="right"
      :visible="modalVisible"
      @update:visible="handleModalVisibleChange"
    >
      <Form
        :key="forceUpdateKey"
        class="h-full"
        :initialValues="formValues"
        :resolver="formResolver"
        @submit="handleSubmit"
      >
        <div class="space-y-5">
          <div class="grid grid-cols-2 gap-form-field">
            <FormItem
              v-slot="{ id }"
              label="检查项名称"
              name="name"
              required
            >
              <InputText
                :id="id"
                v-model="formValues.name"
              />
            </FormItem>

            <FormItem
              v-slot="{ id }"
              label="检查项级别"
              name="level"
              required
            >
              <ProSelect
                v-model="formValues.level"
                :labelId="id"
                :options="Object.values(threatLevelConfig).map(item => ({ label: item.label, value: item.value }))"
              />
            </FormItem>
          </div>

          <div class="grid grid-cols-2 gap-form-field">
            <FormItem
              v-slot="{ id }"
              label="检查项描述"
              name="desc"
              required
            >
              <Textarea
                :id="id"
                v-model="formValues.desc"
              />
            </FormItem>

            <FormItem
              v-slot="{ id }"
              label="检查项修复建议"
              name="suggest"
              required
            >
              <Textarea
                :id="id"
                v-model="formValues.suggest"
              />
            </FormItem>

            <FormItem
              label="检查项修复命令"
              name="command"
              required
            >
              <template #default="{ id }">
                <Textarea
                  :id="id"
                  v-model="formValues.command"
                />
              </template>

              <template #help>
                <CodeBlock
                  code="# 修复命令示例，必须包含以下两条命令
echo 0 # 0 表示不通过
echo 1 # 1 表示通过"
                  lang="shell"
                />
              </template>
            </FormItem>
          </div>
        </div>

        <button
          v-show="false"
          ref="form-submit"
          type="submit"
        />
      </Form>

      <template #footer>
        <Divider />

        <FormActionGroup
          :confirmButtonProps="{
            label: confirmBtnLabel,
          }"
          :loading="loading"
          @cancel="handleClose"
          @confirm="handleSubmitButtonClick"
        />
      </template>
    </Drawer>
  </div>
</template>
