<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/enums/query-key'

interface Props {
  selectItem?: BaselineSystem
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:selectItem': [item: BaselineSystem | undefined]
}>()

const selectedBaselinePolicy = computed({
  get: () => props.selectItem,
  set: (val) => {
    emit('update:selectItem', val)
  },
})

const { data: baselinePolicyList, isLoading } = useQuery({
  queryKey: queryKeys.security.compliance.systemPolicy.all(),
  queryFn: () => ConfigService.getBaselineSystemList(),
})

watch(baselinePolicyList, (newList) => {
  selectedBaselinePolicy.value = newList?.list.at(0)
}, { immediate: true })

const handleSelect = (item: BaselineSystem) => {
  selectedBaselinePolicy.value = item
}
</script>

<template>
  <div>
    <ul class="space-y-1">
      <template v-if="isLoading">
        <li
          v-for="idx of 4"
          :key="idx"
          class="py-0.5"
        >
          <Skeleton
            height="25px"
            width="100%"
          />
        </li>
      </template>

      <template v-else>
        <li
          v-for="it of baselinePolicyList?.list"
          :key="it.id"
          class="group/tpl-li h-[34px] cursor-pointer rounded-lg px-3 py-1 transition-all duration-200 flex-center"
          :class="[selectedBaselinePolicy?.id === it.id ? 'bg-emphasis' : 'hover:bg-emphasis']"
          @click="handleSelect(it)"
        >
          <span class="flex-1 truncate">{{ it.name }}</span>
        </li>
      </template>
    </ul>
  </div>
</template>
