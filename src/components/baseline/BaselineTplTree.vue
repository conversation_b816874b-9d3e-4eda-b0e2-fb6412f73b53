<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { number, object } from 'valibot'

import { ProMenuActionType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'

interface Props {
  selectItem?: BaselineTplListItem
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:selectItem': [item: BaselineTplListItem | undefined]
}>()

const selectedBaselineTemplate = computed({
  get: () => props.selectItem,
  set: (val) => {
    emit('update:selectItem', val)
  },
})

const { data: baselineTplList, refetch: refetchBaselineTpl } = useQuery({
  queryKey: queryKeys.security.compliance.templates(),
  queryFn: () => ConfigService.getBaselineTplList(),
})

const {
  loading,
  isFormCreate,
  isFormUpdate,
  formValues,
  formResolver,
  modalVisible,
  openCreateModal,
  openUpdateModal,
  confirmBtnLabel,
  handleClose,
  handleSubmit,
  formTitleText,
  updatingItem: updatingAssetTemplate,
  handleModalVisibleChange,
} = useFormControl<BaselineFormValues, BaselineTplListItem>({
  initialValues: {
  },
  resolver: object({
    name: schemaNotEmptyString('请填写模板名称'),
    system_id: number('请选择系统'),
  }),
  btnLabel: {
    create: '添加模板',
    update: '保存',
  },
  formTitle: {
    create: '添加基线模板',
    update: '编辑基线模板',
  },
  fetchDetail: (updatingItem) => toRaw(updatingItem),
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      if (isFormCreate.value) {
        await ConfigService.createBaselineTpl({
          name: states.name.value,
          system_id: states.system_id.value,
        })
      }
      else if (isFormUpdate.value) {
        const updateItemId = updatingAssetTemplate.value?.id

        if (updateItemId) {
          await ConfigService.updateBaselineTpl(updateItemId, formValues.value)
        }
      }
    }
  },
  onSubmitSuccess: () => {
    refetchBaselineTpl()
  },
})

watch(baselineTplList, (newList) => {
  selectedBaselineTemplate.value = newList?.list.at(0)
}, { immediate: true })

const { dualMenuRef, dualMenu } = useDualMenu()

const { $confirm } = useNuxtApp()

const selectedItem = ref<BaselineTplListItem>()
const deleteItem = ref<BaselineTplListItem>()

const handleOpenMenu = (item: BaselineTplListItem, ev: MouseEvent) => {
  selectedItem.value = item
  dualMenu.value?.showMenu(ev)
}

const handleSelect = (item: BaselineTplListItem) => {
  selectedBaselineTemplate.value = item
}

const optionMenus: ProMenuItem[] = [
  {
    label: '编辑',
    actionType: ProMenuActionType.Edit,
    command: () => {
      if (selectedItem.value) {
        openUpdateModal(selectedItem.value)
      }
    },
  },
  {
    label: '删除',
    actionType: ProMenuActionType.Delete,
    command: () => {
      deleteItem.value = selectedItem.value

      $confirm.dialog({
        message: `确定要删除资产模板「${deleteItem.value?.name}」吗？`,
        accept: async () => {
          if (deleteItem.value?.id) {
            await ConfigService.deleteBaselineTpl(deleteItem.value.id)
            refetchBaselineTpl()
          }

          deleteItem.value = undefined
        },
        reject: () => {
          deleteItem.value = undefined
        },
      })
    },
  },
]

const { data: baselineSystemList } = useQuery({
  queryKey: queryKeys.security.compliance.systemPolicy.all(),
  queryFn: () => ConfigService.getBaselineSystemList(),
})

const osOptions = computed(() => baselineSystemList.value?.list.map((it) => ({
  label: it.name,
  value: it.id,
})))
</script>

<template>
  <div>
    <ul class="space-y-1">
      <template v-if="loading">
        <li
          v-for="idx of 4"
          :key="idx"
          class="py-0.5"
        >
          <Skeleton
            height="25px"
            width="100%"
          />
        </li>
      </template>

      <template v-else>
        <li
          v-for="it of baselineTplList?.list"
          :key="it.id"
          class="group/tpl-li h-[34px] cursor-pointer rounded-lg px-3 py-1 transition-all duration-200 flex-center"
          :class="[selectedBaselineTemplate?.id === it.id ? 'bg-emphasis' : 'hover:bg-emphasis']"
          @click="handleSelect(it)"
          @contextmenu="handleOpenMenu(it, $event)"
        >
          <span class="flex-1 truncate">{{ it.name }}</span>

          <span class="ml-auto hidden shrink-0 group-hover/tpl-li:inline-flex">
            <ProBtnMore
              :dt="{
                sm: {
                  padding: {
                    x: '0.1rem',
                    y: '0.1rem',
                  },
                },
              }"
              onlyIcon
              size="small"
              variant="text"
              @click.stop="handleOpenMenu(it, $event)"
            />
          </span>
        </li>
      </template>
    </ul>

    <DualMenu
      :ref="dualMenuRef"
      :menuItems="optionMenus"
    />

    <Divider />

    <Button
      fluid
      label="添加基线模板"
      severity="secondary"
      @click="openCreateModal()"
    />

    <ProDialogForm
      :formActionGroupProps="{
        confirmButtonProps: {
          label: confirmBtnLabel,
        },
      }"
      :initialValues="formValues"
      :loading="loading"
      :resolver="formResolver"
      :title="formTitleText"
      :visible="modalVisible"
      @cancel="handleClose"
      @submit="handleSubmit"
      @update:visible="handleModalVisibleChange"
    >
      <FormItem
        label="模板名称"
        name="name"
        required
      >
        <template #default="{ id }">
          <InputText
            :id="id"
            v-model.trim="formValues.name"
            placeholder="这个模板将用于什么？"
          />
        </template>

        <template #helpTip>
          模板名称将帮助您快速识别用途
        </template>
      </FormItem>

      <FormItem
        label="操作系统"
        name="system_id"
        required
      >
        <template #default>
          <ProSelect
            v-model="formValues.system_id"
            :disabled="isFormUpdate"
            :options="osOptions"
            placeholder="选择基线检查系统"
          />
        </template>
      </FormItem>
    </ProDialogForm>
  </div>
</template>
