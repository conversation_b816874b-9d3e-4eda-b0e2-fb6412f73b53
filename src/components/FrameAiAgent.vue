<script setup lang="ts">
const props = defineProps<{
  routePath?: string
}>()

const runtimeConfig = useRuntimeConfig()
const { aiAgentHost } = runtimeConfig.public

const src = `${aiAgentHost}?redirect=${props.routePath || ''}`
</script>

<template>
  <iframe
    v-if="aiAgentHost"
    class="size-full"
    :src="src"
  />

  <div
    v-else
    class="size-full justify-center flex-center"
  >
    <div class="text-2xl">
      请配置 AI 助手地址
    </div>
  </div>
</template>
