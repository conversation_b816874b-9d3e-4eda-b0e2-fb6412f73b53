<script setup lang="ts">
import { ChevronDownIcon, ChevronRightIcon } from 'lucide-vue-next'

import type { DepartmentTreeNode } from '~/types-tw/user'

const userStore = useAdminDepartmentStore()
const { activeDepartmentId, departmentTree, expandedKeys } = storeToRefs(userStore)

const handleSelect = (nodeInfo: Pick<DepartmentTreeNode, 'key' | 'label' | 'parent' | 'children'>) => {
  userStore.setActiveDepartment({
    id: Number(nodeInfo.key),
    name: nodeInfo.label ?? '',
  })
}

const handleToggle = (ev: { key: DepartmentTreeNode['key'], expanded: boolean }) => {
  userStore.setExpandedKeys({
    ...expandedKeys.value,
    [ev.key]: ev.expanded,
  })
}
</script>

<template>
  <div>
    <Tree
      class="!p-0"
      :expandedKeys="expandedKeys"
      :pt="{
        nodeContent: {
          class: 'group/node-content h-full h-[30px] !py-0',
        },
        nodeLabel: {
          class: 'size-full min-w-0',
        },
      }"
      :selectionKeys="{ [`${activeDepartmentId}`]: true }"
      selectionMode="single"
      :value="departmentTree"
      @nodeSelect="handleSelect"
      @nodeToggle="handleToggle"
    >
      <template #default="slotProps: { node: UserGroupTreeNode }">
        <span
          class="size-full gap-2 flex-center"
        >
          <span class="h-[31px] flex-1 truncate leading-[31px]">{{ slotProps.node.label }}</span>
        </span>
      </template>

      <template #nodetoggleicon="{ expanded }: { expanded: boolean }">
        <Component
          :is="expanded ? ChevronDownIcon : ChevronRightIcon"
          :size="15"
        />
      </template>
    </Tree>
  </div>
</template>
