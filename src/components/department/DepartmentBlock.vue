<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { get } from 'lodash-es'

const store = useAdminDepartmentStore()
const fetchDepartments = store.fetchDepartments
const { departmentTree } = storeToRefs(store)

const { themeMode } = useTheme()

const contentBg = computed(() => {
  const v = $dt('content.background').value

  return get(v, `${themeMode.value}.value`)
})

// 滚动状态控制
const scrollContainerRef = ref<HTMLElement | null>(null)
const showTopGradient = ref(false)
const showBottomGradient = ref(false)

// 检查滚动位置并更新渐变显示状态
const updateScrollGradients = () => {
  if (scrollContainerRef.value) {
    const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.value

    // 当滚动位置不在顶部时显示顶部渐变
    showTopGradient.value = scrollTop > 0

    // 当滚动位置不在底部时显示底部渐变
    showBottomGradient.value = scrollTop + clientHeight < scrollHeight - 1 // 添加 1px 容差
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 获取部门数据
  fetchDepartments()

  // 设置滚动监听
  if (scrollContainerRef.value) {
    scrollContainerRef.value.addEventListener('scroll', updateScrollGradients)
    // 初始化渐变状态
    nextTick(updateScrollGradients)
  }
})

onBeforeUnmount(() => {
  if (scrollContainerRef.value) {
    scrollContainerRef.value.removeEventListener('scroll', updateScrollGradients)
  }
})

// 当树数据更新后，重新检查滚动状态
watch(departmentTree, () => {
  nextTick(updateScrollGradients)
}, { deep: true })
</script>

<template>
  <div class="relative flex h-full flex-col gap-4">
    <div class="relative flex-1 overflow-hidden">
      <!-- 顶部渐变提示 -->
      <div
        v-show="showTopGradient"
        class="pointer-events-none absolute -inset-x-2 top-0 z-10 h-6"
        :style="`background: radial-gradient(ellipse at center top, ${contentBg}66, ${contentBg}00);`"
      />

      <!-- 滚动容器 -->
      <div
        ref="scrollContainerRef"
        class="size-full overflow-auto pr-in-splitter"
      >
        <DepartmentTree />
      </div>

      <!-- 底部渐变提示 -->
      <div
        v-show="showBottomGradient"
        class="pointer-events-none absolute -inset-x-2 bottom-0 z-10 h-6"
        :style="`background: radial-gradient(ellipse at center bottom, ${contentBg}66, ${contentBg}00);`"
      />
    </div>
  </div>
</template>
