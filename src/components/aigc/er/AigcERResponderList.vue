<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus'

import { cloneDeep } from 'lodash-es'
import { GripVerticalIcon, PlusIcon, TrashIcon } from 'lucide-vue-next'
import { nanoid } from 'nanoid'

import type { ERReportFormValues } from '~/types/tw-aigc'

const { $confirm } = useNuxtApp()

const options = defineModel<ERReportFormValues['responders']>('modelValue', {
  default: () => [],
})

const defaultOperation: ERReportFormValues['responders'][number] = {
  name: '',
  date: '',
  location: '',
}

const newOperation = ref<ERReportFormValues['responders'][number]>(cloneDeep(defaultOperation))

const isDragging = ref(false)

const handleAddOption = () => {
  if (newOperation.value) {
    options.value = [
      {
        name: newOperation.value.name,
        date: newOperation.value.date,
        location: newOperation.value.location,
      },
      ...options.value,
    ]

    newOperation.value = defaultOperation
  }
}

const handleRemoveOption = (index: number) => {
  if (Array.isArray(options.value)) {
    const optionToDelete = options.value[index]

    $confirm.dialog({
      message: `确定要删除操作「${optionToDelete.name}」吗？`,
      accept: () => {
        options.value = options.value.filter((_, idx) => idx !== index)
      },
    })
  }
}

const handlerClass = nanoid(4)

const addable = computed(() => {
  const isEmpty = newOperation.value.name.trim().length === 0

  return !isEmpty
})
</script>

<template>
  <div class="flex flex-col gap-2">
    <div>
      <div class="grid grid-cols-[1.5rem_1fr_1fr_1fr_2rem] items-center gap-2 pb-2">
        <div />

        <div class="text-sm text-secondary">
          姓名
        </div>

        <div class="text-sm text-secondary">
          日期
        </div>

        <div class="text-sm text-secondary">
          地点
        </div>
      </div>

      <div class="grid grid-cols-[1.5rem_1fr_1fr_1fr_2rem] items-center gap-2">
        <div />

        <InputText
          v-model="newOperation.name"
          fluid
          placeholder="输入人员姓名"
          size="small"
        />

        <ProDatePicker
          v-model="newOperation.date"
          fluid
          placeholder="选择日期"
          size="small"
        />

        <InputText
          v-model="newOperation.location"
          fluid
          placeholder="输入人员地点"
          size="small"
        />

        <div>
          <ProBtn
            v-tooltip.top="addable ? '添加新人员' : '完成输入以添加'"
            :disabled="!addable"
            severity="secondary"
            size="small"
            variant="outlined"
            @click="handleAddOption()"
          >
            <template #icon>
              <PlusIcon :size="15" />
            </template>
          </ProBtn>
        </div>
      </div>
    </div>

    <VueDraggable
      v-model="options"
      :animation="150"
      class="flex flex-col gap-2"
      :handle="`.${handlerClass}`"
      itemid="name"
      @end="isDragging = false"
      @start="isDragging = true"
    >
      <div
        v-for="(item, idx) of options"
        :key="item.name"
        class="grid grid-cols-[1.5rem_1fr_1fr_1fr_2rem] items-center gap-2"
      >
        <div
          class="cursor-grab justify-center rounded p-1 flex-center hover:bg-emphasis"
          :class="`${handlerClass} ${isDragging ? 'opacity-0' : ''}`"
        >
          <GripVerticalIcon
            class="opacity-50"
            :size="15"
          />
        </div>

        <InputText
          v-model="options[idx].name"
          fluid
          size="small"
        />

        <ProDatePicker
          v-model="options[idx].date"
          fluid
          size="small"
        />

        <InputText
          v-model="options[idx].location"
          fluid
          size="small"
        />

        <div>
          <ProBtn
            :class="`${isDragging ? 'opacity-0' : ''}`"
            severity="danger"
            size="small"
            variant="text"
            @click="handleRemoveOption(idx)"
          >
            <template #icon>
              <TrashIcon :size="15" />
            </template>
          </ProBtn>
        </div>
      </div>
    </VueDraggable>
  </div>
</template>
