<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus'

import { cloneDeep } from 'lodash-es'
import { GripVerticalIcon, PlusIcon, TrashIcon } from 'lucide-vue-next'
import { nanoid } from 'nanoid'

import type { ERReportFormValues } from '~/types/tw-aigc'

const { $confirm } = useNuxtApp()

const options = defineModel<ERReportFormValues['attackTimeline']>('modelValue', {
  default: () => [],
})

const defaultOperation: ERReportFormValues['attackTimeline'][number] = {
  time: '',
  ip: '',
  trace: '',
}

const newOperation = ref<ERReportFormValues['attackTimeline'][number]>(cloneDeep(defaultOperation))

const isDragging = ref(false)

const handleAddOption = () => {
  if (newOperation.value) {
    options.value = [
      {
        time: newOperation.value.time,
        ip: newOperation.value.ip,
        trace: newOperation.value.trace,
      },
      ...options.value,
    ]

    newOperation.value = defaultOperation
  }
}

const handleRemoveOption = (index: number) => {
  if (Array.isArray(options.value)) {
    const optionToDelete = options.value[index]

    $confirm.dialog({
      message: `确定要删除操作「${optionToDelete.time}」吗？`,
      accept: () => {
        options.value = options.value.filter((_, idx) => idx !== index)
      },
    })
  }
}

const handlerClass = nanoid(4)

const addable = computed(() => {
  const isEmpty = newOperation.value.ip.trim().length === 0

  return !isEmpty
})
</script>

<template>
  <div class="flex flex-col gap-2">
    <div>
      <div class="grid grid-cols-[1.5rem_1fr_1fr_1fr_2rem] items-center gap-2 pb-2">
        <div />

        <div class="text-sm text-secondary">
          日期
        </div>

        <div class="text-sm text-secondary">
          IP 地址
        </div>

        <div class="text-sm text-secondary">
          黑客痕迹
        </div>
      </div>

      <div class="grid grid-cols-[1.5rem_1fr_1fr_1fr_2rem] items-center gap-2">
        <div />

        <ProDatePicker
          v-model="newOperation.time"
          fluid
          placeholder="选择日期"
          size="small"
        />

        <InputText
          v-model="newOperation.ip"
          fluid
          placeholder="输入 IP 地址"
          size="small"
        />

        <InputText
          v-model="newOperation.trace"
          fluid
          placeholder="输入黑客痕迹"
          size="small"
        />

        <div>
          <ProBtn
            v-tooltip.top="addable ? '完成添加' : '完成输入以添加'"
            :disabled="!addable"
            severity="secondary"
            size="small"
            variant="outlined"
            @click="handleAddOption()"
          >
            <template #icon>
              <PlusIcon :size="15" />
            </template>
          </ProBtn>
        </div>
      </div>
    </div>

    <VueDraggable
      v-model="options"
      :animation="150"
      class="flex flex-col gap-2"
      :handle="`.${handlerClass}`"
      itemid="name"
      @end="isDragging = false"
      @start="isDragging = true"
    >
      <div
        v-for="(item, idx) of options"
        :key="item.time"
        class="grid grid-cols-[1.5rem_1fr_1fr_1fr_2rem] items-center gap-2"
      >
        <div
          class="cursor-grab justify-center rounded p-1 flex-center hover:bg-emphasis"
          :class="`${handlerClass} ${isDragging ? 'opacity-0' : ''}`"
        >
          <GripVerticalIcon
            class="opacity-50"
            :size="15"
          />
        </div>

        <ProDatePicker
          v-model="options[idx].time"
          fluid
          size="small"
        />

        <InputText
          v-model="options[idx].ip"
          fluid
          size="small"
        />

        <InputText
          v-model="options[idx].trace"
          fluid
          size="small"
        />

        <div>
          <ProBtn
            :class="`${isDragging ? 'opacity-0' : ''}`"
            severity="danger"
            size="small"
            variant="text"
            @click="handleRemoveOption(idx)"
          >
            <template #icon>
              <TrashIcon :size="15" />
            </template>
          </ProBtn>
        </div>
      </div>
    </VueDraggable>
  </div>
</template>
