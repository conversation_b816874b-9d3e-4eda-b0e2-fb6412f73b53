<script setup lang="ts">
import { CopyIcon, MessageSquareIcon, MessageSquarePlusIcon, MessageSquareShareIcon, PlusIcon } from 'lucide-vue-next'

import { ProMenuActionType } from '~/enums/pro'
import type { ChatHistory } from '~/types/tw-aigc'

const props = defineProps<{
  histories: ChatHistory[]
  currentChatId: string
}>()

const emits = defineEmits<{
  selectChat: [historyId: string]
  deleteChat: [historyId: string]
  createNewChat: []
  copyChat: [historyId: string]
  renameChat: [historyId: string, newTitle: string]
}>()

const { $confirm } = useNuxtApp()

const selectChatHistory = (historyId: string) => {
  emits('selectChat', historyId)
}

const deleteChatHistory = (historyId: string) => {
  $confirm.dialog({
    header: '删除聊天',
    message: '您确定要删除这个聊天吗？此操作无法撤销。',
    accept: () => {
      emits('deleteChat', historyId)
    },
  })
}

const createNewChat = () => {
  emits('createNewChat')
}

const copyChatHistory = (historyId: string) => {
  emits('copyChat', historyId)
}

// 添加编辑状态和当前编辑的历史记录 ID
const editingHistoryId = ref<string | null>(null)
const editingTitle = ref('')

// 开始编辑历史记录标题
const startRenaming = (historyId: string) => {
  const history = props.histories.find((h: ChatHistory) => h.id === historyId)

  if (!history) {
    return
  }

  editingHistoryId.value = historyId
  editingTitle.value = history.previewMessage

  // 使用nextTick确保DOM更新后再聚焦输入框
  nextTick(() => {
    const inputEl = document.getElementById(`edit-title-${historyId}`)

    if (inputEl instanceof HTMLInputElement) {
      inputEl.focus()
      inputEl.select()
    }
  })
}

// 保存重命名
const saveRenaming = () => {
  if (editingHistoryId.value && editingTitle.value.trim()) {
    emits('renameChat', editingHistoryId.value, editingTitle.value.trim())
    editingHistoryId.value = null
  }
}

// 取消重命名
const cancelRenaming = () => {
  editingHistoryId.value = null
}

// 处理输入框按键事件
const handleTitleKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    e.preventDefault()
    saveRenaming()
  }
  else if (e.key === 'Escape') {
    e.preventDefault()
    cancelRenaming()
  }
}

const contextMenuHistory = ref<ChatHistory>()

const { dualMenuRef, dualMenu } = useDualMenu()

const menuOptions: ProMenuItem[] = [
  {
    label: '重命名',
    actionType: ProMenuActionType.Edit,
    command: async () => {
      if (contextMenuHistory.value) {
        startRenaming(contextMenuHistory.value.id)
      }
    },
  },
  {
    label: '复制',
    itemIcon: CopyIcon,
    command: () => {
      if (contextMenuHistory.value) {
        copyChatHistory(contextMenuHistory.value.id)
      }
    },
  },
  {
    label: '分享',
    itemIcon: MessageSquareShareIcon,
  },
  {
    label: '删除',
    actionType: ProMenuActionType.Delete,
    command: () => {
      if (contextMenuHistory.value) {
        deleteChatHistory(contextMenuHistory.value.id)
      }
    },
  },
]

function handleOpenMenu(ev: MouseEvent, history: ChatHistory) {
  ev.stopPropagation()
  dualMenu.value?.showMenu(ev)
  contextMenuHistory.value = history
}
</script>

<template>
  <div class="flex size-full flex-col">
    <div class="h-12 gap-2 border-b border-divider px-4 pr-2 flex-center">
      <h2 class="text-base font-medium">
        聊天记录
      </h2>

      <Button
        v-tooltip="{ value: '新建对话', position: 'right' }"
        class="ml-auto"
        severity="contrast"
        size="small"
        text
        @click="createNewChat()"
      >
        <template #icon>
          <MessageSquarePlusIcon :size="15" />
        </template>
      </Button>
    </div>

    <div class="flex-1 overflow-y-auto">
      <div
        v-if="histories.length === 0"
        class="h-full flex-col justify-center p-4 text-center flex-center text-secondary"
      >
        <MessageSquareIcon
          class="mb-3"
          :size="26"
        />

        <p>暂无聊天记录</p>

        <p class="mt-1 text-sm">
          开始新对话以创建聊天记录
        </p>
      </div>

      <div
        v-else
        class="space-y-2 px-2 py-3"
      >
        <div
          v-for="history of histories"
          :key="history.id"
          class="group/node-content group cursor-pointer rounded-lg px-2.5 py-2 hover:bg-emphasis"
          :class="{
            'bg-emphasis': history.id === currentChatId,
          }"
          @click="selectChatHistory(history.id)"
          @contextmenu.stop.prevent="handleOpenMenu($event, history)"
        >
          <div class="gap-2 flex-center">
            <!-- 编辑模式时显示输入框 -->
            <div
              v-if="editingHistoryId === history.id"
              class="flex-1"
              @click.stop
            >
              <InputText
                :id="`edit-title-${history.id}`"
                v-model="editingTitle"
                class="w-full !p-1 !text-sm"
                placeholder="输入新标题"
                @blur="saveRenaming"
                @keydown="handleTitleKeydown"
              />
            </div>
            <!-- 非编辑模式时显示预览信息 -->
            <div
              v-else
              class="truncate"
              :title="history.previewMessage"
            >
              {{ history.previewMessage }}
            </div>

            <div class="ml-auto hidden shrink-0 group-hover/node-content:inline-flex">
              <ProBtnMore
                :dt="{
                  sm: {
                    padding: {
                      x: '0.1rem',
                      y: '0.1rem',
                    },
                  },
                }"
                onlyIcon
                size="small"
                variant="text"
                @click.stop="handleOpenMenu($event, history)"
              />
            </div>
          </div>

          <div class="mt-1 justify-between gap-1 text-xs flex-center text-secondary">
            <span>{{ history.messages.length }} 条消息</span>
          </div>
        </div>
      </div>
    </div>

    <div class="p-card-container">
      <Button
        fluid
        label="新对话"
        outlined
        @click="createNewChat()"
      >
        <template #icon>
          <PlusIcon :size="16" />
        </template>
      </Button>
    </div>

    <DualMenu
      :ref="dualMenuRef"
      :menuItems="menuOptions"
    />
  </div>
</template>
