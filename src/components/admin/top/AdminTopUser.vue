<script setup lang="ts">
import UserDropdownMenu from '~/components/layout/UserDropdownMenu.vue'
</script>

<template>
  <UserDropdownMenu>
    <template #default="{ user }">
      <div
        class="inline-block aspect-square overflow-hidden rounded-lg border border-divider bg-content p-0.5"
      >
        <div class="gap-3 whitespace-nowrap flex-center">
          <UserAvatar
            :avatar="user?.avatar"
            class="overflow-hidden"
            :username="user?.username"
          />
        </div>
      </div>
    </template>
  </UserDropdownMenu>
</template>
