<script setup lang="ts">
import { AlignJustifyIcon } from 'lucide-vue-next'

const store = useAdminNavMenuStore()
const { navMenuItems } = storeToRefs(store)

const { isNavMenuItemActive } = useNavMenu()
</script>

<template>
  <Menubar
    :model="navMenuItems"
    :pt="{
      root: {
        class: '!border-none !p-0',
      },
    }"
  >
    <template #item="{ item: it }: { item: NavMenuItem, }">
      <NuxtLink
        v-if="it.route && !it.group"
        v-slot="{ href, navigate }"
        custom
        :to="it.route"
      >
        <AdminTopMenuItem
          :active="isNavMenuItemActive(it)"
          :hasChildren="!!it.items"
          :href="href"
          :label="it.label"
          :labelIcon="getRouteIcon(it.key)"
          :navigate="navigate"
        />
      </NuxtLink>

      <AdminTopMenuItem
        v-else
        :active="isNavMenuItemActive(it)"
        :hasChildren="!!it.items"
        :label="it.label"
        :labelIcon="getRouteIcon(it.key)"
      />
    </template>

    <template #buttonicon>
      <AlignJustifyIcon :size="15" />
    </template>
  </Menubar>
</template>
