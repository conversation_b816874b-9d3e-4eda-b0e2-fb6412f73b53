<script setup lang="ts">
import { ChevronDownIcon } from 'lucide-vue-next'

defineProps<{
  label: string
  labelIcon?: Component | null
  href?: string
  target?: string
  /** 是否处于激活状态 */
  active?: boolean
  hasChildren?: boolean
  navigate?: () => void
}>()
</script>

<template>
  <a
    class="cursor-pointer select-none gap-2 rounded-md px-4 py-1.5 flex-center"
    :class="{
      'bg-emphasis font-medium': active,
      '!pr-2.5': hasChildren,
    }"
    :href="href"
    :target="target"
    @click="navigate"
  >
    <div class="shrink-0">
      <Component
        :is="labelIcon"
        v-if="labelIcon"
        :size="16"
      />
    </div>

    <span
      class="flex-1 truncate"
      :class="{ 'font-semibold': active }"
    >
      {{ label }}
    </span>

    <ChevronDownIcon
      v-if="hasChildren"
      class="ml-auto shrink-0"
      :size="14"
    />
  </a>
</template>
