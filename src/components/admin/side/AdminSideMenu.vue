<script setup lang="ts">
import { useNavMenu } from '~/composables/useNavMenu'

defineProps<{
  navMenuItems?: NavMenuItem[]
  expandedKeys?: ExpandedKeys
}>()

const emit = defineEmits<{
  'update:expandedKeys': [keys: ExpandedKeys]
}>()

const { isNavMenuItemActive } = useNavMenu()
</script>

<template>
  <PanelMenu
    :dt="{
      item: {
        focus: {
          background: 'transparent',
          color: 'currentColor',
        },
      },
    }"
    :expandedKeys="expandedKeys"
    :model="navMenuItems"
    :pt="{
      root: {
        class: 'admin-nav-menu',
      },
      panel: {
        class: '!border-none !bg-transparent',
      },
    }"
    @update:expandedKeys="emit('update:expandedKeys', $event)"
  >
    <template #item="{ item: it }: { item: NavMenuItem }">
      <NuxtLink
        v-if="it.route && !it.group"
        v-slot="{ href, navigate }"
        custom
        :to="it.route"
      >
        <AdminSideMenuItem
          :active="isNavMenuItemActive(it)"
          :hasChildren="!!it.items"
          :href="href"
          :label="it.label"
          :labelIcon="getRouteIcon(it.key)"
          :navigate="navigate"
        />
      </NuxtLink>

      <AdminSideMenuItem
        v-else
        :active="isNavMenuItemActive(it)"
        :expanded="expandedKeys?.[it.key]"
        :hasChildren="!!it.items"
        :label="it.label"
        :labelIcon="getRouteIcon(it.key)"
      />
    </template>
  </PanelMenu>
</template>

<style lang="css">
.admin-nav-menu {
  &.p-panelmenu {
    @apply gap-0;

    .p-panelmenu-panel {
      .p-panelmenu-content {
        @apply pt-2;
      }
    }
  }

  .p-panelmenu-submenu {
    @apply flex flex-col gap-1;
  }
}
</style>
