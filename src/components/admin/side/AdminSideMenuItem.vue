<script setup lang="ts">
import { ChevronDownIcon, ChevronUpIcon } from 'lucide-vue-next'

defineProps<{
  label: string
  labelIcon?: Component | null
  href?: string
  target?: string
  /** 是否处于激活状态 */
  active?: boolean
  hasChildren?: boolean
  navigate?: () => void
  /** 是否展开 */
  expanded?: boolean
}>()
</script>

<template>
  <a
    class="cursor-pointer select-none gap-2 rounded-md px-4 py-1.5 flex-center"
    :class="{
      'bg-content font-medium shadow-sm outline outline-1 outline-divider': active,
      'hover:bg-primary-contrast/80': !active,
    }"
    :href="href"
    :target="target"
    @click="navigate"
  >
    <div class="shrink-0">
      <Component
        :is="labelIcon"
        v-if="labelIcon"
        :class="{ 'opacity-75': !active }"
        :size="15"
      />
    </div>

    <span class="truncate">{{ label }}</span>

    <Component
      :is="expanded ? ChevronUpIcon : ChevronDownIcon"
      v-if="hasChildren"
      class="ml-auto size-4 shrink-0"
    />
  </a>
</template>
