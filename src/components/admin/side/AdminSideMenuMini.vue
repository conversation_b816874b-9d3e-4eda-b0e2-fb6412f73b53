<script setup lang="ts">
defineProps<{
  navMenuItems?: NavMenuItem[]
}>()

const { popoverRef, popover } = useProPopover()

const { isNavMenuItemActive } = useNavMenu()

/** 动态的子菜单，根据当前 active 的 navMenuItem 来生成 */
const subMenuItems = ref<NavMenuItem[]>()

const handleSubMenuClick = async (navMenuItem: NavMenuItem) => {
  if (!navMenuItem.group && navMenuItem.route) {
    await navigateTo(navMenuItem.route)
  }
}

const handleSubMenuOver = (navMenuItem: NavMenuItem, ev: Event) => {
  if (Array.isArray(navMenuItem.items) && navMenuItem.items.length > 0) {
    subMenuItems.value = navMenuItem.items

    if (ev.target instanceof HTMLElement) {
      popover.value?.show(ev.target)
    }
  }
}

const handleSubMenuLeave = (ev: MouseEvent) => {
  popover.value?.hoverLeave(ev)
}
</script>

<template>
  <div class="flex-col justify-center gap-6 flex-center">
    <div
      v-for="navMenuItem of navMenuItems"
      :key="navMenuItem.key"
      class="w-full cursor-pointer flex-col justify-center gap-2 rounded-lg p-2 flex-center hover:bg-content-hover"
      :class="{
        'bg-content-hover': isNavMenuItemActive(navMenuItem),
      }"
      @click="handleSubMenuClick(navMenuItem)"
      @mouseenter="handleSubMenuOver(navMenuItem, $event)"
      @mouseleave="handleSubMenuLeave"
    >
      <Component
        :is="getRouteIcon(navMenuItem.key)"
        class="size-6"
      />

      <span class="text-center text-sm">
        {{ navMenuItem.label }}
      </span>
    </div>

    <ProPopover
      :ref="popoverRef"
      contentClass="!p-0"
      position="right-start"
      trigger="hover"
    >
      <template #content>
        <Menu
          class="!border-none"
          :model="subMenuItems"
        >
          <template #item="{ item: it, props }">
            <NuxtLink
              v-if="it.route"
              v-slot="{ href, navigate }"
              custom
              :to="it.route"
            >
              <UserMenuItem
                :href="href"
                :itemProps="props"
                :label="it.label"
                :labelIcon="getRouteIcon(it.key)"
                :navigate="navigate"
              />
            </NuxtLink>

            <UserMenuItem
              v-else-if="it.url"
              :href="it.url"
              :itemProps="props"
              :label="it.label"
              :labelIcon="getRouteIcon(it.key)"
              :target="it.target"
            />

            <UserMenuItem
              v-else
              :itemProps="props"
              :label="it.label"
              :labelIcon="getRouteIcon(it.key)"
            />
          </template>
        </Menu>
      </template>
    </ProPopover>

    <!-- <Menu
      :ref="popMenuRef"
      :model="subMenuItems"
      popup
    >
      <template #item="{ item: it, props }">
        <NuxtLink
          v-if="it.route"
          v-slot="{ href, navigate }"
          custom
          :to="it.route"
        >
          <UserMenuItem
            :href="href"
            :itemProps="props"
            :label="it.label"
            :labelIcon="getRouteIcon(it.key)"
            :navigate="navigate"
          />
        </NuxtLink>

        <UserMenuItem
          v-else-if="it.url"
          :href="it.url"
          :itemProps="props"
          :label="it.label"
          :labelIcon="getRouteIcon(it.key)"
          :target="it.target"
        />

        <UserMenuItem
          v-else
          :itemProps="props"
          :label="it.label"
          :labelIcon="getRouteIcon(it.key)"
        />
      </template>
    </Menu> -->
  </div>
</template>
