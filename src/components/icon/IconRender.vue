<script setup lang="ts">
import { safeParse } from 'valibot'

interface Props {
  /** 序列化后的图标配置值 */
  iconVal?: string
}

const props = defineProps<Props>()

const icon = computed(() => {
  if (props.iconVal) {
    let parsedIconVal: TemplateIcon | null = null

    try {
      parsedIconVal = JSON.parse(props.iconVal)
    }
    catch {
      // 不处理
    }

    if (parsedIconVal) {
      const result = safeParse(schemaTemplateIcon, parsedIconVal)

      if (result.success) {
        return result.output
      }
    }
  }

  return null
})

defineSlots<{
  fallback?: []
}>()
</script>

<template>
  <span class="size-full inline-flex-center">
    <Icon
      v-if="icon"
      :name="icon.name"
    />

    <span
      v-else
      class="size-full justify-center inline-flex-center"
    >
      <slot name="fallback">-</slot>
    </span>
  </span>
</template>
