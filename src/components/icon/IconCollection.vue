<script setup lang="ts">
import { is } from 'valibot'

interface Props {
  iconVal?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:iconVal': [value: Props['iconVal']]
}>()

const collections = [
  {
    groupName: '数据库',
    icons: [
      {
        name: 'devicon:postgresql',
        label: 'PostgreSQL',
      },
      {
        name: 'devicon:mysql',
        label: 'MySQL',
      },
      {
        name: 'devicon:redis',
        label: 'Redis',
      },
      {
        name: 'devicon:microsoftsqlserver',
        label: 'SQL Server',
      },
      {
        name: 'devicon:mongodb',
        label: 'MongoDB',
      },
    ],
  },
  {
    groupName: '云服务',
    icons: [
      {
        name: 'logos:aws',
        label: 'AWS',
      },
      {
        name: 'logos:google-cloud',
        label: 'Google Cloud',
      },
      {
        name: 'devicon:azure',
        label: 'Azure',
      },
    ],
  },
  {
    groupName: '操作系统',
    icons: [
      {
        name: 'logos:ubuntu',
        label: 'Ubuntu',
      },
      {
        name: 'logos:centos-icon',
        label: 'CentOS',
      },
      {
        name: 'logos:redhat-icon',
        label: 'RedHat',
      },
      {
        name: 'logos:debian',
        label: 'Debian',
      },
      {
        name: 'devicon:linux',
        label: 'Linux',
      },
      {
        name: 'devicon:windows8',
        label: 'Windows',
      },
    ],
  },
  {
    groupName: '编程语言',
    icons: [
      {
        name: 'devicon:python',
        label: 'Python',
      },
      {
        name: 'devicon:javascript',
        label: 'JavaScript',
      },
      {
        name: 'devicon:java',
        label: 'Java',
      },
      {
        name: 'devicon:csharp',
        label: 'C#',
      },
      {
        name: 'devicon:php',
        label: 'PHP',
      },
      {
        name: 'devicon:go',
        label: 'Go',
      },
      {
        name: 'devicon:rust',
        label: 'Rust',
      },
    ],
  },
  {
    groupName: '常用',
    icons: [
      {
        name: 'custom-icon:physical-machine',
        label: '物理机',
      },
      {
        name: 'custom-icon:virtual-machine',
        label: '虚拟机',
      },
      {
        name: 'custom-icon:network-equipment',
        label: '网络设备',
      },
      {
        name: 'custom-icon:hardware',
        label: '硬件设备',
      },
      {
        name: 'custom-icon:computer',
        label: '计算机',
      },
      {
        name: 'custom-icon:router',
        label: '路由器',
      },
      {
        name: 'custom-icon:switch',
        label: '交换机',
      },
    ],
  },
] satisfies IconCollection[]

const handleSelectIcon = (icon: TemplateIcon) => {
  if (is(schemaTemplateIcon, icon)) {
    emit('update:iconVal', JSON.stringify(icon))
  }
}

const isIconSelected = (icon: IconItem) => {
  if (props.iconVal) {
    try {
      const parsedIcon = JSON.parse(props.iconVal)

      return icon.name === parsedIcon.name
    }
    catch {
      return false
    }
  }

  return false
}
</script>

<template>
  <div class="flex flex-col gap-5">
    <div
      v-for="collection of collections"
      :key="collection.groupName"
      class="flex flex-col gap-3"
    >
      <div class="font-medium">
        {{ collection.groupName }}
      </div>

      <div class="grid grid-cols-[repeat(auto-fit,minmax(60px,60px))] gap-3">
        <div
          v-for="icon of collection.icons"
          :key="icon.name"
          class="w-full cursor-pointer flex-col justify-center gap-2 overflow-hidden rounded-md p-2 flex-center"
          :class="{
            'bg-content-hover': isIconSelected(icon),
          }"
          @click="handleSelectIcon({ type: 'internal', name: icon.name, label: icon.label })"
        >
          <div class="size-6">
            <Icon
              :name="icon.name"
            />
          </div>

          <div class="truncate text-center text-xs">
            {{ icon.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
