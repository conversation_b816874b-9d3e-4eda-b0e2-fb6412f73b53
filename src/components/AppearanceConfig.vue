<script setup lang="ts">
import { palette } from '@primevue/themes'
import { HashIcon, MoonIcon, SunIcon, SunMoonIcon } from 'lucide-vue-next'

import { primaryColors, surfaceColors } from '@/enums/theme'

const appStore = useAppStore()
const { themeState, useCustomPrimaryColor, customPrimaryColor } = storeToRefs(appStore)

const themeOptions = [
  { value: 'light', label: '明亮' },
  { value: 'dark', label: '暗黑' },
  { value: 'auto', label: '自动' },
] satisfies { value: ThemeName, label: string }[]

const layoutOptions = [
  { value: 'admin-horizontal', label: '水平' },
  { value: 'admin-vertical', label: '垂直' },
] satisfies { value: LayoutName, label: string }[]

const transitionOptions = [
  { value: 'none', label: '无动画' },
  { value: 'fade', label: '淡入淡出' },
  { value: 'slide', label: '滑动' },
  { value: 'zoom', label: '缩放' },
] satisfies { value: PageTransition, label: string }[]

const previewingTransition = ref<PageTransition>()
const timer = ref<NodeJS.Timeout>()

const triggerPreview = (type: PageTransition) => {
  if (timer.value) {
    clearTimeout(timer.value)
  }

  previewingTransition.value = type

  timer.value = setTimeout(() => {
    previewingTransition.value = undefined
  }, 1000)
}

const setCustomPrimaryColor = (color: string) => {
  appStore.setPrimaryColor({ name: color, palette: palette(`#${color}`) })
}
</script>

<template>
  <div class="flex flex-col gap-form-field-large">
    <FormItem label="主色">
      <div class="flex-wrap gap-4 pt-2 flex-center">
        <button
          v-for="primary of primaryColors"
          :key="primary.name"
          class="size-6 rounded-full outline outline-2 outline-offset-2"
          :class="[
            themeState.primaryColor === primary.name ? 'outline-primary-500' : 'outline-transparent',
          ]"
          :style="{
            backgroundColor: `${primary.name === 'noir' ? 'currentColor' : primary.palette['500']}`,
          }"
          :title="primary.name"
          type="button"
          @click="appStore.setPrimaryColor(primary)"
        />

        <Divider
          class="!m-0"
          layout="vertical"
        />

        <div
          class="relative after:absolute after:-inset-1.5 after:rounded-xl after:bg-emphasis after:transition-opacity"
          :class="{
            'after:opacity-100': useCustomPrimaryColor,
            'after:opacity-0 hover:after:opacity-100': !useCustomPrimaryColor,
          }"
          @click="customPrimaryColor ? setCustomPrimaryColor(customPrimaryColor) : null"
        >
          <div class="relative z-10 gap-1 flex-center">
            <ColorPicker
              format="hex"
              :modelValue="customPrimaryColor"
              :pt="{
                root: '!rounded-full overflow-hidden border border-divider',
              }"
              @click.stop
              @update:modelValue="setCustomPrimaryColor($event)"
            />

            <IconField @click.stop>
              <InputIcon class="inline-flex-center">
                <HashIcon :size="12" />
              </InputIcon>

              <InputText
                class="w-24 !pl-7"
                :modelValue="customPrimaryColor"
                placeholder="自定义"
                size="small"
                @update:modelValue="setCustomPrimaryColor($event)"
              />
            </IconField>
          </div>
        </div>
      </div>
    </FormItem>

    <FormItem label="字体主色">
      <div class="flex flex-wrap gap-4 pt-2">
        <button
          v-for="surface of surfaceColors"
          :key="surface.name"
          class="size-6 rounded-full outline outline-2 outline-offset-2"
          :class="[
            themeState.surfaceColor === surface.name ? 'outline-primary-500' : 'outline-transparent',
          ]"
          :style="{ backgroundColor: `${surface.palette['500']}` }"
          :title="surface.name"
          type="button"
          @click="appStore.setSurfaceColor(surface)"
        />
      </div>
    </FormItem>

    <FormItem label="明暗主题">
      <ProSelectButton
        v-model="themeState.themeMode"
        dataKey="value"
        :options="themeOptions"
      >
        <template #option="{ option }: { option: typeof themeOptions[number] }">
          <span class="gap-2 flex-center">
            <span class="text-sm">
              <SunIcon
                v-if="option.value === 'light'"
                :size="15"
              />
              <MoonIcon
                v-else-if="option.value === 'dark'"
                :size="15"
              />
              <SunMoonIcon
                v-else-if="option.value === 'auto'"
                :size="15"
              />
            </span>
            <span>{{ option.label }}</span>
          </span>
        </template>
      </ProSelectButton>
    </FormItem>

    <FormItem label="页面切换动画">
      <div class="flex flex-col gap-4">
        <ProSelectButton
          v-model="themeState.transition"
          dataKey="value"
          :options="transitionOptions"
          size="small"
        >
          <template #option="{ option }: { option: typeof transitionOptions[number] }">
            <div
              class="flex-col gap-3 px-4 py-2 flex-center"
              @mouseenter="triggerPreview(option.value)"
            >
              <div class="relative size-8 rounded">
                <div
                  class="absolute inset-0 rounded bg-primary-500/20"
                  :class="{
                    [`${option.value}-preview`]: true,
                    'is-previewing': previewingTransition === option.value,
                  }"
                />
              </div>

              <span class="whitespace-nowrap text-sm font-normal">{{ option.label }}</span>
            </div>
          </template>
        </ProSelectButton>
      </div>
    </FormItem>

    <FormItem label="页面布局">
      <ProSelectButton
        v-model="themeState.layout"
        dataKey="value"
        :options="layoutOptions"
      />
    </FormItem>

    <FormItem label="更多">
      <span class="gap-5 flex-center">
        <span class="text-sm">面包屑导航</span>
        <ToggleSwitch v-model="themeState.showBreadcrumb" />
      </span>
    </FormItem>
  </div>
</template>
