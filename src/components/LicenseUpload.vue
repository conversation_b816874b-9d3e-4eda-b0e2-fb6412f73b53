<script setup lang="ts">
import { CopyrightIcon } from 'lucide-vue-next'
import type { FileUploadSelectEvent } from 'primevue'
import { array, file, length, maxSize, pipe, safeParse } from 'valibot'

const emit = defineEmits<{
  uploadSuccess: []
}>()

const { $toast } = useNuxtApp()

const FilesSchema = pipe(
  array(pipe(file(), maxSize(5 * 1024 * 1024, '文件大小不能超过 5MB'))),
  length(1, '只能上传一个文件'),
)

const handleFileSelect = async (ev: FileUploadSelectEvent) => {
  const { success, output: files, issues } = safeParse(FilesSchema, ev.files)

  if (success) {
    const file = files[0]

    const formData = new FormData()
    formData.append('file', file)

    const result = await UserService.uploadLicense(formData)

    if (result) {
      $toast.success({
        summary: '许可证上传成功',
        detail: '系统许可已更新',
      })

      emit('uploadSuccess')
    }
  }
  else {
    issues.forEach((issue) => {
      $toast.error({
        summary: '所选文件不符合要求',
        detail: issue.message,
      })
    })
  }
}
</script>

<template>
  <FileUpload
    accept=".lic"
    auto
    chooseLabel="上传许可证"
    customUpload
    mode="basic"
    :multiple="false"
    @select="handleFileSelect($event)"
  >
    <template #chooseicon>
      <CopyrightIcon :size="14" />
    </template>
  </FileUpload>
</template>
