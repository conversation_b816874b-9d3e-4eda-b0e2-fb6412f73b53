<script setup lang="ts">
import { Tag } from 'primevue'

import NumericDisplay from '~/components/NumericDisplay.vue'
import PopupMenu from '~/components/PopupMenu.vue'
import ProBtnMore from '~/components/pro/btn/ProBtnMore.vue'
import UserAvatar from '~/components/user/UserAvatar.vue'
import { queryKeys } from '~/constants-tw/query-key'
import { ProValueType } from '~/enums/pro'
import { UserService } from '~/services-tw/org'
import type { WechatUser, WechatUserListItem } from '~/types-tw/user'

const { tableRef } = useTableAction()

const activeClientId = ref<WechatUser['id']>()

const columns = computed<ProTableColumn<WechatUserListItem>[]>(() => [
  {
    header: '用户',
    render: (data) => h('span', { class: 'gap-2 whitespace-nowrap flex-center' }, [
      h(UserAvatar, {
        avatar: data.avatar,
        class: 'overflow-hidden',
        username: data.nickname,
      }),
      h('span', { class: 'flex flex-col text-sm' }, [
        h('span', data.nickname),
        h('span', { class: 'text-secondary' }, data.email),
      ]),
    ]),
  },
  {
    field: 'mobile',
    header: '手机号',
    render: (data) => h(NumericDisplay, {
      class: 'text-secondary text-[13px]',
      value: data.phone ? maskMobile(data.phone) : '-',
    }),
  },
  {
    field: 'is_follow',
    header: '公众号状态',
    render: (data) => h(Tag, {
      value: data.is_follow ? '已关注' : '未关注',
      severity: data.is_follow ? 'success' : 'secondary',
    }),
  },
  {
    field: 'created_at',
    header: '加入时间',
    valueType: ProValueType.Date,
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', { class: 'table-action-group' }, [
      h(PopupMenu, {
        options: [
          {
            label: '详情',
            command: () => {
              activeClientId.value = data.id
            },
          },
        ],
      }, {
        default: () => h(ProBtnMore, {
          onlyIcon: true,
        }),
      }),
    ]),
  },
])
</script>

<template>
  <div>
    <ProTable
      :ref="tableRef"
      :columns="columns"
      :queryKey="queryKeys.wechatUser.list()"
      :requestFn="UserService.getWechatUsers"
      :toolbarConfig="{
        search: {
          key: 'username',
          placeholder: '搜索用户名称',
        },
      }"
    />

    <Dialog
      class="dialog-half"
      dismissableMask
      :draggable="false"
      header="用户信息"
      modal
      :visible="!!activeClientId"
      @update:visible="activeClientId = undefined"
    >
      <ClientWechatDetail :clientId="activeClientId" />
    </Dialog>
  </div>
</template>
