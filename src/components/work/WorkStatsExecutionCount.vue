<script setup lang="ts">
import type { ApexOptions } from 'apexcharts'

interface ChartData {
  categories: string[]
  series: {
    name: string
    data: number[]
  }[]
}

withDefaults(defineProps<{
  height?: number | string
}>(), {
  height: 350,
})

const { isDarkMode } = useTheme()

// 使用 workspace stats 数据
const workspaceStatsStore = useWorkspaceStatsStore()
const { workflowExecutionStats } = storeToRefs(workspaceStatsStore)

// 计算图表数据
const chartData = computed<ChartData>(() => {
  if (!workflowExecutionStats.value?.length) {
    return {
      categories: [],
      series: [{
        name: '执行数量',
        data: [],
      }],
    }
  }

  return {
    categories: workflowExecutionStats.value.map((item) => item.name),
    series: [{
      name: '执行数量',
      data: workflowExecutionStats.value.map((item) => item.count),
    }],
  }
})

// 图表配置
const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  chart: {
    type: 'line',
    height: '100%',
    width: '100%',
    parentHeightOffset: 0,
    toolbar: {
      show: false,
    },
    zoom: {
      enabled: false,
    },
  },
  colors: ['#2196f3'], // 使用蓝色主题
  series: chartData.value.series,
  stroke: {
    curve: 'smooth',
    width: 3,
  },
  fill: {
    type: 'gradient',
    gradient: {
      shadeIntensity: 1,
      opacityFrom: 0.4,
      opacityTo: 0.1,
      stops: [0, 100],
    },
  },
  dataLabels: {
    enabled: true,
    offsetY: -10,
    style: {
      fontSize: '12px',
      colors: [isDarkMode.value ? '#fff' : '#333'],
    },
  },
  grid: {
    borderColor: isDarkMode.value ? '#374151' : '#e5e7eb',
    strokeDashArray: 4,
    xaxis: {
      lines: {
        show: true,
      },
    },
    yaxis: {
      lines: {
        show: true,
      },
    },
    padding: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 20,
    },
  },
  xaxis: {
    categories: chartData.value.categories,
    labels: {
      style: {
        colors: isDarkMode.value ? '#cbd5e1' : '#475569',
        fontSize: '12px',
      },
      rotate: -45,
      rotateAlways: true,
    },
    axisBorder: {
      show: false,
    },
    axisTicks: {
      show: false,
    },
  },
  yaxis: {
    labels: {
      style: {
        colors: isDarkMode.value ? '#cbd5e1' : '#475569',
        fontSize: '12px',
      },
    },
    min: 0,
    tickAmount: 5,
  },
  markers: {
    size: 5,
    colors: ['#2196f3'],
    strokeColors: '#fff',
    strokeWidth: 2,
    hover: {
      size: 7,
    },
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div class="size-full">
    <div
      ref="chartContainer"
      class="size-full"
      :style="typeof height === 'number' ? { height: `${height}px` } : { height }"
    />
  </div>
</template>
