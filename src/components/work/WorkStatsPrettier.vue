<script setup lang="ts">
import { TrendingUpIcon } from 'lucide-vue-next'

// 定义统计数据接口
interface StatItem {
  title: string
  value: string | number
  trend: number
  duration?: string
  chartData: number[] // 图表数据
}

// 示例数据，实际项目中可能从API获取
const stats = ref<StatItem[]>([
  {
    title: '新增客户',
    value: '526',
    trend: 2.4,
    chartData: [30, 40, 35, 45, 50, 55, 45, 50, 40], // 模拟图表数据
  },
  {
    title: '新增合同',
    value: '16',
    trend: 8.6,
    chartData: [20, 30, 25, 15, 25, 35, 45, 25, 35], // 模拟图表数据
  },
  {
    title: '回款金额',
    value: '¥160,000',
    trend: 6.0,
    chartData: [10, 15, 20, 25, 20, 30, 35, 40, 35], // 模拟图表数据
  },
])

// 获取趋势颜色
const getTrendColor = (trend: number) => {
  return trend >= 0 ? 'text-success-500' : 'text-danger-500'
}

// 获取趋势图标类
const getTrendIconClass = (trend: number) => {
  return trend >= 0 ? '' : 'rotate-180'
}
</script>

<template>
  <div>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
      <div
        v-for="(item, index) of stats"
        :key="index"
        class="flex-1 overflow-hidden rounded-xl border border-divider bg-page"
      >
        <div>
          <div class="px-3 py-2 font-medium">
            {{ item.title }}
          </div>

          <div class="rounded-xl bg-content">
            <div class="gap-4 p-3 pb-0 flex-center">
              <span class="text-3xl font-semibold tabular-nums">
                {{ item.value }}
              </span>

              <div class="flex items-center gap-1">
                <TrendingUpIcon
                  :class="[getTrendColor(item.trend), getTrendIconClass(item.trend)]"
                  :size="16"
                />
                <span :class="getTrendColor(item.trend)">
                  {{ Math.abs(item.trend) }}%
                </span>
              </div>
            </div>

            <div>
              <WorkStatsLineChart :data="item.chartData" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="pt-10">
      <WorkStatsTaskChart />
    </div>

    <div class="pt-5">
      <WorkNewClient />
    </div>
  </div>
</template>
