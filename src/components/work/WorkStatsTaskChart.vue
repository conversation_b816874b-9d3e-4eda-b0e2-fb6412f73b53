<script setup lang="ts">
import type { ApexOptions } from 'apexcharts'

// 模拟网站流量数据 - 根据图片中的数据
const months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']

// 图表数据 - 根据图片中的数据
const chartData = {
  actualTraffic: [40, 25, 35, 30, 22, 45, 55, 40, 30, 45, 60, 70], // 实际流量数据（实线）
  expectedTraffic: [60, 50, 40, 35, 45, 55, 50, 45, 40, 45, 40, 35], // 预期流量数据（虚线）
}

const { isDarkMode } = useTheme()

// 图表配置
const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  chart: {
    type: 'area',
    height: 250,
    toolbar: {
      show: false,
    },
    background: 'transparent',
    stacked: false,
    zoom: {
      enabled: false,
    },
  },
  colors: ['#111111', '#AAAAAA'], // 黑色实线，灰色虚线
  series: [
    {
      name: '实际流量',
      data: chartData.actualTraffic,
      type: 'area',
    },
    {
      name: '预期流量',
      data: chartData.expectedTraffic,
      type: 'line',
    },
  ],
  xaxis: {
    categories: months,
    labels: {
      style: {
        colors: isDarkMode.value ? '#CBD5E1' : '#64748B',
        fontSize: '12px',
      },
    },
    axisBorder: {
      show: false,
    },
    axisTicks: {
      show: false,
    },
  },
  yaxis: {
    labels: {
      show: false, // 隐藏 y 轴标签
    },
    axisBorder: {
      show: false,
    },
    axisTicks: {
      show: false,
    },
  },
  stroke: {
    curve: 'smooth',
    width: [3, 2], // 实线粗一些，虚线细一些
    dashArray: [0, 5], // 第一条线是实线，第二条线是虚线
    colors: ['#111111', '#AAAAAA'], // 确保线条颜色与colors属性一致
  },
  grid: {
    show: true,
    borderColor: isDarkMode.value ? '#334155' : '#f1f1f1',
    strokeDashArray: 1,
    position: 'back',
    xaxis: {
      lines: {
        show: true,
      },
    },
    yaxis: {
      lines: {
        show: true,
      },
    },
    padding: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
    },
  },
  markers: {
    size: 0, // 不显示点标记
  },
  dataLabels: {
    enabled: false,
  },
  tooltip: {
    enabled: true,
    theme: isDarkMode.value ? 'dark' : 'light',
    shared: true,
  },
  legend: {
    show: false, // 隐藏图例
  },
  fill: {
    type: ['gradient', 'none'], // 第一条线使用渐变填充，第二条线不填充
    gradient: {
      shade: 'dark',
      type: 'vertical',
      shadeIntensity: 0.1,
      opacityFrom: 0.4,
      opacityTo: 0.05,
      stops: [0, 90, 100],
      colorStops: [
        {
          offset: 0,
          color: '#111111',
          opacity: 0.4,
        },
        {
          offset: 100,
          color: '#111111',
          opacity: 0.05,
        },
      ],
    },
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div>
    <div>
      <h3 class="text-lg font-medium">
        任务统计
      </h3>

      <p class="text-sm text-surface-500">
        此图表展示了全年任务完成情况的对比分析。
      </p>
    </div>

    <div ref="chartContainer" />
  </div>
</template>
