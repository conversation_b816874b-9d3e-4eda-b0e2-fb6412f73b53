<script setup lang="ts">
import type { ApexOptions } from 'apexcharts'

// 定义图表数据接口
interface ChartData {
  categories: string[] // x轴类别
  series: {
    name: string
    data: number[]
  }[]
}

withDefaults(defineProps<{
  height?: number | string
}>(), {
  height: 100,
})

const { isDarkMode } = useTheme()

// 模拟数据 - 30天的数据点
const generateMockData = (): ChartData => {
  const now = new Date()
  const categories: string[] = []
  const data: number[] = []

  // 生成过去30天的日期和随机数据
  for (let i = 7; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(now.getDate() - i)

    // 格式化日期为 MM-DD 格式
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    categories.push(`${month}-${day}`)

    // 生成10-90之间的随机数，并保持一定的连续性但波动更大
    const lastValue = data.length > 0 ? data[data.length - 1] : 50
    const change = Math.random() * 30 - 15 // -15到15之间的随机变化，增加波动范围
    let newValue = lastValue + change

    // 确保值在10-90之间，扩大数值范围
    newValue = Math.max(10, Math.min(90, newValue))
    data.push(Math.round(newValue))
  }

  return {
    categories,
    series: [{
      name: '工作量',
      data,
    }],
  }
}

// 生成模拟数据
const chartData = ref<ChartData>(generateMockData())

// 图表配置
const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  chart: {
    type: 'area',
    height: '100%',
    width: '100%',
    parentHeightOffset: 0,
    offsetX: -10,
    offsetY: 0,
    toolbar: {
      show: false, // 隐藏工具栏
    },
    zoom: {
      enabled: false, // 禁用缩放
    },
    animations: {
      enabled: true,
      easing: 'easeinout',
      speed: 800,
    },
  },
  colors: ['#9d7fea'], // 浅紫色折线
  series: chartData.value.series,
  stroke: {
    curve: 'smooth', // 平滑曲线
    width: 3,
  },
  fill: {
    type: 'gradient',
    gradient: {
      shadeIntensity: 1,
      opacityFrom: 0.7,
      opacityTo: 0,
      stops: [0, 100],
      colorStops: [
        {
          offset: 0,
          color: '#b39ddb', // 浅紫色渐变起点
          opacity: 0.1,
        },
        {
          offset: 100,
          color: '#b39ddb', // 渐变终点，完全透明
          opacity: 0,
        },
      ],
    },
  },
  dataLabels: {
    enabled: false, // 禁用数据标签
  },
  grid: {
    show: false, // 完全隐藏网格
    borderColor: isDarkMode.value ? '#374151' : '#e5e7eb',
    strokeDashArray: 4, // 虚线网格
    xaxis: {
      lines: {
        show: false, // 隐藏 x 轴线
      },
    },
    yaxis: {
      lines: {
        show: false, // 隐藏 y 轴线
      },
    },
    padding: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 10,
    },
  },
  xaxis: {
    categories: chartData.value.categories,
    labels: {
      show: false, // 隐藏 x 轴标签
    },
    axisBorder: {
      show: false, // 隐藏 x 轴边框
    },
    axisTicks: {
      show: false, // 隐藏 x 轴刻度
    },
  },
  yaxis: {
    tickAmount: 5,
    labels: {
      show: false, // 隐藏 y 轴标签
    },
    axisBorder: {
      show: false, // 隐藏 y 轴边框
    },
    axisTicks: {
      show: false, // 隐藏 y 轴刻度
    },
  },
  tooltip: {
    x: {
      show: true,
    },
    y: {
      formatter: (value) => `${value}`,
    },
    marker: {
      show: false,
    },
  },
  markers: {
    size: 0, // 隐藏数据点标记
    hover: {
      size: 5, // 悬停时显示标记
    },
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div class="size-full">
    <div
      ref="chartContainer"
      class="size-full"
      :style="typeof height === 'number' ? { height: `${height}px` } : { height }"
    />
  </div>
</template>
