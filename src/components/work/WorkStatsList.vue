<script setup lang="ts">
// 定义统计数据接口
interface StatItem {
  title: string
  value: string
  text: string
}

const workspaceStatsStore = useWorkspaceStatsStore()
const { stats } = storeToRefs(workspaceStatsStore)

// 使用计算属性来响应数据变化
const statsCount = computed<StatItem[]>(() => [
  {
    title: '健康指数',
    value: String(stats?.value?.base_count.health_index ?? 0),
    text: '稳定运行中',
  },
  {
    title: '流程总数',
    value: String(stats?.value?.base_count.total_count ?? 0),
    text: '稳定运行中',
  },
  {
    title: '流程状态',
    value: `${stats?.value?.base_count.active_count ?? 0} / ${stats?.value?.base_count.total_count ?? 0}`,
    text: '14 个已启用',
  },
  {
    title: '异常数量',
    value: String(stats?.value?.base_count.execution_failed_count ?? 0),
    text: '较昨日 -2',
  },
  {
    title: '执行中数量',
    value: String(stats?.value?.base_count.execution_running_count ?? 0),
    text: '正常进行',
  },
])
</script>

<template>
  <div>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-5">
      <div
        v-for="(item, index) of statsCount"
        :key="index"
        class="flex-1 overflow-hidden rounded-xl border border-divider bg-page"
      >
        <div>
          <div class="px-3 py-2 font-medium">
            {{ item.title }}
          </div>

          <div class="rounded-xl bg-content">
            <div class="flex-col gap-1 p-3 flex-center">
              <div class="text-3xl font-semibold tabular-nums">
                {{ item.value }}
              </div>

              <div class="text-sm text-secondary">
                {{ item.text }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
