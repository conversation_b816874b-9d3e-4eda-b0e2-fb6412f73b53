<script setup lang="ts">
import { Tag } from 'primevue'

import { ProValueType } from '~/enums/pro'

interface WorkStatsExecutionRecord {
  name: string
  status: 1 | 2 | 3
  startTime: string
  duration: string
}

const columns: ProTableColumn<WorkStatsExecutionRecord>[] = [
  {
    field: 'name',
    header: '工作流名称',
  },
  {
    field: 'status',
    header: '状态',
    render: (row) => {
      const statusConfig = {
        1: { severity: 'success', value: '成功' },
        2: { severity: 'danger', value: '失败' },
        3: { severity: 'warn', value: '执行中' },
      }
      const config = statusConfig[row.status]

      return h(Tag, { severity: config.severity }, () => config.value)
    },
  },
  {
    field: 'startTime',
    header: '开始执行时间',
    valueType: ProValueType.DateTime,
  },
  {
    field: 'duration',
    header: '耗时',
  },
]

const dataSource = ref<WorkStatsExecutionRecord[]>([
  {
    name: '主机漏洞扫描',
    status: 1,
    startTime: '2025-03-18 10:15:32',
    duration: '10s',
  },
  {
    name: '可疑附件分析',
    status: 2,
    startTime: '2025-03-18 10:15:32',
    duration: '47s',
  },
  {
    name: '终端安全检查',
    status: 3,
    startTime: '2025-03-18 10:15:32',
    duration: '2h 10s',
  },
  {
    name: '流量异常检测',
    status: 1,
    startTime: '2025-03-18 10:15:32',
    duration: '10s',
  },
])
</script>

<template>
  <ProTable
    :columns="columns"
    :dataSource="dataSource"
  />
</template>
