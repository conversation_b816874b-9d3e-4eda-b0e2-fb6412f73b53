<script setup lang="ts">
import type { ApexOptions } from 'apexcharts'

// 状态配置
const statusConfig = {
  success: { label: '成功', color: '#4CAF50' },
  error: { label: '失败', color: '#F44336' },
  waiting: { label: '等待中', color: '#FF9800' },
  canceled: { label: '已取消', color: '#9E9E9E' },
} as const

// 使用 workspace stats 数据
const workspaceStatsStore = useWorkspaceStatsStore()
const { executionStatusStats } = storeToRefs(workspaceStatsStore)

// 计算执行统计数据
const executionStats = computed(() => {
  if (!executionStatusStats.value?.length) {
    return {
      total: 0,
      list: [],
    }
  }

  const total = executionStatusStats.value.reduce((sum, item) => sum + item.count, 0)
  const list = executionStatusStats.value.map((item) => ({
    status: item.status,
    count: item.count,
  }))

  return {
    total,
    list,
  }
})

const { themeMode } = useTheme()

const chartOptions = computed<ApexOptions>(() => {
  type Data = { series: number[], labels: string[], colors: string[] }
  const defaultData: Data = { series: [], labels: [], colors: [] }

  const { series, labels, colors } = executionStats.value?.list.reduce<Data>((acc: Data, it) => {
    const config = statusConfig[it.status]
    acc.series.push(it.count)
    acc.labels.push(config.label)
    acc.colors.push(config.color)

    return acc
  }, defaultData) || defaultData

  const total = executionStats.value?.total || 0
  const waitingCount = executionStats.value?.list.find((it) => it.status === 'waiting')?.count || 0
  const percentage = total > 0 ? Math.round((waitingCount / total) * 100) : 0

  return {
    theme: {
      mode: themeMode.value,
    },
    series,
    labels,
    colors,
    chart: {
      type: 'donut',
      height: 200,
    },
    dataLabels: {
      enabled: true,
    },
    tooltip: {
      enabled: false,
    },
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
          labels: {
            show: true,
            total: {
              show: true,
              label: '执行中',
              formatter: () => `${total} (${percentage}%)`,
            },
          },
        },
      },
    },
    states: {
      hover: {
        filter: {
          type: 'none',
        },
      },
    },
    legend: {
      position: 'right',
      offsetY: 20,
      height: 230,
    },
  }
})

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div>
    <div ref="chartContainer" />
  </div>
</template>
