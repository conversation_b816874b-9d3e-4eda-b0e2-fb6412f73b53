<script setup lang="ts">
import type { ApexOptions } from 'apexcharts'

interface ChartData {
  categories: string[]
  series: {
    name: string
    data: number[]
  }[]
}

withDefaults(defineProps<{
  height?: number | string
}>(), {
  height: 350,
})

const { isDarkMode } = useTheme()

const workspaceStatsStore = useWorkspaceStatsStore()
const { workflowNodeStats } = storeToRefs(workspaceStatsStore)

const chartData = computed<ChartData>(() => {
  if (!workflowNodeStats.value?.length) {
    return {
      categories: [],
      series: [{
        name: '节点数量',
        data: [],
      }],
    }
  }

  return {
    categories: workflowNodeStats.value.map((item) => item.name),
    series: [{
      name: '节点数量',
      data: workflowNodeStats.value.map((item) => item.node_count),
    }],
  }
})

// 图表配置
const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  chart: {
    type: 'bar',
    height: '100%',
    toolbar: {
      show: false,
    },
    animations: {
      enabled: true,
      easing: 'easeinout',
      speed: 800,
    },
  },
  plotOptions: {
    bar: {
      horizontal: false,
      columnWidth: '55%',
      borderRadius: 4,
      distributed: true,
    },
  },
  colors: chartData.value.categories.map((_, index) => {
    const colors = ['#2196f3', '#4caf50', '#ffc107', '#ff9800', '#f44336', '#9c27b0', '#00bcd4', '#795548']

    return colors[index % colors.length]
  }),
  series: chartData.value.series,
  dataLabels: {
    enabled: true,
    style: {
      fontSize: '12px',
      colors: ['#fff'],
    },
  },
  legend: {
    show: false,
  },
  grid: {
    borderColor: isDarkMode.value ? '#374151' : '#e5e7eb',
    strokeDashArray: 4,
    xaxis: {
      lines: {
        show: false,
      },
    },
  },
  xaxis: {
    categories: chartData.value.categories,
    labels: {
      style: {
        fontSize: '12px',
      },
      rotate: -45,
      rotateAlways: false,
      trim: false,
      maxHeight: 100,
    },
    axisBorder: {
      show: false,
    },
    axisTicks: {
      show: false,
    },
  },
  yaxis: {
    labels: {
      style: {
        fontSize: '12px',
      },
    },
  },
  tooltip: {
    y: {
      formatter: (value) => `${value}`,
    },
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div class="size-full">
    <div
      ref="chartContainer"
      class="size-full"
      :style="typeof height === 'number' ? { height: `${height}px` } : { height }"
    />
  </div>
</template>
