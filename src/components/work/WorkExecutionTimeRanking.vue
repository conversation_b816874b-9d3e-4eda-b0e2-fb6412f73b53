<script setup lang="ts">
import { $dt } from '@primevue/themes'
import type { ApexOptions } from 'apexcharts'

const { isDarkMode } = useTheme()
const workspaceStatsStore = useWorkspaceStatsStore()
const { workflowExecutionStats } = storeToRefs(workspaceStatsStore)

// 按执行次数排序的数据
const sortedData = computed(() => {
  return [...workflowExecutionStats.value]
    .sort((a, b) => b.count - a.count)
    .slice(0, 10) // 只显示前10个
})

const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  chart: {
    type: 'bar',
    height: 400,
    toolbar: {
      show: false,
    },
  },
  plotOptions: {
    bar: {
      horizontal: true,
      barHeight: '70%',
      distributed: true,
    },
  },
  colors: [
    $dt('red.500').value,
    $dt('red.400').value,
    $dt('red.300').value,
    $dt('orange.500').value,
    $dt('orange.400').value,
    $dt('yellow.500').value,
    $dt('yellow.400').value,
    $dt('green.500').value,
    $dt('green.400').value,
    $dt('green.300').value,
  ],
  dataLabels: {
    enabled: true,
    formatter: (val: number) => `${val}次`,
    style: {
      fontSize: '12px',
    },
  },
  series: [{
    name: '执行次数',
    data: sortedData.value.map((item) => item.count),
  }],
  xaxis: {
    categories: sortedData.value.map((item) => item.name),
  },
  yaxis: {
    labels: {
      style: {
        fontSize: '12px',
      },
    },
  },
  grid: {
    borderColor: '#f1f1f1',
  },
  tooltip: {
    y: {
      formatter: (val: number) => `${val}次`,
    },
  },
  legend: {
    show: false,
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div ref="chartContainer" />
</template>
