<script setup lang="ts">
import { valibotResolver } from '@primevue/forms/resolvers/valibot'
import { useQuery } from '@tanstack/vue-query'
import { object } from 'valibot'

import { UserService } from '~/services-tw/org'
import type { TW_LoginFormValues } from '~/types-tw/user'

const resolver = valibotResolver(
  object({
    phone: schemaNotEmptyString('请输入手机号'),
    password: schemaNotEmptyString('请输入密码'),
  }),
)

const loginPayload = ref<TW_LoginFormValues | null>(null)

const { refetch: login, isLoading: isLoginLoading } = useQuery({
  queryKey: ['login', loginPayload.value],
  queryFn: async () => {
    if (loginPayload.value) {
      return UserService.loginWithPassword(loginPayload.value)
    }

    return null
  },
  enabled: false,
})

const handleLogin = async ({ valid, states }: FormSubmitOptions<TW_LoginFormValues>) => {
  if (valid) {
    loginPayload.value = {
      phone: states.phone.value,
      password: states.password.value,
    }

    const { data } = await login()

    if (data) {
      handleLoginSuccess({ token: data.token, user: normalizedUser(data.user_info) })
    }
  }
}
</script>

<template>
  <Form
    :resolver="resolver"
    @submit="handleLogin"
  >
    <div class="flex flex-col gap-5">
      <FormItem
        v-slot="{ id }"
        label="手机号"
        name="phone"
      >
        <InputText
          :id="id"
          fluid
          placeholder="请输入手机号"
          type="text"
        />
      </FormItem>

      <FormItem
        v-slot="{ id }"
        label="密码"
        name="password"
      >
        <Password
          :feedback="false"
          fluid
          :inputId="id"
          placeholder="请输入密码"
          toggleMask
          type="password"
        />
      </FormItem>

      <Button
        class="mt-2"
        label="确认登录"
        :loading="isLoginLoading"
        size="large"
        type="submit"
      />
    </div>
  </Form>
</template>
