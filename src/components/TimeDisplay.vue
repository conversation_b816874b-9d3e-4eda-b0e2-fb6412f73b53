<script setup lang="ts">
/**
 * 时间显示组件， 主要用于对表格中的时间字段进行格式化和样式处理
 */

import { DateFormat } from '~/enums/common'

withDefaults (
  defineProps<{
    time?: string
    format?: DateFormat
  }>(),
  {
    format: DateFormat.YYYY_MM_DD,
  },
)
</script>

<template>
  <span
    v-tooltip.top="
      format === DateFormat.YYYY_MM_DD_HH_MM_SS
        ? undefined
        : { value: formatDate(time, DateFormat.YYYY_MM_DD_HH_MM_SS), showDelay: 400 }
    "
    class="text-sm"
    :class="{ 'whitespace-nowrap': format === DateFormat.YYYY_MM_DD }"
  >
    {{ formatDate(time, format) ?? '--' }}
  </span>
</template>
