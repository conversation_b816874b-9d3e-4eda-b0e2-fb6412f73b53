<script setup lang="ts">
interface ScrollGradientContainerProps {
  /** 滚动容器类名 */
  scrollClassName?: string
  /** 渐变高度 */
  gradientHeight?: string
  /** 渐变颜色 */
  gradientFromColor?: string
  /** 顶部渐变类名 */
  topGradientClass?: string
  /** 底部渐变类名 */
  bottomGradientClass?: string
}

interface ScrollGradientContainerEmits {
  scroll: [event: Event]
}

// 定义 props 和 emits
withDefaults(defineProps<ScrollGradientContainerProps>(), {
  gradientHeight: 'h-10',
  gradientFromColor: 'from-background',
})

const emit = defineEmits<ScrollGradientContainerEmits>()

// 模板引用
const scrollRef = ref<HTMLDivElement>()

// 响应式状态
const scrollState = reactive({
  canScrollUp: false,
  canScrollDown: false,
})

// 检查滚动状态的函数
const handleInternalScroll = () => {
  if (scrollRef.value) {
    const { scrollTop, scrollHeight, clientHeight } = scrollRef.value
    const canScrollUp = scrollTop > 0
    const canScrollDown = scrollTop < scrollHeight - clientHeight - 1

    scrollState.canScrollUp = canScrollUp
    scrollState.canScrollDown = canScrollDown
  }
}

// 处理滚动事件
const handleScroll = (event: Event) => {
  handleInternalScroll()

  // 触发外部事件
  emit('scroll', event)
}

// 暴露 scrollRef 给父组件
defineExpose({
  scrollRef,
})

// 组件挂载后的逻辑
onMounted(() => {
  const scrollElement = scrollRef.value

  if (scrollElement) {
    // 初始检查
    handleInternalScroll()

    // 监听容器本身的尺寸变化
    const resizeObserver = new ResizeObserver(handleInternalScroll)
    resizeObserver.observe(scrollElement)

    // 监听内容变化（DOM 节点增删、属性变化等）
    const mutationObserver = new MutationObserver(handleInternalScroll)
    mutationObserver.observe(scrollElement, {
      childList: true, // 监听子节点的增加和删除
      subtree: true, // 监听所有后代节点
      attributes: true, // 监听属性变化
      characterData: true, // 监听文本内容变化
    })

    // 组件卸载时清理
    onUnmounted(() => {
      resizeObserver.disconnect()
      mutationObserver.disconnect()
    })
  }
})
</script>

<template>
  <div class="relative flex-1 overflow-hidden">
    <!-- 顶部渐变遮罩 -->
    <div
      v-if="scrollState.canScrollUp"
      class="pointer-events-none absolute inset-x-0 top-0 z-10 bg-gradient-to-b from-content to-transparent"
      :class="[
        gradientHeight,
        gradientFromColor,
        topGradientClass,
      ]"
    />

    <!-- 底部渐变遮罩 -->
    <div
      v-if="scrollState.canScrollDown"
      class="pointer-events-none absolute inset-x-0 bottom-0 z-10 bg-gradient-to-t from-content to-transparent"
      :class="[
        gradientHeight,
        gradientFromColor,
        bottomGradientClass,
      ]"
    />

    <div
      ref="scrollRef"
      class="h-full overflow-y-auto"
      :class="scrollClassName"
      @scroll="handleScroll"
    >
      <slot />
    </div>
  </div>
</template>
