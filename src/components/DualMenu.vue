<script setup lang="ts">
interface Props {
  menuItems?: ProMenuItem[]
  disablePopupMenu?: boolean
  disableContextMenu?: boolean
}

defineProps<Props>()

const emit = defineEmits<{
  /** 隐藏菜单时触发 */
  hide: []
}>()

const handleHide = () => {
  emit('hide')
}

const { popMenuRef, popMenu } = usePopMenu()
const { popMenuRef: ctxMenuRef, popMenu: ctxMenu } = usePopMenu()

const showMenu = (ev: MouseEvent) => {
  const openType = ev.type === 'contextmenu' ? 'context' : 'popup'

  if (openType === 'popup') {
    popMenu.value?.toggle(ev)
    ctxMenu.value?.hide()
  }
  else if (openType === 'context') {
    ctxMenu.value?.show(ev)
    popMenu.value?.hide()
  }
}

defineExpose({
  showMenu,
})
</script>

<template>
  <Menu
    v-if="!disablePopupMenu"
    :ref="popMenuRef"
    :model="menuItems"
    popup
    @blur="handleHide()"
  >
    <template #item="{ item, props: p }: { item: ProMenuItem, props: { action: AnyType, icon: AnyType, label: AnyType } }">
      <ProMenuItem
        :item="item"
        :itemProps="p"
      />
    </template>
  </Menu>

  <ContextMenu
    v-if="!disableContextMenu"
    :ref="ctxMenuRef"
    :model="menuItems"
    @hide="handleHide()"
  >
    <template #item="{ item, props: p }: { item: ProMenuItem, props: { action: AnyType, icon: AnyType, label: AnyType } }">
      <ProMenuItem
        :item="item"
        :itemProps="p"
      />
    </template>
  </ContextMenu>
</template>
