<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import UserSelectorChip from '~/components/user/selector/UserSelectorChip.vue'
import WechatUserSelector from '~/components/WechatUserSelector.vue'
import { queryKeys } from '~/constants-tw/query-key'
import { ProValueType } from '~/enums/pro'
import { UserService } from '~/services-tw/org'
import type { TW_Client, WechatUser } from '~/types-tw/user'

const props = defineProps<{
  clientId?: TW_Client['id']
}>()

const { data: clientDetail, refetch: refetchClientDetail } = useQuery({
  queryKey: queryKeys.client.detail(props.clientId),
  queryFn: () => {
    if (props.clientId) {
      return UserService.getClient(props.clientId)
    }

    return null
  },
  enabled: !!props.clientId,
})

const detailData = computed<ProFormLabelValueItem[]>(() => {
  return [
    {
      label: '客户名称',
      value: clientDetail.value?.name,
    },
    {
      label: '客户编号',
      value: clientDetail.value?.number,
    },
    {
      label: '客户类型',
      value: clientDetail.value?.type,
    },
    {
      label: '联系方式',
      value: clientDetail.value?.work_phone,
    },
    {
      label: '所在省',
      value: clientDetail.value?.country_province,
    },
    {
      label: '所在市',
      value: clientDetail.value?.country_province_city,
    },
    {
      label: '联系地址',
      value: clientDetail.value?.address,
    },
    {
      label: '相关用户',
      value: () => {
        return h(WechatUserSelector, {
          modelValue: clientDetail.value?.wechat_users?.map((item) => ({
            ...item,
            label: item.nickname,
            value: item.id,
          })),
          'onUpdate:modelValue': (value: WechatUser[] | undefined) => {
            if (clientDetail.value?.id) {
              UserService
                .updateClientUser(clientDetail.value?.id, value?.map((item) => item.id) ?? [])
                .then(() => {
                  refetchClientDetail()
                })
            }
          },
        })
      },
    },
    {
      valueType: ProValueType.Divider,
    },
    {
      label: '发票单位名称',
      value: clientDetail.value?.invoice_unit_name,
    },
    {
      label: '发票单位地址',
      value: clientDetail.value?.invoice_unit_address,
    },
    {
      label: '发票单位银行名称',
      value: clientDetail.value?.invoice_unit_bank_name,
    },
    {
      label: '发票单位银行账号',
      value: clientDetail.value?.invoice_unit_bank_number,
    },
    {
      label: '发票单位税号',
      value: clientDetail.value?.invoice_unit_number,
    },
    {
      label: '发票单位电话',
      value: clientDetail.value?.invoice_unit_phone,
    },
    {
      valueType: ProValueType.Divider,
    },
    {
      label: '客户经理',
      value: () => {
        const user = clientDetail.value?.user

        if (user) {
          return h(UserSelectorChip, {
            value: {
              id: user.id,
              username: user.name,
              avatar: user.avatar,
            },
          })
        }
      },
    },
  ]
})
</script>

<template>
  <div v-if="clientDetail">
    <ProFormLabelValue
      :data="detailData"
      layout="vertical"
      readOnly
    />
  </div>
</template>
