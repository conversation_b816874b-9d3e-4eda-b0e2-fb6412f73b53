<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { CalendarIcon, ClockIcon, FileTextIcon, HashIcon } from 'lucide-vue-next'

import { queryKeys } from '~/constants-tw/query-key'
import { ProjectService } from '~/services-tw/project'
import type { TW_Project } from '~/types-tw/project'

const props = defineProps<{
  projectId: TW_Project['id']
}>()

const { data: projectDetail, isLoading, isError } = useQuery({
  queryKey: queryKeys.project.publicDetail(props.projectId),
  // 注释掉原本的API调用代码
  // queryFn: () => ProjectService.getPublicProjectDetail(props.projectId),
  // 使用模拟数据替代实际API调用，并添加延时
  queryFn: () => {
    return ProjectService.getPublicProjectDetail(props.projectId)
    // return getMockProjectData()
  },
  enabled: !!props.projectId,
})

const projectFields = computed<(ProFormLabelValueItem & { icon?: Component })[]>(() => {
  if (!projectDetail.value) {
    return []
  }

  const progress = projectDetail.value?.statistics?.task?.count
    ? `${Math.round(((projectDetail.value?.statistics?.task?.close ?? 0) / projectDetail.value?.statistics?.task?.count) * 100)}%`
    : '-'

  return [
    {
      label: '项目名称',
      value: projectDetail.value.name,
      icon: FileTextIcon,
    },
    {
      label: '项目编号',
      value: projectDetail.value.number,
      icon: HashIcon,
    },
    {
      label: '截止日期',
      value: projectDetail.value.start_date.join('  -  '),
      icon: CalendarIcon,
    },
    {
      label: '项目进度',
      value: progress,
      icon: ClockIcon,
    },
  ]
})
</script>

<template>
  <div>
    <div v-if="isLoading || isError">
      <VanSkeleton
        :row="3"
        title
      />
    </div>

    <div
      v-else
      class="grid grid-cols-12 gap-admin-layout"
    >
      <template
        v-for="field of projectFields"
        :key="field.label"
      >
        <!-- Label -->
        <div class="col-span-4 gap-2 flex-center">
          <Component
            :is="field.icon"
            v-if="field.icon"
            class="size-4"
          />
          <div>
            {{ field.label }}：
          </div>
        </div>

        <!-- Value -->
        <div class="col-span-8">
          <div class="line-clamp-4">
            {{ field.value }}
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
