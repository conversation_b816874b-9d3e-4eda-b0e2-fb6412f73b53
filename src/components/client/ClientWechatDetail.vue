<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/constants-tw/query-key'
import { ProValueType } from '~/enums/pro'
import { UserService } from '~/services-tw/org'
import type { TW_Client } from '~/types-tw/user'

const props = defineProps<{
  clientId?: TW_Client['id']
}>()

const { data: clientDetail } = useQuery({
  queryKey: queryKeys.wechatUser.detail(props.clientId),
  queryFn: () => {
    if (props.clientId) {
      return UserService.getWechatUser(props.clientId)
    }

    return null
  },
  enabled: !!props.clientId,
})

const detailData = computed<ProFormLabelValueItem[]>(() => {
  return [
    {
      label: '头像',
      value: clientDetail.value?.avatar,
      valueType: ProValueType.Image,
    },
    {
      label: '微信昵称',
      value: clientDetail.value?.nickname,
    },
    {
      label: '姓名',
      value: clientDetail.value?.username,
    },
    {
      label: '邮箱',
      value: clientDetail.value?.email,
    },
    {
      label: '手机号',
      value: clientDetail.value?.phone,
    },
    {
      label: '加入时间',
      value: clientDetail.value?.created_at,
      valueType: ProValueType.Date,
    },
  ]
})
</script>

<template>
  <div v-if="clientDetail">
    <ProFormLabelValue
      :data="detailData"
      layout="vertical"
      readOnly
    />
  </div>
</template>
