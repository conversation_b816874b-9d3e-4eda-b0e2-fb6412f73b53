<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/constants-tw/query-key'
import { ProjectService } from '~/services-tw/project'
import type { TW_Project } from '~/types-tw/project'

const props = defineProps<{
  projectId: TW_Project['id']
}>()

const { data: feedbackData, isLoading } = useQuery({
  queryKey: queryKeys.project.clientFeedback(props.projectId),
  queryFn: () => {
    return ProjectService.getProjectFeedback(props.projectId)
  },
})

// 创建评分项数组
const scoreItems = computed(() => [
  { label: '交付质量：', score: computed(() => Number(feedbackData.value?.satisfaction?.overall?.score || 0)), key: 'quality' },
  { label: '按时完成：', score: computed(() => Number(feedbackData.value?.satisfaction?.response?.score || 0)), key: 'time' },
  { label: '服务态度：', score: computed(() => Number(feedbackData.value?.satisfaction?.communication?.score || 0)), key: 'service' },
])

// 获取评论列表
const reviews = computed(() => feedbackData.value?.judge_list || [])

// 获取标签列表并按数量排序
const currentTags = computed(() => {
  if (!feedbackData.value?.hot_tags) {
    return []
  }

  // 将对象转换为数组格式 [{label: string, count: number}]
  const tagsArray = Object.entries(feedbackData.value.hot_tags).map(([label, count]) => ({
    label,
    count: Number(count),
  }))

  // 按数量降序排序
  return tagsArray.sort((a, b) => b.count - a.count)
})

const hasFeedback = computed(() => !!feedbackData.value?.satisfaction.overall)
</script>

<template>
  <div>
    <div
      v-if="isLoading"
      class="flex justify-center py-8"
    >
      <VanLoading type="spinner" />
    </div>

    <template v-else-if="hasFeedback">
      <!-- 总体评分区域 -->
      <div class="mb-6 flex items-center">
        <div class="mr-4 text-[64px] font-bold text-warning-500">
          {{ Number(feedbackData?.satisfaction?.overall?.score || 0).toFixed(1) }}
        </div>

        <div class="flex flex-1 justify-center">
          <div class="flex flex-col gap-2">
            <!-- 使用v-for循环渲染评分项 -->
            <div
              v-for="it of scoreItems"
              :key="it.key"
              class="gap-3 flex-center"
            >
              <span>{{ it.label }}</span>
              <div class="flex">
                <VanRate
                  allowHalf
                  color="#ffd21e"
                  :modelValue="it.score.value"
                  readonly
                  voidColor="#eee"
                  voidIcon="star"
                />
                <span class="ml-2">{{ it.score.value }} 星</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 评价标签 -->
      <div
        v-if="currentTags.length > 0"
        class="mb-4"
      >
        <h3 class="mb-2 font-medium">
          评价标签
        </h3>

        <div class="flex flex-wrap gap-2">
          <VanTag
            v-for="tag of currentTags"
            :key="tag.label"
            size="medium"
            type="primary"
          >
            {{ tag.label }} ({{ tag.count }})
          </VanTag>
        </div>
      </div>

      <!-- 评价列表 -->
      <div>
        <div
          v-for="(review, index) of reviews"
          :key="index"
        >
          <div class="mb-2 gap-3 flex-center">
            <VanImage
              class="size-10 overflow-hidden rounded-md"
              fit="cover"
              src=""
            />

            <div>
              <div>
                {{ review.user_name }}
              </div>
              <div class="text-xs text-surface-500">
                {{ review.created_at }}
              </div>
            </div>
          </div>

          <div class="text-sm">
            {{ review.comment }}
          </div>

          <VanDivider v-if="index < reviews.length - 1" />
        </div>
      </div>
    </template>

    <template v-else>
      <div class="flex flex-col items-center justify-center">
        <VanEmpty description="暂无评价" />
      </div>
    </template>
  </div>
</template>
