<script setup lang="ts">
import { ChevronDownIcon, ChevronUpIcon, CirclePlayIcon, SettingsIcon } from 'lucide-vue-next'

import { taskScanConfig, TaskScanType } from '~/enums/asset'

type TaskCard = {
  type: TaskScanType
  title: string
  data?: TaskScanConfig
  enabled: boolean
}

const props = defineProps<{
  networkDeviceDetail?: NetworkDevice
}>()

const { $toast } = useNuxtApp()

const editingNodeConfig = ref<NetworkDevice>()

const handleNodeConfigSuccess = () => {
  editingNodeConfig.value = undefined
}

const initialActiveTab = ref<TaskScanType>()

const handleEdit = (taskCard: TaskCard) => {
  initialActiveTab.value = taskCard.type
  editingNodeConfig.value = props.networkDeviceDetail
}

const handleExecute = async (taskId: TaskListItem['id']) => {
  const res = await TaskService.executeTask(taskId)

  $toast.success({
    summary: '执行信息',
    detail: res.__raw.message,
  })
}

const taskCards = computed<TaskCard[]>(() => {
  return Object.values(taskScanConfig).map((scanConfig) => {
    const data = scanConfig.value === TaskScanType.AssetScan
      ? props.networkDeviceDetail?.asset_scan_task
      : scanConfig.value === TaskScanType.ComplianceScan
        ? props.networkDeviceDetail?.baseline_scan_task
        : props.networkDeviceDetail?.vul_scan_task

    const enabled = scanConfig.value === TaskScanType.AssetScan
      ? !!props.networkDeviceDetail?.asset_scan_task?.id
      : scanConfig.value === TaskScanType.ComplianceScan
        ? !!props.networkDeviceDetail?.baseline_scan_task?.id
        : !!props.networkDeviceDetail?.vul_scan_task?.id

    return {
      type: scanConfig.value,
      title: scanConfig.label,
      data,
      enabled,
    }
  })
})

const viewLogTaskType = ref<TaskScanType[]>([])

const handleViewLog = (type: TaskScanType) => {
  const index = viewLogTaskType.value.indexOf(type)

  if (index === -1) {
    viewLogTaskType.value.push(type)
  }
  else {
    viewLogTaskType.value.splice(index, 1)
  }
}
</script>

<template>
  <div class="flex min-w-[400px] flex-col gap-5">
    <Card
      v-for="taskCard of taskCards"
      :key="taskCard.title"
      class="border border-divider !shadow-none"
    >
      <template #title>
        <div class="gap-3 flex-center">
          <div class="text-lg">
            {{ taskCard.title }}
          </div>

          <ProBtn
            v-if="taskCard.enabled"
            label="运行任务"
            severity="success"
            size="small"
            variant="text"
            @click="taskCard.data?.id && handleExecute(taskCard.data.id)"
          >
            <template #icon="{ size }">
              <CirclePlayIcon :size="size" />
            </template>
          </ProBtn>

          <Tag
            v-else
            severity="secondary"
            size="small"
            variant="simple"
          >
            未启用
          </Tag>

          <ProBtn
            class="ml-auto"
            label="配置任务"
            onlyIcon
            severity="secondary"
            size="small"
            variant="text"
            @click="handleEdit(taskCard)"
          >
            <template #icon="{ size }">
              <SettingsIcon :size="size" />
            </template>
          </ProBtn>
        </div>
      </template>

      <template #content>
        <div>
          <div class="flex items-end gap-8 pt-1">
            <div class="space-y-1">
              <div class="text-sm">
                总执行次数：<NumericDisplay>{{ taskCard.data?.total_run_count }}</NumericDisplay>
              </div>

              <div class="text-sm">
                最后执行时间：{{ taskCard.data?.last_run_at }}
              </div>
            </div>

            <div class="ml-auto">
              <Button
                label="查看执行记录"
                severity="secondary"
                size="small"
                @click="handleViewLog(taskCard.type)"
              >
                <template #icon>
                  <Component
                    :is="viewLogTaskType.includes(taskCard.type)
                      ? ChevronUpIcon
                      : ChevronDownIcon"
                    :size="15"
                  />
                </template>
              </Button>
            </div>
          </div>

          <div v-if="taskCard.data?.id && viewLogTaskType.includes(taskCard.type)">
            <Divider />

            <TaskExecutionRecord :taskId="taskCard.data.id" />
          </div>
        </div>
      </template>
    </Card>

    <AssetNetworkNodeConfig
      :initialActiveTab="initialActiveTab"
      :node="editingNodeConfig"
      @close="editingNodeConfig = undefined"
      @updateSuccess="handleNodeConfigSuccess()"
    />
  </div>
</template>
