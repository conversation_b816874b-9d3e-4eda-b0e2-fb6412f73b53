<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { ScrollTextIcon } from 'lucide-vue-next'
import { Tag } from 'primevue'

import ProBtnInfo from '~/components/pro/btn/ProBtnInfo.vue'
import { TaskStatus, taskStatusConfig } from '~/enums/asset'
import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'

import type CodeBlock from '../CodeBlock.vue'

const props = defineProps<{
  taskId: Task['id']
}>()

const logId = ref<TaskExecutionLog['id']>()

const handleViewLog = (data: TaskExecutionLog) => {
  logId.value = data.id
}

const { data: logData } = useQuery({
  queryKey: queryKeys.asset.task.executionLog(logId),
  queryFn: () => {
    if (logId.value) {
      return TaskService.getTaskExecutionLog(logId.value)
    }

    return null
  },
  refetchInterval: (data) => {
    // 仅当有日志 ID 且任务没有执行完成时，启用 2 秒轮询
    if (logId.value && data.state.data?.status !== TaskStatus.Success) {
      return 2000
    }

    return false
  },
  refetchIntervalInBackground: false,
  retry: 3,
})

const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    logId.value = undefined
  }
}

const columns: ProTableColumn<TaskExecutionLog>[] = [
  {
    field: 'create_time',
    header: '执行时间',
    valueType: ProValueType.DateTime,
  },
  {
    field: 'run_type_name',
    header: '执行类型',
  },
  {
    header: '执行状态',
    render: (data) => h(Tag, {
      severity: taskStatusConfig[data.status].severity,
      value: taskStatusConfig[data.status].label,
    }),
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', { class: 'table-action-group' }, [
      h(ProBtnInfo, {
        label: '查看日志',
        severity: 'secondary',
        size: 'small',
        onClick: () => handleViewLog(data),
      }, {
        icon: ({ size }: { size: number }) => h(ScrollTextIcon, { size }),
      }),
    ]),
  },
]

const handleAfterRender = (preElement: HTMLPreElement) => {
  preElement.scrollTop = preElement.scrollHeight
}
</script>

<template>
  <div>
    <ProTable
      :columns="columns"
      :queryParams="{ task_id: props.taskId }"
      :requestFn="(queryParams) => {
        if (queryParams.task_id) {
          return TaskService.getTaskExecutionList(queryParams)
        }

        return null
      }"
      size="small"
      :toolbar="false"
    />

    <Drawer
      header="执行日志"
      modal
      position="right"
      :style="{ width: '40vw' }"
      :visible="!!logId"
      @update:visible="handleVisibleChange"
    >
      <div class="h-full pt-1">
        <CodeBlock
          class="h-full"
          :code="logData?.result"
          lang="shell"
          showLineNumbers
          @afterRender="handleAfterRender"
        />
      </div>
    </Drawer>
  </div>
</template>
