<script setup lang="ts">
const { textColor } = useTheme()

const opacity = 0.08
</script>

<template>
  <svg
    fill="none"
    :height="480"
    viewBox="0 0 480 480"
    :width="480"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      cx="240"
      cy="240"
      :opacity="opacity"
      r="47.5"
      :stroke="textColor"
    />
    <circle
      cx="240"
      cy="240"
      :opacity="opacity - 0.02"
      r="79.5"
      :stroke="textColor"
    />
    <circle
      cx="240"
      cy="240"
      :opacity="opacity - 0.04"
      r="111.5"
      :stroke="textColor"
    />
    <circle
      cx="240"
      cy="240"
      :opacity="opacity - 0.06"
      r="143.5"
      :stroke="textColor"
    />
    <circle
      cx="240"
      cy="240"
      :opacity="opacity - 0.08"
      r="143.5"
      :stroke="textColor"
    />
    <circle
      cx="240"
      cy="240"
      :opacity="opacity - 0.1"
      r="175.5"
      :stroke="textColor"
    />
    <circle
      cx="240"
      cy="240"
      :opacity="opacity - 0.12"
      r="207.5"
      :stroke="textColor"
    />
    <circle
      cx="240"
      cy="240"
      :opacity="opacity - 0.14"
      r="239.5"
      :stroke="textColor"
    />
  </svg>
</template>
