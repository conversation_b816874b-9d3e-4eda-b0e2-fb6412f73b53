<script setup lang="ts">
import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'

interface Props {
  ipId?: SubnetIp['id']
  assetScanTaskId?: AssetScanTask['id']
}

defineProps<Props>()

const searchIp = ref<string>()

const columns: ProTableColumn<IpLog>[] = [
  {
    header: '当前 IP',
    field: 'ip.ipv4',
  },
  {
    header: 'MAC 地址',
    field: 'ip.mac',
  },
  {
    header: '旧 IP',
    field: 'old_ipv4',
  },
  {
    header: '新 IP',
    field: 'new_ipv4',
  },
  {
    header: '变更时间',
    field: 'create_time',
    valueType: ProValueType.DateTime,
  },
]
</script>

<template>
  <ProTable
    :columns="columns"
    :queryKey="queryKeys.asset.networkDevice.ipLog"
    :queryParams="{ ip_id: ipId, asset_scan_task_id: assetScanTaskId, ip: searchIp }"
    :requestFn="(queryParams) => AssetService.getIpChangeRecord(queryParams)"
  >
    <template #toolbarRight>
      <div class="gap-2 py-1 flex-center">
        <div class="ml-auto gap-4 flex-center">
          <SearchInput
            placeholder="IP"
            @update:debounceChange="searchIp = $event"
          />
        </div>
      </div>
    </template>
  </ProTable>
</template>
