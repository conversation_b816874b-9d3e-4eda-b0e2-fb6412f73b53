<script setup lang="ts">
import { BadgeAlertIcon, BadgeCheckIcon, ChevronDownIcon } from 'lucide-vue-next'
import type { TagProps } from 'primevue'

import type { VulThreatLevel } from '~/enums/security'
import { getThreatLevelLabel, getThreatLevelSeverity } from '~/enums/security'

defineProps<{
  data: Server
}>()

const getVulnerabilityCount = (data: Server) => {
  if (!Array.isArray(data.level)) {
    return 0
  }

  return data.level.reduce((sum, level) => sum + level.count, 0)
}

const getHighestThreatLevelSeverity = (data: Server): TagProps['severity'] => {
  if (Array.isArray(data.level) && data.level.length > 0) {
    const highestLevel = data.level.reduce<VulThreatLevel | null>((highest, current) => {
      const currentLevel = current.level

      if (currentLevel) {
        if (highest) {
          return currentLevel > highest ? currentLevel : highest
        }

        return currentLevel
      }

      return highest
    }, null)

    if (highestLevel) {
      return getThreatLevelSeverity(highestLevel)
    }
  }
}
</script>

<template>
  <ProPopover
    v-if="Array.isArray(data.level) && data.level.length > 0 && getVulnerabilityCount(data) > 0"
    trigger="hover"
  >
    <Tag
      class="cursor-default"
      :severity="getHighestThreatLevelSeverity(data)"
    >
      <span class="shrink-0 gap-1.5 flex-center">
        <BadgeAlertIcon :size="14" />
        {{ `${getVulnerabilityCount(data)} 个漏洞` }}
        <ChevronDownIcon :size="14" />
      </span>
    </Tag>

    <template #content>
      <div class="flex flex-col gap-2">
        <div
          v-for="level of data.level"
          :key="level"
          class="gap-2 flex-center"
        >
          <Tag :severity="getThreatLevelSeverity(level.level || undefined)">
            {{ getThreatLevelLabel(level.level || undefined) }}
          </Tag>
          <span class="tabular-nums">{{ level.count }} 个</span>
        </div>
      </div>
    </template>
  </ProPopover>

  <Tag
    v-else
    class="cursor-default"
    severity="success"
  >
    <span class="gap-1.5 flex-center">
      <BadgeCheckIcon :size="14" />
      安全
    </span>
  </Tag>
</template>
