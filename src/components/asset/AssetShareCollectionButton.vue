<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import dayjs from 'dayjs'
import { ChevronDownIcon, SquareArrowUpRightIcon } from 'lucide-vue-next'

import { AssetShareCollectionLinkStatus } from '~/enums/asset'
import { DateFormat, TooltipShowDelay } from '~/enums/common'
import { queryKeys } from '~/enums/query-key'
import { RouteKey } from '~/enums/route'

const store = useAssetInfoStore()
const { activeNetworkDeviceId } = storeToRefs(store)

const { popoverRef, popover } = usePopover()

const formValues = ref<AssetShareCollectionLinkInfoFormValues>({
  expires: null,
  status: AssetShareCollectionLinkStatus.Disable,
})

const expirationDate = computed(() => {
  return formValues.value.expires
})

const expireDay = computed(() => {
  if (!expirationDate.value) {
    return '永久有效'
  }

  const expiresDate = dayjs(expirationDate.value)
  const now = dayjs()

  const diffDays = Math.round(expiresDate.diff(now, 'day', true))

  if (diffDays < 0) {
    return '已过期'
  }

  const diffYears = expiresDate.diff(now, 'year')

  if (diffYears >= 1) {
    return `${formatDate(expirationDate.value, DateFormat.YYYY_MM_DD_HH_MM)}前有效`
  }

  const formattedExpirationDate = formatDate(expirationDate.value, DateFormat.MM_DD_HH_MM)

  if (diffDays === 0) {
    const diffHours = expiresDate.diff(now, 'hour')

    if (diffHours <= 0) {
      return `即将过期(${formattedExpirationDate}前)`
    }

    return `${diffHours} 小时有效(${formattedExpirationDate}前)`
  }

  return `${diffDays} 天有效(${formattedExpirationDate}前)`
})

const { data: linkInfo } = useQuery({
  queryKey: queryKeys.asset.networkDevice.linkInfo(activeNetworkDeviceId),
  queryFn: () => {
    if (activeNetworkDeviceId.value) {
      return AssetService.getAssetShareCollectionLinkInfo(activeNetworkDeviceId.value)
    }

    return null
  },
  select: (data) => {
    formValues.value = {
      expires: data?.expires || null,
      status: typeof data?.status === 'number' ? data.status : AssetShareCollectionLinkStatus.Disable,
    }

    return data
  },
})

/** 分享链接 key */
const shareKey = computed(() => linkInfo.value?.key)

watch(formValues, () => {
  if (activeNetworkDeviceId.value) {
    AssetService.updateAssetShareCollectionLinkInfo(activeNetworkDeviceId.value, formValues.value)
  }
}, { deep: true })

const { $toast } = useNuxtApp()

const handleCopyLink = async () => {
  if (shareKey.value) {
    const path = getRoutePath(RouteKey.资产填报, { collectionKey: shareKey.value })
    const shareUrl = `${window.location.origin}${path}`

    await copyToClipboard(shareUrl)

    $toast.success({ summary: '链接已复制' })
  }
}

const origin = ref<string>()

onMounted(() => {
  origin.value = import.meta.client ? (window as Window).location.host : undefined
})

const shareLink = computed(() => {
  if (origin.value && shareKey.value) {
    const path = getRoutePath(RouteKey.资产填报, { collectionKey: shareKey.value })

    return `${origin.value}${path}`
  }

  return ''
})

const expirationDateOptions = computed<ProMenuItem[]>(() => {
  return [
    {
      label: '自定义日期',
      command: ({ originalEvent }) => {
        originalEvent.preventDefault()
        originalEvent.stopPropagation()

        popover.value?.toggle(originalEvent)
      },
    },
    {
      label: '7 天有效',
      command: ({ originalEvent }) => {
        originalEvent.preventDefault()
        originalEvent.stopPropagation()

        formValues.value.expires = formatDate(dayjs().add(7, 'day').toDate(), DateFormat.YYYY_MM_DD_HH_MM_SS) || null
      },
    },
    {
      label: '30 天有效',
      command: ({ originalEvent }) => {
        originalEvent.preventDefault()
        originalEvent.stopPropagation()

        formValues.value.expires = formatDate(dayjs().add(30, 'day').toDate(), DateFormat.YYYY_MM_DD_HH_MM_SS) || null
      },
    },
    {
      label: '永久有效',
      command: ({ originalEvent }) => {
        originalEvent.preventDefault()
        originalEvent.stopPropagation()

        formValues.value.expires = null
      },
    },
  ]
})
</script>

<template>
  <ProPrimePopover>
    <Button
      label="资产信息采集"
      severity="secondary"
      size="small"
      variant="outlined"
    >
      <template #icon>
        <SquareArrowUpRightIcon :size="16" />
      </template>
    </Button>

    <template #content>
      <div class="max-w-[320px]">
        <div class="mb-0.5 text-base font-semibold">
          分享链接
        </div>

        <Message
          class="mb-2"
          severity="secondary"
          size="small"
          variant="simple"
        >
          拥有该链接的人可以登记资产信息
        </Message>

        <div class="gap-2 space-y-4 py-4">
          <FormItem
            v-slot="{ id }"
            label="启用链接"
            layout="horizontal"
            name="status"
          >
            <ToggleSwitch
              :inputId="id"
              :modelValue="formValues.status === AssetShareCollectionLinkStatus.Enable"
              @update:modelValue="formValues.status = $event ? AssetShareCollectionLinkStatus.Enable : AssetShareCollectionLinkStatus.Disable"
            />
          </FormItem>

          <FormItem
            label="链接有效期至"
            layout="horizontal"
            name="expiredAt"
          >
            <PopupMenu :options="expirationDateOptions">
              <Button
                :disabled="formValues.status === AssetShareCollectionLinkStatus.Disable"
                severity="secondary"
                size="small"
                variant="outlined"
              >
                <span>{{ expireDay }}</span>
                <ChevronDownIcon :size="14" />
              </Button>
            </PopupMenu>

            <Popover :ref="popoverRef">
              <div class="flex flex-col gap-2">
                <ProDatePicker
                  inline
                  :minDate="new Date()"
                  :modelValue="expirationDate ? new Date(expirationDate) : undefined"
                  showTime
                  @update:modelValue="formValues.expires = formatDate($event, DateFormat.YYYY_MM_DD_HH_MM_SS) || null"
                />

                <div class="flex justify-end">
                  <Button
                    label="确定"
                    @click.stop="popover?.hide()"
                  />
                </div>
              </div>
            </Popover>
          </FormItem>
        </div>

        <div class="gap-2 text-sm flex-center">
          <span
            v-if="shareLink"
            v-tooltip.bottom="{ value: shareLink, showDelay: TooltipShowDelay.Slow }"
            class="truncate"
          >
            {{ shareLink }}
          </span>

          <span class="shrink-0">
            <Button
              :disabled="!shareKey || formValues.status === AssetShareCollectionLinkStatus.Disable"
              label="复制链接"
              size="small"
              @click="handleCopyLink"
            />
          </span>
        </div>
      </div>
    </template>
  </ProPrimePopover>
</template>
