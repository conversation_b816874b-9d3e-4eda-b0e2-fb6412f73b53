<script setup lang="ts">
import { Tag } from 'primevue'
import { number, object } from 'valibot'

import ProBtnDelete from '~/components/pro/btn/ProBtnDelete.vue'
import ProBtnEdit from '~/components/pro/btn/ProBtnEdit.vue'
import { serverPortStatusConfig } from '~/enums/asset'
import { ProValueType } from '~/enums/pro'

import AssetFingerprintPopover from '../AssetFingerprintPopover.vue'

const props = defineProps<{
  serverId: Server['id']
}>()

const serverId = toRef(props, 'serverId')

const { tableRef, tableAction } = useTableAction()

const {
  loading,
  isFormCreate,
  isFormUpdate,
  modalVisible,
  formResolver,
  formValues,
  openCreateModal,
  openUpdateModal,
  handleClose,
  formTitleText,
  confirmBtnLabel,
  updatingItem,
  handleSubmit,
  handleModalVisibleChange,
} = useFormControl<ServerPortFormValues, ServerPort>({
  initialValues: {
    port: undefined,
  },
  btnLabel: {
    create: '确认添加 ',
    update: '保存更改',
  },
  formTitle: {
    create: '添加端口',
    update: '编辑端口',
  },
  resolver: object({
    port: number('请填写端口'),
  }),
  fetchDetail: (it) => toRaw(it),
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const payload = {
        port: states.port.value,
      }

      if (isFormCreate.value) {
        if (serverId.value) {
          await AssetService.createServerPort(serverId.value, payload)
        }
      }
      else if (isFormUpdate.value) {
        if (updatingItem.value && serverId.value) {
          await AssetService.updateServerPort(serverId.value, updatingItem.value.id, payload)
        }
      }
    }
  },
  onSubmitSuccess: () => {
    tableAction.value?.reload()
  },
})

const handleDeleteServerPort = async (serverPortId: ServerPort['id']) => {
  await AssetService.deleteServerPort(serverPortId)
  tableAction.value?.reload()
}

const columns: ProTableColumn<ServerPort>[] = [
  {
    field: 'port',
    header: '端口',
  },
  {
    field: 'fingerprint',
    header: '指纹',
    render: (row) => h(AssetFingerprintPopover, {
      value: row.fingerprint,
    }),
  },
  {
    field: 'status',
    header: '状态',
    valueType: ProValueType.Select,
    valueEnum: toValueEnum(serverPortStatusConfig),
    render: (row) => h(Tag, {
      severity: serverPortStatusConfig[row.status].severity,
      value: serverPortStatusConfig[row.status].label,
    }),
  },
  {
    field: 'last_scan_time',
    header: '上次扫描时间',
    valueType: ProValueType.DateTime,
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (row) => h('div', { class: 'table-action-group' }, [
      h(ProBtnEdit, {
        onClick: () => openUpdateModal(row),
      }),
      h(ProBtnDelete, {
        confirmMessage: `确定要删除端口「${row.port}」吗？`,
        onConfirm: () => handleDeleteServerPort(row.id),
      }),
    ]),
  },
]
</script>

<template>
  <div>
    <ProTable
      :ref="tableRef"
      :columns="columns"
      :queryParams="{ serverId }"
      :requestFn="(queryParams) => {
        if (typeof queryParams.serverId === 'number') {
          return AssetService.getServerPortList({
            server_id: queryParams.serverId,
          })
        }

        return null
      }"
    >
      <template #toolbarRight>
        <div class="flex-center">
          <div class="ml-auto">
            <Button
              label="添加端口"
              @click="openCreateModal()"
            />
          </div>
        </div>
      </template>
    </ProTable>

    <ProDialogForm
      :formActionGroupProps="{
        confirmButtonProps: {
          label: confirmBtnLabel,
        },
      }"
      :formProps="{ resolver: formResolver }"
      :initialValues="formValues"
      :loading="loading"
      :title="formTitleText"
      :visible="modalVisible"
      @cancel="handleClose"
      @submit="handleSubmit"
      @update:visible="handleModalVisibleChange"
    >
      <FormItem
        v-slot="{ id }"
        label="端口"
        name="port"
      >
        <InputNumber
          v-model="formValues.port"
          :inputId="id"
          placeholder="服务器端口"
          :useGrouping="false"
        />
      </FormItem>
    </ProDialogForm>
  </div>
</template>
