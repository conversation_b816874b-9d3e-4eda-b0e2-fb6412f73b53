<script setup lang="ts">
import { ChartLineIcon, MapPinIcon } from 'lucide-vue-next'
import { Button, Dialog, Tag } from 'primevue'
import { object } from 'valibot'

import AssetIpLog from '~/components/asset/AssetIpLog.vue'
import AssetServerInfoPopover from '~/components/asset/AssetServerInfoPopover.vue'
import ProBtn from '~/components/pro/btn/ProBtn.vue'
import ProBtnDelete from '~/components/pro/btn/ProBtnDelete.vue'
import ProBtnEdit from '~/components/pro/btn/ProBtnEdit.vue'
import { assetConfig, AssetType, subnetIpStatusConfig } from '~/enums/asset'
import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'
import { schemaOptionalString } from '~/types/schema'

import type { ServerInfoFieldsetModel } from '../server/AssetServerInfoFieldset.vue'

const props = withDefaults(
  defineProps<{
    subnetId?: Subnet['id']
    serverId?: Server['id']
    /** 是否隐藏服务器信息 */
    hideServer?: boolean
    /** 是否可编辑 */
    editable?: boolean
  }>(),
  { editable: true },
)

const { subnetId, serverId, hideServer } = toRefs(props)

const { tableRef, tableAction } = useTableAction()

const serverInfo = ref<ServerInfoFieldsetModel>()
const canEditServerInfo = ref(false)

const store = useAssetInfoStore()
const { searchIp } = storeToRefs(store)

const {
  loading,
  isFormCreate,
  isFormUpdate,
  modalVisible,
  formResolver,
  formValues,
  openCreateModal,
  openUpdateModal,
  handleClose,
  formTitleText,
  confirmBtnLabel,
  updatingItem,
  handleSubmit,
  handleModalVisibleChange,
} = useFormControl<SubnetIpFormValues, SubnetIp>({
  btnLabel: {
    create: '确认添加',
    update: '保存更改',
  },
  formTitle: {
    create: '添加 IP',
    update: '更新 IP',
  },
  resolver: object({
    ipv4: schemaNotEmptyString('请填写 IPv4 地址'),
    ipv6: schemaOptionalString(),
    mac: schemaOptionalString(),
  }),
  fetchDetail: (it) => {
    const raw = toRaw(it)

    serverInfo.value = {
      serverTemplateId: raw.server?.template_id,
      serverId: raw.server?.id,
    }

    canEditServerInfo.value = !raw.server?.id

    return raw
  },
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const payload = {
        ipv4: states.ipv4.value,
        ipv6: states.ipv6?.value,
        mac: states.mac?.value,
        server_id: serverInfo.value?.serverId,
      }

      if (isFormCreate.value) {
        if (subnetId.value) {
          await AssetService.createSubnetIp(subnetId.value, payload)
        }
      }
      else if (isFormUpdate.value) {
        if (updatingItem.value) {
          await AssetService.updateSubnetIp(updatingItem.value.id, payload)
        }
      }
    }
  },
  onSubmitSuccess: () => {
    tableAction.value?.reload()
  },
})

const handleDeleteSubnetIp = async (subnetIpId: SubnetIp['id']) => {
  await AssetService.deleteSubnetIp(subnetIpId)
  tableAction.value?.reload()
}

const queryParams = computed<SubnetIpListQuery>(() => ({
  subnet_id: subnetId.value,
  server_id: serverId.value,
  search: searchIp.value,
}))

const ipLogVisible = ref(false)
const selectedIpId = ref<SubnetIp['id']>()

const columns = computed<ProTableColumn<SubnetIp>[]>(() => [
  {
    field: 'ipv4',
    header: 'IPv4',
  },
  {
    field: 'ipv6',
    header: 'IPv6',
    render: (data) => data.ipv6 || '-',
  },
  {
    field: 'mac',
    header: 'MAC 地址',
    render: (data) => data.mac || '-',
  },
  {
    field: 'status',
    header: '状态',
    render: (data) => {
      if (!data.status) {
        return null
      }

      return h(Tag, {
        severity: subnetIpStatusConfig[data.status].severity,
        value: subnetIpStatusConfig[data.status].label,
      })
    },
  },
  {
    field: 'asset',
    header: `所属${assetConfig[AssetType.Server].label}`,
    hideInTable: hideServer.value,
    render: (data) => {
      return h(AssetServerInfoPopover, {
        server: data.server,
      })
    },
  },
  {
    field: 'create_time',
    header: '首次发现时间',
    valueType: ProValueType.DateTime,
  },
  {
    field: 'last_scan_time',
    header: '上次扫描时间',
    valueType: ProValueType.DateTime,
  },
  {
    hideInTable: !props.editable,
    class: 'min-w-table-actions',
    frozen: true,
    alignFrozen: 'right',
    valueType: ProValueType.ActionGroup,
    render: (data: SubnetIp) => {
      return h('div', { class: 'table-action-group' }, [
        h(ProBtn, {
          label: 'IP 变更记录',
          onlyIcon: true,
          size: 'small',
          variant: 'text',
          onClick: () => {
            selectedIpId.value = data.id
            ipLogVisible.value = true
          },
        }, {
          icon: ({ size }: { size: number }) => h(ChartLineIcon, { size }),
        }),

        h(ProBtnEdit, {
          onlyIcon: true,
          onClick: () => openUpdateModal(data),
        }),

        h(ProBtnDelete, {
          onlyIcon: true,
          confirmMessage: `确定要删除 IP「${data.ipv4}」吗？`,
          onConfirm: () => handleDeleteSubnetIp(data.id),
        }),
      ])
    },
  },
])
</script>

<template>
  <div class="h-full">
    <ProTable
      :ref="tableRef"
      :columns="columns"
      :queryKey="queryKeys.asset.subnet.ip.all(subnetId)"
      :queryParams="queryParams"
      :requestFn="(queryParams: SubnetIpListQuery) => {
        return AssetService.getSubnetIpList(queryParams)
      }"
      scrollable
      scrollHeight="flex"
    >
      <template #toolbarRight>
        <div class="gap-4 flex-center">
          <SearchInput
            :modelValue="searchIp"
            placeholder="搜索 IP"
            size="small"
            @update:debounceChange="searchIp = $event"
          >
            <template #searchIcon="{ iconSize }">
              <MapPinIcon :size="iconSize" />
            </template>
          </SearchInput>

          <div
            v-if="editable"
            class="ml-auto"
          >
            <Button
              label="添加 IP"
              @click="openCreateModal()"
            />
          </div>
        </div>
      </template>
    </ProTable>

    <ProDialogForm
      :dialogProps="{
        class: 'dialog-form-wider',
      }"
      :formActionGroupProps="{
        confirmButtonProps: {
          label: confirmBtnLabel,
        },
      }"
      :formProps="{ resolver: formResolver }"
      :initialValues="formValues"
      :loading="loading"
      :title="formTitleText"
      :visible="modalVisible"
      @cancel="handleClose"
      @submit="handleSubmit"
      @update:visible="handleModalVisibleChange"
    >
      <div class="flex flex-col gap-form-field">
        <div class="grid grid-cols-2 gap-form-field">
          <FormItem
            v-slot="{ id }"
            label="IPv4"
            name="ipv4"
            required
          >
            <InputText
              :id="id"
              v-model="formValues.ipv4"
              placeholder="输入 IPv4 地址"
            />
          </FormItem>

          <FormItem
            v-slot="{ id }"
            label="IPv6"
            name="ipv6"
          >
            <InputText
              :id="id"
              v-model="formValues.ipv6"
              placeholder="输入 IPv6 地址"
            />
          </FormItem>

          <FormItem
            v-slot="{ id }"
            label="MAC 地址"
            name="mac"
          >
            <InputText
              :id="id"
              v-model="formValues.mac"
              placeholder="输入 MAC 地址"
              showClear
            />
          </FormItem>
        </div>

        <AssetServerInfoFieldset
          v-model="serverInfo"
          :disabled="!canEditServerInfo"
          disablePort
        />
      </div>
    </ProDialogForm>

    <Dialog
      v-model:visible="ipLogVisible"
      class="dialog-full"
      dismissableMask
      header="IP 变更记录"
      modal
    >
      <AssetIpLog
        v-if="ipLogVisible"
        :ipId="selectedIpId"
      />
    </Dialog>
  </div>
</template>
