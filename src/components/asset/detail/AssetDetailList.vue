<script setup lang="ts">
import AssetFieldValue from '~/components/asset/field/AssetFieldValue.vue'

const props = defineProps<{
  assetFields: BasicAssetDetail['asset']
  templateFields?: AssetField[]
  assetTemplateId?: NonNullable<BasicAssetDetail['template']>['id']
}>()

const assetTemplateId = toRef(props, 'assetTemplateId')

const { data: assetTemplateFields } = useQueryTemplateFields(assetTemplateId)

const fields = computed(() => {
  // 优先使用 templateFields（如果存在的话）
  if (Array.isArray(props.templateFields) && props.templateFields.length > 0) {
    return props.templateFields
  }

  // 否则使用从 API 获取的模板字段（如果存在的话）
  return assetTemplateFields.value || []
})
</script>

<template>
  <template v-if="fields.length > 0">
    <LabelValueList
      :data="fields.map((field) => ({
        label: field.name,
        value: () => h(AssetFieldValue, {
          fieldType: field.field_type?.id,
          options: field.kwargs?.options,
          value: assetFields?.[field.field_name],
        }),
      }))"
    />
  </template>

  <div v-else>
    该资产未记录服务器信息
  </div>
</template>
