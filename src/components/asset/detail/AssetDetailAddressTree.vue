<script setup lang="ts">
import { object } from 'valibot'

import type ProPopover from '~/components/pro/ProPopover.vue'
import { SubnetIpStatus, subnetIpStatusConfig } from '~/enums/asset'
import { ProMenuActionType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'

const { isDarkMode } = useTheme()

const activeSubnet = defineModel<Subnet>()

const { $confirm } = useNuxtApp()

const store = useAssetInfoStore()
const { activeNetworkDeviceId } = storeToRefs(store)

const contextMenuSubnet = ref<SubnetTreeNode>()

const emit = defineEmits<{
  listChange: [subnetList: Subnet[]]
}>()

const {
  data: subnetListData,
  isLoading: subnetListLoading,
  refresh: refreshSubnetList,
  loadMore,
  hasMore,
} = useLoadMore<Subnet, { ipSegment: Subnet['ip_segment'] | undefined }>({
  queryKey: queryKeys.asset.subnet.all({ networkDeviceId: activeNetworkDeviceId }),
  queryFn: ({ page, pageSize, ipSegment }) => {
    if (typeof activeNetworkDeviceId.value === 'number') {
      return AssetService.getSubnetList({
        network_equipment_id: activeNetworkDeviceId.value,
        ip_segment: ipSegment,
        page,
        page_size: pageSize,
      })
    }

    return null
  },
  pageSize: 20,
  onDataChange: (data) => {
    emit('listChange', data)
  },
})

const subnetSearch = ref<Subnet['ip_segment']>()

watch(subnetSearch, (newSearch) => {
  refreshSubnetList({ ipSegment: newSearch })
})

const subnetTreeList = computed<SubnetTreeNode[] | undefined>(() => subnetListData.value?.map((it) => ({
  ...it,
  key: it.id.toString(),
  label: it.ip_segment,
})))

const {
  isFormCreate,
  isFormUpdate,
  formValues,
  updatingItem,
  modalVisible,
  loading,
  confirmBtnLabel,
  formTitleText,
  formResolver,
  openCreateModal,
  openUpdateModal,
  handleClose,
  handleSubmit,
  handleModalVisibleChange,
} = useFormControl<SubnetFormValues, Subnet>({
  initialValues: {
    network_equipment_id: activeNetworkDeviceId.value,
    ip_segment: '',
  },
  resolver: object({
    ip_segment: schemaNotEmptyString('请输入子网 IP 段'),
  }),
  btnLabel: {
    create: '确认添加',
    update: '保存更改',
  },
  formTitle: {
    create: '添加子网',
    update: '编辑子网',
  },
  fetchDetail: () => contextMenuSubnet.value as SubnetFormValues,
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      if (isFormCreate.value) {
        await AssetService.createSubnet({
          ip_segment: states.ip_segment.value,
          network_equipment_id: formValues.value.network_equipment_id,
        })
      }
      else if (isFormUpdate.value) {
        const id = updatingItem.value?.id

        if (id) {
          await AssetService.updateSubnet(id, {
            ip_segment: states.ip_segment.value,
            network_equipment_id: formValues.value.network_equipment_id,
          })
        }
      }
    }
  },
  onSubmitSuccess: () => {
    refreshSubnetList()
  },
})

watch(subnetTreeList, (newTree) => {
  if (!activeSubnet.value) {
    const firstSubnet = newTree?.[0]

    if (firstSubnet) {
      activeSubnet.value = firstSubnet
    }
  }
}, { immediate: true })

const selectionKeys = computed(() => ({ [`${activeSubnet.value?.id}`]: true }))

const handleTreeNodeSelect = (node: SubnetTreeNode) => {
  activeSubnet.value = node
}

const menuOptions: ProMenuItem[] = [
  {
    label: '编辑',
    actionType: ProMenuActionType.Edit,
    command: () => {
      if (contextMenuSubnet.value) {
        openUpdateModal(contextMenuSubnet.value)
      }
    },
  },
  {
    label: '删除',
    actionType: ProMenuActionType.Delete,
    command: async () => {
      const subnetId = contextMenuSubnet.value?.id

      if (subnetId) {
        $confirm.dialog({
          header: '确定删除子网吗？',
          accept: async () => {
            await AssetService.deleteSubnet(subnetId)
            refreshSubnetList()
          },
        })
      }
    },
  },
]

const { dualMenuRef, dualMenu } = useDualMenu()

const handleOpenMenu = (ev: MouseEvent, nodeData: SubnetTreeNode) => {
  dualMenu.value?.showMenu(ev)
  contextMenuSubnet.value = nodeData
}

const viewedSubnet = ref<SubnetTreeNode>()

const { refKey: proPopoverRefKey, ref: proPopoverRef } = useRef<InstanceType<typeof ProPopover>>()

const handleMouseEnter = (node: SubnetTreeNode, ev: MouseEvent) => {
  if (ev.target instanceof HTMLElement) {
    viewedSubnet.value = node
    proPopoverRef.value?.show(ev.target)
  }
}

const handleMouseLeave = (ev: MouseEvent) => {
  if (ev.target instanceof HTMLElement) {
    proPopoverRef.value?.hoverLeave(ev)
  }
}

const skeletonCount = 15
</script>

<template>
  <div class="h-full overflow-hidden">
    <div class="flex h-full flex-col gap-2">
      <div class="space-y-2 pr-in-splitter">
        <div class="justify-between gap-2 flex-center">
          <h2 class="text-xl font-bold">
            子网
          </h2>

          <Button
            label="添加子网"
            severity="secondary"
            @click="openCreateModal()"
          />
        </div>

        <SearchInput
          fluid
          placeholder="搜索子网 IP 段"
          size="small"
          @update:debounceChange="subnetSearch = $event"
        />
      </div>

      <ScrollPanel
        class="min-h-0 flex-1 pr-in-splitter"
        :dt="{
          bar: {
            background: isDarkMode ? '{surface.700}' : '{surface.200}',
          },
        }"
      >
        <div class="pb-2">
          <div
            v-if="subnetListLoading && !subnetTreeList?.length"
            class="space-y-2.5"
          >
            <div
              v-for="i of skeletonCount"
              :key="i"
              class="justify-center flex-center"
              :style="{
                opacity: 1 - i / skeletonCount,
              }"
            >
              <Skeleton height="1.5rem" />
            </div>
          </div>

          <template v-else>
            <div v-if="subnetTreeList?.length">
              <Tree
                :pt="{
                  root: {
                    class: '!p-0',
                  },
                  nodeToggleButton: {
                    class: '!hidden',
                  },
                  nodeContent: {
                    class: 'group/node-content h-full !h-[31px] !py-0',
                  },
                  nodeLabel: {
                    class: 'size-full min-w-0 flex-center',
                  },
                }"
                :selectionKeys="selectionKeys"
                selectionMode="single"
                :value="subnetTreeList"
                @nodeSelect="handleTreeNodeSelect"
              >
                <template #default="{ node }: { node: SubnetTreeNode }">
                  <span class="size-full gap-1 flex-center">
                    <span
                      class="shrink-0 rounded p-1.5 inline-flex-center hover:bg-surface-300/80 dark:hover:bg-surface-700/80"
                      @mouseenter="handleMouseEnter(node, $event)"
                      @mouseleave="handleMouseLeave($event)"
                    >
                      <span
                        class="inline-block size-1.5 rounded-full"
                        :class="{
                          'bg-success-500': node.status === SubnetIpStatus.在线,
                          'bg-danger-500': node.status === SubnetIpStatus.离线,
                          'bg-surface-500': node.status === SubnetIpStatus.未知,
                        }"
                      />
                    </span>

                    <span
                      class="min-w-0 flex-1 flex-center"
                      @contextmenu.stop.prevent="handleOpenMenu($event, node)"
                    >
                      <span class="h-[31px] flex-1 truncate leading-[31px]">
                        {{ node.ip_segment }}
                      </span>

                      <span class="ml-auto hidden shrink-0 group-hover/node-content:inline-flex">
                        <ProBtnMore
                          :dt="{
                            sm: {
                              padding: {
                                x: '0.1rem',
                                y: '0.1rem',
                              },
                            },
                          }"
                          onlyIcon
                          size="small"
                          variant="text"
                          @click.stop="handleOpenMenu($event, node)"
                        />
                      </span>
                    </span>
                  </span>
                </template>
              </Tree>

              <Button
                v-if="hasMore"
                class="mt-2"
                fluid
                label="加载更多"
                :loading="subnetListLoading"
                severity="secondary"
                size="small"
                @click="loadMore()"
              />
            </div>

            <div
              v-else
              class="justify-center flex-center"
            >
              <p class="p-5 text-sm text-secondary">
                暂无子网数据
              </p>
            </div>
          </template>
        </div>
      </ScrollPanel>
    </div>

    <ProPopover
      :ref="proPopoverRefKey"
      position="bottom-start"
      trigger="hover"
    >
      <template #content>
        <div>
          <div class="space-y-3 text-sm">
            <p v-if="viewedSubnet">
              状态：<Tag
                :severity="
                  viewedSubnet.status === SubnetIpStatus.在线 ? 'success' : viewedSubnet.status === SubnetIpStatus.离线 ? 'danger' : 'secondary'
                "
                :value="subnetIpStatusConfig[viewedSubnet.status].label"
              />
            </p>

            <ul class="space-y-1">
              <li>在线设备：<span class="font-medium text-success-500"><NumericDisplay :value="String(viewedSubnet?.count.online || 0)" /></span></li>
              <li>离线设备：<span class="font-medium text-danger-500"><NumericDisplay :value="String(viewedSubnet?.count.offline || 0)" /></span></li>
              <li>未知设备：<span class="font-medium text-surface-500"><NumericDisplay :value="String(viewedSubnet?.count.unknown || 0)" /></span></li>
            </ul>

            <p>
              最近一次扫描时间：{{ viewedSubnet?.last_scan_time || '-' }}
            </p>
          </div>
        </div>
      </template>
    </ProPopover>

    <DualMenu
      :ref="dualMenuRef"
      :menuItems="menuOptions"
    />

    <ProDialogForm
      :formActionGroupProps="{
        confirmButtonProps: {
          label: confirmBtnLabel,
        },
      }"
      :formProps="{ resolver: formResolver }"
      :initialValues="formValues"
      :loading="loading"
      :title="formTitleText"
      :visible="modalVisible"
      @cancel="handleClose"
      @submit="handleSubmit"
      @update:visible="handleModalVisibleChange"
    >
      <FormItem
        v-slot="{ id }"
        label="子网 IP 段"
        name="ip_segment"
      >
        <InputText
          :id="id"
          v-model="formValues.ip_segment"
          placeholder="请输入子网 IP 段，如 ***********/24"
        />
      </FormItem>
    </ProDialogForm>
  </div>
</template>
