<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import type { AssetType } from '~/enums/asset'
import { queryKeys } from '~/enums/query-key'

const props = defineProps<{
  title?: string
  assetType: AssetType
}>()

const assetType = toRef(props, 'assetType')

const { data: templateList, isLoading } = useQuery({
  queryKey: queryKeys.asset.template.all(assetType),
  queryFn: () => AssetService.getAssetTemplateList({
    asset_type: assetType.value,
  }),
})

const store = useAssetInfoStore()
const { activeTemplateMap, activeTemplateId } = storeToRefs(store)

const handleTemplateSelect = (template: AssetTemplateListItem | undefined) => {
  activeTemplateMap.value = {
    ...activeTemplateMap.value,
    [assetType.value]: template,
  }
}
</script>

<template>
  <div class="flex h-full">
    <div class="flex w-full flex-col gap-3">
      <div v-if="!!title">
        <h2 class="text-lg font-bold">
          {{ title }}
        </h2>
      </div>

      <ScrollPanel class="min-h-0 flex-1 overflow-y-auto pr-in-splitter">
        <AssetTplList
          :activeTemplateId="activeTemplateId"
          :loading="isLoading"
          showUnknown
          :templateList="templateList?.list"
          @select="handleTemplateSelect"
        />
      </ScrollPanel>
    </div>
  </div>
</template>
