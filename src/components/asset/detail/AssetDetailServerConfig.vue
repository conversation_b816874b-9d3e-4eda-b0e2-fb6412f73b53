<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { number, object } from 'valibot'

import { Protocol, protocolConfig } from '~/enums/asset'
import { queryKeys } from '~/enums/query-key'

const enum StepType {
  SSH = 1,
  Baseline = 2,
}

const { $toast } = useNuxtApp()

const props = defineProps<{
  serverId: Server['id']
}>()

const serverId = toRef(props, 'serverId')

const { data: serverConfig, isLoading } = useQuery({
  queryKey: queryKeys.asset.server.config.all(serverId),
  queryFn: () => AssetService.getServerConfig(serverId.value),
})

const serverConfigId = computed(() => serverConfig.value?.id)

const {
  isFormCreate,
  isFormUpdate,
  formValues,
  formResolver,
  handleSubmit,
  forceUpdateKey,
  forceUpdateForm,
} = useFormControl<ServerConfigFormValues, ServerConfigFormValues>({
  resolver: object({
    // port: number('请输入端口'),
    protocol: number('请输入协议'),
    username: schemaNotEmptyString('请输入账号'),
    password: schemaNotEmptyString('请输入密码'),
  }),
  resetOnClose: false,
  initialFormEditState: serverConfigId.value ? 'update' : 'create',
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const payload: ServerConfigFormValues = {
        port: states.port?.value,
        protocol: states.protocol?.value,
        username: states.username?.value,
        password: states.password?.value,
      }

      if (isFormCreate.value) {
        await AssetService.createServerConfig(serverId.value, payload)
      }
      else if (isFormUpdate.value) {
        if (serverConfigId.value) {
          await AssetService.updateServerConfig(serverConfigId.value, payload)
        }
      }

      $toast.success({
        summary: '保存成功',
        detail: 'SSH 配置已保存',
      })
    }
  },
})

// 在表单初始填写时，切换协议时自动设置端口
watch(() => formValues.value?.protocol, (val) => {
  if (!formValues.value?.port) {
    if (val === Protocol.SSH) {
      formValues.value.port = 22
    }
    else if (val === Protocol.WMIC) {
      formValues.value.port = 5985
    }

    forceUpdateForm()
  }
})

const complianceTemplateId = ref<AssetTemplate['id']>()

watch(serverConfig, (newConfig) => {
  complianceTemplateId.value = newConfig?.template_baseline_id

  formValues.value = newConfig || {}

  forceUpdateForm()
}, { immediate: true })

const savingBaseline = ref(false)

const handleSaveBaseline = async () => {
  if (serverConfigId.value && complianceTemplateId.value) {
    try {
      savingBaseline.value = true

      await AssetService.updateServerConfig(serverConfigId.value, {
        template_baseline_id: complianceTemplateId.value,
      })

      $toast.success({
        summary: '保存成功',
        detail: '基线配置已保存',
      })
    }
    finally {
      savingBaseline.value = false
    }
  }
}
</script>

<template>
  <div>
    <div class="mx-auto w-1/2">
      <Stepper :value="StepType.SSH">
        <StepList>
          <Step :value="StepType.SSH">
            配置 SSH
          </Step>
        </StepList>

        <StepPanels>
          <StepPanel :value="StepType.SSH">
            <div class="flex justify-center">
              <Form
                :key="forceUpdateKey"
                :initialValues="toRaw(formValues)"
                :resolver="formResolver"
                @submit="handleSubmit"
              >
                <div class="flex min-w-96 flex-col gap-form-field">
                  <FormItem
                    v-slot="{ id }"
                    label="协议"
                    name="protocol"
                    required
                  >
                    <ProSelect
                      v-model="formValues.protocol"
                      :labelId="id"
                      :options="Object.values(protocolConfig).map(item => ({
                        label: item.label,
                        value: item.value,
                      }))"
                      placeholder="选择协议"
                    />
                  </FormItem>

                  <FormItem
                    v-slot="{ id }"
                    label="端口"
                    name="port"
                    required
                  >
                    <InputNumber
                      v-model="formValues.port"
                      fluid
                      :inputId="id"
                      placeholder="端口"
                      :useGrouping="false"
                    />
                  </FormItem>

                  <FormItem
                    v-slot="{ id }"
                    label="账号"
                    name="username"
                    required
                  >
                    <InputText
                      :id="id"
                      v-model="formValues.username"
                      placeholder="输入账号"
                    />
                  </FormItem>

                  <FormItem
                    v-slot="{ id }"
                    label="密码"
                    name="password"
                    required
                  >
                    <Password
                      v-model="formValues.password"
                      :feedback="false"
                      fluid
                      :inputId="id"
                      placeholder="输入密码"
                      toggleMask
                    />
                  </FormItem>

                  <FormActionGroup
                    :cancelButtonProps="false"
                    :confirmButtonProps="{
                      label: '保存 SSH 配置',
                    }"
                    :loading="isLoading"
                  />
                </div>
              </Form>
            </div>
          </StepPanel>
        </StepPanels>
      </Stepper>

      <Stepper :value="StepType.Baseline">
        <StepList>
          <Step :value="StepType.Baseline">
            配置基线模板
          </Step>
        </StepList>

        <StepPanels>
          <StepPanel :value="StepType.Baseline">
            <div class="flex w-full justify-center">
              <div class="flex w-96 flex-col gap-5">
                <FormItem
                  label="基线模板"
                  required
                >
                  <FormItemComplianceTemplateSelect
                    v-model="complianceTemplateId"
                    placeholder="选择基线模板"
                    showClear
                  />
                </FormItem>

                <div class="flex justify-end">
                  <Button
                    label="保存基线配置"
                    :loading="savingBaseline"
                    @click="handleSaveBaseline()"
                  />
                </div>
              </div>
            </div>
          </StepPanel>
        </StepPanels>
      </Stepper>
    </div>
  </div>
</template>
