<script setup lang="ts">
const activeSubnet = ref<Subnet>()
const activeSubnetId = computed(() => activeSubnet.value?.id)

const skeletonCount = 18

const isTreeEmpty = ref(false)
</script>

<template>
  <ProSplitter
    :left="{ size: 20, minSize: 15 }"
    leftPanelContentClass="!p-0"
    :right="{ minSize: 50 }"
  >
    <template #left>
      <AssetDetailAddressTree
        v-model="activeSubnet"
        @listChange="isTreeEmpty = $event.length === 0"
      />
    </template>

    <template #right>
      <AssetDetailAddressList
        v-if="activeSubnetId"
        :subnetId="activeSubnetId"
      />

      <template v-else>
        <div v-if="isTreeEmpty">
          <div class="justify-center p-10 flex-center text-secondary">
            暂无数据
          </div>
        </div>

        <div
          v-else
          class="space-y-3 px-5 py-2"
        >
          <div
            v-for="i of skeletonCount"
            :key="i"
          >
            <Skeleton
              height="1.8rem"
              :style="{ opacity: 1 - i / skeletonCount }"
            />
          </div>
        </div>
      </template>
    </template>
  </ProSplitter>
</template>
