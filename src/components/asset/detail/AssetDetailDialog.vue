<script setup lang="ts">
import { get } from 'lodash-es'
import { Tag } from 'primevue'

import AssetFingerprintPopover from '~/components/asset/AssetFingerprintPopover.vue'
import type { AssetType } from '~/enums/asset'
import { assetConfig, subnetIpStatusConfig } from '~/enums/asset'

const props = defineProps<{
  assetType?: AssetType
  assetId?: BasicAssetDetail['id']
}>()

const assetType = toRef(props, 'assetType')
const assetId = toRef(props, 'assetId')

const emit = defineEmits<{
  close: []
}>()

const { data: assetDetail, refetch: fetchAssetDetail } = useQueryAssetDetail({
  assetType,
  assetId,
})

const handleSaveSuccess = () => {
  fetchAssetDetail()
}

const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    emit('close')
  }
}

const dialogTitle = computed(() => {
  if (props.assetType) {
    const assetTypeName = assetConfig[props.assetType].label
    const assetName = assetDetail.value?.asset?.name

    return `${assetTypeName}${assetName ? `【${assetName}】` : ''}`
  }

  return '资产详情'
})
</script>

<template>
  <Dialog
    class="dialog-full"
    dismissableMask
    :draggable="false"
    :header="dialogTitle"
    modal
    :visible="!!assetId && !!assetType"
    @update:visible="handleVisibleChange($event)"
  >
    <div class="space-y-5">
      <div>
        <h3 class="pb-2 text-lg font-bold">
          所属终端设备
        </h3>

        <div>
          <AssetServerInfoPopover
            :assetType="assetType"
            :server="get(assetDetail, 'server')"
          />
        </div>
      </div>

      <div>
        <h3 class="pb-2 text-lg font-bold">
          端口
        </h3>

        <div>
          <LabelValueList
            :data="[
              {
                label: '端口号',
                value: assetDetail?.port?.port,
              },
              {
                label: '端口指纹',
                value: assetDetail?.port?.fingerprint ? () => h(AssetFingerprintPopover, {
                  value: assetDetail?.port?.fingerprint,
                }) : null,
              },
              {
                label: '状态',
                value: assetDetail?.port?.status
                  ? () => h(Tag, {
                    severity: subnetIpStatusConfig[assetDetail!.port!.status].severity,
                  }, () => subnetIpStatusConfig[assetDetail!.port!.status].label)
                  : null,
              },
            ]"
          />
        </div>
      </div>

      <div>
        <h3 class="gap-2 pb-2 text-lg font-bold flex-center">
          资产信息

          <span>
            <AssetInfoEdit
              :assetId="assetId"
              :assetType="assetType"
              @saveSuccess="handleSaveSuccess"
            />
          </span>
        </h3>

        <AssetDetailList
          :assetFields="assetDetail?.asset"
          :assetTemplateId="assetDetail?.template_id"
        />
      </div>
    </div>
  </Dialog>
</template>
