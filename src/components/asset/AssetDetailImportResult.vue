<script setup lang="ts">
import { CheckIcon, XIcon } from 'lucide-vue-next'
import type { TreeExpandedKeys } from 'primevue'

import type { AssetImportResult, AssetImportResultNode } from '~/types/asset'

const props = defineProps<{
  resultData: AssetImportResult
}>()

type ResultTreeNode = TreeNodeWrap<{ data: AssetImportResultNode & { itemIcon: Component | null } }>

const treeData = computed<ResultTreeNode[]>(() => {
  // 定义递归转换函数
  const convertToTreeNode = (nodes: AssetImportResult, nodePath?: string): ResultTreeNode[] => {
    return nodes.map((node, idx) => {
      // 根据状态设置不同的图标
      let itemIcon: Component | null = null

      if (node.status === 'success') {
        itemIcon = CheckIcon
      }
      else if (node.status === 'error') {
        itemIcon = XIcon
      }

      const key = `${nodePath ?? ''}-${idx}`

      const treeNode: ResultTreeNode = {
        key,
        label: node.message,
        data: {
          status: node.status,
          message: node.message,
          itemIcon,
        },
        children: node.children ? convertToTreeNode(node.children, key) : undefined,
      }

      return treeNode
    })
  }

  return convertToTreeNode(props.resultData)
})

// 收集所有节点的key，用于默认展开所有节点
const expandedKeys = computed<TreeExpandedKeys>(() => {
  const keys: TreeExpandedKeys = {}

  const collectKeys = (nodes: ResultTreeNode[]) => {
    for (const node of nodes) {
      keys[node.key] = true

      if (node.children && node.children.length > 0) {
        collectKeys(node.children)
      }
    }
  }

  collectKeys(treeData.value)

  return keys
})
</script>

<template>
  <Tree
    class="!p-0"
    :expandedKeys="expandedKeys"
    :value="treeData"
  >
    <template #default="slotProps: { node: ResultTreeNode }">
      <span
        class="size-full gap-2 flex-center"
      >
        <Component
          :is="slotProps.node.data.itemIcon"
          v-if="slotProps.node.data.itemIcon"
          :class="{
            'text-success-500': slotProps.node.data.status === 'success',
            'text-danger-500': slotProps.node.data.status === 'error',
          }"
          :size="15"
        />

        <span
          class="flex-1 truncate leading-[31px]"
          :class="{
            'text-success-500': slotProps.node.data.status === 'success',
            'text-danger-500': slotProps.node.data.status === 'error',
          }"
        >{{ slotProps.node.label }}</span>
      </span>
    </template>
  </Tree>
</template>
