<script setup lang="ts">
import { assetDetailConfig, AssetType } from '~/enums/asset'

defineProps<{
  server?: ServerInfo
}>()

const store = useAssetInfoStore()

const handleViewServerDetail = (serverId: Server['id']) => {
  store.setViewedServer({
    id: serverId,
  })
}
</script>

<template>
  <ProPopover
    v-if="server"
    :showDelay="500"
    trigger="hover"
  >
    <Button
      :label="server.name || server.ip_list.at(0)?.ipv4"
      severity="info"
      size="small"
      variant="text"
      @click="handleViewServerDetail(server.id)"
    >
      <template #icon>
        <Component
          :is="assetDetailConfig[AssetType.Server].icon"
          class="shrink-0"
          :size="13"
        />
      </template>
    </Button>

    <template #content>
      <div
        v-for="(ip, idx) of server.ip_list"
        :key="ip.id"
        class="space-y-1"
      >
        <ul class="space-y-2">
          <li>
            <Tag
              class="!font-medium"
              :value="`IPv4: ${ip.ipv4}`"
            />
          </li>

          <li>
            <Tag
              class="!font-medium"
              :value="`IPv6: ${ip.ipv6 || '-'}`"
            />
          </li>

          <li>
            <Tag
              class="!font-medium"
              :value="`MAC: ${ip.mac || '-'}`"
            />
          </li>
        </ul>

        <Divider v-if="idx !== server.ip_list.length - 1" />
      </div>
    </template>
  </ProPopover>

  <span v-else>
    -
  </span>
</template>
