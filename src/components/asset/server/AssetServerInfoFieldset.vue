<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { debounce } from 'lodash-es'

import { assetConfig, AssetType } from '~/enums/asset'
import { queryKeys } from '~/enums/query-key'

defineProps<{
  /** 是否禁用整个表单编辑 */
  disabled?: boolean
  /** 是否禁用端口选择 */
  disablePort?: boolean
}>()

export interface ServerInfoFieldsetModel {
  serverTemplateId?: Server['template_id']
  serverId?: Server['id']
  portId?: ServerPort['id']
}

const serverInfo = defineModel<ServerInfoFieldsetModel>({
  default: () => ({
    serverTemplateId: undefined,
    serverId: undefined,
    portId: undefined,
  }),
})

const store = useAssetInfoStore()
const { activeNetworkDeviceId } = storeToRefs(store)

/** 用户选择的服务器类型 */
const selectedServer = computed(() => serverInfo.value.serverTemplateId)

/** 用户搜索的服务器名称 */
const searchServerName = ref<NonNullable<Server['asset']>['name']>()

/** 用户选择的服务器 */
const selectedServerId = computed(() => serverInfo.value.serverId)

/** 用户搜索的服务器端口 */
const searchServerPort = ref<string>()

watchEffect(() => {
  // 当服务器类型未选择时，清空服务器和端口信息
  if (!selectedServer.value) {
    serverInfo.value = {
      serverId: undefined,
      portId: undefined,
    }

    searchServerName.value = undefined
    searchServerPort.value = undefined
  }
})

// 服务器列表查询
const { data: serversData, isLoading: isServersLoading } = useQuery({
  queryKey: queryKeys.asset.server.all({ serverTemplateId: selectedServer, networkId: activeNetworkDeviceId }),
  queryFn: () => {
    if (selectedServer.value) {
      // 跟据当前「网络设备」和「选择的服务器类型」查询服务器列表
      if (activeNetworkDeviceId.value && selectedServer.value) {
        return AssetService.getServerList({
          page: 1,
          page_size: 20,
          network_equipment_id: activeNetworkDeviceId.value,
          template_id: selectedServer.value,
          search: searchServerName.value,
        })
      }
    }

    return null
  },
})

const serverOptions = computed(() => {
  return serversData.value?.list.map((it) => ({
    label: it.asset?.name ?? '未确认',
    value: it.id,
  }))
})

// 服务器端口列表查询
const { data: serverPortData, isLoading: isServerPortListLoading } = useQuery({
  queryKey: queryKeys.asset.server.port.all({ serverId: selectedServerId, port: searchServerPort }),
  queryFn: () => {
    if (selectedServer.value) {
      if (selectedServerId.value) {
        return AssetService.getServerPortList({
          page: 1,
          page_size: 20,
          server_id: selectedServerId.value,
          port: searchServerPort.value,
        })
      }
    }

    return null
  },
})

const serverPortOptions = computed(() => {
  return serverPortData.value?.list.map((it) => ({
    label: String(it.port),
    value: it.id,
  }))
})

const handleServerSearchChange = debounce((val: string) => {
  searchServerName.value = val
}, 300)

const handleServerPortSearchChange = debounce((val: string) => {
  searchServerPort.value = val
}, 300)
</script>

<template>
  <Fieldset legend="终端设备信息">
    <div class="grid grid-cols-2 gap-form-field pt-in-fieldset">
      <FormItem
        v-slot="{ id }"
        :label="`${assetConfig[AssetType.Server].label}类型`"
        name="serverInfo.serverTemplateId"
      >
        <FormItemAssetTemplateSelect
          v-model:value="serverInfo.serverTemplateId"
          :assetType="AssetType.Server"
          :disabled="disabled"
          :labelId="id"
          :placeholder="`选择${assetConfig[AssetType.Server].label}类型（可选）`"
          showClear
        />
      </FormItem>

      <FormItem
        v-slot="{ id }"
        :label="`所属${assetConfig[AssetType.Server].label}`"
        name="serverInfo.serverId"
      >
        <ProSelect
          v-model="serverInfo.serverId"
          :disabled="disabled"
          :labelId="id"
          :loading="isServersLoading"
          :options="serverOptions"
          :placeholder="`选择${assetConfig[AssetType.Server].label}（可选）`"
          :search="{
            enable: true,
            placeholder: `搜索${assetConfig[AssetType.Server].label}`,
          }"
          :searchValue="searchServerName"
          @update:searchValue="handleServerSearchChange"
        />
      </FormItem>

      <FormItem
        v-if="!disablePort && selectedServerId"
        v-slot="{ id }"
        label="服务器端口"
        name="serverInfo.portId"
      >
        <ProSelect
          v-model="serverInfo.portId"
          :disabled="disabled"
          :labelId="id"
          :loading="isServerPortListLoading"
          :options="serverPortOptions"
          placeholder="选择服务器端口（可选）"
          :search="{
            enable: true,
            placeholder: '搜索服务器端口',
          }"
          :searchValue="searchServerPort"
          @update:searchValue="handleServerPortSearchChange"
        />
      </FormItem>
    </div>
  </Fieldset>
</template>
