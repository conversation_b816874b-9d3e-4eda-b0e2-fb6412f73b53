<script setup lang="ts">
import { AssetType } from '~/enums/asset'

const props = defineProps<{
  serverId?: Server['id']
}>()

const serverId = toRef(props, 'serverId')

const emit = defineEmits<{
  close: []
}>()

const { data, refetch: fetchServerDetail } = useQueryAssetDetail({
  assetType: AssetType.Server,
  assetId: serverId,
})

const handleSaveSuccess = () => {
  fetchServerDetail()
}

const serverDetail = computed(() => data.value as ServerDetail | undefined)

const enum ServerInfoTab {
  Detail,
  Config,
  IP,
  Port,
}

const serverInfoTab = ref(ServerInfoTab.Detail)

const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    // 关闭时，切换回默认的详情 tab，避免切换到其他 tab 时，再次打开时，默认切换到其他 tab
    serverInfoTab.value = ServerInfoTab.Detail

    emit('close')
  }
}

const dialogTitle = computed(() => {
  const assetName = serverDetail?.value?.asset?.name || serverDetail.value?.ip_list?.at(0)?.ipv4

  return `终端设备${assetName ? `【${assetName}】` : ''}`
})
</script>

<template>
  <Dialog
    class="dialog-full"
    dismissableMask
    :draggable="false"
    :header="dialogTitle"
    modal
    :visible="!!serverDetail"
    @update:visible="handleVisibleChange"
  >
    <Tabs v-model:value="serverInfoTab">
      <TabList>
        <Tab :value="ServerInfoTab.Detail">
          详情
        </Tab>

        <Tab :value="ServerInfoTab.Config">
          配置
        </Tab>

        <Tab :value="ServerInfoTab.IP">
          IP
        </Tab>

        <Tab :value="ServerInfoTab.Port">
          端口
        </Tab>
      </TabList>

      <TabPanels>
        <TabPanel :value="ServerInfoTab.Detail">
          <div class="pt-in-tab">
            <div>
              <h3 class="gap-2 pb-2 text-lg font-bold flex-center">
                资产信息

                <span>
                  <AssetInfoEdit
                    :assetId="serverDetail?.id"
                    :assetType="AssetType.Server"
                    @saveSuccess="handleSaveSuccess"
                  />
                </span>
              </h3>

              <AssetDetailList
                :assetFields="serverDetail?.asset"
                :assetTemplateId="serverDetail?.template_id"
              />
            </div>
          </div>
        </TabPanel>

        <TabPanel :value="ServerInfoTab.Config">
          <div class="pt-in-tab">
            <AssetDetailServerConfig
              v-if="serverDetail?.id"
              :serverId="serverDetail.id"
            />
          </div>
        </TabPanel>

        <TabPanel :value="ServerInfoTab.IP">
          <div class="pt-in-tab">
            <AssetDetailAddressList
              v-if="serverDetail?.id"
              :editable="false"
              hideServer
              :serverId="serverDetail.id"
            />
          </div>
        </TabPanel>

        <TabPanel :value="ServerInfoTab.Port">
          <div class="pt-in-tab">
            <AssetDetailServerPortList
              v-if="serverDetail?.id"
              :serverId="serverDetail.id"
            />
          </div>
        </TabPanel>
      </TabPanels>
    </Tabs>
  </Dialog>
</template>
