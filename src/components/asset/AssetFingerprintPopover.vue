<script setup lang="ts">
import { ChevronDownIcon } from 'lucide-vue-next'

const props = defineProps<{
  value?: ServerPort['fingerprint']
}>()

const fingerprints = toRef(props.value)

const { $toast } = useNuxtApp()

const handleCopyFingerprint = async (fingerprint: string) => {
  await copyToClipboard(fingerprint)

  $toast.success({ summary: '复制成功' })
}
</script>

<template>
  <ProPopover
    v-if="Array.isArray(fingerprints) && fingerprints.length > 0"
    trigger="hover"
  >
    <Tag severity="secondary">
      <span class="flex-wrap font-normal inline-flex-center">
        <span class="whitespace-nowrap pr-1 font-medium">{{ fingerprints.at(0) }}</span>

        <span class="whitespace-nowrap inline-flex-center">
          <span v-if="fingerprints.length > 1">
            等 {{ fingerprints.length }} 个
          </span>

          <ChevronDownIcon
            v-if="fingerprints.length > 1"
            class="ml-1 shrink-0"
            :size="14"
          />
        </span>
      </span>
    </Tag>

    <template
      v-if="fingerprints.length > 1"
      #content
    >
      <ul class="space-y-2">
        <li
          v-for="fingerprint of fingerprints"
          :key="fingerprint"
          class="gap-1 truncate flex-center"
        >
          <Tag
            class="cursor-copy !font-medium"
            severity="secondary"
            :value="fingerprint"
            @click="handleCopyFingerprint(fingerprint)"
          />
        </li>
      </ul>
    </template>
  </ProPopover>

  <span
    v-else
    class="px-3"
  >
    -
  </span>
</template>
