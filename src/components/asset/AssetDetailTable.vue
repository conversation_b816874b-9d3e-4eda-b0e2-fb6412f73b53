<script setup lang="ts" generic="DataRecord = BasicAssetDetail">
import { EthernetPortIcon, ShieldQuestionIcon } from 'lucide-vue-next'
import { Tag } from 'primevue'

import AssetFingerprintPopover from '~/components/asset/AssetFingerprintPopover.vue'
import AssetIpTag from '~/components/asset/AssetIpTag.vue'
import AssetSecurityInfo from '~/components/asset/AssetSecurityInfo.vue'
import AssetServerInfoPopover from '~/components/asset/AssetServerInfoPopover.vue'
import IconRender from '~/components/icon/IconRender.vue'
import NumericDisplay from '~/components/NumericDisplay.vue'
import ProBtnDelete from '~/components/pro/btn/ProBtnDelete.vue'
import ProBtnDetail from '~/components/pro/btn/ProBtnDetail.vue'
import ProBtnEdit from '~/components/pro/btn/ProBtnEdit.vue'
import { assetConfig, AssetType, subnetIpStatusConfig } from '~/enums/asset'
import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'

import type AssetDetailEditForm from './AssetDetailEditForm.vue'
import AssetDetailImportResult from './AssetDetailImportResult.vue'

/**
 * 资产详情表格
 *
 * 用于展示资产详情的列表信息
 */

const props = defineProps<{
  assetType: AssetType
}>()

const slots = defineSlots<{
  extraActions: (props: { record: DataRecord }) => VNode
}>()

const assetName = computed(() => assetConfig[props.assetType].label)
const assetNameHeader = computed(() => props.assetType === AssetType.Other ? '资产名称' : `${assetName.value}名称`)

const store = useAssetInfoStore()
const {
  activeNetworkDevice,
  activeNetworkDeviceId,
  activeTemplateId,
  isAssetCollection,
  assetCollectionKey,
} = storeToRefs(store)

const { $toast, $dialog } = useNuxtApp()

const { tableRef, tableAction } = useTableAction()

const { data: tplFields } = useQueryTemplateFields(activeTemplateId)

const { refKey: refEditFormKey, ref: refEditForm } = useRef<InstanceType<typeof AssetDetailEditForm>>()

const service = computed(() => {
  switch (props.assetType) {
    case AssetType.Server: {
      return {
        list: AssetService.getServerList,
        delete: AssetService.deleteServer,
        listOfCollect: AssetService.getAssetCollectServers,
      }
    }

    case AssetType.Database:{
      return {
        list: AssetService.getDatabaseList,
        delete: AssetService.deleteDatabase,
        listOfCollect: AssetService.getAssetCollectDatabases,
      }
    }

    case AssetType.BusinessSystem: {
      return {
        list: AssetService.getBusinessSystemList,
        delete: AssetService.deleteBusinessSystem,
        listOfCollect: AssetService.getAssetCollectBusinessSystems,
      }
    }

    case AssetType.Other: {
      return {
        list: AssetService.getOtherAssetList,
        delete: AssetService.deleteOtherAsset,
        listOfCollect: AssetService.getAssetCollectOtherAssets,
      }
    }

    default:
      throw new Error(`不支持的资产类型: ${props.assetType}`)
  }
})

const fieldColumns = computed(() => {
  return tplFields.value?.filter((field: AssetField) => {
    const shouldShow = field.name !== 'id' && field.name !== 'name'

    return shouldShow
  })
})

const handleDelete = async (item: BasicAssetDetail) => {
  await service.value.delete(item.id)
  tableAction.value?.reload()
}

const handleViewServerDetail = (serverId: Server['id']) => {
  store.setViewedServer({
    id: serverId,
  })
}

const activeAsset = ref<BasicAssetDetail>()

const handleViewAssetDetail = (asset: BasicAssetDetail) => {
  if (props.assetType === AssetType.Server) {
    handleViewServerDetail(asset.id)
  }
  else {
    activeAsset.value = asset
  }
}

/** 搜索的资产名称 */
const searchName = ref<string>()

/** 搜索的端口或指纹 */
const searchPortOrFingerprint = ref<string>()

watch(activeTemplateId, () => {
  searchName.value = undefined
  searchPortOrFingerprint.value = undefined
})

const queryParams = computed<WithPageQuery<Partial<{ network_equipment_id: NetworkDevice['id'], template_id: number, search: string }>>>(() => ({
  network_equipment_id: activeNetworkDeviceId.value,
  template_id: activeTemplateId.value,

  search: searchName.value,
  port: searchPortOrFingerprint.value,
}))

const isServerAsset = computed(() => props.assetType === AssetType.Server)

const columns = computed<ProTableColumn<BasicAssetDetail>[]>(() => {
  const cols: ProTableColumn<BasicAssetDetail>[] = []

  // 如果是资产采集，则展示资产类型，方便用户区分资产类别
  if (isAssetCollection.value) {
    cols.push({
      header: '资产类型',
      field: 'template',
      render: (data: ServerListItem) => {
        return h('div', { class: 'gap-2 font-medium flex-center' }, [
          h('div', { class: 'size-[16px] shrink-0' }, [
            data.template?.icon
              ? h(IconRender, {
                  iconVal: data.template.icon,
                  fallback: () => h(ShieldQuestionIcon, { size: 16 }),
                })
              : h(ShieldQuestionIcon, { size: 16 }),
          ]),
          data.template?.name || '未确认',
        ])
      },
    })
  }
  else {
    cols.push({
      header: assetNameHeader.value,
      field: 'asset',
      render: (data) => data.asset?.name || '未确认',
    })
  }

  if (!isServerAsset.value) {
    // 如果是终端设备资产，则应该不展示所属服务器
    cols.push({
      header: `所属${assetConfig[AssetType.Server].label}`,
      field: 'server',
      render: (data) => h(AssetServerInfoPopover, {
        assetType: props.assetType,
        server: data.server,
      }),
    })
  }
  else {
    // 如果是终端设备资产，则应该展示 IP
    cols.push({
      header: 'IP',
      field: 'ip_list',
      render: (data: ServerListItem) => h(AssetIpTag, {
        ipList: data.ip_list,
      }),
    })
  }

  // 如果是非服务器资产，则应该展示端口和端口指纹
  if (!isServerAsset.value) {
    cols.push(
      {
        header: '端口',
        field: 'port',
        render: (data) => h(NumericDisplay, { class: 'text-sm' }, () =>
          data.port?.port || '-',
        ),
      },
      {
        header: '端口指纹',
        field: 'fingerprint',
        render: (data) => h(AssetFingerprintPopover, {
          value: data.port?.fingerprint,
        }),
      },
    )
  }

  // 添加安全性列
  if (props.assetType === AssetType.Server && !isAssetCollection.value) {
    cols.push({
      header: '安全性',
      field: 'level',
      style: { minWidth: '140px' },
      render: (data: Server) => h(AssetSecurityInfo, { data }),
    })
  }

  // 添加动态模板字段列
  fieldColumns.value?.forEach((field) => {
    const fieldName = field.field_name

    cols.push({
      header: field.name,
      field: `asset.${fieldName}`,
      hideInTable: field.kwargs?.displayInList !== true,
      render: (data) => String(data.asset?.[fieldName] || '-'),
    })
  })

  if (!isServerAsset.value) {
    cols.push({
      header: '状态',
      field: 'status',
      class: 'w-[160px]',
      render: (data) => data.port?.status
        ? h(Tag, {
            severity: subnetIpStatusConfig[data.port.status].severity,
          }, () => subnetIpStatusConfig[data.port!.status].label)
        : null,
    })

    cols.push({
      header: '上次扫描时间',
      field: 'port.last_scan_time',
      class: 'w-[160px]',
      valueType: ProValueType.DateTime,
    })
  }

  cols.push({
    header: '首次发现时间',
    field: 'create_time',
    class: 'w-[160px]',
    valueType: ProValueType.DateTime,
  })

  cols.push({
    class: 'min-w-table-actions-wide',
    frozen: true,
    alignFrozen: 'right',
    valueType: ProValueType.ActionGroup,
    render: (data) => {
      // 是否为未确认的资产：如果没有模板，则认为该资产为未确认的资产
      const isUnconfirmed = !data.template_id

      return h('div', { class: 'table-action-group' }, [
        slots.extraActions?.({ record: data as DataRecord }),

        // 详情按钮
        !isAssetCollection.value && h(ProBtnDetail, {
          label: '详情',
          variant: 'text',
          onClick: () => handleViewAssetDetail(data),
        }),

        // 编辑按钮
        h(ProBtnEdit, {
          label: '编辑',
          onlyIcon: !isAssetCollection.value,
          variant: 'text',
          onClick: () => refEditForm.value?.openUpdateModal(data as UnsafeAny),
        }),

        // 删除按钮
        !isAssetCollection.value && !isUnconfirmed && h(ProBtnDelete, {
          confirmMessage: data.asset?.name
            ? `确定要删除资产「${data.asset?.name}」吗？`
            : '确定要删除该资产吗？',
          label: '删除',
          onlyIcon: !isAssetCollection.value,
          variant: 'text',
          onConfirm: () => handleDelete(data),
        }),
      ])
    },
  })

  return cols
})

const exportFileExtension = 'xlsx'

const handleExportTemplate = async () => {
  const blobData = await AssetService.exportAssetTemplate({
    network_equipment_id: activeNetworkDeviceId.value,
    asset_type: props.assetType,
  })

  downloadBlob(blobData, `资产模板_${assetName.value} - ${activeNetworkDevice.value?.asset?.name || ''}.${exportFileExtension}`)

  $toast.success({
    summary: '成功导出资产模板',
  })
}

const handleImportAsset = () => {
  selectFile({
    accept: `.${exportFileExtension}`,
    onSelect: async (file) => {
      const targetFile = file.at(0)

      if (targetFile) {
        if (activeNetworkDeviceId.value) {
          const formData = new FormData()
          formData.append('network_equipment_id', String(activeNetworkDeviceId.value))
          formData.append('asset_type', String(props.assetType))
          formData.append('file', targetFile)

          const resultData = await AssetService.importAsset(formData)

          $dialog.open(h(AssetDetailImportResult, {
            resultData,
          }), {
            props: {
              header: '导入结果',
            },
          })

          tableAction.value?.reload()
        }
      }
    },
  })
}
</script>

<template>
  <div class="h-full">
    <ProTable
      :ref="tableRef"
      :columns="columns"
      :queryKey="queryKeys.asset.networkDevice.list(assetType)"
      :queryParams="queryParams"
      :requestFn="(queryParams) => {
        // 需要同时跟据「网络设备」和「资产模板」才能进行查询
        if (typeof queryParams?.network_equipment_id === 'number') {
          return service.list(queryParams as WithPageQuery<{ network_equipment_id: number; template_id?: number }>)
        }

        // 如果是资产采集，则需要根据「资产采集」进行查询
        if (isAssetCollection && assetCollectionKey) {
          return service.listOfCollect(assetCollectionKey, queryParams)
        }

        return null
      }"
      scrollable
      scrollHeight="flex"
      :toolbarConfig="{
        search: {
          key: 'ip',
          placeholder: '搜索 IP',
        },
      }"
    >
      <template #toolbarRight>
        <div class="flex-wrap gap-2 flex-center">
          <SearchInput
            v-if="!isServerAsset && !isAssetCollection"
            :placeholder="`搜索${assetName}名称`"
            size="small"
            @update:debounceChange="searchName = $event"
          />

          <SearchInput
            v-if="!isServerAsset"
            placeholder="搜索端口或指纹"
            size="small"
            @update:debounceChange="searchPortOrFingerprint = $event"
          >
            <template #searchIcon="{ iconSize }">
              <EthernetPortIcon :size="iconSize" />
            </template>
          </SearchInput>

          <div class="ml-auto gap-2 flex-center">
            <Button
              label="导出模板"
              variant="outlined"
              @click="handleExportTemplate()"
            />

            <Button
              label="导入资产"
              variant="outlined"
              @click="handleImportAsset()"
            />

            <Button
              v-if="!isAssetCollection"
              :label="`添加${assetName}`"
              @click="refEditForm?.openCreateModal({ template_id: activeTemplateId })"
            />
          </div>
        </div>
      </template>
    </ProTable>

    <AssetDetailDialog
      :assetId="activeAsset?.id"
      :assetType="assetType"
      @close="activeAsset = undefined"
    />

    <AssetDetailEditForm
      :ref="refEditFormKey"
      :assetType="assetType"
      :networkDeviceId="activeNetworkDeviceId"
      @submitSuccess="tableAction?.reload()"
    />
  </div>
</template>
