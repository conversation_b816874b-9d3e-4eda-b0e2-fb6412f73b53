<script setup lang="ts">
import { MinusIcon, PlusIcon } from 'lucide-vue-next'
import { enum_, object } from 'valibot'

import type { TaskScanType } from '~/enums/asset'
import { TaskScheduleType } from '~/enums/asset'
import { DateFormat } from '~/enums/common'

const props = defineProps<{
  initialValues?: TaskScanConfig
  taskType?: TaskScanType
}>()

const initialValues = toRef(props, 'initialValues')

const emit = defineEmits<{
  submit: [values: TaskScanFormValues]
}>()

const scheduleTypes = [
  { id: TaskScheduleType.TimeInterval, label: '时间间隔' },
  { id: TaskScheduleType.PlanTime, label: '计划时间' },
  { id: TaskScheduleType.TimingTime, label: '定时时间' },
]

const defaultValues = {
  schedule_type: TaskScheduleType.TimeInterval,
  schedule: {
    minute: '*',
    hour: '*',
    day_of_week: '*',
    day_of_month: '*',
    month_of_year: '*',
  },
} as const satisfies Partial<TaskScanFormValues>

const initialFormValues = computed(() => {
  let initialScheduleType: TaskScheduleType

  // 这里需要特殊处理值的获取：
  // 根据初始化类型，从初始化值中获取对应的值，然后再作为表单初始值填充到表单中
  const initialSchedule = (() => {
    if (initialValues.value?.interval) {
      initialScheduleType = TaskScheduleType.TimeInterval

      return initialValues.value.interval_object
    }
    else if (initialValues.value?.crontab) {
      initialScheduleType = TaskScheduleType.PlanTime

      return initialValues.value.crontab_object
    }
    else if (initialValues.value?.clocked) {
      initialScheduleType = TaskScheduleType.TimingTime

      return initialValues.value.clocked_object
    }
    else {
      initialScheduleType = defaultValues.schedule_type

      return defaultValues.schedule
    }
  })()

  return {
    ...(initialValues.value as TaskScanFormValues),
    schedule_type: initialScheduleType,
    schedule: initialSchedule || {}, // 需要保证 schedule 对象存在，否则会导致表单赋值失败
  }
})

const { formValues, formResolver, isFormDirty } = useFormControl<TaskScanFormValues>({
  initialValues: initialFormValues.value,
  resolver: object({
    name: schemaNotEmptyString('请输入任务名称'),
    description: schemaNotEmptyString('请输入任务描述'),
    schedule_type: enum_(TaskScheduleType, '请选择定时类型'),
  }),
  enableDirtyCheck: true,
})

const fieldsetLegend = computed(() => {
  return scheduleTypes.find((type) => type.id === formValues.value.schedule_type)?.label
})

const handleSubmit = ({ valid, states }: FormSubmitOptions<TaskScanFormValues>) => {
  if (valid) {
    const values: TaskScanFormValues = {
      name: states.name.value,
      description: states.description.value,
      schedule_type: states.schedule_type.value,
      expires: formValues.value.expires,
      start_time: formValues.value.start_time,
      schedule: formValues.value.schedule,
    }

    emit('submit', values)
  }
}

const periodOptions = computed(() => {
  return [
    { label: '天', value: 'days' },
    { label: '小时', value: 'hours' },
    { label: '分钟', value: 'minutes' },
    { label: '秒', value: 'seconds' },
  ]
})
</script>

<template>
  <Form
    :initialValues="initialFormValues"
    :resolver="formResolver"
    @submit="handleSubmit"
  >
    <div class="flex flex-col gap-form-field pt-2">
      <FormItem
        v-slot="{ id }"
        label="任务名称"
        layout="horizontal"
        name="name"
      >
        <InputText
          :id="id"
          v-model="formValues.name"
          placeholder="输入任务名称，最多 50 个字符"
        />
      </FormItem>

      <FormItem
        v-slot="{ id }"
        label="任务描述"
        layout="horizontal"
        name="description"
      >
        <InputText
          :id="id"
          v-model="formValues.description"
          placeholder="输入任务描述，最多 200 个字符"
        />
      </FormItem>

      <FormItem
        v-slot="{ id }"
        label="任务过期时间"
        layout="horizontal"
        name="expires"
      >
        <ProDatePicker
          dateFormat="yy-mm-dd"
          :inputId="id"
          :modelValue="formValues.expires ? new Date(formValues.expires) : undefined"
          placeholder="选择任务的过期时间"
          showTime
          @update:modelValue="formValues.expires = formatDate($event)"
        />
      </FormItem>

      <FormItem
        v-slot="{ id }"
        label="任务首次开始执行时间"
        layout="horizontal"
        name="start_time"
      >
        <ProDatePicker
          dateFormat="yy-mm-dd"
          :inputId="id"
          :modelValue="formValues.start_time ? new Date(formValues.start_time) : undefined"
          placeholder="选择任务的首次执行时间"
          showTime
          @update:modelValue="formValues.start_time = formatDate($event)"
        />
      </FormItem>

      <!-- MARK: 定时类型 -->
      <div class="flex flex-col gap-form-field">
        <FormItem
          label="定时类型"
          layout="horizontal"
          name="schedule_type"
        >
          <RadioButtonGroup
            v-model="formValues.schedule_type"
            class="flex flex-wrap gap-4"
          >
            <div
              v-for="type of scheduleTypes"
              :key="type.id"
              class="gap-2 flex-center"
            >
              <RadioButton
                :inputId="String(`${taskType || ''}${type.id}`)"
                :value="type.id"
              />
              <label :for="String(`${taskType || ''}${type.id}`)">
                {{ type.label }}
              </label>
            </div>
          </RadioButtonGroup>
        </FormItem>

        <Fieldset :legend="fieldsetLegend">
          <div class="flex flex-col gap-form-field pt-in-fieldset">
            <template v-if="formValues.schedule_type === TaskScheduleType.TimeInterval">
              <FormItem
                label="执行间隔"
                name="schedule.every"
              >
                <template #default="{ id }">
                  <InputNumber
                    buttonLayout="horizontal"
                    :inputId="id"
                    :min="0"
                    :modelValue="formValues.schedule?.every"
                    showButtons
                    @update:modelValue="formValues.schedule!.every = $event"
                  >
                    <template #incrementbuttonicon>
                      <PlusIcon :size="15" />
                    </template>
                    <template #decrementbuttonicon>
                      <MinusIcon :size="15" />
                    </template>
                  </InputNumber>
                </template>

                <template #helpTip>
                  设置两次任务执行之间的时间间隔数值
                </template>
              </FormItem>

              <FormItem
                label="间隔单位"
                name="schedule.period"
              >
                <template #default="{ id }">
                  <ProSelect
                    :labelId="id"
                    :modelValue="formValues.schedule?.period"
                    :options="periodOptions"
                    placeholder="选择时间间隔单位"
                    @update:modelValue="formValues.schedule!.period = $event"
                  />
                </template>

                <template #helpTip>
                  可选择天、小时、分钟或秒作为间隔单位
                </template>
              </FormItem>
            </template>

            <template v-else-if="formValues.schedule_type === TaskScheduleType.PlanTime">
              <FormItem
                label="每小时的第几分钟"
                name="schedule.minute"
              >
                <template #default="{ id }">
                  <InputText
                    :id="id"
                    :modelValue="formValues.schedule?.minute"
                    placeholder="支持 0-59，使用 * 表示每分钟"
                    @update:modelValue="formValues.schedule!.minute = $event"
                  />
                </template>

                <template #helpTip>
                  指定任务在每小时的第几分钟执行，* 表示每分钟都执行
                </template>
              </FormItem>

              <FormItem
                label="每天的第几小时"
                name="schedule.hour"
              >
                <template #default="{ id }">
                  <InputText
                    :id="id"
                    :modelValue="formValues.schedule?.hour"
                    placeholder="支持 0-23，使用 * 表示每小时"
                    @update:modelValue="formValues.schedule!.hour = $event"
                  />
                </template>

                <template #helpTip>
                  指定任务在每天的第几小时执行，* 表示每小时都执行
                </template>
              </FormItem>

              <FormItem
                label="每周的第几天"
                name="schedule.day_of_week"
              >
                <template #default="{ id }">
                  <InputText
                    :id="id"
                    :modelValue="formValues.schedule?.day_of_week"
                    placeholder="支持 0-6（0表示周日），使用 * 表示每天"
                    @update:modelValue="formValues.schedule!.day_of_week = $event"
                  />
                </template>

                <template #helpTip>
                  指定任务在每周的星期几执行，* 表示每周都执行
                </template>
              </FormItem>

              <FormItem
                label="每月的第几天"
                name="schedule.day_of_month"
              >
                <template #default="{ id }">
                  <InputText
                    :id="id"
                    :modelValue="formValues.schedule?.day_of_month"
                    placeholder="支持 1-31，使用 * 表示每天"
                    @update:modelValue="formValues.schedule!.day_of_month = $event"
                  />
                </template>

                <template #helpTip>
                  指定任务在每月的第几天执行，* 表示每月都执行
                </template>
              </FormItem>

              <FormItem
                label="每年的第几个月"
                name="schedule.month_of_year"
              >
                <template #default="{ id }">
                  <InputText
                    :id="id"
                    :modelValue="formValues.schedule?.month_of_year"
                    placeholder="支持 1-12，使用 * 表示每月"
                    @update:modelValue="formValues.schedule!.month_of_year = $event"
                  />
                </template>

                <template #helpTip>
                  指定任务在每年的第几月执行，* 表示每年都执行
                </template>
              </FormItem>
            </template>

            <template v-else-if="formValues.schedule_type === TaskScheduleType.TimingTime">
              <FormItem
                label="执行时间"
                name="schedule.clocked_time"
              >
                <template #default="{ id }">
                  <ProDatePicker
                    dateFormat="yy-mm-dd"
                    :inputId="id"
                    :modelValue="formValues.schedule?.clocked_time ? new Date(formValues.schedule?.clocked_time) : undefined"
                    placeholder="选择具体的执行时间"
                    showTime
                    @update:modelValue="formValues.schedule!.clocked_time = formatDate($event, DateFormat.YYYY_MM_DD_HH_MM_SS)"
                  />
                </template>

                <template #helpTip>
                  设置任务的具体执行时间点，任务将在该时间点执行一次
                </template>
              </FormItem>
            </template>
          </div>
        </Fieldset>
      </div>

      <div class="flex flex-col items-end gap-2">
        <FormActionGroup
          :cancelButtonProps="false"
          :confirmButtonProps="{
            label: '保存任务配置',
            disabled: !isFormDirty,
          }"
        />

        <Message
          v-if="isFormDirty"
          severity="secondary"
          size="small"
          variant="simple"
        >
          内容发生更改，请保存以应用
        </Message>
      </div>
    </div>
  </Form>
</template>
