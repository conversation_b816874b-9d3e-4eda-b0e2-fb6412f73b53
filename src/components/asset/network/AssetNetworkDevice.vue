<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import arrayToTree from 'array-to-tree'
import { cloneDeep } from 'lodash-es'
import { ArrowUpRightIcon, PencilLineIcon, WorkflowIcon } from 'lucide-vue-next'
import { number, object } from 'valibot'

import { AssetType } from '~/enums/asset'
import { TooltipShowDelay } from '~/enums/common'
import { queryKeys } from '~/enums/query-key'
import { RouteKey } from '~/enums/route'

const props = defineProps<{
  networkId: Network['id']
}>()

const networkId = toRef(props, 'networkId')

const { $confirm } = useNuxtApp()

const { data: networkDeviceList, refetch: refetchNetworkDeviceList, isLoading } = useQuery({
  queryKey: queryKeys.asset.networkDevice.all(networkId),
  queryFn: () => {
    return AssetService.getNetDeviceList({ network_id: networkId.value })
  },
  enabled: !!networkId.value,
})

const fieldStates = ref<NetworkDeviceFormValues['asset']>({})

const {
  loading,
  modalVisible,
  isFormCreate,
  isFormUpdate,
  formResolver,
  formValues,
  updatingItem,
  openCreateModal,
  openUpdateModal,
  confirmBtnLabel,
  formTitleText,
  handleClose,
  handleModalVisibleChange,
  handleSubmit,
} = useFormControl<NetworkDeviceFormValues, NetworkDevice>({
  initialValues: {
    template_id: undefined,
  },
  resolver: object({
    template_id: number('请选择资产模板'),
  }),
  btnLabel: {
    create: '确认添加',
    update: '保存更改',
  },
  formTitle: {
    create: '添加网络设备',
    update: '编辑网络设备',
  },
  fetchDetail: (it) => {
    if (it.asset) {
      fieldStates.value = cloneDeep(it.asset)
    }

    return toRaw(it)
  },
  onSubmit: async ({ valid }) => {
    if (valid) {
      if (isFormCreate.value) {
        const newNetDevice: NetworkDeviceFormValues = {
          ...formValues.value,
          asset: fieldStates.value,
          network_id: networkId.value,
        }

        await AssetService.createNetDevice(newNetDevice)
      }
      else if (isFormUpdate.value) {
        const updatingId = updatingItem.value?.id

        if (typeof updatingId === 'number') {
          const updatingNetDevice: NetworkDeviceFormValues = {
            ...formValues.value,
            asset: fieldStates.value,
          }

          await AssetService.updateNetDevice(updatingId, updatingNetDevice)
        }
      }
    }
  },
  onSubmitSuccess: () => {
    refetchNetworkDeviceList()
  },
  onClose: () => {
    fieldStates.value = {}
  },
})

const networkDeviceTree = computed<NetworkDeviceTreeNode[] | undefined>(() => {
  const list = networkDeviceList.value?.list

  if (Array.isArray(list) && list.length > 0) {
    const flatList = list.map<NetworkDeviceTreeNode>((it) => ({
      ...it,
      key: it.id.toString(),
      label: it.asset?.name || '未命名设备',
    }))

    return arrayToTree<NetworkDeviceTreeNode>(flatList)
  }

  return undefined
})

// 默认展开所有网络层级
const expandedKeys = computed<ExpandedKeys>(() => {
  const keys: ExpandedKeys = {}

  if (!networkDeviceTree.value) {
    return keys
  }

  // 递归遍历网络树，获取所有节点的 key
  const traverseTree = (nodes: NetworkDeviceTreeNode[]) => {
    nodes.forEach((node) => {
      // 将当前节点的 key 添加到展开键值对象中
      keys[node.key] = true

      // 如果当前节点有子节点，则继续遍历
      if (Array.isArray(node.children) && node.children.length > 0) {
        traverseTree(node.children)
      }
    })
  }

  traverseTree(networkDeviceTree.value)

  return keys
})

const contextMenuNetworkDevice = ref<NetworkDevice>()

const editingNodeConfig = ref<NetworkDevice>()

const handleEditNode = (node: NetworkDevice) => {
  openUpdateModal(node)
}

const { popMenuRef: ctxMenuRef, popMenu: ctxMenu } = usePopMenu()

const menuOptions: PrimeVueMenuItem[] = [
  {
    label: '编辑',
    command: () => {
      if (contextMenuNetworkDevice.value) {
        handleEditNode(contextMenuNetworkDevice.value)
      }
    },
  },
  {
    label: '节点配置',
    command: () => {
      if (contextMenuNetworkDevice.value) {
        editingNodeConfig.value = contextMenuNetworkDevice.value
      }
    },
  },
  {
    label: '删除',
    command: async () => {
      const networkDeviceId = contextMenuNetworkDevice.value?.id
      const networkDeviceName = contextMenuNetworkDevice.value?.asset?.name

      if (networkDeviceId) {
        $confirm.dialog({
          message: networkDeviceName ? `确定删除网络设备 「${networkDeviceName}」吗？` : '确定删除该网络设备吗？',
          accept: async () => {
            await AssetService.deleteNetDevice(networkDeviceId)
            refetchNetworkDeviceList()
          },
        })
      }
    },
  },
]

const handleViewNode = async (nodeInfo: NetworkDeviceTreeNode) => {
  await navigateTo(getRoutePath(RouteKey.网络设备详情, { deviceId: nodeInfo.id }))
}

const handleNodeConfigSuccess = () => {
  refetchNetworkDeviceList()
  editingNodeConfig.value = undefined
}

const handleItemContext = (ev: UnsafeAny) => {
  ctxMenu.value?.show(ev.originalEvent)
}

const statsColumns = [
  { field: 'ip_subnet', header: '子网' },
  { field: 'ip', header: 'IP' },
  { field: 'server', header: '终端设备' },
  { field: 'web', header: '业务系统' },
  { field: 'database', header: '数据库' },
  { field: 'other', header: '其他' },
] satisfies { field: keyof NetworkDeviceTreeNode['count'], header: string }[]
</script>

<template>
  <div>
    <div class="justify-between pb-4 flex-center">
      <h2 class="text-xl font-bold">
        网络设备
      </h2>

      <Button
        label="添加网络设备"
        severity="secondary"
        @click="openCreateModal()"
      />
    </div>

    <TreeTable
      v-model:contextMenuSelection="contextMenuNetworkDevice"
      contextMenu
      :expandedKeys="expandedKeys"
      :loading="isLoading"
      size="small"
      :value="networkDeviceTree"
      @rowContextmenu="handleItemContext($event)"
    >
      <Column
        expander
        field="label"
        header="设备名称"
      >
        <template #body="{ node }">
          <span class="flex-1">{{ node.label }}</span>
        </template>
      </Column>

      <Column
        v-for="col of statsColumns"
        :key="col.field"
        class="text-sm"
        :header="col.header"
      >
        <template #body="{ node }: { node: NetworkDeviceTreeNode }">
          <NumericDisplay>
            {{ node.count?.[col.field] }}
          </NumericDisplay>
        </template>
      </Column>

      <Column class="min-w-table-actions">
        <template #body="{ node }: { node: NetworkDeviceTreeNode }">
          <div class="w-full table-action-group">
            <ProBtn
              severity="secondary"
              size="small"
              :tooltip="{ value: '编辑', showDelay: TooltipShowDelay.Fast }"
              variant="text"
              @click.stop="handleEditNode(node)"
            >
              <template #icon="{ size }">
                <PencilLineIcon :size="size" />
              </template>
            </ProBtn>

            <ProBtn
              :severity="node.node_status ? 'success' : 'secondary'"
              size="small"
              :tooltip="{ value: node.node_status ? '节点状态: 正常' : '节点配置', showDelay: TooltipShowDelay.Fast }"
              variant="text"
              @click.stop="editingNodeConfig = node"
            >
              <template #icon="{ size }">
                <WorkflowIcon :size="size" />
              </template>
            </ProBtn>

            <ProBtn
              severity="info"
              size="small"
              variant="text"
              @click.stop="handleViewNode(node)"
            >
              <span class="gap-0.5 flex-center">
                查看
                <ArrowUpRightIcon class="size-4" />
              </span>
            </ProBtn>
          </div>
        </template>
      </Column>

      <template #loadingicon>
        <div class="size-full justify-center bg-content py-6 flex-center">
          <Message severity="secondary">
            数据加载中...
          </Message>
        </div>
      </template>

      <template #empty>
        <ProTableEmpty />
      </template>
    </TreeTable>

    <ContextMenu
      :ref="ctxMenuRef"
      :model="menuOptions"
      @hide="contextMenuNetworkDevice = undefined"
    />

    <ProDialogForm
      :confirmBtnLabel="confirmBtnLabel"
      :dialogProps="{
        dismissableMask: false,
        class: 'dialog-form-wider',
      }"
      :formActionGroupProps="{
        confirmButtonProps: {
          label: confirmBtnLabel,
        },
      }"
      :formProps="{ resolver: formResolver }"
      :initialValues="formValues"
      :loading="loading"
      :title="formTitleText"
      :visible="modalVisible"
      @cancel="handleClose"
      @submit="handleSubmit"
      @update:visible="handleModalVisibleChange"
    >
      <div class="grid grid-cols-2 gap-form-field">
        <FormItem
          label="设备类型"
          name="template_id"
          required
        >
          <FormItemAssetTemplateSelect
            v-model="formValues.template_id"
            :assetType="AssetType.NetworkDevice"
            :disabled="isFormUpdate"
            placeholder="选择设备类型"
          />
        </FormItem>

        <FormItem
          label="上级网络设备"
          name="parent_id"
        >
          <FormItemTreeSelect
            v-model:value="formValues.parent_id"
            :disabledKeys="updatingItem?.id.toString()"
            :options="networkDeviceTree"
            placeholder="选择上级网络设备"
            showClear
          />
        </FormItem>
      </div>

      <Fieldset
        v-if="formValues.template_id"
        legend="设备信息"
      >
        <div class="pt-in-fieldset">
          <AssetFieldForm
            v-model:fieldStates="fieldStates"
            :templateId="formValues.template_id"
          />
        </div>
      </Fieldset>
    </ProDialogForm>

    <AssetNetworkNodeConfig
      :node="editingNodeConfig"
      @close="editingNodeConfig = undefined"
      @updateSuccess="handleNodeConfigSuccess()"
    />
  </div>
</template>
