<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { CheckIcon, XIcon } from 'lucide-vue-next'

import { taskScanConfig, TaskScanType } from '~/enums/asset'
import { queryKeys } from '~/enums/query-key'

const props = withDefaults(defineProps<{
  node?: NetworkDevice
  initialActiveTab?: TaskScanType
}>(), {
  initialActiveTab: TaskScanType.AssetScan,
})

const { node, initialActiveTab } = toRefs(props)

const visible = computed(() => !!node.value)

const currentNetworkDeviceId = computed(() => node.value?.id)

const emit = defineEmits<{
  close: []
  updateSuccess: []
}>()

const { $toast } = useNuxtApp()

const { data: testResult, isSuccess, isError, error, isLoading: isTesting, refetch: testLink } = useQuery({
  queryKey: queryKeys.asset.networkDevice.status,
  queryFn: () => {
    const nodeUrl = node.value?.node_url

    if (currentNetworkDeviceId.value && nodeUrl) {
      return AssetService.getNetworkDeviceStatus(currentNetworkDeviceId.value, { nodeUrl })
    }

    return null
  },
})

const hasTestedLink = ref(false)

watchEffect(() => {
  if (!node.value?.node_url) {
    hasTestedLink.value = false
  }
})

const handleTestLink = async () => {
  await testLink()
  hasTestedLink.value = true
}

const activeTab = ref(TaskScanType.AssetScan)

watchEffect(() => {
  if (visible.value) {
    activeTab.value = initialActiveTab.value
  }
})

const handleClose = () => {
  emit('close')
}

const handleDone = () => {
  emit('updateSuccess')
}

const handleAssetScanSubmit = async (values: TaskScanFormValues) => {
  const taskId = node.value?.asset_scan_task_id
  const isUpdate = !!taskId

  if (currentNetworkDeviceId.value) {
    if (isUpdate) {
      await TaskService.updateScanTask(taskId, values)
      $toast.success({
        summary: '更新成功',
        detail: '资产扫描任务已更新',
      })
    }
    else {
      await TaskService.createAssetScanTask(currentNetworkDeviceId.value, values)
      $toast.success({
        summary: '创建成功',
        detail: '资产扫描任务已创建',
      })
    }

    handleDone()
  }
}

const handleBaselineScanSubmit = async (values: TaskScanFormValues) => {
  const taskId = node.value?.baseline_scan_task_id
  const isUpdate = !!taskId

  if (currentNetworkDeviceId.value) {
    if (isUpdate) {
      await TaskService.updateScanTask(taskId, values)
      $toast.success({
        summary: '更新成功',
        detail: '基线检查任务已更新',
      })
    }
    else {
      await TaskService.createComplianceScanTask(currentNetworkDeviceId.value, values)
      $toast.success({
        summary: '创建成功',
        detail: '基线检查任务已创建',
      })
    }

    handleDone()
  }
}

const handleVulScanSubmit = async (values: TaskScanFormValues) => {
  const taskId = node.value?.vul_scan_task_id
  const isUpdate = !!taskId

  if (currentNetworkDeviceId.value) {
    if (isUpdate) {
      await TaskService.updateScanTask(taskId, values)
      $toast.success({
        summary: '更新成功',
        detail: '漏洞扫描任务已更新',
      })
    }
    else {
      await TaskService.createVulnerabilityScanTask(currentNetworkDeviceId.value, values)
      $toast.success({
        summary: '创建成功',
        detail: '漏洞扫描任务已创建',
      })
    }

    handleDone()
  }
}

const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    handleClose()
  }
}

const taskScanTabs = computed(() => {
  return Object.values(taskScanConfig)
})
</script>

<template>
  <Dialog
    class="dialog-form-wider"
    :header="`网络节点配置 - ${node?.asset?.name}`"
    modal
    :visible="!!node"
    @update:visible="handleVisibleChange"
  >
    <FormItem
      v-if="node"
      v-slot="{ id }"
      label="网络节点链接"
      name="node_url"
    >
      <div class="w-full gap-2 flex-center">
        <div class="flex-1">
          <InputText
            :id="id"
            v-model="node.node_url"
            fluid
            placeholder="输入网络节点链接"
          />
        </div>

        <Button
          label="测试连接并保存"
          :loading="isTesting"
          type="button"
          @click="handleTestLink"
        />
      </div>

      <template v-if="node?.node_url">
        <template v-if="hasTestedLink">
          <Message
            v-if="isSuccess"
            severity="success"
            size="small"
            variant="simple"
          >
            <span class="gap-1 inline-flex-center">
              <CheckIcon :size="15" />
              {{ testResult?.__raw.message || '测试成功，已自动保存链接' }}
            </span>
          </Message>

          <Message
            v-if="isError"
            severity="error"
            size="small"
            variant="simple"
          >
            <span class="gap-1 inline-flex-center">
              <XIcon :size="15" />
              {{ error?.message || '测试失败，请检查链接是否正确' }}
            </span>
          </Message>
        </template>

        <template v-else>
          <Message
            v-if="node.node_status"
            severity="success"
            size="small"
            variant="simple"
          >
            <span class="gap-1 inline-flex-center">
              <CheckIcon :size="15" />
              节点连接正常
            </span>
          </Message>
        </template>
      </template>
    </FormItem>

    <Divider layout="horizontal" />

    <Tabs v-model:value="activeTab">
      <TabList>
        <Tab
          v-for="scanConfig of taskScanTabs"
          :key="scanConfig.value"
          :value="scanConfig.value"
        >
          {{ scanConfig.label }}
        </Tab>
      </TabList>

      <TabPanels>
        <TabPanel
          v-for="scanConfig of taskScanTabs"
          :key="scanConfig.value"
          :value="scanConfig.value"
        >
          <AssetNetworkNodeSchedule
            v-if="scanConfig.value === TaskScanType.AssetScan"
            :initialValues="node?.asset_scan_task"
            :taskType="scanConfig.value"
            @submit="handleAssetScanSubmit"
          />

          <AssetNetworkNodeSchedule
            v-else-if="scanConfig.value === TaskScanType.ComplianceScan"
            :initialValues="node?.baseline_scan_task"
            :taskType="scanConfig.value"
            @submit="handleBaselineScanSubmit"
          />

          <AssetNetworkNodeSchedule
            v-else-if="scanConfig.value === TaskScanType.VulScan"
            :initialValues="node?.vul_scan_task"
            :taskType="scanConfig.value"
            @submit="handleVulScanSubmit"
          />
        </TabPanel>
      </TabPanels>
    </Tabs>
  </Dialog>
</template>
