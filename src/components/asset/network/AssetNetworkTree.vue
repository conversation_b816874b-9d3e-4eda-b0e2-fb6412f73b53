<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import arrayToTree from 'array-to-tree'
import { object } from 'valibot'

import { queryKeys } from '~/enums/query-key'

const store = useAssetInfoStore()
const { activeNetworkId } = storeToRefs(store)

const { $confirm } = useNuxtApp()

const { data: networkListData, refetch: refetchNetworkList, isLoading } = useQuery({
  queryKey: queryKeys.asset.network.all(),
  queryFn: () => AssetService.getNetworkList(),
})

const {
  isFormCreate,
  isFormUpdate,
  formValues,
  updatingItem,
  modalVisible,
  loading,
  confirmBtnLabel,
  formTitleText,
  formResolver,
  openCreateModal,
  openUpdateModal,
  handleClose,
  handleSubmit,
  handleModalVisibleChange,
} = useFormControl<NetworkFormValues, Network>({
  initialValues: {
    name: '',
    parent_id: undefined,
  },
  resolver: object({
    name: schemaNotEmptyString('请输入网络名称'),
  }),
  btnLabel: {
    create: '确认添加',
    update: '保存更改',
  },
  formTitle: {
    create: '添加网络',
    update: '编辑网络',
  },
  fetchDetail: (it) => toRaw(it),
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      if (isFormCreate.value) {
        await AssetService.createNetwork({
          name: states.name.value,
          parent_id: formValues.value.parent_id,
        })
      }
      else if (isFormUpdate.value) {
        const id = updatingItem.value?.id

        if (id) {
          await AssetService.updateNetwork(id, {
            name: states.name.value,
            parent_id: formValues.value.parent_id,
          })
        }
      }
    }
  },
  onSubmitSuccess: () => {
    refetchNetworkList()
  },
})

const networkTree = computed<NetworkTreeNode[] | undefined>(() => {
  const list = networkListData.value

  if (Array.isArray(list) && list.length > 0) {
    const normalizedList = list.map<NetworkTreeNode>((it) => ({
      ...it,
      key: it.id.toString(),
      label: it.name,
      data: it,
    }))

    return arrayToTree<NetworkTreeNode>(normalizedList)
  }

  return undefined
})

/**
 * 监听网络树数据变化，确保始终有一个激活的网络
 *
 * 当网络树数据加载完成后：
 * - 检查是否已有选中的网络（activeNetworkId）
 * - 如果没有选中的网络，则自动选择第一个网络作为默认选中项
 * - immediate: true 确保组件初始化时立即执行一次回调
 */
watch(networkTree, (newTree) => {
  if (!activeNetworkId.value) {
    const firstNetwork = newTree?.at(0)

    if (firstNetwork) {
      activeNetworkId.value = firstNetwork.id
    }
  }
}, { immediate: true })

// 默认展开所有网络层级
const expandedKeys = computed<ExpandedKeys>(() => {
  const keys: ExpandedKeys = {}

  if (!networkTree.value) {
    return keys
  }

  // 递归遍历网络树，获取所有节点的 key
  const traverseTree = (nodes: NetworkTreeNode[]) => {
    nodes.forEach((node) => {
      // 将当前节点的 key 添加到展开键值对象中
      keys[node.key] = true

      // 如果当前节点有子节点，则继续遍历
      if (Array.isArray(node.children) && node.children.length > 0) {
        traverseTree(node.children)
      }
    })
  }

  traverseTree(networkTree.value)

  return keys
})

const selectionKeys = computed(() => ({ [`${activeNetworkId.value}`]: true }))

const handleTreeNodeSelect = (node: NetworkTreeNode) => {
  activeNetworkId.value = node.id
}

const { popMenuRef: ctxMenuRef, popMenu: ctxMenu } = usePopMenu()

const selectedNode = ref<NetworkTreeNode>()

const handleItemContext = (ev: { originalEvent: MouseEvent }) => {
  ctxMenu.value?.show(ev.originalEvent)
}

const menuOptions: PrimeVueMenuItem[] = [
  {
    label: '编辑',
    command: () => {
      if (selectedNode.value) {
        openUpdateModal(selectedNode.value.data)
      }
    },
  },
  {
    label: '删除',
    command: async () => {
      const networkId = selectedNode.value?.id

      if (networkId) {
        $confirm.dialog({
          header: '确定删除网络吗？',
          message: `删除网络将同时删除该网络下的所有设备，请谨慎操作。`,
          accept: async () => {
            await AssetService.deleteNetwork(networkId)
            refetchNetworkList()
          },
        })
      }
    },
  },
]

const statsColumns = [
  { field: 'network_equipment', header: '网络设备' },
  { field: 'node', header: '节点' },
] satisfies { field: keyof NetworkTreeNode['count'], header: string }[]
</script>

<template>
  <div class="flex h-full flex-col">
    <div class="justify-between pb-4 flex-center">
      <h2 class="text-xl font-bold">
        网络
      </h2>

      <Button
        label="添加网络"
        severity="secondary"
        @click="openCreateModal()"
      />
    </div>

    <div class="min-h-0 flex-1">
      <TreeTable
        v-model:contextMenuSelection="selectedNode"
        contextMenu
        :expandedKeys="expandedKeys"
        :loading="isLoading"
        scrollable
        scrollHeight="flex"
        :selectionKeys="selectionKeys"
        selectionMode="single"
        size="small"
        :value="networkTree"
        @nodeSelect="handleTreeNodeSelect"
        @rowContextmenu="handleItemContext($event)"
      >
        <Column
          expander
          field="name"
          header="网络名称"
        />

        <Column
          v-for="col of statsColumns"
          :key="col.field"
          class="text-sm"
          :header="col.header"
        >
          <template #body="{ node }: { node: NetworkTreeNode }">
            <NumericDisplay>
              {{ node.count?.[col.field] }}
            </NumericDisplay>
          </template>
        </Column>

        <template #loadingicon>
          <div class="size-full justify-center bg-content py-6 flex-center">
            <Message severity="secondary">
              数据加载中...
            </Message>
          </div>
        </template>
      </TreeTable>

      <ContextMenu
        :ref="ctxMenuRef"
        :model="menuOptions"
        @hide="selectedNode = undefined"
      />

      <ProDialogForm
        :formActionGroupProps="{
          confirmButtonProps: {
            label: confirmBtnLabel,
          },
        }"
        :formProps="{ resolver: formResolver }"
        :initialValues="formValues"
        :loading="loading"
        :title="formTitleText"
        :visible="modalVisible"
        @cancel="handleClose"
        @submit="handleSubmit"
        @update:visible="handleModalVisibleChange"
      >
        <FormItem
          label="网络名称"
          name="name"
          required
        >
          <template #default="{ id }">
            <InputText
              :id="id"
              v-model="formValues.name"
              placeholder="输入便于识别的网络名称"
            />
          </template>

          <template #helpTip>
            例如：总部网络、分公司网络、测试网络等
          </template>
        </FormItem>

        <FormItem
          label="上级网络"
          name="parent_id"
        >
          <FormItemTreeSelect
            v-model:value="formValues.parent_id"
            :disabledKeys="updatingItem?.id.toString()"
            :options="networkTree"
            placeholder="选择上级网络"
            showClear
          />
        </FormItem>
      </ProDialogForm>
    </div>
  </div>
</template>
