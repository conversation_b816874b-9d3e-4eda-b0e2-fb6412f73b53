<script setup lang="ts">
import type { AssetType } from '~/enums/asset'

import type AssetDetailEditForm from './AssetDetailEditForm.vue'

const { refKey: refEditFormKey, ref: refEditForm } = useRef<InstanceType<typeof AssetDetailEditForm>>()

const props = defineProps<{
  assetType?: AssetType
  assetId?: BasicAssetDetail['id']
}>()

const assetType = toRef(props, 'assetType')
const assetId = toRef(props, 'assetId')

const { data: assetDetail, refetch: fetchAssetDetail } = useQueryAssetDetail({
  assetType: assetType,
  assetId: assetId,
  options: {
    enabled: false,
  },
})

const handleEditClick = async () => {
  const res = await fetchAssetDetail()
  refEditForm?.value?.openUpdateModal(res.data as BasicAssetDetail)
}

const emit = defineEmits<{
  saveSuccess: []
}>()
</script>

<template>
  <ProBtnEdit
    v-if="assetType && assetDetail?.network_equipment_id"
    onlyIcon
    severity="secondary"
    size="small"
    variant="text"
    @click="handleEditClick()"
  />

  <AssetDetailEditForm
    v-if="assetType && assetDetail?.network_equipment_id"
    :ref="refEditFormKey"
    :assetType="assetType"
    :networkDeviceId="assetDetail.network_equipment_id"
    @submitSuccess="emit('saveSuccess')"
  />
</template>
