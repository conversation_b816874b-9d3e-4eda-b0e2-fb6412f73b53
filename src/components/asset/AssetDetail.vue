<script setup lang="ts">
import { ChartLineIcon, SearchIcon } from 'lucide-vue-next'

import { assetDetailConfig, AssetDetailTab, AssetType } from '~/enums/asset'

const store = useAssetInfoStore()

const { activeAssetTab, isAssetCollection, activeNetworkDeviceId, viewedServer } = storeToRefs(store)

type AssetTab = {
  type: AssetDetailTabType
  label: string
  icon: Component
}

// 从资产详情标签配置中获取资产详情标签列表
const assetTabs = computed(() => {
  const tabs: AssetTab[] = Object.values(assetDetailConfig)

  if (isAssetCollection.value) {
    return tabs.filter((tab) => tab.type !== AssetType.NetworkDevice)
  }

  tabs.unshift({
    type: 'search',
    label: '资产搜索',
    icon: SearchIcon,
  })

  tabs.push({
    type: 'ip-log',
    label: 'IP 变更记录 ',
    icon: ChartLineIcon,
  })

  return tabs
})

watchEffect(() => {
  if (isAssetCollection.value) {
    const firstTab = assetTabs.value.at(0)

    if (firstTab) {
      activeAssetTab.value = firstTab.type
    }
  }
})

const assetSearchValue = ref('')
</script>

<template>
  <div class="size-full">
    <Tabs
      v-model:value="activeAssetTab"
      class="h-full"
    >
      <TabList>
        <Tab
          v-for="asset of assetTabs"
          :key="asset.type"
          :value="asset.type"
        >
          <span class="gap-2 flex-center">
            <Component
              :is="asset.icon"
              v-if="asset.icon"
              :size="16"
            />
            {{ asset.label }}
          </span>
        </Tab>
      </TabList>

      <TabPanels class="h-full min-h-0 flex-1 !p-0 !pt-in-tab">
        <TabPanel
          v-for="asset of assetTabs"
          :key="asset.type"
          class="h-full"

          :value="asset.type"
        >
          <template v-if="activeAssetTab === asset.type">
            <AssetDetailAddress
              v-if="asset.type === AssetType.NetworkDevice"
            />

            <template v-else-if="asset.type === AssetDetailTab.Search">
              <AssetSearchResult
                v-if="assetSearchValue"
                :networkDeviceId="activeNetworkDeviceId"
                :searchValue="assetSearchValue"
                @searchValueChange="assetSearchValue = $event"
              />

              <AssetSearchBox
                v-else
                @triggerSearch="assetSearchValue = $event"
              />
            </template>

            <AssetIpLog
              v-else-if="asset.type === AssetDetailTab.IpLog"
            />

            <template v-else>
              <AssetDetailTable
                v-if="isAssetCollection"
                :assetType="asset.type as AssetType"
              />

              <ProSplitter
                v-else
                leftPanelContentClass="!p-0"
              >
                <template
                  v-if="!isAssetCollection"
                  #left
                >
                  <AssetDetailTemplateList
                    :assetType="asset.type as AssetType"
                    :title="asset.label"
                  />
                </template>

                <template #right>
                  <AssetDetailTable :assetType="asset.type as AssetType" />
                </template>
              </ProSplitter>
            </template>
          </template>
        </TabPanel>
      </TabPanels>
    </Tabs>

    <!-- 由于终端设备的详情可以从不同的 tab 中触发打开，所以需要把组件放置在这里，全局监听打开终端设备详情 -->
    <AssetServerInfo
      :serverId="viewedServer?.id"
      @close="store.setViewedServer(undefined)"
    />
  </div>
</template>
