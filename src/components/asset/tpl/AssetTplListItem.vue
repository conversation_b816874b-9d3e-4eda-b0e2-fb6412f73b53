<script setup lang="ts">
import { BoltIcon } from 'lucide-vue-next'

interface Props {
  item?: AssetTemplateListItem
  isActive?: boolean
  editable?: boolean
}

defineProps<Props>()

const emit = defineEmits<{
  select: []
  openMenu: [event: MouseEvent]
}>()

defineSlots<{
  default: [item: AssetTemplateListItem]
}>()
</script>

<template>
  <li
    class="group/tpl-li cursor-pointer rounded-lg px-3 py-2 transition-colors duration-200 flex-center"
    :class="[isActive ? 'bg-emphasis' : 'hover:bg-emphasis']"
    @click="emit('select')"
    @contextmenu.stop.prevent="editable && emit('openMenu', $event)"
  >
    <slot :item="item">
      <div class="w-full gap-3 font-semibold flex-center">
        <span class="size-6 shrink-0 justify-center inline-flex-center">
          <IconRender :iconVal="item?.icon">
            <template #fallback>
              <BoltIcon class="opacity-85" />
            </template>
          </IconRender>
        </span>

        <span class="min-w-0 flex-1 truncate">{{ item?.name }}</span>

        <span
          v-if="editable && item?.is_update"
          class="ml-auto hidden shrink-0 group-hover/tpl-li:inline-flex"
        >
          <ProBtnMore
            :dt="{
              sm: {
                padding: {
                  x: '0.1rem',
                  y: '0.1rem',
                },
              },
            }"
            onlyIcon
            size="small"
            variant="text"
            @click.stop="emit('openMenu', $event)"
          />
        </span>
      </div>
    </slot>
  </li>
</template>
