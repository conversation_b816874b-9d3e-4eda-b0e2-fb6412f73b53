<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { BoltIcon } from 'lucide-vue-next'
import { object } from 'valibot'

import type { AssetType } from '~/enums/asset'
import { ProMenuActionType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'

interface AssetTemplateTreeProps {
  assetType: AssetType
  selectItem?: AssetTemplateListItem['id']
}

const props = defineProps<AssetTemplateTreeProps>()

const assetType = toRef(props, 'assetType')

const emit = defineEmits<{
  'update:selectItem': [item: AssetTemplateListItem['id'] | undefined]
}>()

const selectedAssetTemplateId = computed({
  get: () => props.selectItem,
  set: (val) => {
    emit('update:selectItem', val)
  },
})

const { data: assetTemplateList, refetch: refetchAssetTemplate, isLoading } = useQuery({
  queryKey: queryKeys.asset.template.all(assetType),
  queryFn: () => {
    if (assetType.value) {
      return AssetService.getAssetTemplateList({ asset_type: assetType.value })
    }

    return null
  },
  enabled: !!assetType.value,
})

const {
  loading,
  isFormCreate,
  isFormUpdate,
  formValues,
  formResolver,
  modalVisible,
  openCreateModal,
  openUpdateModal,
  confirmBtnLabel,
  handleClose,
  handleSubmit,
  formTitleText,
  updatingItem: updatingAssetTemplate,
  handleModalVisibleChange,
} = useFormControl<AssetTemplateFormValues, AssetTemplateListItem>({
  initialValues: {
    icon: '',
  },
  resolver: object({
    name: schemaNotEmptyString('请填写模板名称'),
  }),
  btnLabel: {
    create: '添加模板',
    update: '保存',
  },
  formTitle: {
    create: '添加资产模板',
    update: '编辑资产模板',
  },
  fetchDetail: (updatingItem) => toRaw(updatingItem),
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      if (isFormCreate.value) {
        await AssetService.createAssetTemplate(props.assetType, {
          name: states.name.value,
          icon: formValues.value.icon,
        })
      }
      else if (isFormUpdate.value) {
        const updateItemId = updatingAssetTemplate.value?.id

        if (updateItemId) {
          await AssetService.updateAssetTemplate(updateItemId, formValues.value)
        }
      }
    }
  },
  onSubmitSuccess: () => {
    refetchAssetTemplate()
  },
})

watch(assetTemplateList, (newList) => {
  selectedAssetTemplateId.value = newList?.list.at(0)?.id
}, { immediate: true })

const { dualMenuRef, dualMenu } = useDualMenu()

const { $confirm } = useNuxtApp()

const selectedItem = ref<AssetTemplateListItem>()
const deleteItem = ref<AssetTemplateListItem>()

const handleOpenMenu = (item: AssetTemplateListItem, ev: MouseEvent) => {
  if (item.is_update) {
    selectedItem.value = item
    dualMenu.value?.showMenu(ev)
  }
}

const handleSelect = (item: AssetTemplateListItem) => {
  selectedAssetTemplateId.value = item.id
}

const menuOptions: ProMenuItem[] = [
  {
    label: '编辑',
    actionType: ProMenuActionType.Edit,
    command: () => {
      if (selectedItem.value) {
        openUpdateModal(selectedItem.value)
      }
    },
  },
  {
    label: '删除',
    actionType: ProMenuActionType.Delete,
    command: () => {
      deleteItem.value = selectedItem.value

      $confirm.dialog({
        message: `确定要删除资产模板「${deleteItem.value?.name}」吗？`,
        accept: async () => {
          if (deleteItem.value?.id) {
            await AssetService.deleteAssetTemplate(deleteItem.value.id)
            refetchAssetTemplate()
          }

          deleteItem.value = undefined
        },
        reject: () => {
          deleteItem.value = undefined
        },
      })
    },
  },
]

const { popoverRef, popover } = usePopover()
</script>

<template>
  <div class="flex flex-col">
    <AssetTplList
      :activeTemplateId="selectedAssetTemplateId"
      editable
      :loading="isLoading"
      :templateList="assetTemplateList?.list"
      @openMenu="handleOpenMenu"
      @select="handleSelect"
    />

    <DualMenu
      :ref="dualMenuRef"
      :menuItems="menuOptions"
    />

    <Divider />

    <Button
      fluid
      label="添加资产模板"
      severity="secondary"
      @click="openCreateModal()"
    />

    <ProDialogForm
      :formActionGroupProps="{
        confirmButtonProps: {
          label: confirmBtnLabel,
        },
      }"
      :initialValues="formValues"
      :loading="loading"
      :resolver="formResolver"
      :title="formTitleText"
      :visible="modalVisible"
      @cancel="handleClose"
      @submit="handleSubmit"
      @update:visible="handleModalVisibleChange"
    >
      <FormItem
        label="模板名称"
        name="name"
        required
      >
        <template #default="{ id }">
          <InputText
            :id="id"
            v-model.trim="formValues.name"
            placeholder="这个模板将用于什么？"
          />
        </template>

        <template #helpTip>
          模板名称将帮助您快速识别用途
        </template>
      </FormItem>

      <FormItem
        v-slot="{ field }: { field: FormFieldContext }"
        label="图标"
        name="icon"
      >
        <span>
          <Button
            severity="secondary"
            type="button"
            @click="popover?.toggle"
          >
            <span class="inline-block size-8">
              <IconRender :iconVal="formValues.icon">
                <template #fallback>
                  <BoltIcon class="opacity-85" />
                </template>
              </IconRender>
            </span>
          </Button>
        </span>

        <Popover :ref="popoverRef">
          <ScrollPanel class="max-h-[420px] w-[420px]">
            <IconCollection
              :iconVal="formValues.icon"
              @update:iconVal="formValues.icon = $event, field.onChange({ target: { value: $event } }), popover?.hide()"
            />
          </ScrollPanel>
        </Popover>
      </FormItem>
    </ProDialogForm>
  </div>
</template>
