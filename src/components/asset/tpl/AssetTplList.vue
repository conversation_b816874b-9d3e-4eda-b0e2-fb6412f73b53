<script setup lang="ts">
import { ShieldQuestionIcon } from 'lucide-vue-next'

interface AssetTemplateListProps {
  loading?: boolean
  templateList?: AssetTemplateListItem[]
  activeTemplateId?: AssetTemplateListItem['id']
  /** 是否显示未知模板 */
  showUnknown?: boolean
  /** 是否可编辑 */
  editable?: boolean
}

defineProps<AssetTemplateListProps>()

const emit = defineEmits<{
  select: [template: AssetTemplateListItem | undefined]
  openMenu: [template: AssetTemplateListItem, event: MouseEvent]
}>()

const handleSelect = (template: AssetTemplateListItem | undefined) => {
  emit('select', template)
}

const handleOpenMenu = (template: AssetTemplateListItem, event: MouseEvent) => {
  emit('openMenu', template, event)
}

const skeletonCount = 8
</script>

<template>
  <ul class="m-0 flex w-full list-none flex-col gap-1">
    <AssetTplListItem
      v-if="showUnknown"
      :isActive="!activeTemplateId"
      @select="handleSelect(undefined)"
    >
      <div class="gap-3 truncate font-semibold flex-center">
        <span class="size-6 justify-center inline-flex-center">
          <ShieldQuestionIcon class="opacity-85" />
        </span>

        <span>未确认</span>
      </div>
    </AssetTplListItem>

    <template v-if="loading">
      <li
        v-for="idx of skeletonCount"
        :key="idx"
        class="py-1"
        :style="{ opacity: 1 - idx / skeletonCount }"
      >
        <Skeleton
          height="25px"
          width="100%"
        />
      </li>
    </template>

    <template v-else>
      <AssetTplListItem
        v-for="it of templateList"
        :key="it.id"
        :editable="editable"
        :isActive="activeTemplateId === it.id"
        :item="it"
        @openMenu="handleOpenMenu(it, $event)"
        @select="handleSelect(it)"
      />
    </template>
  </ul>
</template>
