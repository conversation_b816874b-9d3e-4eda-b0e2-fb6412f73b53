<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { Tag } from 'primevue'
import { number, object } from 'valibot'

import ProBtnDelete from '~/components/pro/btn/ProBtnDelete.vue'
import ProBtnEdit from '~/components/pro/btn/ProBtnEdit.vue'
import { AssetFieldType, assetFieldTypeConfig } from '~/enums/asset'
import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'
import type { ProSelectOption } from '~/types/pro'
import { schemaVar } from '~/types/schema'

interface AssetTemplateFieldsProps {
  /** 当前查看的资产模板 ID */
  assetTemplateId: AssetTemplate['id']
}

const props = defineProps<AssetTemplateFieldsProps>()

const assetTemplateId = computed(() => props.assetTemplateId)

const { data: assetTemplateFieldList, isLoading, refetch: refetchAssetTemplateFieldList } = useQuery({
  queryKey: queryKeys.asset.template.field.all(assetTemplateId),
  queryFn: () => {
    if (assetTemplateId.value) {
      return AssetService.getAssetTemplateFieldList({ template_id: assetTemplateId.value })
    }

    return null
  },
  enabled: !!assetTemplateId.value,
})

const {
  loading,
  isFormCreate,
  isFormUpdate,
  formValues,
  forceUpdateKey,
  formResolver,
  confirmBtnLabel,
  formTitleText,
  updatingItem,
  openCreateModal,
  openUpdateModal,
  modalVisible,
  handleClose,
  handleSubmit,
  handleModalVisibleChange,
} = useFormControl<AssetFieldFormValues, AssetField>({
  initialValues: {
    name: '',
    field_name: '',
    field_type_id: AssetFieldType.Input,
    kwargs: {
      required: false,
      unique: false,
      displayInList: false,
    },
  },
  resolver: object({
    name: schemaNotEmptyString('请填写字段别名'),
    field_name: schemaVar(),
    field_type_id: number('请选择字段类型'),
  }),
  btnLabel: {
    create: '添加字段',
    update: '保存字段',
  },
  formTitle: {
    create: '添加字段',
    update: '编辑字段',
  },
  fetchDetail: (it) => toRaw(it),
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      if (isFormCreate.value) {
        const newField: AssetFieldFormValues = {
          name: states.name.value,
          field_name: states.field_name.value,
          field_type_id: states.field_type_id.value,
          kwargs: formValues.value.kwargs,
        }

        await AssetService.createAssetTemplateField({
          ...newField,
          template_id: assetTemplateId.value,
        })
      }
      else if (isFormUpdate.value) {
        const updatingFieldId = updatingItem.value?.id

        if (updatingFieldId) {
          const newField: AssetFieldFormValues = {
            ...formValues.value,
            name: states.name.value,
            field_type_id: states.field_type_id.value,
          }

          await AssetService.updateAssetTemplateField(updatingFieldId, newField)
        }
      }
    }
  },
  onSubmitSuccess: () => {
    refetchAssetTemplateFieldList()
  },
})

const formSubmitButton = useTemplateRef<HTMLButtonElement>('form-submit')

const handleSubmitButtonClick = () => {
  formSubmitButton.value?.click()
}

const handleDeleteField = async (currentAssetField: AssetField) => {
  await AssetService.deleteAssetTemplateField(currentAssetField.id)

  refetchAssetTemplateFieldList()
}

const searchField = ref<string>()

/** 字段类型选项 */
const fieldTypeOptions = computed(() => {
  const fieldTypeList = Object.values(assetFieldTypeConfig)

  return fieldTypeList.reduce<ProSelectOption[]>((acc, item) => {
    if (searchField.value) {
      if (item.name.includes(searchField.value)) {
        acc.push({ label: item.name, value: item.id })
      }
    }
    else {
      acc.push({ label: item.name, value: item.id })
    }

    return acc
  }, [])
})

const columns = computed<ProTableColumn<AssetField>[]>(() => [
  {
    field: 'field_name',
    header: '英文标识',
  },
  {
    field: 'name',
    header: '字段展示名称',
    render: (record) => h('strong', record.name),
  },
  {
    field: 'field_type_id',
    header: '类型',
    width: '240px',
    render: (record) => {
      const fieldType = assetFieldTypeConfig[record.field_type_id]

      return h('div', { class: 'flex items-center' }, [
        h(fieldType.icon, { class: 'mr-1', size: 15 }),
        fieldType.name,
      ])
    },
  },
  {
    field: 'kwargs',
    header: '属性',
    width: '240px',
    render: (record) => {
      if (!record.kwargs?.required && !record.kwargs?.unique) {
        return h('span', '-')
      }

      return h('div', { class: 'gap-1 flex-center' },
        [
          record.kwargs?.required && h(Tag, { severity: 'info', value: '必填' }),
          record.kwargs?.unique && h(Tag, { severity: 'success', value: '唯一' }),
          record.kwargs?.displayInList && h(Tag, { severity: 'secondary', value: '展示' }),
        ],
      )
    },
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (record) => {
      return h('div', { class: 'table-action-group' }, record.is_update
        ? [
            h(ProBtnEdit, {
              onlyIcon: true,
              onClick: () => openUpdateModal(record),
            }),
            h(ProBtnDelete, {
              onlyIcon: true,
              confirmMessage: `确定要删除字段「${record.name}」吗？`,
              deleteSuccessMessage: `字段「${record.name}」已删除`,
              onConfirm: () => handleDeleteField(record),
            }),
          ]
        : [h('span', { class: 'px-5' }, '-')])
    },
  },
])
</script>

<template>
  <div>
    <ProTable
      :columns="columns"
      :dataSource="assetTemplateFieldList"
      :loading="isLoading"
    >
      <template #toolbarRight>
        <div class="flex-center">
          <div class="ml-auto">
            <Button
              label="添加字段"
              @click="openCreateModal()"
            />
          </div>
        </div>
      </template>
    </ProTable>

    <Drawer
      class="!w-[650px]"
      :header="formTitleText"
      position="right"
      :visible="modalVisible"
      @update:visible="handleModalVisibleChange"
    >
      <Form
        :key="forceUpdateKey"
        class="h-full"
        :initialValues="formValues"
        :resolver="formResolver"
        @submit="handleSubmit"
      >
        <div class="flex h-full flex-col gap-form-field">
          <Fieldset legend="基础信息">
            <div class="grid grid-cols-2 gap-form-field pt-in-fieldset">
              <FormItem
                v-slot="{ id }"
                label="英文标识"
                name="field_name"
                required
              >
                <InputText
                  :id="id"
                  v-model="formValues.field_name"
                  placeholder="用于数据库中字段名"
                />
              </FormItem>

              <FormItem
                v-slot="{ id }"
                label="字段展示名称"
                name="name"
                required
              >
                <InputText
                  :id="id"
                  v-model="formValues.name"
                  placeholder="用于列表中展示"
                />
              </FormItem>

              <FormItem
                v-slot="{ id }"
                label="字段类型"
                name="field_type_id"
                required
              >
                <ProSelect
                  v-model="formValues.field_type_id"
                  v-model:searchValue="searchField"
                  :disabled="isFormUpdate"
                  :labelId="id"
                  :options="fieldTypeOptions"
                  :search="{
                    enable: true,
                    placeholder: '搜索字段类型',
                  }"
                >
                  <template #option="{ option }: { option: ProSelectOption }">
                    <div class="flex items-center">
                      <Component
                        :is="assetFieldTypeConfig[option.value as AssetFieldType].icon"
                        v-if="Object.hasOwn(assetFieldTypeConfig, option.value) && assetFieldTypeConfig[option.value as AssetFieldType].icon"
                        class="mr-2"
                        :size="15"
                      />
                      <div>{{ option.label }}</div>
                    </div>
                  </template>

                  <template #value="{ value }: { value: AssetFieldType }">
                    <div class="flex items-center">
                      <Component
                        :is="assetFieldTypeConfig[value as AssetFieldType].icon"
                        v-if="Object.hasOwn(assetFieldTypeConfig, value) && assetFieldTypeConfig[value as AssetFieldType].icon"
                        class="mr-2"
                        :size="15"
                      />
                      <div>{{ assetFieldTypeConfig[value as AssetFieldType].name }}</div>
                    </div>
                  </template>
                </ProSelect>
              </FormItem>

              <FormItem
                v-if="formValues.kwargs"
                v-slot="{ id }"
                label="字段默认值"
                name="kwargs.defaultValue"
              >
                <AssetField
                  :fieldType="formValues.field_type_id"
                  :inputId="id"
                  :options="formValues.kwargs.options"
                  placeholder="在表单中默认设置的值"
                  :value="formValues.kwargs.defaultValue"
                  @update:value="formValues.kwargs.defaultValue = $event"
                />
              </FormItem>
            </div>
          </Fieldset>

          <Fieldset
            v-if="!!formValues.kwargs"
            legend="通用属性"
          >
            <div class="flex flex-wrap gap-form-field-large pt-in-fieldset">
              <FormItem
                v-slot="{ id }"
                label="必填"
                layout="horizontal"
                name="kwargs.required"
              >
                <ToggleSwitch
                  v-model="formValues.kwargs.required"
                  :inputId="id"
                />
              </FormItem>

              <FormItem
                v-slot="{ id }"
                label="唯一"
                layout="horizontal"
                name="kwargs.unique"
              >
                <ToggleSwitch
                  v-model="formValues.kwargs.unique"
                  :disabled="isFormUpdate"
                  :inputId="id"
                />
              </FormItem>
            </div>
          </Fieldset>

          <Fieldset
            v-if="!!formValues.kwargs"
            legend="高级属性"
          >
            <div class="flex flex-col gap-form-field-large pt-in-fieldset">
              <FormItem
                v-if="assetFieldTypeConfig[formValues.field_type_id].hasPlaceholder"
                v-slot="{ id }"
                label="内容输入提示"
                name="kwargs.placeholder"
              >
                <InputText
                  :id="id"
                  v-model="formValues.kwargs.placeholder"
                  placeholder="该字段在表单中显示的提示文本"
                />
              </FormItem>

              <FormItem
                v-if="assetFieldTypeConfig[formValues.field_type_id].enumable"
                label="选项"
              >
                <div class="w-1/2">
                  <AssetTplFieldOptions
                    v-model="formValues.kwargs.options"
                  />
                </div>
              </FormItem>
            </div>
          </Fieldset>
        </div>

        <button
          v-show="false"
          ref="form-submit"
          type="submit"
        />
      </Form>

      <template #footer>
        <Divider />

        <FormActionGroup
          :confirmButtonProps="{
            label: confirmBtnLabel,
          }"
          :loading="loading"
          @cancel="handleClose"
          @confirm="handleSubmitButtonClick"
        />
      </template>
    </Drawer>
  </div>
</template>
