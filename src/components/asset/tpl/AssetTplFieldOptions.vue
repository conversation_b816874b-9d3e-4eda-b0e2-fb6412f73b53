<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus'

import { GripVerticalIcon, PlusIcon, TrashIcon } from 'lucide-vue-next'
import { nanoid } from 'nanoid'

interface OptionItem {
  label: string
}

const { $confirm } = useNuxtApp()

const options = defineModel<OptionItem[]>('modelValue', {
  default: () => [],
})

const newOptionValue = ref('')
const isDuplicate = ref(false)

const isDragging = ref(false)

// 监听输入值变化，实时检查是否重复
watch(newOptionValue, (value) => {
  if (value) {
    isDuplicate.value = options.value.some((opt) => opt.label === value)
  }
  else {
    isDuplicate.value = false
  }
})

const handleAddOption = () => {
  if (newOptionValue.value && !isDuplicate.value) {
    options.value = [
      { label: newOptionValue.value },
      ...options.value,
    ]
    newOptionValue.value = ''
  }
}

const handleRemoveOption = (index: number) => {
  if (Array.isArray(options.value)) {
    const optionToDelete = options.value[index]

    $confirm.dialog({
      message: `确定要删除选项「${optionToDelete.label}」吗？`,
      accept: () => {
        options.value = options.value.filter((_, idx) => idx !== index)
      },
    })
  }
}

const handlerClass = nanoid(4)

const addable = computed(() => {
  return !isDuplicate.value && newOptionValue.value.trim().length > 0
})
</script>

<template>
  <div class="flex flex-col gap-2">
    <div>
      <div class="grid grid-cols-[1.5rem_1fr_2rem] items-center gap-2">
        <div />

        <InputText
          v-model="newOptionValue"
          fluid
          :invalid="isDuplicate"
          placeholder="输入新选项"
          size="small"
        />

        <ProBtn
          v-tooltip.top="addable ? '添加新选项' : '输入选项值以添加'"
          :disabled="!addable"
          severity="secondary"
          size="small"
          variant="outlined"
          @click="handleAddOption()"
        >
          <template #icon>
            <PlusIcon :size="15" />
          </template>
        </ProBtn>
      </div>

      <div
        v-if="isDuplicate"
        class="grid grid-cols-[1.5rem_1fr_2rem] items-center gap-2 pt-1"
      >
        <div />

        <Message
          severity="error"
          size="small"
          variant="simple"
        >
          选项值已存在
        </Message>
      </div>
    </div>

    <!-- 已添加的选项列表 -->
    <VueDraggable
      v-model="options"
      :animation="150"
      class="flex flex-col gap-2"
      :handle="`.${handlerClass}`"
      itemid="label"
      @end="isDragging = false"
      @start="isDragging = true"
    >
      <div
        v-for="(item, idx) of options"
        :key="item.label"
        class="grid grid-cols-[1.5rem_1fr_2rem] items-center gap-2"
      >
        <div>
          <div
            v-show="!isDragging"
            class="cursor-grab justify-center rounded p-1 flex-center hover:bg-emphasis"
            :class="handlerClass"
          >
            <GripVerticalIcon
              class="opacity-50"
              :size="15"
            />
          </div>
        </div>

        <InputText
          v-model="options[idx].label"
          fluid
          size="small"
        />

        <div class="basis-8">
          <ProBtn
            v-show="!isDragging"
            severity="danger"
            size="small"
            variant="text"
            @click="handleRemoveOption(idx)"
          >
            <template #icon>
              <TrashIcon :size="15" />
            </template>
          </ProBtn>
        </div>
      </div>
    </VueDraggable>
  </div>
</template>
