<script setup lang="ts">
defineProps<{
  ipList?: ServerInfo['ip_list']
}>()

defineSlots<{
  default: () => VNode
}>()
</script>

<template>
  <ProPopover
    v-if="ipList"
    position="bottom-start"
    :showDelay="500"
    trigger="hover"
  >
    <template #default>
      <slot />
    </template>

    <template #content>
      <div
        v-for="(ip, idx) of ipList"
        :key="ip.id"
        class="space-y-1"
      >
        <ul class="space-y-2">
          <li>
            <Tag
              class="!font-medium"
              :value="`IPv4: ${ip.ipv4}`"
            />
          </li>

          <li>
            <Tag
              class="!font-medium"
              :value="`IPv6: ${ip.ipv6 || '-'}`"
            />
          </li>

          <li>
            <Tag
              class="!font-medium"
              :value="`MAC: ${ip.mac || '-'}`"
            />
          </li>
        </ul>

        <Divider v-if="idx !== ipList.length - 1" />
      </div>
    </template>
  </ProPopover>

  <span v-else>
    -
  </span>
</template>
