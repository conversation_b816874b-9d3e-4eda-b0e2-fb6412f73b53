<script setup lang="ts">
const searchText = ref('')

const emit = defineEmits<{
  triggerSearch: [string | undefined]
}>()
</script>

<template>
  <div class="h-full justify-center flex-center">
    <div class="flex flex-col items-center justify-center gap-6">
      <div class="mb-2 text-4xl font-bold">
        资产搜索
      </div>

      <div class="w-full max-w-2xl">
        <AssetSearchInput
          v-model="searchText"
          showSearchButton
          @change="emit('triggerSearch', $event)"
        />

        <div class="mt-2 text-center text-sm text-secondary">
          支持搜索资产名称、IP 地址、MAC 地址等
        </div>
      </div>
    </div>
  </div>
</template>
