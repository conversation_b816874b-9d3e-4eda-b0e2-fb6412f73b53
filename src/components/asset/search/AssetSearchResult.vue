<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { assetConfig, assetCriticalityConfig, assetSearchConfig, AssetSearchType, AssetType, subnetIpStatusConfig } from '~/enums/asset'
import { queryKeys } from '~/enums/query-key'

const skeletonCount = 8

const props = defineProps<{
  searchValue?: string
  networkDeviceId?: NetworkDevice['id']
}>()

const searchValue = toRef(props, 'searchValue')
const networkDeviceId = toRef(props, 'networkDeviceId')

const emit = defineEmits<{
  searchValueChange: [string | undefined]
}>()

const searchText = ref<string>()

watch(searchValue, (newSearch) => {
  searchText.value = newSearch
}, { immediate: true })

const pageInfo = ref<PageInfo>({
  page: 1,
  page_size: 20,
  page_sum: 0,
  total: 0,
})

const pageParams = computed(() => ({
  page: pageInfo.value.page,
  page_size: pageInfo.value.page_size,
}))

const searchTypeAll = 'all'

const searchType = ref<AssetSearchType | typeof searchTypeAll>(searchTypeAll)

watch(searchType, () => {
  pageInfo.value.page = 1
})

const handlePageChange = (pInfo: PrimeVuePageInfo) => {
  const newPage = pInfo.page + 1
  const newPageSize = pInfo.rows

  const shouldTriggerChange = newPage !== pageInfo.value.page || newPageSize !== pageInfo.value.page_size

  if (shouldTriggerChange) {
    pageInfo.value.page = newPage
    pageInfo.value.page_size = newPageSize
  }
}

type TabItem = {
  type: AssetSearchType | null
  name: string
  count: number
}

const tabs = ref<TabItem[]>([])

const { data: results, isLoading } = useQuery({
  queryKey: queryKeys.asset.search({ searchValue, searchType, pageParams, networkDeviceId }),
  queryFn: () => {
    if (searchValue.value) {
      const realSearchType = searchType.value === searchTypeAll ? undefined : searchType.value

      return AssetService.getAssetSearch({
        search: searchValue.value,
        type: realSearchType,
        network_equipment_id: networkDeviceId.value,
        ...pageParams.value,
      })
    }

    return null
  },
  select: (data) => {
    const countItems = data?.count.filter((item) => item.count > 0)

    if (data) {
      pageInfo.value.total = data.total || 0
      pageInfo.value.page_sum = data.page_sum || 0
    }

    if (Array.isArray(countItems) && countItems.length > 0) {
      tabs.value = countItems.map((item) => ({
        type: item.type,
        name: item.name,
        count: item.count,
      }))
    }

    return data?.list
  },
})

const activeAsset = ref<AssetSearchItem>()

const canShowDetail = (item: AssetSearchItem) => {
  // IP 资产不需要展示详情
  return item.search_type !== AssetSearchType.Ip
}

const handleViewDetail = (seachItem: AssetSearchItem) => {
  if (canShowDetail(seachItem)) {
    activeAsset.value = seachItem
  }
}

const getAssetTag = (item: AssetSearchItem) => {
  if (item.search_type && item.search_type in assetSearchConfig) {
    return assetSearchConfig[item.search_type].label
  }

  return item.template?.name
}
</script>

<template>
  <div class="flex h-full flex-col">
    <div class="pb-4">
      <AssetSearchInput
        v-model="searchText"
        @change="emit('searchValueChange', $event)"
      />
    </div>

    <Tabs
      v-if="tabs.length > 0 && searchValue"
      v-model:value="searchType"
    >
      <TabList>
        <Tab
          v-for="countData of tabs"
          :key="countData.type || searchTypeAll"
          class="!px-4 !py-2 text-sm !font-medium"
          :value="countData.type || searchTypeAll"
        >
          <span>{{ countData.name }}</span>
          <span>
            ({{ countData.count }})
          </span>
        </Tab>
      </TabList>
    </Tabs>

    <ScrollPanel class="min-h-0 flex-1">
      <div class="p-in-tab">
        <div
          v-if="isLoading"
          class="space-y-4"
        >
          <div
            v-for="i of skeletonCount"
            :key="i"
          >
            <div class="space-y-3">
              <div class="gap-2 flex-center">
                <Skeleton
                  class="basis-10"
                  height="1.5rem"
                />
                <Skeleton
                  class="basis-1/3"
                  height="1.5rem"
                />
              </div>

              <div class="flex gap-2 opacity-70">
                <Skeleton
                  v-for="j of 6"
                  :key="j"
                  class="flex-1"
                />
              </div>
            </div>
            <Divider v-if="i !== skeletonCount" />
          </div>
        </div>

        <DataView
          v-else
          data-key="id"
          layout="list"
          :value="results"
        >
          <template #list="{ items }: { items: AssetSearchItem[] }">
            <div class="space-y-4">
              <template
                v-for="it of items"
                :key="it.id"
              >
                <div class="flex gap-5 rounded-md border border-divider px-4 py-3">
                  <div class="flex-1">
                    <div
                      class="gap-4 inline-flex-center"
                      :class="{
                        'relative z-0 cursor-pointer after:absolute after:-inset-x-2 after:-inset-y-0.5 after:-z-10 after:rounded-md hover:after:bg-emphasis': canShowDetail(it),
                      }"
                      @click="handleViewDetail(it)"
                    >
                      <div class="size-6">
                        <IconRender
                          v-if="it.template?.icon"
                          :iconVal="it.template.icon"
                        >
                          <template #fallback>
                            <Component
                              :is="assetSearchConfig[it.search_type].icon"
                              class="size-6"
                            />
                          </template>
                        </IconRender>

                        <Component
                          :is="assetSearchConfig[it.search_type].icon"
                          v-else-if="it.search_type"
                          class="size-6"
                        />
                      </div>

                      <div class="flex-1 gap-2 truncate text-lg font-semibold flex-center">
                        <span v-if="it.asset?.name">
                          {{ it.asset?.name }}
                        </span>

                        <span
                          v-else-if="it.search_type === AssetSearchType.Ip"
                          class="gap-2 flex-center"
                        >
                          <span class="flex-wrap gap-x-5 gap-y-1 flex-center">
                            <span class="whitespace-nowrap">
                              IPv4: <strong class="font-medium">{{ it.ipv4 }}</strong>
                            </span>
                            <span
                              v-if="it.ipv6"
                              class="whitespace-nowrap"
                            >
                              IPv6: <strong class="font-medium">{{ it.ipv6 }}</strong>
                            </span>
                            <span
                              v-if="it.mac"
                              class="whitespace-nowrap"
                            >
                              MAC地址: <strong class="font-medium">{{ it.mac }}</strong>
                            </span>
                            <span class="gap-1 whitespace-nowrap flex-center">
                              状态:
                              <Tag
                                :severity="subnetIpStatusConfig[it.status].severity"
                                :value="subnetIpStatusConfig[it.status].label"
                              />
                            </span>
                          </span>
                        </span>

                        <span
                          v-else
                          class="opacity-70"
                        >
                          未确认
                        </span>
                      </div>
                    </div>

                    <div class="flex-1 flex-wrap gap-x-3 gap-y-2 pt-4 flex-center">
                      <Tag
                        v-if="getAssetTag(it)"
                        severity="secondary"
                        :value="getAssetTag(it)"
                      />

                      <template v-if="it.search_type === AssetSearchType.Ip">
                        <SearchLabelValue
                          v-if="it.server?.name"
                          :label="`所属${assetConfig[AssetType.Server].label}`"
                          :value="it.server.name"
                        />

                        <SearchLabelValue
                          label="所属网段"
                          :value="it.subnet.ip_segment"
                        />

                        <SearchLabelValue
                          label="所属网络设备"
                          :value="it.subnet?.network_equipment.asset?.name || '未确认'"
                        />

                        <SearchLabelValue
                          label="设备名称"
                          :value="it.asset?.name || '未确认'"
                        />
                      </template>

                      <template v-else-if="it.search_type === AssetSearchType.NetworkDevice">
                        <SearchLabelValue
                          label="设备名称"
                          :value="it.asset?.name || '未确认'"
                        />

                        <span
                          v-if="it.asset?.grade"
                          class="whitespace-nowrap"
                        >
                          重要程度:
                          <Tag
                            :severity="assetCriticalityConfig[it.asset.grade].severity"
                            :value="assetCriticalityConfig[it.asset.grade].label"
                          />
                        </span>

                        <AssetIpPopover :ipList="it.ip_list">
                          <SearchLabelValue
                            label="IP"
                            :value="it.ip_list.at(0)?.ipv4 || '-'"
                          />
                        </AssetIpPopover>

                        <SearchLabelValue
                          label="所属网络"
                          :value="it.network.name"
                        />
                      </template>

                      <template v-else-if="it.search_type === AssetSearchType.Server">
                        <AssetIpPopover :ipList="it.ip_list">
                          <SearchLabelValue
                            label="IP"
                            :value="it.ip_list.at(0)?.ipv4 || '-'"
                          />
                        </AssetIpPopover>

                        <span
                          v-if="it.asset?.grade"
                          class="whitespace-nowrap"
                        >
                          重要程度:
                          <Tag
                            :severity="assetCriticalityConfig[it.asset.grade].severity"
                            :value="assetCriticalityConfig[it.asset.grade].label"
                          />
                        </span>
                      </template>

                      <template v-else-if="it.search_type === AssetSearchType.Database || it.search_type === AssetSearchType.Web || it.search_type === AssetSearchType.Other">
                        <AssetIpPopover :ipList="it.server?.ip_list">
                          <SearchLabelValue
                            label="终端设备 IP"
                            :value="it.server?.ip_list.at(0)?.ipv4 || '-'"
                          />
                        </AssetIpPopover>

                        <SearchLabelValue
                          label="端口"
                          :value="it.port?.port || '-'"
                        />

                        <SearchLabelValue
                          :label="`所属${assetConfig[AssetType.Server].label}`"
                          :value="it.server?.name || '未确认'"
                        />

                        <span
                          v-if="it.asset?.grade"
                          class="whitespace-nowrap"
                        >
                          重要程度:
                          <Tag
                            :severity="assetCriticalityConfig[it.asset.grade].severity"
                            :value="assetCriticalityConfig[it.asset.grade].label"
                          />
                        </span>
                      </template>
                    </div>
                  </div>

                  <div class="ml-auto flex flex-col items-end justify-center gap-2">
                    <Button
                      v-if="canShowDetail(it)"
                      label="查看详情"
                      severity="secondary"
                      size="small"
                      @click="handleViewDetail(it)"
                    />
                  </div>
                </div>
              </template>
            </div>
          </template>

          <template #empty>
            <ProTableEmpty />
          </template>
        </DataView>
      </div>
    </ScrollPanel>

    <template v-if="pageInfo.total > 0">
      <Divider class="!mt-0" />

      <ProPaginator
        :pageInfo="pageInfo"
        :pageSizeOptions="[10, 20, 50, 100]"
        @pageChange="handlePageChange($event)"
      />
    </template>

    <AssetServerInfo
      v-if="activeAsset?.search_type === AssetSearchType.Server"
      :serverId="activeAsset?.id"
      @close="activeAsset = undefined"
    />

    <AssetDetailDialog
      v-else
      :assetId="activeAsset?.id"
      :assetType="activeAsset?.search_type ? assetSearchConfig[activeAsset.search_type].assetType : undefined"
      @close="activeAsset = undefined"
    />
  </div>
</template>
