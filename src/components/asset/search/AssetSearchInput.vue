<script setup lang="ts">
import { XIcon } from 'lucide-vue-next'
import type { InputTextProps } from 'primevue'

defineProps<{
  showSearchButton?: boolean
}>()

const modelValue = defineModel<InputTextProps['modelValue']>('modelValue')

const emit = defineEmits<{
  change: [InputTextProps['modelValue']]
}>()

const { popoverRef, popover } = usePopover()

const { searchHistory, addHistory, removeHistory, clearHistory } = useSearchHistory('asset-search')

const handleSearch = () => {
  emit('change', modelValue.value)

  popover.value?.hide()

  if (modelValue.value) {
    addHistory(modelValue.value)
  }
}

const handleHistoryClick = (keyword: string) => {
  modelValue.value = keyword
  emit('change', keyword)
  popover.value?.hide()
}

const handleOpenHistory = (event: FocusEvent) => {
  if (searchHistory.value.length > 0) {
    popover.value?.show(event)
  }
}

const handleRemoveHistory = (ev: MouseEvent, keyword: string) => {
  ev.stopPropagation()

  removeHistory(keyword)

  if (searchHistory.value.length === 0) {
    popover.value?.hide()
  }
}

const handleClearHistory = () => {
  clearHistory()
  popover.value?.hide()
}
</script>

<template>
  <div class="inline-block">
    <div class="gap-2 flex-center">
      <SearchInput
        v-model="modelValue"
        placeholder="输入资产关键字搜索"
        @click="handleOpenHistory"
        @enter="handleSearch()"
        @focus="handleOpenHistory"
      />

      <Button
        v-if="showSearchButton"
        :disabled="!modelValue"
        label="前往搜索"
        @click="handleSearch()"
      />
    </div>

    <Popover
      :ref="popoverRef"
      :pt="{ root: { class: 'min-w-[280px] max-w-[350px]' } }"
    >
      <div>
        <div class="justify-between pb-2 flex-center">
          <span class="text-sm text-secondary">搜索历史</span>
          <Button
            label="清空"
            severity="secondary"
            size="small"
            variant="text"
            @click="handleClearHistory"
          />
        </div>

        <div class="flex flex-wrap gap-2">
          <Chip
            v-for="keyword of searchHistory"
            :key="keyword"
            class="cursor-pointer text-sm"
            :label="keyword"
            removable
            @click="handleHistoryClick(keyword)"
            @remove="handleRemoveHistory($event, keyword)"
          >
            <template #removeicon="{ removeCallback }">
              <span
                class="opacity-50 inline-flex-center hover:opacity-100"
                @click="removeCallback"
              >
                <XIcon :size="14" />
              </span>
            </template>
          </Chip>
        </div>
      </div>
    </Popover>
  </div>
</template>
