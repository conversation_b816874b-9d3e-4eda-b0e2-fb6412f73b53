<script setup lang="ts">
import { AssetFieldType } from '~/enums/asset'

interface Props {
  value?: AnyType
  fieldType: AssetFieldType
  options?: {
    label?: string
    value: string
  }[]
  placeholder?: string
  inputId?: string
  readOnly?: boolean
}

const props = defineProps<Props>()

const { value, fieldType, placeholder, inputId, readOnly } = toRefs(props)

const emit = defineEmits<{
  'update:value': [value: Props['value']]
}>()

const commonProps = computed(() => ({
  modelValue: value.value,
  placeholder: placeholder.value,
  id: inputId.value,
  inputId: inputId.value,
  disabled: readOnly.value,
  'onUpdate:modelValue': (val: Props['value']) => emit('update:value', val),
}))

const datePickerProps = computed(() => ({
  ...commonProps.value,
  showIcon: true,
}))

const selectOptions = computed(() => props.options?.map((opt) => ({ label: opt.label ?? opt.value, value: opt.value })))
</script>

<template>
  <template v-if="fieldType === AssetFieldType.Input">
    <InputText
      fluid
      v-bind="commonProps"
    />
  </template>

  <template v-else-if="fieldType === AssetFieldType.InputTextarea || fieldType === AssetFieldType.RichText">
    <Textarea
      fluid
      v-bind="commonProps"
    />
  </template>

  <template v-else-if="fieldType === AssetFieldType.InputNumber">
    <InputNumber
      fluid
      v-bind="commonProps"
      :useGrouping="false"
    />
  </template>

  <template v-else-if="fieldType === AssetFieldType.Select">
    <ProSelect
      fluid
      v-bind="commonProps"
      :options="selectOptions"
    />
  </template>

  <template v-else-if="fieldType === AssetFieldType.Checkbox">
    <CheckboxGroup
      class="flex flex-wrap gap-4"
      v-bind="commonProps"
    >
      <template v-if="selectOptions?.length">
        <div
          v-for="opt of selectOptions"
          :key="opt.value"
          class="gap-2 flex-center"
        >
          <Checkbox
            :inputId="opt.value"
            :value="opt.value"
          />
          <label :for="opt.value">
            {{ opt.label }}
          </label>
        </div>
      </template>

      <div
        v-else
        class="py-1 text-sm"
      >
        暂无选项
      </div>
    </CheckboxGroup>
  </template>

  <template v-else-if="fieldType === AssetFieldType.Radio">
    <RadioButtonGroup
      class="flex flex-wrap gap-4"
      v-bind="commonProps"
    >
      <template v-if="selectOptions?.length">
        <div
          v-for="opt of selectOptions"
          :key="opt.value"
          class="gap-2 flex-center"
        >
          <RadioButton
            :inputId="opt.value"
            :value="opt.value"
          />
          <label :for="opt.value">
            {{ opt.label }}
          </label>
        </div>
      </template>

      <div
        v-else
        class="py-1 text-sm"
      >
        暂无选项
      </div>
    </RadioButtonGroup>
  </template>

  <template v-else-if="fieldType === AssetFieldType.Switch">
    <ToggleSwitch
      v-bind="commonProps"
    />
  </template>

  <template v-else-if="fieldType === AssetFieldType.TimeSelect">
    <ProDatePicker
      fluid
      v-bind="datePickerProps"
      timeOnly
    />
  </template>

  <template v-else-if="fieldType === AssetFieldType.DateSelect">
    <ProDatePicker
      fluid
      v-bind="datePickerProps"
    />
  </template>

  <template v-else-if="fieldType === AssetFieldType.DatetimeSelect">
    <ProDatePicker
      fluid
      v-bind="datePickerProps"
      showTime
    />
  </template>

  <template v-else>
    <Message severity="secondary">
      暂不支持该字段类型：{{ fieldType }}
    </Message>
  </template>
</template>
