<script setup lang="ts">
/**
 * AssetFieldValue 组件
 * 用于根据不同的字段类型展示资产字段的值
 */

import { AssetFieldType } from '~/enums/asset'

interface Props {
  fieldType?: AssetFieldType
  value?: AnyType
  /** 字段配置中设置的选项 */
  options?: {
    label: string
    value: string
  }[]
  /** 空值时的默认文本 */
  emptyText?: string
}

const props = withDefaults(defineProps<Props>(), {
  emptyText: '-',
})

const { value, fieldType } = toRefs(props)
</script>

<template>
  <template v-if="fieldType === AssetFieldType.Input || fieldType === AssetFieldType.InputTextarea || fieldType === AssetFieldType.RichText || fieldType === AssetFieldType.InputNumber">
    {{ value || emptyText }}
  </template>

  <template v-else-if="fieldType === AssetFieldType.Select || fieldType === AssetFieldType.Checkbox || fieldType === AssetFieldType.Radio">
    {{ options?.find(opt => opt.value === value)?.label || value || emptyText }}
  </template>

  <template v-else-if="fieldType === AssetFieldType.Switch">
    {{ value || emptyText }}
  </template>

  <template v-else-if="fieldType === AssetFieldType.TimeSelect || fieldType === AssetFieldType.DateSelect || fieldType === AssetFieldType.DatetimeSelect">
    {{ value || emptyText }}
  </template>

  <template v-else>
    <Message severity="secondary">
      暂不支持该字段类型：{{ fieldType }}
    </Message>
  </template>
</template>
