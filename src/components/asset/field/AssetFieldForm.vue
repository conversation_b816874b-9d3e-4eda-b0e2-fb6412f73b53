<script setup lang="ts">
const props = defineProps<{
  fieldList?: AssetField[]
  templateId?: AssetTemplate['id']
  loading?: boolean
  /** 是否为只读模式 */
  readOnly?: boolean
}>()

const { fieldList, templateId, loading, readOnly } = toRefs(props)

const emit = defineEmits<{
  'update:field-states': [value: Record<string, unknown>]
}>()

const fieldStates = defineModel<Record<string, unknown>>('fieldStates', {
  default: () => ({}),
})

const fieldStatesRef = computed({
  get: () => fieldStates.value,
  set: (value) => {
    fieldStates.value = value
    emit('update:field-states', value)
  },
})

// 当单个字段值变化时更新整个对象
const updateFieldValue = (fieldName: string, value: unknown) => {
  fieldStatesRef.value = {
    ...fieldStatesRef.value,
    [fieldName]: value,
  }
}

const templateIdRef = computed(() => templateId.value)

const { data: selfFieldList, isLoading: isLoadingFields } = useQueryTemplateFields(templateIdRef)

const finalFieldList = computed(() => {
  if (fieldList.value) {
    return fieldList.value
  }

  return selfFieldList.value
})

watch(
  finalFieldList,
  (fields) => {
    if (fields) {
      const newFieldStates = { ...fieldStatesRef.value }

      fields.forEach((field) => {
        const hasExistingValue = field.field_name in newFieldStates
        const hasDefaultValue = field.kwargs?.defaultValue !== undefined

        if (!hasExistingValue && hasDefaultValue) {
          newFieldStates[field.field_name] = field.kwargs?.defaultValue
        }
      })

      fieldStatesRef.value = newFieldStates
    }
  },
  { immediate: true },
)
</script>

<template>
  <div v-if="loading || isLoadingFields">
    <div class="grid grid-cols-2 gap-form-field">
      <Skeleton
        v-for="idx of 4"
        :key="idx"
        height="33px"
        width="100%"
      />
    </div>
  </div>

  <Form
    v-else
    class="w-full @container"
  >
    <div class="grid grid-cols-1 gap-form-field @md:grid-cols-2">
      <FormItem
        v-for="field of finalFieldList"
        :key="field.id"
        v-slot="{ id }"
        :label="field.name"
        :name="field.field_name"
        :required="field.kwargs?.required"
      >
        <AssetField
          :fieldType="field.field_type_id"
          :inputId="id"
          :options="field.kwargs?.options"
          :placeholder="field.kwargs?.placeholder"
          :readOnly="readOnly"
          :value="fieldStates[field.field_name]"
          @update:value="(v: string) => updateFieldValue(field.field_name, v)"
        />
      </FormItem>
    </div>
  </Form>
</template>
