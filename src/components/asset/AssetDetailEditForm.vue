<script setup lang="ts">
import { number, object } from 'valibot'

import { assetConfig, AssetType } from '~/enums/asset'

import type { ServerInfoFieldsetModel } from './server/AssetServerInfoFieldset.vue'

const props = defineProps<{
  assetType: AssetType
  networkDeviceId?: NetworkDevice['id']
  showServerInfo?: boolean
}>()

const store = useAssetInfoStore()
const { isAssetCollection, assetCollectionKey } = storeToRefs(store)

const emit = defineEmits<{
  submitSuccess: []
}>()

const assetName = computed(() => assetConfig[props.assetType].label)

const service = computed(() => {
  switch (props.assetType) {
    case AssetType.Server: {
      return {
        create: AssetService.createServer,
        update: AssetService.updateServer,
        updateCollect: AssetService.updateAssetCollectServer,
      }
    }

    case AssetType.Database:{
      return {
        create: AssetService.createDatabase,
        update: AssetService.updateDatabase,
        updateCollect: AssetService.updateAssetCollectDatabase,
      }
    }

    case AssetType.BusinessSystem: {
      return {
        create: AssetService.createBusinessSystem,
        update: AssetService.updateBusinessSystem,
        updateCollect: AssetService.updateAssetCollectBusinessSystem,
      }
    }

    case AssetType.Other: {
      return {
        create: AssetService.createOtherAsset,
        update: AssetService.updateOtherAsset,
        updateCollect: AssetService.updateAssetCollectOtherAsset,
      }
    }

    default:
      throw new Error(`不支持的资产类型: ${props.assetType}`)
  }
})

const serverInfo = ref<ServerInfoFieldsetModel>({})
const isUnknownAsset = ref(false)

const {
  loading,
  isFormCreate,
  isFormUpdate,
  modalVisible,
  formResolver,
  formValues,
  openCreateModal,
  openUpdateModal,
  handleClose,
  formTitleText,
  confirmBtnLabel,
  updatingItem,
  handleSubmit,
  handleModalVisibleChange,
} = useFormControl<BasicAssetDetailFormValues, Database>({
  initialValues: {
    asset: undefined,
  },
  btnLabel: {
    create: '确认添加',
    update: '保存更改',
  },
  formTitle: {
    create: `添加${assetName.value}`,
    update: `编辑${assetName.value}`,
  },
  resolver: object({
    template_id: number(`请选择${assetName.value}类型`),
  }),
  fetchDetail: (it) => {
    const raw = toRaw(it)

    isUnknownAsset.value = !raw.template_id

    serverInfo.value = {
      serverTemplateId: raw.server?.template_id,
      serverId: raw.server?.id,
      portId: raw.port_id,
    }

    return raw
  },
  onFieldChange: {
    // 当资产类型发生变化时，清空资产信息
    template_id: (_, oldValue) => {
      if (oldValue) {
        const assetName = formValues.value.asset?.name

        // 当用户切换资产类型时，一般会希望保留资产名称，因此如果资产名称存在，则保留资产名称，否则清空资产信息
        if (assetName) {
          formValues.value.asset = { name: assetName }
        }
        else {
          formValues.value.asset = undefined
        }
      }
    },
  },
  onClose: () => {
    serverInfo.value = {}
  },
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const payload = {
        template_id: states.template_id?.value,
        asset: formValues.value.asset,
        server_id: serverInfo.value.serverId,
        port_id: serverInfo.value.portId,
      }

      if (isFormCreate.value) {
        if (props.networkDeviceId) {
          await service.value.create(props.networkDeviceId, payload)
        }
      }
      else if (isFormUpdate.value) {
        const updatingId = updatingItem.value?.id

        if (updatingId) {
          // 如果资产是采集资产，则更新采集资产
          if (isAssetCollection.value && assetCollectionKey.value) {
            await service.value.updateCollect(updatingId, assetCollectionKey.value, payload)
          }
          else if (props.networkDeviceId) {
            await service.value.update(props.networkDeviceId, updatingId, payload)
          }
        }
      }
    }
  },
  onSubmitSuccess: () => {
    emit('submitSuccess')
  },
})

defineExpose({
  openCreateModal,
  openUpdateModal,
})

// 判断资产类型是否可编辑
const templateEditable = computed(() => {
  // 创建模式下始终可编辑
  if (isFormCreate.value) {
    return true
  }

  if (isUnknownAsset.value) {
    return true
  }

  // 编辑模式下，如果未选择资产类型（即未知资产），则允许用户选择资产类型
  return !formValues.value.template_id
})
</script>

<template>
  <ProDialogForm
    :dialogProps="{
      dismissableMask: false,
      class: 'dialog-form-wider',
    }"
    :formActionGroupProps="{
      confirmButtonProps: {
        label: confirmBtnLabel,
      },
    }"
    :formProps="{ resolver: formResolver }"
    :initialValues="formValues"
    :loading="loading"
    :title="formTitleText"
    :visible="modalVisible"
    @cancel="handleClose"
    @submit="handleSubmit"
    @update:visible="handleModalVisibleChange"
  >
    <div class="flex flex-col gap-form-field">
      <div class="grid grid-cols-2 gap-form-field">
        <FormItem
          :label="`${assetName}类型`"
          name="template_id"
          required
        >
          <FormItemAssetTemplateSelect
            v-model="formValues.template_id"
            :assetType="assetType"
            :disabled="!templateEditable"
            :placeholder="`选择${assetName}类型`"
          />
        </FormItem>
      </div>
    </div>

    <AssetServerInfoFieldset
      v-if="assetType !== AssetType.Server && showServerInfo"
      v-model="serverInfo"
    />

    <Fieldset
      v-if="formValues.template_id"
      :legend="`${assetName}信息`"
    >
      <div class="pt-in-fieldset">
        <FormItem
          v-slot="{ field }: { field: FormFieldContext }"
          name="asset"
          noStyle
        >
          <AssetFieldForm
            :fieldStates="formValues.asset || {}"
            :templateId="formValues.template_id"
            @update:fieldStates="(v: Record<string, unknown>) => {
              // 手动调用 onChange 是为了正确追踪字段的变化
              field.onChange({ target: { value: v } })
              formValues.asset = v
            }"
          />
        </FormItem>
      </div>
    </Fieldset>
  </ProDialogForm>
</template>
