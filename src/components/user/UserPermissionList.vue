<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import arrayToTree from 'array-to-tree'
import { PencilLineIcon, PlusIcon, TrashIcon } from 'lucide-vue-next'
import { object } from 'valibot'

import { TooltipShowDelay } from '~/enums/common'
import { queryKeys } from '~/enums/query-key'
import { UserService } from '~/services-tw/org'
import type { PermissionFormValues, PermissionItem } from '~/types-tw/user'

// 定义权限树节点类型
interface PermissionTreeNode extends PermissionItem {
  key: string
  label: string
  data: PermissionItem
  children?: PermissionTreeNode[]
}

const { $confirm } = useNuxtApp()

const { data: permissionListData, refetch: refetchPermissionList, isLoading } = useQuery({
  queryKey: queryKeys.user.permission.all(),
  queryFn: () => UserService.getPermissions(),
})

const showSelectParentPermission = ref(false)

const {
  isFormCreate,
  isFormUpdate,
  formValues,
  updatingItem,
  modalVisible,
  loading,
  confirmBtnLabel,
  formTitleText,
  formResolver,
  openCreateModal,
  openUpdateModal,
  handleClose,
  handleSubmit,
  handleModalVisibleChange,
} = useFormControl<PermissionFormValues, PermissionItem>({
  initialValues: {
    parent_id: undefined,
    name: '',
    code: '',
  },
  resolver: object({
    name: schemaNotEmptyString('请输入权限名称'),
    code: schemaNotEmptyString('请输入权限标识'),
  }),
  btnLabel: {
    create: '确认创建',
    update: '保存更改',
  },
  formTitle: {
    create: '创建权限',
    update: '编辑权限',
  },
  fetchDetail: (it) => toRaw(it),
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      if (isFormCreate.value) {
        await UserService.createPermission({
          parent_id: formValues.value.parent_id,
          name: states.name.value,
          code: states.code.value.trim(),
        })
      }
      else if (isFormUpdate.value) {
        const id = updatingItem.value?.id

        if (id) {
          await UserService.updatePermission(id, {
            parent_id: formValues.value.parent_id,
            name: states.name.value,
            code: states.code.value.trim(),
          })
        }
      }
    }
  },
  onSubmitSuccess: () => {
    refetchPermissionList()
  },
  onClose: () => {
    showSelectParentPermission.value = false
  },
})

const permissionTree = computed<PermissionTreeNode[] | undefined>(() => {
  const list = permissionListData.value

  if (Array.isArray(list) && list.length > 0) {
    const normalizedList = list.map<PermissionTreeNode>((it) => ({
      ...it,
      key: String(it.id),
      label: it.name,
      data: it,
    }))

    return arrayToTree<PermissionTreeNode>(normalizedList)
  }

  return undefined
})

// 默认展开所有网络层级
const expandedKeys = computed<ExpandedKeys>(() => {
  const keys: ExpandedKeys = {}

  if (!permissionTree.value) {
    return keys
  }

  // 递归遍历网络树，获取所有节点的 key
  const traverseTree = (nodes: PermissionTreeNode[]) => {
    nodes.forEach((node) => {
      // 将当前节点的 key 添加到展开键值对象中
      keys[node.key] = true

      // 如果当前节点有子节点，则继续遍历
      if (Array.isArray(node.children) && node.children.length > 0) {
        traverseTree(node.children)
      }
    })
  }

  traverseTree(permissionTree.value)

  return keys
})

const { popMenuRef: ctxMenuRef, popMenu: ctxMenu } = usePopMenu()

const selectedNode = ref<PermissionTreeNode>()

const handleItemContext = (ev: { originalEvent: MouseEvent }) => {
  ctxMenu.value?.show(ev.originalEvent)
}

// 添加创建下级权限的方法
const openCreateSubPermission = (parentNode: PermissionTreeNode) => {
  // 使用 openCreateModal 方法，并传入预设的父级权限 ID
  openCreateModal({
    parent_id: parentNode.id,
    name: '',
    code: '',
  })
}

const menuOptions: PrimeVueMenuItem[] = [
  {
    label: '添加下级权限',
    command: () => {
      if (selectedNode.value) {
        openCreateSubPermission(selectedNode.value)
      }
    },
  },
  {
    label: '编辑',
    command: () => {
      if (selectedNode.value) {
        openUpdateModal(selectedNode.value.data)
      }
    },
  },
  {
    label: '删除',
    command: async () => {
      const permissionId = selectedNode.value?.id

      if (permissionId) {
        $confirm.dialog({
          header: '确定删除权限吗？',
          message: `删除权限将同时删除该权限下的所有子权限，请谨慎操作！`,
          accept: async () => {
            await UserService.deletePermission(permissionId)
            refetchPermissionList()
          },
        })
      }
    },
  },
]
</script>

<template>
  <div class="flex h-full flex-col">
    <div class="pb-4 flex-center">
      <Button
        class="ml-auto"
        label="创建权限"
        @click="openCreateModal()"
      />
    </div>

    <div class="min-h-0 flex-1">
      <TreeTable
        v-model:contextMenuSelection="selectedNode"
        contextMenu
        :expandedKeys="expandedKeys"
        :loading="isLoading"
        scrollable
        scrollHeight="flex"
        size="small"
        :value="permissionTree"
        @rowContextmenu="handleItemContext($event)"
      >
        <Column
          expander
          field="name"
          header="权限名称"
        />

        <Column
          field="code"
          header="权限标识"
        >
          <template #body="{ node }: { node: PermissionTreeNode }">
            <Tag
              :pt="{
                root: {
                  class: 'border border-divider',
                },
                label: {
                  class: 'font-normal',
                },
              }"
              severity="secondary"
              :value="node.data.code"
            />
          </template>
        </Column>

        <Column class="min-w-table-actions">
          <template #body="{ node }: { node: PermissionTreeNode }">
            <div class="w-full table-action-group">
              <ProBtn
                size="small"
                :tooltip="{ value: '添加下级权限', showDelay: TooltipShowDelay.Fast }"
                variant="text"
                @click.stop="openCreateSubPermission(node)"
              >
                <template #icon="{ size }">
                  <PlusIcon :size="size" />
                </template>
              </ProBtn>

              <ProBtn
                severity="secondary"
                size="small"
                :tooltip="{ value: '编辑', showDelay: TooltipShowDelay.Fast }"
                variant="text"
                @click.stop="openUpdateModal(node.data)"
              >
                <template #icon="{ size }">
                  <PencilLineIcon :size="size" />
                </template>
              </ProBtn>

              <ProBtn
                severity="danger"
                size="small"
                :tooltip="{ value: '删除', showDelay: TooltipShowDelay.Fast }"
                variant="text"
                @click.stop="$confirm.dialog({
                  header: '确定删除权限吗？',
                  message: `删除权限将同时删除该权限下的所有子权限，请谨慎操作！`,
                  accept: async () => {
                    await UserService.deletePermission(node.id)
                    refetchPermissionList()
                  },
                })"
              >
                <template #icon="{ size }">
                  <TrashIcon :size="size" />
                </template>
              </ProBtn>
            </div>
          </template>
        </Column>

        <template #loadingicon>
          <div class="size-full bg-content py-6">
            <LoadingSkeleton
              :columns="5"
              :rows="5"
            />
          </div>
        </template>
      </TreeTable>

      <ContextMenu
        :ref="ctxMenuRef"
        :model="menuOptions"
        @hide="selectedNode = undefined"
      />

      <ProDialogForm
        :formActionGroupProps="{
          confirmButtonProps: {
            label: confirmBtnLabel,
          },
        }"
        :formProps="{ resolver: formResolver }"
        :initialValues="formValues"
        :loading="loading"
        :title="formTitleText"
        :visible="modalVisible"
        @cancel="handleClose"
        @submit="handleSubmit"
        @update:visible="handleModalVisibleChange"
      >
        <FormItem
          label="权限名称"
          name="name"
          required
        >
          <template #default="{ id }">
            <InputText
              :id="id"
              v-model="formValues.name"
              placeholder="例如：添加用户、编辑用户、删除用户等"
            />
          </template>

          <template #helpTip>
            用于识别权限用途的名称
          </template>
        </FormItem>

        <FormItem
          label="权限 Code"
          name="code"
          required
        >
          <template #default="{ id }">
            <InputText
              :id="id"
              v-model.trim="formValues.code"
              :disabled="isFormUpdate"
              placeholder="格式形如 feature.action，例如：user.add"
            />
          </template>

          <template
            v-if="!isFormUpdate"
            #helpTip
          >
            标识该权限，应使用英文字母，创建后无法修改
          </template>
        </FormItem>

        <div v-if="!showSelectParentPermission && !formValues.parent_id">
          <Button
            label="设置上级权限"
            size="small"
            variant="text"
            @click="showSelectParentPermission = true"
          >
            <template #icon>
              <PlusIcon :size="14" />
            </template>
          </Button>
        </div>

        <FormItem
          v-if="showSelectParentPermission || formValues.parent_id"
          label="上级权限"
          name="parent_id"
        >
          <FormItemTreeSelect
            v-model:value="formValues.parent_id"
            :disabledKeys="updatingItem?.id.toString()"
            :options="permissionTree"
            placeholder="选择上级权限"
            showClear
          />
        </FormItem>
      </ProDialogForm>
    </div>
  </div>
</template>
