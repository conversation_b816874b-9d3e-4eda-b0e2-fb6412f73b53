<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { get } from 'lodash-es'
import { PlusIcon } from 'lucide-vue-next'
import { object, string } from 'valibot'

const { $confirm, $toast } = useNuxtApp()

const store = useAdminUserGroupStore()
const fetchUserGroups = store.fetchUserGroups
const { userGroupTree } = storeToRefs(store)

const { themeMode } = useTheme()

const contentBg = computed(() => {
  const v = $dt('content.background').value

  return get(v, `${themeMode.value}.value`)
})

// 滚动状态控制
const scrollContainerRef = ref<HTMLElement | null>(null)
const showTopGradient = ref(false)
const showBottomGradient = ref(false)

// 检查滚动位置并更新渐变显示状态
const updateScrollGradients = () => {
  if (scrollContainerRef.value) {
    const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.value

    // 当滚动位置不在顶部时显示顶部渐变
    showTopGradient.value = scrollTop > 0

    // 当滚动位置不在底部时显示底部渐变
    showBottomGradient.value = scrollTop + clientHeight < scrollHeight - 1 // 添加 1px 容差
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 获取用户组数据
  fetchUserGroups()

  // 设置滚动监听
  if (scrollContainerRef.value) {
    scrollContainerRef.value.addEventListener('scroll', updateScrollGradients)
    // 初始化渐变状态
    nextTick(updateScrollGradients)
  }
})

onBeforeUnmount(() => {
  if (scrollContainerRef.value) {
    scrollContainerRef.value.removeEventListener('scroll', updateScrollGradients)
  }
})

// 当树数据更新后，重新检查滚动状态
watch(userGroupTree, () => {
  nextTick(updateScrollGradients)
}, { deep: true })

const {
  loading,
  isFormCreate,
  isFormUpdate,
  formValues,
  formResolver,
  handleClose,
  modalVisible,
  openCreateModal,
  openUpdateModal,
  updatingItem,
  handleSubmit,
  handleModalVisibleChange,
  confirmBtnLabel,
  formTitleText,
} = useFormControl<UserGroupFormValues, UserGroup>({
  btnLabel: {
    create: '添加组织',
    update: '保存更改',
  },
  formTitle: {
    create: '添加组织',
    update: '编辑组织',
  },
  resolver: object({
    name: string('请输入组织名称'),
    user_id: schemaOptionalString(),
  }),
  fetchDetail: (userGroup) => {
    return {
      name: userGroup.name,
      parent_id: userGroup.parent_id,
      user_id: userGroup.user_id,
    }
  },
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const payload: UserGroupFormValues = {
        name: states.name.value,
        parent_id: formValues.value.parent_id,
        user_id: states.user_id?.value,
      }

      if (isFormCreate.value) {
        await UserService.createUserGroup(payload)
        $toast.success({ summary: '新增用户组成功' })
      }
      else if (isFormUpdate.value) {
        const updatingItemId = updatingItem.value?.id

        if (updatingItemId) {
          await UserService.updateUserGroup(updatingItemId, payload)
        }
      }
    }
  },
  onSubmitSuccess: () => {
    fetchUserGroups()
  },
})

const handleDeleteUserGroup = async (userGroup: UserGroup) => {
  $confirm.dialog({
    header: '谨慎操作',
    message: `确定删除用户组「${userGroup.name}」吗？`,
    accept: async () => {
      await UserService.deleteUserGroup(userGroup.id)

      $toast.info({
        summary: '删除成功',
        detail: `用户组「${userGroup.name}」已删除`,
      })

      handleClose()
      fetchUserGroups()
    },
  })
}
</script>

<template>
  <div class="relative flex h-full flex-col gap-4">
    <div class="pr-in-splitter">
      <Button
        class="w-full"
        label="新建组织"
        size="small"
        variant="outlined"
        @click="openCreateModal()"
      >
        <template #icon>
          <PlusIcon :size="16" />
        </template>
      </Button>
    </div>

    <div class="relative flex-1 overflow-hidden">
      <!-- 顶部渐变提示 -->
      <div
        v-show="showTopGradient"
        class="pointer-events-none absolute -inset-x-2 top-0 z-10 h-6"
        :style="`background: radial-gradient(ellipse at center top, ${contentBg}66, ${contentBg}00);`"
      />

      <!-- 滚动容器 -->
      <div
        ref="scrollContainerRef"
        class="size-full overflow-auto pr-in-splitter"
      >
        <UserGroupTree
          @createUserGroup="openCreateModal($event)"
          @deleteUserGroup="handleDeleteUserGroup($event)"
          @updateUserGroup="openUpdateModal($event)"
        />
      </div>

      <!-- 底部渐变提示 -->
      <div
        v-show="showBottomGradient"
        class="pointer-events-none absolute -inset-x-2 bottom-0 z-10 h-6"
        :style="`background: radial-gradient(ellipse at center bottom, ${contentBg}66, ${contentBg}00);`"
      />
    </div>

    <ProDialogForm
      :formActionGroupProps="{
        confirmButtonProps: {
          label: confirmBtnLabel,
        },
      }"
      :initialValues="formValues"
      :loading="loading"
      :resolver="formResolver"
      :title="formTitleText"
      :visible="modalVisible"
      @cancel="handleClose"
      @submit="handleSubmit"
      @update:visible="handleModalVisibleChange"
    >
      <FormItem
        v-slot="{ id }"
        label="上级组织"
        name="parent_id"
      >
        <FormItemTreeSelect
          v-model:value="formValues.parent_id"
          :disabledKeys="updatingItem?.id.toString()"
          :inputId="id"
          :options="userGroupTree"
          placeholder="选择上级组织"
          showClear
        />
      </FormItem>

      <FormItem
        v-slot="{ id }"
        label="组织名称"
        name="name"
        required
      >
        <InputText
          :id="id"
          v-model="formValues.name"
          placeholder="输入组织名称，用于识别组织"
        />
      </FormItem>
    </ProDialogForm>
  </div>
</template>
