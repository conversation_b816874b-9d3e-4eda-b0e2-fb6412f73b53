<script setup lang="ts">
import { consola } from 'consola'
import { PlusIcon } from 'lucide-vue-next'
import { object, string } from 'valibot'

const { $confirm, $toast } = useNuxtApp()

const store = useAdminUserGroupStore()
const fetchUserGroups = store.fetchUserGroups
const { userGroupTree } = storeToRefs(store)

onMounted(() => {
  fetchUserGroups()
})

const {
  loading,
  // isFormCreate,
  // isFormUpdate,
  formValues,
  formResolver,
  handleClose,
  modalVisible,
  openCreateModal,
  openUpdateModal,
  updatingItem,
  handleSubmit,
  handleModalVisibleChange,
  confirmBtnLabel,
  formTitleText,
} = useFormControl<UserAuthResourceFormValues, UserAuthResource>({
  btnLabel: {
    create: '新增权限',
    update: '保存更改',
  },
  formTitle: {
    create: '新增权限资源',
    update: '编辑权限资源',
  },
  resolver: object({
    name: string('请输入权限资源名称'),
    code: string('请输入权限资源标识'),
  }),
  fetchDetail: (authResource) => {
    return {
      name: authResource.name,
      parent_id: authResource.parent_id,
      code: authResource.code,
      operations: authResource.operations,
      description: authResource.description,
    }
  },
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      consola.info(states)
      // const payload: UserAuthResourceFormValues = {
      //   name: states.name.value,
      //   parent_id: formValues.value.parent_id,
      //   code: states.code.value,
      //   operations: states.operations.value,
      //   description: states.description?.value,
      // }

      // if (isFormCreate.value) {
      //   await UserService.createUserGroup(payload)
      //   $toast.success({ summary: '新增权限资源成功' })
      // } else if (isFormUpdate.value) {
      //   const updatingItemId = updatingItem.value?.id

      //   if (updatingItemId) {
      //     await UserService.updateUserGroup(updatingItemId, payload)
      //   }
      // }
    }
  },
  onSubmitSuccess: () => {
    fetchUserGroups()
  },
})

const handleDeleteUserGroup = async (userGroup: UserGroup) => {
  $confirm.dialog({
    header: '谨慎操作',
    message: `确定删除权限资源「${userGroup.name}」吗？`,
    accept: async () => {
      await UserService.deleteUserGroup(userGroup.id)

      $toast.info({
        summary: '删除成功',
        detail: `权限资源「${userGroup.name}」已删除`,
      })

      handleClose()
      fetchUserGroups()
    },
  })
}
</script>

<template>
  <div class="flex flex-col gap-form-field">
    <div class="flex-center">
      <h2 class="text-lg font-bold">
        系统资源
      </h2>

      <div class="ml-auto gap-1 flex-center">
        <ProBtn
          size="small"
          variant="text"
          @click="openCreateModal()"
        >
          <PlusIcon class="size-4" />
        </ProBtn>
      </div>
    </div>

    <UserAuthResourceTree
      @createUserGroup="openCreateModal($event)"
      @deleteUserGroup="handleDeleteUserGroup($event)"
      @updateUserGroup="openUpdateModal($event)"
    />

    <ProDialogForm
      class="dialog-form-wider"
      :formActionGroupProps="{
        confirmButtonProps: {
          label: confirmBtnLabel,
        },
      }"
      :initialValues="formValues"
      :loading="loading"
      :resolver="formResolver"
      :title="formTitleText"
      :visible="modalVisible"
      @cancel="handleClose"
      @submit="handleSubmit"
      @update:visible="handleModalVisibleChange"
    >
      <FormItem
        v-slot="{ id }"
        label="上级资源"
        name="parent_id"
      >
        <FormItemTreeSelect
          v-model:value="formValues.parent_id"
          :disabledKeys="updatingItem?.id.toString()"
          :inputId="id"
          :options="userGroupTree"
          placeholder="选择上级资源"
          showClear
        />
      </FormItem>

      <FormItem
        v-slot="{ id }"
        label="资源名称"
        name="name"
        required
      >
        <InputText
          :id="id"
          v-model="formValues.name"
          placeholder="输入资源名称，用于分类和识别资源"
        />
      </FormItem>

      <FormItem
        label="资源 Code"
        name="code"
        required
      >
        <template #default="{ id }">
          <InputText
            :id="id"
            v-model="formValues.code"
            placeholder="例如：user"
          />
        </template>

        <template #helpTip>
          标识该资源，应为英文字母，创建后无法修改
        </template>
      </FormItem>

      <FormItem
        v-slot="{ id }"
        label="权限操作"
        name="operations"
        required
      >
        <UserAuthResourceOperations
          :id="id"
          v-model="formValues.operations"
        />
      </FormItem>

      <FormItem
        v-slot="{ id }"
        label="资源描述"
        name="description"
      >
        <Textarea
          :id="id"
          v-model="formValues.description"
          placeholder="如：用户管理"
        />
      </FormItem>
    </ProDialogForm>
  </div>
</template>
