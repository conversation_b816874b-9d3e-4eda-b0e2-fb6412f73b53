<script setup lang="ts">
import { ChevronDownIcon, ChevronRightIcon } from 'lucide-vue-next'
import type { TreeNode } from 'primevue/treenode'

import { routeConfig } from '~/enums/route'

const menuItemIds = defineModel<NavMenuItemWithId['id'][]>()

const store = useAdminNavMenuStore()
const { navMenuItems } = toRefs(store)

const transformNode = (item: NavMenuItem): TreeNode => {
  return {
    key: item.key,
    label: item.label,
    children: item.items?.map(transformNode),
  }
}

const menuNodes = computed(() => navMenuItems.value.map(transformNode))

const handleSelectionKeysChange = (keys: PrimeVueTreeSelectionKeys | undefined) => {
  // 将 PrimeVue 的选择键值对象转换为选中的菜单 key 数组
  const selectedMenuKeys = keys
    ? Object.entries(keys).reduce<NavMenuItem['key'][]>((acc, [key, value]) => {
        if (value.checked) {
          acc.push(key as NavMenuItem['key'])
        }

        return acc
      }, [])
    : []

  menuItemIds.value = selectedMenuKeys
}

const selectionKeys = computed<PrimeVueTreeSelectionKeys | undefined>(() => {
  return menuItemIds.value?.reduce((acc, menuId) => {
    if (Object.hasOwn(routeConfig, menuId)) {
      acc[menuId] = { checked: true }
    }

    return acc
  }, {} as PrimeVueTreeSelectionKeys)
})
</script>

<template>
  <Tree
    class="!p-0"
    :selectionKeys="selectionKeys"
    selectionMode="checkbox"
    :value="menuNodes"
    @update:selectionKeys="handleSelectionKeysChange"
  >
    <template #nodetoggleicon="{ expanded }: {expanded: boolean}">
      <Component
        :is="expanded ? ChevronDownIcon : ChevronRightIcon"
        :size="15"
      />
    </template>
  </Tree>
</template>
