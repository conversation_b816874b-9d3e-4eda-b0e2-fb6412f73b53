<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus'

import { cloneDeep } from 'lodash-es'
import { GripVerticalIcon, PlusIcon, TrashIcon } from 'lucide-vue-next'
import { nanoid } from 'nanoid'

const { $confirm } = useNuxtApp()

const options = defineModel<UserAuthResourceOperation[]>('modelValue', {
  default: () => [],
})

const defaultOperation: UserAuthResourceOperation = {
  code: '',
  name: '',
}

const newOperation = ref<UserAuthResourceOperation>(cloneDeep(defaultOperation))

const isDuplicate = ref(false)
const isInvalidCode = ref(false)

const isDragging = ref(false)

// 监听 code 变化进行格式校验
watch(() => newOperation.value.code, (value) => {
  if (value) {
    // 只允许英文字母
    isInvalidCode.value = !/^[a-zA-Z]+$/.test(value)
    isDuplicate.value = options.value.some((opt) => opt.code === value)
  }
  else {
    isInvalidCode.value = false
    isDuplicate.value = false
  }
})

const handleAddOption = () => {
  if (newOperation.value && !isDuplicate.value) {
    options.value = [
      { code: newOperation.value.code, name: newOperation.value.name },
      ...options.value,
    ]

    newOperation.value = defaultOperation
  }
}

const handleRemoveOption = (index: number) => {
  if (Array.isArray(options.value)) {
    const optionToDelete = options.value[index]

    $confirm.dialog({
      message: `确定要删除操作「${optionToDelete.name}」吗？`,
      accept: () => {
        options.value = options.value.filter((_, idx) => idx !== index)
      },
    })
  }
}

const handlerClass = nanoid(4)

const addable = computed(() => {
  if (isDuplicate.value || isInvalidCode.value) {
    return false
  }

  const isEmpty = newOperation.value.code.trim().length === 0

  return !isEmpty
})
</script>

<template>
  <div class="flex flex-col gap-2">
    <div>
      <div class="grid grid-cols-[1.5rem_1fr_1fr_2rem] items-center gap-2 pb-2">
        <div />

        <div class="text-sm text-secondary">
          操作标识
        </div>

        <div class="text-sm text-secondary">
          操作名称
        </div>
      </div>
      <div class="grid grid-cols-[1.5rem_1fr_1fr_2rem] items-center gap-2">
        <div />

        <InputText
          v-model="newOperation.code"
          fluid
          :invalid="isDuplicate || isInvalidCode"
          placeholder="应为英文字母，例如：edit"
          size="small"
        />

        <InputText
          v-model="newOperation.name"
          fluid
          placeholder="例如：编辑"
          size="small"
        />

        <div>
          <ProBtn
            v-tooltip.top="addable ? '添加新权限' : '完成输入以添加'"
            :disabled="!addable"
            severity="secondary"
            size="small"
            variant="outlined"
            @click="handleAddOption()"
          >
            <template #icon>
              <PlusIcon :size="15" />
            </template>
          </ProBtn>
        </div>
      </div>

      <!-- 错误提示 -->
      <div class="grid grid-cols-[1.5rem_1fr] gap-2 pt-1">
        <div />

        <Message
          v-if="isInvalidCode"
          severity="error"
          size="small"
          variant="simple"
        >
          操作标识只能包含英文字母
        </Message>

        <Message
          v-else-if="isDuplicate"
          severity="error"
          size="small"
          variant="simple"
        >
          操作标识已存在
        </Message>
      </div>
    </div>

    <!-- 已添加的选项列表 -->
    <VueDraggable
      v-model="options"
      :animation="150"
      class="flex flex-col gap-2"
      :handle="`.${handlerClass}`"
      itemid="code"
      @end="isDragging = false"
      @start="isDragging = true"
    >
      <div
        v-for="(item, idx) of options"
        :key="item.code"
        class="grid grid-cols-[1.5rem_1fr_1fr_2rem] items-center gap-2"
      >
        <div
          v-show="!isDragging"
          class="cursor-grab justify-center rounded p-1 flex-center hover:bg-emphasis"
          :class="handlerClass"
        >
          <GripVerticalIcon
            class="opacity-50"
            :size="15"
          />
        </div>

        <InputText
          v-model="options[idx].code"
          fluid
          size="small"
        />

        <InputText
          v-model="options[idx].name"
          fluid
          size="small"
        />

        <div>
          <ProBtn
            v-show="!isDragging"
            severity="danger"
            size="small"
            variant="text"
            @click="handleRemoveOption(idx)"
          >
            <template #icon>
              <TrashIcon :size="15" />
            </template>
          </ProBtn>
        </div>
      </div>
    </VueDraggable>
  </div>
</template>
