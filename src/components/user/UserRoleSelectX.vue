<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import type { ProSelectProps } from '~/components/pro/select/ProSelect.vue'
import { queryKeys } from '~/enums/query-key'
import { UserService as UserServiceFuYao } from '~/services-tw/org'
import type { TW_Role } from '~/types-tw/user'

const props = defineProps<ProSelectProps>()

const value = defineModel<TW_Role['id'][]>()

const { data: roleList, isLoading } = useQuery({
  queryKey: queryKeys.role.all(),
  queryFn: () => {
    return UserServiceFuYao.getRoles({ page: 1, page_size: 100 })
  },
})

const options = computed(() => roleList.value?.list.map((item) => ({
  label: item.name,
  value: item.id,
})))
</script>

<template>
  <ProMultiSelect
    v-bind="props"
    v-model="value as UnsafeAny"
    :loading="isLoading"
    :options="options"
    placeholder="选择系统角色"
  />
</template>
