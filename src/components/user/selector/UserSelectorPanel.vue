<script setup lang="ts">
import type { ButtonProps } from 'primevue'

import { InjectKey } from '~/constants-tw/common'

import type { OrgItem, UserSelectorPanelOrgListProps } from './UserSelectorPanelOrgList.vue'

export interface UserSelectorPanelProps extends Pick<UserSelectorPanelOrgListProps, 'onlyDepartment' | 'onlyUser'> {
  search?: boolean
  selectedUsers?: OrgItem[]
  cancelButtonProps?: ButtonProps
  confirmButtonProps?: ButtonProps
}

const props = defineProps<UserSelectorPanelProps>()

const emit = defineEmits<{
  cancel: []
  confirm: [users: OrgItem[]]
}>()

const selectedUsers = ref<OrgItem[]>(props.selectedUsers || [])

const searchText = ref<string>()
const searchValue = ref<string>()

interface DialogRef {
  close: (selectedUsers?: OrgItem[]) => void
}

const dialogRef = inject<Ref<DialogRef | undefined>>(InjectKey.PrimeVueDialog, ref())

const handleSearch = () => {
  searchValue.value = searchText.value
}

const handleRemove = (userId: OrgItem['id']) => {
  selectedUsers.value = selectedUsers.value.filter((u) => u.id !== userId)
}

const handleClear = () => {
  selectedUsers.value = []
}

const handleCancel = () => {
  emit('cancel')
  dialogRef.value?.close()
}

const handleConfirm = () => {
  emit('confirm', selectedUsers.value)
  dialogRef.value?.close(selectedUsers.value)
}
</script>

<template>
  <ProSplitter
    :left="{ size: 50 }"
    leftPanelContentClass="!p-0"
    :right="{ size: 50 }"
    wrapperClass="flex-1 overflow-hidden"
  >
    <template #left>
      <div class="flex h-full flex-col gap-2">
        <div
          v-if="props.search"
          class="pr-in-splitter"
        >
          <SearchInput
            v-model="searchText"
            fluid
            placeholder="输入成员名称以搜索"
            size="small"
            @enter="handleSearch()"
          />
        </div>

        <div class="flex-1 overflow-y-auto pr-in-splitter">
          <UserSelectorPanelOrgList
            v-bind="props"
            v-model="selectedUsers"
          />
        </div>
      </div>
    </template>

    <template #right>
      <div class="flex h-full flex-col">
        <div class="flex-1 overflow-y-auto">
          <UserSelectorPanelSelectedList
            :users="selectedUsers"
            @clear="handleClear"
            @remove="handleRemove"
          />
        </div>

        <Divider />

        <div class="justify-end gap-3 flex-center">
          <Button
            v-bind="cancelButtonProps"
            label="取消"
            severity="secondary"
            @click="handleCancel"
          />

          <Button
            v-bind="confirmButtonProps"
            label="确定"
            severity="primary"
            @click="handleConfirm"
          />
        </div>
      </div>
    </template>
  </ProSplitter>
</template>
