<script setup lang="ts">
import { TrashIcon, XIcon } from 'lucide-vue-next'

import type { OrgItem } from './UserSelectorPanelOrgList.vue'

interface Props {
  /** 已选择的用户列表 */
  users?: OrgItem[]
  /** 是否显示移除按钮 */
  showRemove?: boolean
  /** 是否显示清空按钮 */
  showClear?: boolean
  /** 空列表提示文本 */
  emptyText?: string
}

withDefaults(defineProps<Props>(), {
  showRemove: true,
  showClear: true,
  emptyText: '暂无已选择的用户',
})

const emit = defineEmits<{
  /** 移除单个用户 */
  remove: [userId: User['id']]
  /** 清空所有用户 */
  clear: []
}>()

const handleRemove = (userId: User['id']) => {
  emit('remove', userId)
}

const handleClear = () => {
  emit('clear')
}
</script>

<template>
  <div class="flex h-full flex-col">
    <div class="flex items-center justify-between">
      <div v-if="users && users.length > 0">
        已选择
        <span class="font-medium text-primary">{{ users?.length }}</span>
        个成员
      </div>
      <div v-else>
        请选择
      </div>

      <div class="ml-auto">
        <Button
          v-if="showClear && users && users.length > 0"
          label="清空"
          severity="danger"
          size="small"
          text
          @click="handleClear"
        >
          <template #icon>
            <TrashIcon :size="14" />
          </template>
        </Button>
      </div>
    </div>

    <Divider />

    <div class="flex-1 overflow-y-auto">
      <div
        v-if="Array.isArray(users) && users.length > 0"
        class="flex flex-col gap-2"
      >
        <div
          v-for="user of users"
          :key="user.id"
          class="gap-2 rounded px-2 py-1 inline-flex-center hover:bg-emphasis"
        >
          <span class="inline-flex">
            <UserAvatar
              :avatar="user.avatar"
              class="!size-7 !rounded !text-xs"
              :username="user.name"
            />
          </span>

          <span class="text-sm">{{ user.name }}</span>

          <span
            v-if="showRemove"
            class="ml-auto mr-[-0.3rem] shrink-0 cursor-pointer rounded p-0.5 text-secondary hover:bg-danger-50 hover:text-danger-500"
            @click.stop="handleRemove(user.id)"
          >
            <XIcon :size="13" />
          </span>
        </div>
      </div>
      <div
        v-else
        class="py-4 text-center text-secondary"
      >
        {{ emptyText }}
      </div>
    </div>
  </div>
</template>
