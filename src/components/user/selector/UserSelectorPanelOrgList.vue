<script setup lang="ts">
import arrayToTree from 'array-to-tree'
import { ChevronDownIcon, ChevronRightIcon, FolderIcon, LoaderIcon } from 'lucide-vue-next'
import type { TreeNode } from 'primevue/treenode'

import { UserService } from '~/services-tw/org'
import type { TW_Department, TW_UserListItem } from '~/types-tw/user'

export interface UserSelectorPanelOrgListProps {
  /** 是否只能选择部门 */
  onlyDepartment?: boolean
  /** 是否只能选择用户 */
  onlyUser?: boolean
  /** 默认展开的节点 */
  defaultExpandedLevel?: number
}

const props = withDefaults(defineProps<UserSelectorPanelOrgListProps>(), {
  onlyDepartment: false,
  onlyUser: false,
  defaultExpandedLevel: 0,
})

export interface OrgItem {
  id: number
  parent_id?: number
  name: string
  type: 'department' | 'user'
  avatar?: string
}

type OrgTreeNode = Omit<TreeNode, 'data'> & {
  data: OrgItem
}

const selectedUsers = defineModel<OrgItem[]>({ default: [] })

// 存储所有部门数据
const departmentList = ref<TW_Department[]>([])
// 存储已加载的用户数据，按部门ID分组
const usersByDept = ref<Record<number, TW_UserListItem[]>>({})
// 存储完整的组织树形结构
const treeNodes = ref<TreeNode[]>([])
// 存储加载状态
const loadingDepts = ref<Record<number, boolean>>({})

// 构建树节点
const buildTreeNodes = () => {
  const normalizedData = departmentList.value.map<TreeNode>((it) => ({
    key: `dept_${it.id}`,
    label: it.name,
    selectable: !props.onlyUser,
    leaf: false,
    data: {
      id: it.id,
      parent_id: it.parent,
      name: it.name,
      type: 'department',
    },
  }))

  const tree = arrayToTree(normalizedData, {
    customID: 'data.id',
    parentProperty: 'data.parent_id',
  })

  treeNodes.value = tree
}

// 更新树结构，添加用户节点
const updateTreeWithUsers = (deptId: number) => {
  const users = usersByDept.value[deptId] || []

  const findAndUpdateDept = (nodes: TreeNode[]): boolean => {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i]

      if (node.data.type === 'department' && node.data.id === deptId) {
        // 创建用户节点
        const userNodes = users.map<TreeNode>((user) => ({
          key: `user_${user.id}`,
          label: user.name,
          selectable: !props.onlyDepartment,
          leaf: true,
          data: {
            id: user.id,
            parent_id: deptId,
            name: user.name,
            type: 'user',
            avatar: user.avatar,
          },
        }))

        // 保留现有的部门子节点
        const deptChildren = node.children?.filter(
          (child) => child.data.type === 'department',
        ) || []

        // 合并部门和用户节点
        node.children = [...deptChildren, ...userNodes]

        return true
      }

      if (node.children && node.children.length) {
        if (findAndUpdateDept(node.children)) {
          return true
        }
      }
    }

    return false
  }

  findAndUpdateDept(treeNodes.value)
}

// 初始化获取部门数据
const fetchDepartments = async () => {
  try {
    const depts = await UserService.getDepartments()

    if (depts) {
      departmentList.value = depts
      buildTreeNodes()
    }
  }
  catch (error) {
    console.error('获取部门列表失败', error)
  }
}

// 获取指定部门下的用户
const fetchUsersByDepartment = async (deptId: number) => {
  if (props.onlyDepartment) {
    return
  }

  loadingDepts.value[deptId] = true

  try {
    const users = await UserService.getUsers({ dept_id: deptId })

    if (users) {
      usersByDept.value[deptId] = users
      updateTreeWithUsers(deptId)
    }
  }
  catch (error) {
    console.error(`获取部门 ${deptId} 的用户列表失败`, error)
  }
  finally {
    loadingDepts.value[deptId] = false
  }
}

// 根据层级生成初始展开的键
const generateInitialExpandedKeys = (nodes: TreeNode[], currentLevel = 1): ExpandedKeys => {
  const keys: ExpandedKeys = {}

  if (currentLevel > props.defaultExpandedLevel) {
    return keys
  }

  for (const node of nodes) {
    if (node.data.type === 'department') {
      keys[node.key] = true

      if (node.children) {
        Object.assign(keys, generateInitialExpandedKeys(node.children, currentLevel + 1))
      }
    }
  }

  return keys
}

const expandedKeys = ref<ExpandedKeys>({})

// 当树节点构建完成后，设置初始展开状态
watch(treeNodes, () => {
  if (treeNodes.value.length > 0) {
    expandedKeys.value = generateInitialExpandedKeys(treeNodes.value)

    // 仅当用户设置了默认展开层级时才预加载数据
    if (props.defaultExpandedLevel > 0) {
      Object.keys(expandedKeys.value).forEach((key) => {
        if (key.startsWith('dept_')) {
          const deptId = Number(key.replace('dept_', ''))

          if (!usersByDept.value[deptId]) {
            fetchUsersByDepartment(deptId)
          }
        }
      })
    }
  }
}, { immediate: true })

const handleNodeSelect = (ev: OrgTreeNode) => {
  const nodeData = ev.data

  // 根据节点类型和属性决定是否允许选择
  if (nodeData.type === 'department' && props.onlyUser) {
    return
  }

  if (nodeData.type === 'user' && props.onlyDepartment) {
    return
  }

  if (!selectedUsers.value.some((u) => u.id === nodeData.id)) {
    selectedUsers.value = [...selectedUsers.value, nodeData]
  }
  else {
    selectedUsers.value = selectedUsers.value.filter((u) => u.id !== nodeData.id)
  }
}

const handleNodeExpand = (node: OrgTreeNode) => {
  expandedKeys.value = {
    ...expandedKeys.value,
    [node.key]: true,
  }

  // 当展开部门节点时，加载该部门下的用户
  if (node.key.startsWith('dept_')) {
    const deptId = Number(node.key.replace('dept_', ''))

    if (!usersByDept.value[deptId]) {
      fetchUsersByDepartment(deptId)
    }
  }
}

// 处理节点折叠事件
const handleNodeCollapse = (node: OrgTreeNode) => {
  expandedKeys.value = {
    ...expandedKeys.value,
    [node.key]: false,
  }
}

// 组件挂载时获取部门数据
onMounted(() => {
  fetchDepartments()
})
</script>

<template>
  <div>
    <Tree
      class="!p-0"
      :expandedKeys="expandedKeys"
      selectionMode="multiple"
      :value="treeNodes"
      @nodeCollapse="handleNodeCollapse"
      @nodeExpand="handleNodeExpand"
      @nodeSelect="handleNodeSelect"
    >
      <template #default="{ node }: { node: OrgTreeNode }">
        <span class="gap-2 flex-center">
          <Checkbox
            v-if="node.selectable"
            binary
            :modelValue="selectedUsers.some((u) => u.id === node.data.id)"
            @click.prevent
          />

          <!-- 部门图标或用户头像 -->
          <span
            v-if="node.data.type === 'department'"
            class="shrink-0"
          >
            <FolderIcon
              class="text-primary"
              :size="16"
            />
          </span>

          <span
            v-else-if="node.data.type === 'user'"
            class="shrink-0 flex-center"
          >
            <UserAvatar
              :avatar="node.data.avatar"
              class="!size-6 object-cover !text-xs"
              :username="node.data.name"
            />
          </span>

          <!-- 节点名称 -->
          <span class="flex-1 truncate">{{ node.label }}</span>
        </span>
      </template>

      <!-- 自定义展开/折叠图标 -->
      <template #nodetoggleicon="{ node, expanded }">
        <Component
          :is="
            loadingDepts[node.data.id]
              ? LoaderIcon
              : expanded
                ? ChevronDownIcon
                : ChevronRightIcon
          "
          :class="{
            'animate-spin': loadingDepts[node.data.id],
          }"
          :size="15"
        />
      </template>
    </Tree>
  </div>
</template>
