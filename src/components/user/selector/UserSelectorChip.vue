<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { useQuery } from '@tanstack/vue-query'
import { XIcon } from 'lucide-vue-next'

import { queryKeys } from '~/enums/query-key'

interface Props {
  value: {
    id: User['id']
  } & Partial<User>
  showRemove?: boolean
  /** 是否启用用户信息卡片 */
  enablePopoverCard?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showRemove: false,
  enablePopoverCard: false,
})

const userId = computed(() => props.value.id)

const emit = defineEmits<{
  remove: [value: Props['value']['id']]
}>()

const { popoverRef, popover } = usePopover()

const { data: userDetail, refetch: fetchUserDetail } = useQuery({
  queryKey: queryKeys.user.detail(userId),
  queryFn: () => UserService.getUser(userId.value),
  enabled: false,
})

const handleClick = async (ev: MouseEvent) => {
  if (props.enablePopoverCard) {
    ev.stopPropagation()

    popover.value?.toggle(ev)

    if (!userDetail.value) {
      await fetchUserDetail()
    }
  }
}
</script>

<template>
  <span
    class="gap-1 whitespace-nowrap bg-emphasis px-2 py-1 inline-flex-center"
    :style="{ borderRadius: $dt('tag.border.radius').value }"
  >
    <span
      class="inline-flex"
      :class="{
        'cursor-pointer': enablePopoverCard,
      }"
      @click="handleClick($event)"
    >
      <UserAvatar
        :avatar="value.avatar"
        class="!size-5 !rounded !text-xs"
        :username="value.username"
      />
    </span>

    <span class="text-sm">{{ value.username }}</span>

    <span
      v-if="showRemove"
      class="ml-auto mr-[-0.3rem] shrink-0 rounded p-0.5 text-secondary hover:bg-danger-50 hover:text-danger-500"
      @click.stop="emit('remove', value.id)"
    >
      <XIcon :size="13" />
    </span>

    <Popover
      :ref="popoverRef"
    >
      <div class="space-y-3 p-1">
        <div class="gap-3 flex-center">
          <UserAvatar
            :avatar="userDetail?.avatar"
            class="!size-10"
            :username="userDetail?.username"
          />
          <div class="flex flex-col">
            <span class="font-medium">{{ userDetail?.username }}</span>
            <span
              v-if="userDetail?.email"
              class="text-sm text-secondary"
            >
              {{ userDetail?.email }}
            </span>
          </div>
        </div>

        <div
          v-if="userDetail?.organize_name"
          class="text-sm"
        >
          <span class="text-secondary">所属组织：</span>
          {{ userDetail?.organize_name }}
        </div>
      </div>
    </Popover>
  </span>
</template>
