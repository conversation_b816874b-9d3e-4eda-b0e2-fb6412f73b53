<script setup lang="ts">
import type { UserSelectorPanelProps } from './UserSelectorPanel.vue'
import type { OrgItem } from './UserSelectorPanelOrgList.vue'

interface props {
  visible: boolean
  userSelectorPanelProps?: UserSelectorPanelProps
}

const props = defineProps<props>()

const emit = defineEmits<{
  done: [value?: OrgItem[]]
  close: []
}>()

const dialogVisible = ref(props.visible)

watch(() => props.visible, (val) => {
  dialogVisible.value = val
})

const handleClose = () => {
  emit('close')
}

const handleConfirm = (users: OrgItem[]) => {
  emit('done', users)
}
</script>

<template>
  <Dialog
    v-model:visible="dialogVisible"
    class="dialog-half"
    dismissableMask
    header="选择用户"
    modal
    :pt="{
      content: '!overflow-hidden !flex !flex-col',
    }"
    @hide="handleClose"
  >
    <UserSelectorPanel
      v-bind="userSelectorPanelProps"
      :search="false"
      @cancel="handleClose"
      @confirm="handleConfirm"
    />
  </Dialog>
</template>
