<script setup lang="ts">
defineProps<{
  itemProps?: Record<string, undefined>
  label?: string
  labelIcon?: Component | null
  href?: string
  target?: string
  navigate?: () => void
}>()
</script>

<template>
  <a
    v-bind="itemProps?.action || {}"
    class="gap-2 !py-1.5 text-sm flex-center"
    :href="href"
    :target="target"
    @click="navigate"
  >
    <Component
      :is="labelIcon"
      v-if="labelIcon"
      class="size-4 shrink-0"
    />

    <span>{{ label }}</span>
  </a>
</template>
