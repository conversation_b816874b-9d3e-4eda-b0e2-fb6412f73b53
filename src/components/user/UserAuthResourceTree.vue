<script setup lang="ts">
import { ChevronDownIcon, ChevronRightIcon } from 'lucide-vue-next'

import { ProMenuActionType } from '~/enums/pro'

const emit = defineEmits<{
  updateUserGroup: [userGroup: UserGroup]
  createUserGroup: [userGroup: Partial<UserGroup>]
  deleteUserGroup: [userGroup: UserGroup]
}>()

const userStore = useAdminUserGroupStore()
const { activeUserGroupId, userGroupTree, expandedKeys } = storeToRefs(userStore)

const optionMenuNode = ref<UserGroupTreeNode>()

const menuOptions: ProMenuItem[] = [
  {
    label: '编辑',
    actionType: ProMenuActionType.Edit,
    command: () => {
      if (optionMenuNode.value) {
        emit('updateUserGroup', optionMenuNode.value)
      }
    },
  },
  {
    label: '添加子资源',
    actionType: ProMenuActionType.Add,
    command: () => {
      if (optionMenuNode.value) {
        emit('createUserGroup', {
          parent_id: optionMenuNode.value.id,
        })
      }
    },
  },
  {
    label: '删除',
    actionType: ProMenuActionType.Delete,
    command: () => {
      if (optionMenuNode.value) {
        emit('deleteUserGroup', optionMenuNode.value)
      }
    },
  },
]

const handleSelect = (nodeInfo: Pick<UserGroupTreeNode, 'key' | 'label' | 'parent_id' | 'children'>) => {
  userStore.setActiveUserGroup({
    id: Number(nodeInfo.key),
    name: nodeInfo.label ?? '',
  })
}

const { dualMenuRef, dualMenu } = useDualMenu()

const handleOpenMenu = (ev: MouseEvent, nodeData: UserGroupTreeNode) => {
  dualMenu.value?.showMenu(ev)
  optionMenuNode.value = nodeData
}
</script>

<template>
  <div>
    <Tree
      class="!p-0"
      :expandedKeys="expandedKeys"
      :pt="{
        nodeContent: {
          class: 'group/node-content h-full h-[31px] !py-0',
        },
        nodeLabel: {
          class: 'size-full min-w-0',
        },
      }"
      :selectionKeys="{ [`${activeUserGroupId}`]: true }"
      selectionMode="single"
      :value="userGroupTree"
      @nodeSelect="handleSelect"
    >
      <template #default="slotProps: { node: UserGroupTreeNode }">
        <span
          class="size-full gap-2 flex-center"
          @contextmenu.stop.prevent="handleOpenMenu($event, slotProps.node)"
        >
          <span class="h-[31px] flex-1 truncate leading-[31px]">{{ slotProps.node.label }}</span>

          <span class="ml-auto hidden shrink-0 group-hover/node-content:inline-flex">
            <ProBtnMore
              :dt="{
                sm: {
                  padding: {
                    x: '0.1rem',
                    y: '0.1rem',
                  },
                },
              }"
              onlyIcon
              size="small"
              :tooltip="false"
              variant="text"
              @click.stop="handleOpenMenu($event, slotProps.node)"
            />
          </span>
        </span>
      </template>

      <template #nodetoggleicon="{ expanded }: { expanded: boolean }">
        <Component
          :is="expanded ? ChevronDownIcon : ChevronRightIcon"
          :size="15"
        />
      </template>
    </Tree>

    <DualMenu
      :ref="dualMenuRef"
      :menuItems="menuOptions"
    />
  </div>
</template>
