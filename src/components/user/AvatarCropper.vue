<script setup lang="ts">
import { CropIcon, MinusIcon, PlusIcon } from 'lucide-vue-next'

// 扩展HTMLCanvasElement和Document类型
declare global {
  interface HTMLCanvasElement {
    _mouseDownHandler?: (e: MouseEvent) => void
    _wheelHandler?: (e: WheelEvent) => void
    _touchStartHandler?: (e: TouchEvent) => void
  }

  interface Document {
    _mouseMoveHandler?: (e: MouseEvent) => void
    _mouseUpHandler?: () => void
    _touchMoveHandler?: (e: TouchEvent) => void
    _touchEndHandler?: () => void
  }
}

interface Props {
  /** 裁剪区域大小，默认 300px */
  cropperSize?: number
  /** 是否显示裁剪对话框 */
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  cropperSize: 300,
  visible: false,
})

const emit = defineEmits<{
  /** 裁剪完成并提交 */
  crop: [blob: Blob]
  /** 更新对话框可见状态 */
  'update:visible': [visible: boolean]
  /** 关闭对话框 */
  close: []
}>()

// 控制对话框显示
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 裁剪相关的状态
const originalImage = ref<string | null>(null)
const imageElement = ref<HTMLImageElement | null>(null)
const cropperCanvas = ref<HTMLCanvasElement | null>(null)
const cropperContext = ref<CanvasRenderingContext2D | null>(null)
const isDragging = ref(false)
const cropArea = reactive({
  x: 0,
  y: 0,
  size: props.cropperSize,
  initialX: 0,
  initialY: 0,
  startX: 0,
  startY: 0,
})

// 缩放系数
const zoomFactor = ref(1)
const minZoom = 1
const maxZoom = 3
const zoomStep = 0.1

// 设备像素比，用于高清显示
const pixelRatio = ref(window.devicePixelRatio || 1)

// 绘制图像和裁剪框
const drawImage = () => {
  if (!cropperContext.value || !imageElement.value) {
    return
  }

  const ctx = cropperContext.value
  const img = imageElement.value
  const canvasSize = props.cropperSize * pixelRatio.value

  // 清除画布
  ctx.clearRect(0, 0, canvasSize, canvasSize)

  // 计算图像缩放比例，确保至少覆盖裁剪区域
  const baseScale = Math.max(
    props.cropperSize / img.width,
    props.cropperSize / img.height,
  )

  // 应用额外的缩放
  const scale = baseScale * zoomFactor.value

  // 计算缩放后的尺寸
  const scaledWidth = img.width * scale * pixelRatio.value
  const scaledHeight = img.height * scale * pixelRatio.value

  // 应用图像平滑设置，提高缩放质量
  ctx.imageSmoothingEnabled = true
  ctx.imageSmoothingQuality = 'high'

  // 居中绘制图像
  ctx.drawImage(
    img,
    cropArea.x * pixelRatio.value + (canvasSize - scaledWidth) / 2,
    cropArea.y * pixelRatio.value + (canvasSize - scaledHeight) / 2,
    scaledWidth,
    scaledHeight,
  )

  // 绘制裁剪辅助线
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
  ctx.lineWidth = 1 * pixelRatio.value

  // 绘制水平和垂直的三等分线
  const thirdSize = canvasSize / 3

  // 水平线
  ctx.beginPath()
  ctx.moveTo(0, thirdSize)
  ctx.lineTo(canvasSize, thirdSize)
  ctx.stroke()

  ctx.beginPath()
  ctx.moveTo(0, thirdSize * 2)
  ctx.lineTo(canvasSize, thirdSize * 2)
  ctx.stroke()

  // 垂直线
  ctx.beginPath()
  ctx.moveTo(thirdSize, 0)
  ctx.lineTo(thirdSize, canvasSize)
  ctx.stroke()

  ctx.beginPath()
  ctx.moveTo(thirdSize * 2, 0)
  ctx.lineTo(thirdSize * 2, canvasSize)
  ctx.stroke()

  // 绘制边框
  ctx.strokeStyle = 'white'
  ctx.lineWidth = 2 * pixelRatio.value
  ctx.strokeRect(0, 0, canvasSize, canvasSize)
}

// 初始化裁剪画布
const initCropper = () => {
  if (!cropperCanvas.value) {
    return
  }

  const canvas = cropperCanvas.value
  const canvasSize = props.cropperSize * pixelRatio.value

  // 设置实际的画布尺寸（物理像素）
  canvas.width = canvasSize
  canvas.height = canvasSize

  // 设置CSS尺寸（逻辑像素）
  canvas.style.width = `${props.cropperSize}px`
  canvas.style.height = `${props.cropperSize}px`

  cropperContext.value = canvas.getContext('2d')

  // 重置裁剪区域
  cropArea.x = 0
  cropArea.y = 0
  zoomFactor.value = 1

  // 绘制初始图像
  drawImage()
}

// 处理缩放
const handleZoom = (delta: number) => {
  const newZoom = Math.max(minZoom, Math.min(maxZoom, zoomFactor.value + delta))
  zoomFactor.value = newZoom
  drawImage()
}

// 当图像加载完成后初始化裁剪
const handleImageLoad = () => {
  // 重置裁剪区域
  cropArea.x = 0
  cropArea.y = 0

  // 需要在下一个更新周期后初始化裁剪，确保DOM已经更新
  nextTick(() => {
    initCropper()
  })
}

// 提交裁剪结果
const submitCrop = () => {
  if (!cropperCanvas.value) {
    return
  }

  // 创建一个临时画布用于输出最终结果
  const outputCanvas = document.createElement('canvas')
  const outputSize = props.cropperSize // 输出尺寸
  outputCanvas.width = outputSize
  outputCanvas.height = outputSize

  const outputCtx = outputCanvas.getContext('2d')

  if (!outputCtx || !cropperContext.value) {
    return
  }

  // 从原始高清画布复制并缩放到输出画布
  outputCtx.imageSmoothingEnabled = true
  outputCtx.imageSmoothingQuality = 'high'
  outputCtx.drawImage(cropperCanvas.value, 0, 0, props.cropperSize * pixelRatio.value, props.cropperSize * pixelRatio.value, 0, 0, outputSize, outputSize)

  // 使用更高质量导出
  outputCanvas.toBlob((blob) => {
    if (blob) {
      emit('crop', blob)
      dialogVisible.value = false
    }
  }, 'image/jpeg', 1.0) // 使用最高质量
}

// 取消裁剪，清理状态
const handleCancel = () => {
  dialogVisible.value = false
  emit('close')
}

// 设置要裁剪的图像源
const setImageSource = (source: string) => {
  originalImage.value = source
}

// 暴露给父组件的方法
defineExpose({
  setImageSource,
})

// 在对话框显示时添加事件监听
watch(dialogVisible, (newValue) => {
  nextTick(() => {
    if (newValue && cropperCanvas.value) {
      // 直接在函数内部定义处理函数
      const canvasMouseDown = (e: MouseEvent) => {
        isDragging.value = true
        cropArea.startX = e.clientX
        cropArea.startY = e.clientY
        cropArea.initialX = cropArea.x
        cropArea.initialY = cropArea.y
      }

      const documentMouseMove = (e: MouseEvent) => {
        if (!isDragging.value) {
          return
        }

        const dx = e.clientX - cropArea.startX
        const dy = e.clientY - cropArea.startY

        cropArea.x = cropArea.initialX + dx
        cropArea.y = cropArea.initialY + dy

        drawImage()
      }

      const documentMouseUp = () => {
        isDragging.value = false
      }

      const canvasWheel = (e: WheelEvent) => {
        e.preventDefault()

        // 根据滚轮方向进行缩放
        const delta = e.deltaY > 0 ? -zoomStep : zoomStep
        handleZoom(delta)
      }

      // 触摸事件处理
      const canvasTouchStart = (e: TouchEvent) => {
        if (e.touches.length === 1) {
          isDragging.value = true
          cropArea.startX = e.touches[0].clientX
          cropArea.startY = e.touches[0].clientY
          cropArea.initialX = cropArea.x
          cropArea.initialY = cropArea.y
        }
      }

      const documentTouchMove = (e: TouchEvent) => {
        if (!isDragging.value || e.touches.length !== 1) {
          return
        }

        const dx = e.touches[0].clientX - cropArea.startX
        const dy = e.touches[0].clientY - cropArea.startY

        cropArea.x = cropArea.initialX + dx
        cropArea.y = cropArea.initialY + dy

        drawImage()

        // 阻止页面滚动
        e.preventDefault()
      }

      const documentTouchEnd = () => {
        isDragging.value = false
      }

      // 使用新定义的函数添加鼠标事件监听
      cropperCanvas.value.addEventListener('mousedown', canvasMouseDown)
      cropperCanvas.value.addEventListener('wheel', canvasWheel)
      document.addEventListener('mousemove', documentMouseMove)
      document.addEventListener('mouseup', documentMouseUp)

      // 添加触摸事件监听
      cropperCanvas.value.addEventListener('touchstart', canvasTouchStart)
      document.addEventListener('touchmove', documentTouchMove, { passive: false })
      document.addEventListener('touchend', documentTouchEnd)

      // 保存事件引用以便后续移除
      cropperCanvas.value._mouseDownHandler = canvasMouseDown
      cropperCanvas.value._wheelHandler = canvasWheel
      cropperCanvas.value._touchStartHandler = canvasTouchStart
      document._mouseMoveHandler = documentMouseMove
      document._mouseUpHandler = documentMouseUp
      document._touchMoveHandler = documentTouchMove
      document._touchEndHandler = documentTouchEnd
    }
    else {
      // 根据保存的引用移除事件监听
      if (cropperCanvas.value) {
        // 移除鼠标事件
        document.removeEventListener('mousemove', document._mouseMoveHandler!)
        document.removeEventListener('mouseup', document._mouseUpHandler!)
        cropperCanvas.value.removeEventListener('mousedown', cropperCanvas.value._mouseDownHandler!)
        cropperCanvas.value.removeEventListener('wheel', cropperCanvas.value._wheelHandler!)

        // 移除触摸事件
        cropperCanvas.value.removeEventListener('touchstart', cropperCanvas.value._touchStartHandler!)
        document.removeEventListener('touchmove', document._touchMoveHandler!)
        document.removeEventListener('touchend', document._touchEndHandler!)
      }
    }
  })
})

// 在组件卸载时移除事件监听
onUnmounted(() => {
  // 移除鼠标事件
  if (document._mouseMoveHandler) {
    document.removeEventListener('mousemove', document._mouseMoveHandler)
  }

  if (document._mouseUpHandler) {
    document.removeEventListener('mouseup', document._mouseUpHandler)
  }

  // 移除触摸事件
  if (document._touchMoveHandler) {
    document.removeEventListener('touchmove', document._touchMoveHandler)
  }

  if (document._touchEndHandler) {
    document.removeEventListener('touchend', document._touchEndHandler)
  }

  if (cropperCanvas.value) {
    if (cropperCanvas.value._mouseDownHandler) {
      cropperCanvas.value.removeEventListener('mousedown', cropperCanvas.value._mouseDownHandler)
    }

    if (cropperCanvas.value._wheelHandler) {
      cropperCanvas.value.removeEventListener('wheel', cropperCanvas.value._wheelHandler)
    }

    if (cropperCanvas.value._touchStartHandler) {
      cropperCanvas.value.removeEventListener('touchstart', cropperCanvas.value._touchStartHandler)
    }
  }
})
</script>

<template>
  <!-- 裁剪对话框 -->
  <Dialog
    v-model:visible="dialogVisible"
    header="调整头像"
    modal
    :style="{ width: '450px' }"
  >
    <div class="flex-col gap-4 flex-center">
      <!-- 隐藏的图像元素用于加载原始图像 -->
      <img
        ref="imageElement"
        class="hidden"
        :src="originalImage"
        @load="handleImageLoad"
      >

      <!-- 操作提示 -->
      <p class="text-sm text-secondary">
        <span class="font-medium text-primary">拖动</span>调整位置 | <span class="font-medium text-primary">滚轮</span>缩放图片
      </p>

      <!-- 裁剪画布 -->
      <div
        class="relative overflow-hidden rounded-xl border border-divider"
        :style="{
          width: `${props.cropperSize}px`,
          height: `${props.cropperSize}px`,
        }"
      >
        <canvas
          ref="cropperCanvas"
          class="cursor-move"
          :height="props.cropperSize"
          :width="props.cropperSize"
        />
      </div>

      <!-- 缩放控制 -->
      <div class="flex items-center gap-2">
        <Button
          :disabled="zoomFactor <= minZoom"
          severity="secondary"
          size="small"
          variant="text"
          @click="handleZoom(-zoomStep)"
        >
          <MinusIcon :size="14" />
        </Button>

        <div class="w-24 text-center text-sm">
          缩放: {{ Math.round(zoomFactor * 100) }}%
        </div>

        <Button
          :disabled="zoomFactor >= maxZoom"
          severity="secondary"
          size="small"
          variant="text"
          @click="handleZoom(zoomStep)"
        >
          <PlusIcon :size="14" />
        </Button>
      </div>

      <div class="flex w-full justify-center gap-5">
        <Button
          label="取消上传"
          severity="secondary"
          @click="handleCancel()"
        />

        <Button
          label="确认上传"
          @click="submitCrop()"
        >
          <template #icon>
            <CropIcon
              class="mr-1"
              :size="16"
            />
          </template>
        </Button>
      </div>
    </div>
  </Dialog>
</template>
