<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import type { ProSelectProps } from '~/components/pro/select/ProSelect.vue'
import { queryKeys } from '~/enums/query-key'
import { UserService } from '~/services/user'

const props = defineProps<ProSelectProps>()

const value = defineModel<Role['id']>()

const { data: roleList, isLoading } = useQuery({
  queryKey: queryKeys.role.all(),
  queryFn: () => {
    return UserService.getRoleList({ page: 1, page_size: 100 })
  },
})

const options = computed(() => roleList.value?.list.map((item) => ({
  label: item.name,
  value: item.id,
})))
</script>

<template>
  <ProSelect
    v-bind="props"
    v-model="value"
    :loading="isLoading"
    :options="options"
    placeholder="选择系统角色"
  />
</template>
