<script setup lang="ts">
import { UploadIcon } from 'lucide-vue-next'
import type { FileUploadSelectEvent } from 'primevue'
import { array, file, length, maxSize, pipe, safeParse } from 'valibot'

import AvatarCropper from './AvatarCropper.vue'

interface Props {
  /** 自定义类名 */
  class?: string
  /** 文件大小限制（MB），默认 5MB */
  maxSizeMB?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxSizeMB: 5,
})

const emit = defineEmits<{
  /** 文件选择完成 */
  select: [file: File]
  /** 上传错误 */
  error: [message: string]
}>()

const { $toast } = useNuxtApp()

// 裁剪相关状态
const showCropper = ref(false)
const cropperRef = ref<InstanceType<typeof AvatarCropper> | null>(null)
const selectedFile = ref<File | null>(null)

// 构建文件验证Schema
const getFilesSchema = computed(() => {
  return pipe(
    array(pipe(file(), maxSize(props.maxSizeMB * 1024 * 1024, `文件大小不能超过 ${props.maxSizeMB}MB`))),
    length(1, '只能上传一个文件'),
  )
})

// 处理文件选择
const handleFileSelect = (event: FileUploadSelectEvent) => {
  const { success, output: files, issues } = safeParse(getFilesSchema.value, event.files)

  if (success) {
    selectedFile.value = files[0]
    const reader = new FileReader()

    reader.onload = (e) => {
      if (e.target?.result && cropperRef.value) {
        cropperRef.value.setImageSource(e.target.result as string)
        showCropper.value = true
      }
    }

    reader.readAsDataURL(files[0])
  }
  else {
    issues.forEach((issue) => {
      emit('error', issue.message)

      $toast.error({
        summary: '所选文件不符合要求',
        detail: issue.message,
      })
    })
  }
}

// 处理裁剪结果
const handleCrop = (blob: Blob) => {
  // 将 Blob 转换为 File 对象，保留原始文件名
  if (selectedFile.value) {
    const fileName = selectedFile.value.name
    const file = new File([blob], fileName, { type: 'image/jpeg' })
    emit('select', file)
  }
}
</script>

<template>
  <div
    class="relative"
    :class="props.class"
  >
    <!-- 头像上传按钮 -->
    <div class="mt-3 flex gap-2">
      <FileUpload
        accept="image/*"
        auto
        chooseLabel="选择头像"
        class="p-button-outlined"
        customUpload
        mode="basic"
        severity="secondary"
        @select="handleFileSelect($event)"
      >
        <template #chooseicon>
          <UploadIcon
            class="mr-1"
            :size="14"
          />
        </template>
      </FileUpload>
    </div>

    <!-- 裁剪组件 -->
    <AvatarCropper
      ref="cropperRef"
      v-model:visible="showCropper"
      @crop="handleCrop"
    />
  </div>
</template>
