<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/enums/query-key'

const props = defineProps<{
  user?: UserListItem | User
}>()

const userId = computed(() => props.user?.id)

const visible = ref(false)
const password = ref('')

defineExpose({
  open: () => {
    visible.value = true
  },
  close: () => {
    visible.value = false
  },
})

const { $toast } = useNuxtApp()

const { isLoading, refetch: resetPwd } = useQuery({
  queryKey: queryKeys.user.detail(userId),
  queryFn: () => {
    if (userId.value && password.value) {
      return UserService.resetUserPassword(userId.value, { password: password.value })
    }

    return null
  },
  staleTime: 0,
  enabled: false,
})

const handleVisibleChange = (v: boolean) => {
  visible.value = v

  if (!v) {
    password.value = ''
  }
}

const handleResetPwd = async () => {
  const userId = props.user?.id

  if (userId && password.value) {
    await resetPwd()

    $toast.success({ summary: '重置密码成功' })

    visible.value = false
  }
}
</script>

<template>
  <Dialog
    class="w-80"
    header="重置用户密码"
    modal
    :visible="visible"
    @update:visible="handleVisibleChange($event)"
  >
    <div class="flex w-full flex-col gap-form-field">
      <FormItem
        label="新密码"
        name="password"
      >
        <Password
          v-model="password"

          :feedback="false"
          fluid
          inputId="password"
        />
      </FormItem>

      <FormActionGroup
        :confirmButtonProps="{
          label: '重置密码',
          loading: isLoading,
          disabled: !user || !password,
        }"
        @cancel="visible = false"
        @confirm="handleResetPwd"
      />
    </div>
  </Dialog>
</template>
