<script setup lang="ts">
import type { AvatarProps } from 'primevue'

import type { User } from '~/types/user'

interface Props extends AvatarProps, Partial<Pick<User, 'avatar' | 'username'>> {
  class?: string
}

const props = defineProps<Props>()

const avatarUrl = computed(() => {
  if (props.avatar) {
    if (props.avatar.startsWith('http')) {
      return props.avatar
    }

    return appImage(`avatar/${props.avatar}`)
  }

  return undefined
})
</script>

<template>
  <Avatar
    v-bind="props"
    class="overflow-hidden border border-divider"
    :class="props.class"
    :image="avatarUrl"
    :label="props.avatar ? undefined : props.username?.slice(0, 1)"
  />
</template>
