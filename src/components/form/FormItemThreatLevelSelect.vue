<script setup lang="ts">
import type { VulThreatLevel } from '~/enums/security'
import { getThreatLevelLabel, getThreatLevelSeverity, threatLevelConfig } from '~/enums/security'

const threatLevelOptions = Object.entries(threatLevelConfig).map(([key, value]) => ({
  label: value.label,
  value: Number(key),
}))

const modelValue = defineModel<VulThreatLevel>()
</script>

<template>
  <ProSelect
    v-model="modelValue"
    fluid
    :options="threatLevelOptions"
    placeholder="选择威胁等级"
    showClear
  >
    <template #option="{ option }: { option: typeof threatLevelOptions[number] }">
      <Tag
        :severity="getThreatLevelSeverity(option.value)"
        :value="getThreatLevelLabel(option.value)"
      />
    </template>

    <template #value="{ value }: { value: typeof threatLevelOptions[number]['value'] }">
      <Tag
        v-if="value"
        :severity="getThreatLevelSeverity(value)"
        :value="getThreatLevelLabel(value)"
      />
    </template>
  </ProSelect>
</template>
