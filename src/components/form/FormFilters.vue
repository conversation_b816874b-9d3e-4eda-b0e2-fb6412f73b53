<script setup lang="ts">
import { SearchIcon } from 'lucide-vue-next'

import { ProValueType } from '~/enums/pro'

interface FormFiltersProps<C = UnsafeAny> {
  modelValue?: ProTableFilterValues
  columns?: ProTableColumn<C>[]
}

const props = defineProps<FormFiltersProps>()

const finalColumns = computed(() => props.columns?.filter(
  (col) => col.hideInFilter !== true && col.valueType !== ProValueType.ActionGroup,
))

const emit = defineEmits<{
  reset: []
  submit: []
  'update:modelValue': [ProTableFilterValues]
}>()
</script>

<template>
  <Form>
    <div>
      <div class="flex max-h-96 min-w-56 flex-col gap-4 overflow-y-auto">
        <template
          v-for="col of finalColumns"
          :key="col.field"
        >
          <FormItem :label="col.header">
            <ProFormField
              :fieldProps="{
                size: 'small',
              }"
              :fieldType="col.valueType ?? ProValueType.Text"
              :modelValue="typeof col.field === 'string' ? modelValue?.[col.field] : undefined"
              :options="toOptionsFromValueEnum(col.valueEnum)"
              @update:modelValue="(val: UnsafeAny) => {
                if (typeof col.field === 'string') {
                  emit('update:modelValue', {
                    ...modelValue,
                    [col.field]: val,
                  })
                }
              }"
            />
          </FormItem>
        </template>
      </div>

      <div class="flex justify-end gap-2 pt-4">
        <Button
          label="清除筛选"
          severity="secondary"
          size="small"
          @click="emit('reset')"
        />

        <Button
          label="搜索"
          size="small"
          @click="emit('submit')"
        >
          <template #icon>
            <SearchIcon :size="12" />
          </template>
        </Button>
      </div>
    </div>
  </Form>
</template>
