<script setup lang="ts">
import { HelpCircleIcon } from 'lucide-vue-next'

import { TooltipShowDelay } from '~/enums/common'

interface FormItemLabelProps {
  /** 表单项标题 */
  label?: string
  /** 是否显示冒号 */
  colon?: boolean
  /** 表单项提示，通过 tooltip 显示，支持 HTML 内容 */
  tip?: string
  /** 是否必填 */
  required?: boolean
  /** 表单项标题样式 */
  labelClass?: string
  /** 表单项名称 */
  fieldName?: string
}

withDefaults(defineProps<FormItemLabelProps>(), {
  colon: undefined,
  required: false,
})
</script>

<template>
  <label
    class="relative font-semibold inline-flex-center"
    :class="[
      {
        'form-item-label-with-mark': required,
      },
      labelClass,
    ]"
    :for="fieldName"
  >
    {{ label }}

    <BaseTooltip
      v-if="tip"
      class="mx-0.5 opacity-70 inline-flex-center"
      placement="top"
      :showDelay="TooltipShowDelay.Fast"
    >
      <template #content>
        <div v-html="tip" />
      </template>

      <HelpCircleIcon
        class="cursor-help"
        :size="14"
      />
    </BaseTooltip>

    <span v-if="colon">：</span>
  </label>
</template>
