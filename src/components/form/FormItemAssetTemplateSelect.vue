<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { BoltIcon } from 'lucide-vue-next'

import type { ProSelectProps } from '~/components/pro/select/ProSelect.vue'
import type { AssetType } from '~/enums/asset'
import { queryKeys } from '~/enums/query-key'
import type { ProSelectOption } from '~/types/pro'

interface Props extends ProSelectProps {
  assetType: AssetType
}

const props = defineProps<Props>()

const { assetType, ...restSelectProps } = props
const assetTypeProp = computed(() => assetType)

const store = useAssetInfoStore()
const { isAssetCollection, assetCollectionKey } = storeToRefs(store)

const value = defineModel<AssetTemplate['id']>('value')

const { data: assetTemplateList, isLoading } = useQuery({
  queryKey: queryKeys.asset.template.all(assetTypeProp),
  queryFn: () => {
    if (isAssetCollection.value && assetCollectionKey.value) {
      return AssetService.getAssetCollectTemplates(assetCollectionKey.value, {
        asset_type: assetTypeProp.value,
      })
    }

    return AssetService.getAssetTemplateList({
      asset_type: assetTypeProp.value,
    })
  },
})

const options = computed<ProSelectOption<AssetTemplate['id']>[]>(() => assetTemplateList.value?.list?.map((item) => ({
  label: item.name,
  value: item.id,
  icon: item.icon,
})) ?? [])

const selectedOption = computed(() =>
  value.value ? options.value?.find((opt) => opt.value === value.value) : undefined,
)
</script>

<template>
  <ProSelect
    v-bind="restSelectProps"
    :loading="isLoading"
    :modelValue="value"
    :options="options"
    @update:modelValue="value = $event"
  >
    <template #value="{ value: val }: { value: AssetTemplate['id'] }">
      <div
        v-if="selectedOption && val"
        class="flex-center"
      >
        <div class="gap-2 flex-center">
          <span class="size-5 justify-center inline-flex-center">
            <IconRender
              v-if="typeof selectedOption.icon === 'string'"
              :iconVal="selectedOption.icon"
            >
              <template #fallback>
                <BoltIcon />
              </template>
            </IconRender>
          </span>

          <span>{{ selectedOption.label }}</span>
        </div>
      </div>
    </template>

    <template #option="{ option }: { option: ProSelectOption }">
      <div class="gap-2 flex-center">
        <span class="size-5 justify-center inline-flex-center">
          <IconRender
            v-if="typeof option.icon === 'string'"
            :iconVal="option.icon"
          >
            <template #fallback>
              <BoltIcon />
            </template>
          </IconRender>
        </span>

        <span>{{ option.label }}</span>
      </div>
    </template>
  </ProSelect>
</template>
