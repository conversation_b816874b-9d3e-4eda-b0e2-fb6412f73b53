<script setup lang="ts">
import type { FormFieldProps } from '@primevue/forms'
import { nanoid } from 'nanoid'

interface FormItemProps extends FormFieldProps {
  /** 表单项标题 */
  label?: string
  labelClass?: string
  /** 表单字段名 */
  name?: string
  /** 表单项布局 */
  layout?: 'horizontal' | 'vertical'
  /** 是否显示冒号 */
  colon?: boolean
  /** 是否不显示样式 */
  noStyle?: boolean
  /** 是否必填 */
  required?: boolean
  /** 是否隐藏表单项 */
  hidden?: boolean
  /** 表单项提示，通过 tooltip 显示，展示在 label 旁边 */
  tip?: string
}

const props = withDefaults(defineProps<FormItemProps>(), {
  layout: 'vertical',
  colon: undefined,
  noStyle: false,
  required: false,
  hidden: false,
} as const satisfies FormItemProps)

defineSlots<{
  default: VNode[]
  message?: VNode[]
  /** 表单项帮助 */
  help?: VNode[]
  /** 表单项帮助提示 */
  helpTip?: () => string
}>()

const hasColon = computed(() => {
  // 如果 colon 明确设置了值，使用设置的值
  if (props.colon !== undefined) {
    return props.colon
  }

  // 否则根据 layout 判断，默认垂直布局不显示冒号，水平布局显示冒号
  return props.layout === 'horizontal'
})
</script>

<template>
  <FormField
    v-slot="$field: FormFieldContext"
    :class="{ hidden: hidden }"
    :initialValue="initialValue"
    :name="name"
  >
    <div
      :class="{
        'flex gap-2': !noStyle,
        'flex-col': layout === 'vertical' && !noStyle,
        'flex-row items-center': layout === 'horizontal' && !noStyle,
      }"
    >
      <div
        v-if="!noStyle"
        class="gap-1 flex-center"
      >
        <FormItemLabel
          :colon="hasColon"
          :fieldName="name"
          :label="label"
          :labelClass="labelClass"
          :required="required"
          :tip="tip"
        />
      </div>

      <div
        class="flex flex-1 flex-col gap-1.5"
        :class="{ 'items-start': layout === 'horizontal' }"
      >
        <slot
          :id="name || nanoid(4)"
          :field="$field"
          name="default"
        />

        <template v-if="!noStyle">
          <slot name="help" />

          <Message
            v-if="$field?.invalid"
            severity="error"
            size="small"
            variant="simple"
          >
            {{ $field.error?.message }}
          </Message>

          <slot name="message" />

          <Message
            v-if="$slots.helpTip"
            class="pb-1"
            severity="secondary"
            size="small"
            variant="simple"
          >
            <slot name="helpTip" />
          </Message>
        </template>
      </div>
    </div>
  </FormField>
</template>
