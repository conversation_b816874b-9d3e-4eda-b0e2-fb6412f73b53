<script setup lang="ts">
// HACK: 这里要明确引入类型，否则构建时会报错
import type { FormActionGroupProps } from '#imports'

defineSlots<{
  extra?: []
}>()

const props = withDefaults(defineProps<FormActionGroupProps>(), {
  justify: 'end',
  loading: false,
  template: 'cancel extra confirm',
  cancelButtonProps: () => ({
    label: '取消',
  }),
  confirmButtonProps: () => ({
    label: '确定',
  }),
})

const elements = computed(() => {
  return props.template.split(' ').filter(Boolean)
})
</script>

<template>
  <div
    class="flex gap-4"
    :class="justify === 'start' ? 'justify-start' : justify === 'center' ? 'justify-center' : 'justify-end'"
  >
    <template
      v-for="element of elements"
      :key="element"
    >
      <Button
        v-if="element === 'cancel' && cancelButtonProps !== false"
        severity="secondary"
        type="button"
        v-bind="cancelButtonProps"
        @click="onCancel"
      />

      <template v-if="element === 'extra'">
        <slot name="extra" />
      </template>

      <Button
        v-if="element === 'confirm' && confirmButtonProps !== false"
        v-bind="{ loading, ...confirmButtonProps }"
        type="submit"
        @click="onConfirm"
      />
    </template>
  </div>
</template>
