<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/enums/query-key'

const value = defineModel<AssetTemplate['id']>('value')

const { data: assetTemplateList, isLoading } = useQuery({
  queryKey: queryKeys.security.compliance.templates(),
  queryFn: () => ConfigService.getBaselineTplList(),
})

const options = computed(() => assetTemplateList.value?.list.map((item) => ({
  label: item.name,
  value: item.id,
})))
</script>

<template>
  <ProSelect
    :loading="isLoading"
    :modelValue="value"
    :options="options"
    @update:modelValue="value = $event"
  />
</template>
