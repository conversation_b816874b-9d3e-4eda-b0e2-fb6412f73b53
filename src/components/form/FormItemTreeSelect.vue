<script setup lang="ts">
import type { TreeSelectProps } from 'primevue'
import type { TreeNode } from 'primevue/treenode'

interface Props extends TreeSelectProps {
  /** 选中的 TreeNode 的唯一值 */
  value?: string | number
  disabledKeys?: TreeNode['key'] | TreeNode['key'][]
}

const props = defineProps<Props>()

const { disabledKeys } = toRefs(props)

const emit = defineEmits<{
  'update:value': [value: Props['value']]
}>()

const selectedValue = computed<Record<string, boolean> | undefined>({
  get: () => {
    if (props.value) {
      return { [String(props.value)]: true }
    }

    return undefined
  },
  set: (val) => {
    if (val) {
      const selectedNodeKey = Object.keys(val)[0]

      emit('update:value', selectedNodeKey)
    }
    else {
      emit('update:value', val)
    }
  },
})

const finalOptions = computed(() => {
  if (typeof disabledKeys.value === 'string' || (Array.isArray(disabledKeys.value) && disabledKeys.value.length > 0)) {
    const keys = typeof disabledKeys.value === 'string' ? [disabledKeys.value] : disabledKeys.value

    const processTreeNodes = (nodes?: TreeNode[]): TreeNode[] => {
      if (nodes) {
        return nodes.map((node) => {
          const hasChildren = node.children && node.children.length > 0
          const isDisabled = keys.includes(node.key)

          return {
            ...node,
            selectable: !isDisabled,
            children: hasChildren ? processTreeNodes(node.children) : undefined,
          }
        })
      }

      return []
    }

    return processTreeNodes(props.options)
  }

  return props.options
})
</script>

<template>
  <TreeSelect
    v-bind="props"
    v-model:modelValue="selectedValue"
    optionLabel="label"
    :options="finalOptions"
    optionValue="value"
  />
</template>
