<script setup lang="ts">
import { LogOutIcon, PaletteIcon } from 'lucide-vue-next'

import ProMenu from '~/components/pro/menu/ProMenu.vue'
import { ProMenuActionType } from '~/enums/pro'
import { RouteKey } from '~/enums/route'

const userStore = useUserStore()
const user = computed(() => userStore.user)

const appStore = useAppStore()

const { ref: userMenuRef, refKey: userMenuRefKey } = useRef<InstanceType<typeof ProMenu>>()

const userMenuItems = ref<ProMenuItem[]>([
  {
    label: '系统设置',
    itemIcon: getRouteIcon(RouteKey.系统设置),
    command: () => {
      navigateTo(getRoutePath(RouteKey.系统设置))
    },
  },
  {
    label: '个性设置',
    itemIcon: PaletteIcon,
    command: () => {
      appStore.openAppearanceDialog()
    },
  },
  {
    actionType: ProMenuActionType.Separator,
  },
  {
    label: '退出登录',
    itemIcon: LogOutIcon,
    command: () => {
      handleLogout()
    },
  },
])

const handleToggle = (ev: Event) => {
  userMenuRef.value?.popMenu?.toggle(ev)
}
</script>

<template>
  <div
    class="inline-block cursor-pointer"
    @click="handleToggle($event)"
  >
    <slot :user="user" />

    <ProMenu
      :ref="userMenuRefKey"
      :model="userMenuItems"
      popup
    >
      <template #start>
        <div class="gap-4 border-b border-divider p-4 flex-center">
          <UserAvatar
            :avatar="user?.avatar"
            class="overflow-hidden"
            size="large"
            :username="user?.username"
          />

          <div class="flex-1 space-y-1">
            <div class="font-medium">
              {{ user?.username }}
            </div>

            <div class="max-w-32 truncate text-wrap text-sm text-secondary">
              {{ user?.role_name }}
            </div>
          </div>
        </div>
      </template>
    </ProMenu>
  </div>
</template>
