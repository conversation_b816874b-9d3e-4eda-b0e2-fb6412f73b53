<script setup lang="ts">
import { ChevronsUpDownIcon } from 'lucide-vue-next'

import UserDropdownMenu from '~/components/layout/UserDropdownMenu.vue'

const store = useAdminNavMenuStore()
const { collapsed } = storeToRefs(store)
</script>

<template>
  <UserDropdownMenu :class="{ 'w-full': !collapsed }">
    <template #default="{ user }">
      <div
        class="inline-block aspect-square overflow-hidden rounded-xl border border-divider bg-content p-1 @wide-sider:block @wide-sider:aspect-auto @wide-sider:w-full @wide-sider:p-2"
      >
        <div class="gap-3 whitespace-nowrap flex-center">
          <UserAvatar
            :avatar="user?.avatar"
            class="overflow-hidden"
            :username="user?.username"
          />

          <div class="hidden min-w-0 flex-1 @wide-sider:block">
            <div class="justify-between gap-5 flex-center">
              <div class="flex-1 truncate text-sm">
                {{ user?.username }}
              </div>

              <ChevronsUpDownIcon
                class="shrink-0 opacity-50"
                :size="14"
              />
            </div>

            <div class="truncate text-sm text-secondary">
              {{ user?.email || user?.role_name }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </UserDropdownMenu>
</template>
