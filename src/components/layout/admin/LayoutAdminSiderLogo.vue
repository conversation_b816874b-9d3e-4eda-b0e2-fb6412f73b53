<script setup lang="ts">
const runtimeConfig = useRuntimeConfig()
const { siteName, siteLogo } = runtimeConfig.public

const store = useAdminNavMenuStore()
const { collapsed } = storeToRefs(store)
</script>

<template>
  <NuxtLink
    v-if="isFuYao()"
    class="flex-wrap justify-center gap-1 overflow-hidden flex-center"
    :class="{
      'px-admin-sider-mini': collapsed,
      'px-admin-layout': !collapsed,
    }"
    to="/"
  >
    <img
      v-if="siteLogo"
      alt="网站图标"
      class="select-none overflow-hidden object-cover"
      :class="{
        'size-12': collapsed,
        'size-8': !collapsed,
        // 'size-14': !collapsed,
      }"
      :src="siteLogo"
    >

    <div v-if="!collapsed">
      <div class="font-bold">
        广东网安SOP智能体平台
      </div>
      <!-- <div class="text-lg font-bold">
        扶摇 SOP
      </div>
      <div class="text-sm font-medium text-secondary">
        智能体平台
      </div> -->
    </div>
  </NuxtLink>

  <NuxtLink
    v-else
    class="justify-center gap-2 overflow-hidden flex-center"
    :class="{
      'px-admin-sider-mini': collapsed,
      'px-admin-layout': !collapsed,
    }"
    to="/"
  >
    <img
      v-if="siteLogo"
      alt="网站图标"
      class="select-none overflow-hidden object-cover"
      :class="{
        'size-8': collapsed,
        'size-6 lg:size-7': !collapsed,
      }"
      :src="siteLogo"
    >

    <span
      v-if="!collapsed"
      class="font-bold"
    >
      {{ siteName }}
    </span>
  </NuxtLink>
</template>
