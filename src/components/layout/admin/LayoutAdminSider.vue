<script setup lang="ts">
import { ArrowLeftToLineIcon, ArrowRightToLineIcon } from 'lucide-vue-next'

import { TooltipShowDelay } from '~/enums/common'

const { isDarkMode } = useTheme()

const store = useAdminNavMenuStore()
const { collapsed, navMenuItems, expandedKeys } = storeToRefs(store)
</script>

<template>
  <div class="flex h-full flex-col py-admin-layout">
    <LayoutAdminSiderLogo />

    <div class="relative min-h-0 flex-1">
      <div class="pointer-events-none absolute inset-x-0 top-0 z-50 h-10 bg-gradient-to-t from-transparent to-page dark:to-surface-800" />

      <ScrollPanel
        class="h-full"
        :dt="{
          bar: {
            background: isDarkMode ? '{surface.700}' : '{surface.200}',
          },
        }"
      >
        <div
          class="py-10"
          :class="{
            'px-admin-sider-mini': collapsed,
            'px-admin-layout': !collapsed,
          }"
        >
          <AdminSideMenuMini
            v-if="collapsed"
            :navMenuItems="navMenuItems"
          />
          <AdminSideMenu
            v-else
            :expandedKeys="expandedKeys"
            :navMenuItems="navMenuItems"
            @update:expandedKeys="expandedKeys = $event"
          />
        </div>
      </ScrollPanel>

      <div class="pointer-events-none absolute inset-x-0 bottom-0 z-50 h-10 bg-gradient-to-b from-transparent to-page dark:to-surface-800" />
    </div>

    <div class="px-admin-layout">
      <Divider
        layout="horizontal"
        :pt="{
          root: '!mt-0',
        }"
      />

      <div class="flex-wrap justify-center gap-2 pb-2 flex-center">
        <Button
          v-tooltip.top="{ value: collapsed ? '展开侧边栏' : '折叠侧边栏', showDelay: TooltipShowDelay.Fast }"
          severity="secondary"
          size="small"
          variant="outlined"
          @click="store.toggleCollapsed()"
        >
          <template #icon>
            <ArrowRightToLineIcon
              v-if="collapsed"
              :size="14"
            />
            <ArrowLeftToLineIcon
              v-else
              :size="14"
            />
          </template>
        </Button>

        <CommonSearchButton variant="outlined" />
        <CommonAppearanceButton variant="outlined" />
      </div>

      <div class="justify-center flex-center @container">
        <LayoutAdminSiderUser />
      </div>
    </div>
  </div>
</template>
