<script setup lang="ts">
const runtimeConfig = useRuntimeConfig()
const { siteName, siteLogo } = runtimeConfig.public
</script>

<template>
  <div class="relative z-50 h-admin-header px-admin-layout flex-center">
    <NuxtLink
      class="gap-2.5 flex-center"
      to="/"
    >
      <img
        v-if="siteLogo"
        alt="网站图标"
        class="size-8 select-none overflow-hidden object-cover"
        :src="siteLogo"
      >

      <span class="text-lg font-bold">
        {{ siteName }}
      </span>
    </NuxtLink>

    <div class="flex-1 px-10">
      <AdminTopMenu />
    </div>

    <div class="ml-auto gap-3 inline-flex-center">
      <div class="gap-1 flex-center">
        <CommonSearchButton variant="text" />
        <CommonAppearanceButton variant="text" />
      </div>

      <AdminTopUser />
    </div>
  </div>
</template>
