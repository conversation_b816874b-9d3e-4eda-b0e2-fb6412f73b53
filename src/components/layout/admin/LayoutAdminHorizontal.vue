<script setup lang="ts">
const store = useAdminNavMenuStore()
const { collapsed } = storeToRefs(store)

const route = useRoute()

const noContainerPadding = computed(() => route.meta.layoutConfig?.noContainerPadding ?? false)
</script>

<template>
  <div class="relative flex h-full min-w-admin-layout">
    <aside
      class="overflow-hidden"
      :class="{
        'basis-56 lg:basis-60 xl:basis-64': !collapsed,
        'basis-24': collapsed,
      }"
    >
      <LayoutAdminSider />
    </aside>

    <div class="flex min-w-0 flex-1 flex-col">
      <main
        class="min-h-0 flex-1"
        :class="{
          'p-0': noContainerPadding,
          'p-admin-layout !pl-0': !noContainerPadding,
        }"
      >
        <slot />
      </main>
    </div>

    <div
      v-if="false"
      class="pointer-events-none absolute right-0 top-0 -z-10 hidden h-40 w-full max-w-[75%] blur-[140px] supports-[filter:blur(140px)]:block"
      style="background: linear-gradient(97.62deg, rgb(0 71 225 / 22%) 2.27%, rgb(26 214 255 / 22%) 50.88%, rgb(0 220 130 / 22%) 98.48%);"
    />
  </div>
</template>
