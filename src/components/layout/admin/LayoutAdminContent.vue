<script setup lang="ts">
const route = useRoute()

const disableHeader = computed(() => route.meta.layoutConfig?.showHeader === false)
const noContentPadding = computed(() => route.meta.layoutConfig?.noContentPadding)
</script>

<template>
  <CardContainer>
    <LayoutAdminContentHeader v-if="!disableHeader" />

    <div
      class="min-h-0 flex-1 overflow-auto pt-0"
      :class="{ 'p-admin-layout-content': !noContentPadding }"
    >
      <slot />
    </div>
  </CardContainer>
</template>
