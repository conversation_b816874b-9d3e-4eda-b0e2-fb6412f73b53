<script setup lang="ts">
import { ChevronRightIcon, HomeIcon } from 'lucide-vue-next'

import { RouteKey } from '~/enums/route'

const route = useRoute()

const { navMenuItems } = useAdminNavMenuStore()

const breadcrumbs = computed<NavMenuItem[]>(() => {
  const currentPath = route.path
  const menuItems = navMenuItems

  const result: NavMenuItem[] = [
    { label: '首页', route: getRoutePath(RouteKey.首页), key: RouteKey.首页 },
  ]

  // 递归查找匹配的菜单项
  const findMatchingPath = (items: typeof menuItems, parents: string[] = []) => {
    for (const item of items) {
      const currentParents = [...parents, item.label]

      if (isRoutePathEqual(item.route, currentPath)) {
        // 找到匹配项，生成完整的面包屑
        result.push(
          ...currentParents.map((label) => ({ label, route: getRoutePath(item.key as RouteKey), key: item.key })),
        )

        return true
      }

      // 如果有子项，继续递归查找
      if (item.items?.length) {
        if (findMatchingPath(item.items, currentParents)) {
          return true
        }
      }
    }

    return false
  }

  findMatchingPath(menuItems)

  return result
})

// 添加获取同级菜单项的函数
const getSiblingMenuItems = (currentItem: NavMenuItem) => {
  const findSiblings = (items: typeof navMenuItems): NavMenuItem[] => {
    for (const item of items) {
      if (item.items?.length) {
        const siblings = findSiblings(item.items)

        if (siblings.length) {
          return siblings
        }
      }

      if (item.key === currentItem.key) {
        // 返回父级的所有子项
        return items
      }
    }

    return []
  }

  return findSiblings(navMenuItems)
}

const { popMenuRef, popMenu } = usePopMenu()

const activeItem = ref<NavMenuItem>()

const showMenu = (ev: MouseEvent, item: NavMenuItem) => {
  if (item.key !== RouteKey.首页) {
    activeItem.value = item
    popMenu.value?.toggle(ev)
  }
}

const siblingMenuItems = computed(() => {
  if (activeItem.value) {
    return getSiblingMenuItems(activeItem.value).map((item) => ({
      label: item.label,
      command: async () => await navigateTo(item.route),
    }))
  }

  return []
})
</script>

<template>
  <Breadcrumb
    v-if="breadcrumbs.length > 1"
    class="mb-3"
    :dt="{
      gap: '5px',
    }"
    :model="breadcrumbs"
    :pt="{
      root: { class: '!p-0' },
      item: { class: '!text-[13px]' },
    }"
  >
    <template #item="{ item, props }">
      <NuxtLink
        v-if="item.route"
        v-slot="{ href, navigate }"
        custom
        :to="item.route"
      >
        <a
          v-bind="props.action"
          :href="href"
          @click="navigate"
          @click.stop="showMenu($event, item)"
        >
          <HomeIcon
            v-if="item.key === RouteKey.首页"
            :size="14"
          />
          <span v-else>{{ item.label }}</span>
        </a>
      </NuxtLink>

      <a
        v-else
        :href="item.url"
        :target="item.target"
        v-bind="props.action"
      >
        <span>{{ item.label }}</span>
      </a>
    </template>

    <template #separator>
      <ChevronRightIcon :size="13" />
    </template>
  </Breadcrumb>

  <Menu
    :ref="popMenuRef"
    :model="siblingMenuItems"
    popup
  />
</template>
