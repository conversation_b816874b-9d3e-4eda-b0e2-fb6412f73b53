<script setup lang="ts">
const route = useRoute()

const noContainerPadding = computed(() => route.meta.layoutConfig?.noContainerPadding ?? false)
</script>

<template>
  <div class="flex h-full min-w-admin-layout">
    <div class="flex min-w-0 flex-1 flex-col">
      <header class="border-b border-divider bg-content">
        <LayoutAdminHeader />
      </header>

      <main
        class="min-h-0 flex-1"
        :class="{
          'p-0': noContainerPadding,
          'p-admin-layout': !noContainerPadding,
        }"
      >
        <slot />
      </main>
    </div>
  </div>
</template>
