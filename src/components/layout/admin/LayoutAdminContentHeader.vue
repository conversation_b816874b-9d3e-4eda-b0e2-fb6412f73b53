<script setup lang="ts">
const props = withDefaults(defineProps<{
  /** 页面内容标题 */
  title?: string
  /** 页面内容描述 */
  desc?: string
  /** 是否显示面包屑 */
  showBreadcrumb?: boolean
  /** 是否启用导航后退功能 */
  showBack?: boolean
  /** 内容区域包裹类名 */
  wrapperClass?: string
}>(), {
  showBack: undefined,
  showBreadcrumb: undefined,
})

const { pageTitle, pageDesc } = usePageInfo()

const appStore = useAppStore()
const { themeState } = storeToRefs(appStore)

const pageContentTitle = computed(() => {
  if (props.title) {
    return props.title
  }

  return pageTitle.value
})

const pageContentDesc = computed(() => {
  if (props.desc) {
    return props.desc
  }

  return pageDesc.value
})

const route = useRoute()

const enableBack = computed(() => props.showBack ?? route.meta.layoutConfig?.showHeaderBack)

defineSlots<{
  default: []
  titleRight: []
  title: () => VNode[]
  description: () => VNode[]
}>()
</script>

<template>
  <div
    class="p-admin-layout-content pb-0"
    :class="wrapperClass"
  >
    <div v-if="showBreadcrumb !== false && themeState.showBreadcrumb">
      <LayoutAdminBreadcrumb />
    </div>

    <div
      v-if="pageContentTitle"
      class="pb-5 md:pb-6"
    >
      <div class="flex-center">
        <ButtonRouteBack
          v-if="enableBack"
          class="mr-4 md:mr-5"
        />

        <slot name="title">
          <LayoutAdminContentTitle>
            {{ pageContentTitle }}
          </LayoutAdminContentTitle>
        </slot>

        <div
          v-if="$slots.titleRight"
          class="ml-auto"
        >
          <slot name="titleRight" />
        </div>
      </div>

      <slot name="description">
        <LayoutAdminContentDesc v-if="pageContentDesc">
          {{ pageContentDesc }}
        </LayoutAdminContentDesc>
      </slot>
    </div>
  </div>
</template>
