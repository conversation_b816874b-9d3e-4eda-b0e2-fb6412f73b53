<script setup lang="ts">
import { sanitizeUrl } from '@braintree/sanitize-url'
import { useQuery } from '@tanstack/vue-query'
import DOMPurify from 'dompurify'

import { queryKeys } from '~/enums/query-key'
import { threatLevelConfig } from '~/enums/security'

const props = defineProps<{
  /** 漏洞编号 ｜ CVE 编号 ｜ CNNVD 编号 */
  cveKey?: CveKey
}>()

const cveKey = toRef(props, 'cveKey')

const { data: vulDetail, isLoading: isVulDetailLoading } = useQuery({
  queryKey: queryKeys.security.vulLib.detail(cveKey),
  queryFn: () => {
    if (cveKey.value) {
      return SecurityService.getVulLibDetail(cveKey.value)
    }

    return null
  },
})

const tableData = computed(() => {
  if (vulDetail.value) {
    return [
      { label: '漏洞名称', value: vulDetail.value.name },
      { label: '责任厂商', value: vulDetail.value.vendor_name },
      { label: 'CNNVD 编号', value: vulDetail.value.cnnvd_code },
      { label: '风险等级', value: vulDetail.value.level ? threatLevelConfig[vulDetail.value.level].label : '-' },
      { label: 'CVE 编号', value: vulDetail.value.cve_code },
      { label: '漏洞类型', value: vulDetail.value.vul_type_name },
      { label: '披露时间', value: vulDetail.value.publish_time },
      { label: '最后更新', value: vulDetail.value.update_time },
    ]
  }

  return []
})
</script>

<template>
  <article>
    <div
      v-if="isVulDetailLoading"
      class="space-y-8"
    >
      <div
        v-for="i of 3"
        :key="i"
      >
        <h2 class="mb-4">
          <Skeleton
            height="2rem"
            width="8rem"
          />
        </h2>

        <div class="space-y-3 opacity-90">
          <Skeleton
            v-for="j of 4"
            :key="j"
            height="1.2rem"
            :width="j === 4 ? '25rem' : '40rem'"
          />
        </div>
      </div>
    </div>

    <template v-if="vulDetail && !isVulDetailLoading">
      <h2 class="mb-4 text-xl font-bold">
        基本信息
      </h2>

      <ProFormLabelValue
        :data="tableData"
        layout="two-column-table"
        readOnly
      />

      <template v-if="vulDetail.desc">
        <h2 class="mb-4 mt-8 text-xl font-bold">
          漏洞描述
        </h2>

        <pre
          class="whitespace-pre-wrap text-base"
          v-html="DOMPurify.sanitize(vulDetail.desc || '')"
        />
      </template>

      <template v-if="vulDetail.refer_url">
        <h2 class="mb-4 mt-8 text-xl font-bold">
          参考链接
        </h2>

        <CollapsibleContent>
          <div class="space-y-2">
            <div
              v-for="(item, idx) of parseKeyValueText(vulDetail.refer_url)"
              :key="idx"
              class="gap-2 flex-center"
            >
              <span class="whitespace-nowrap">{{ item.label }}: </span>

              <span class="font-medium">
                <a
                  v-if="item.type === 'link'"
                  class="break-all underline-offset-2 text-secondary hover:underline"
                  :href="item.value"
                  target="_blank"
                >
                  {{ item.value }}
                </a>
                <span v-else>{{ item.value }}</span>
              </span>
            </div>
          </div>
        </CollapsibleContent>
      </template>

      <template v-if="vulDetail.patch">
        <h2 class="mb-4 mt-8 text-xl font-bold">
          官方补丁
        </h2>

        <div class="space-y-2">
          <a
            class="underline-offset-2 text-secondary hover:underline"
            :href="sanitizeUrl(vulDetail.patch)"
            target="_blank"
          >
            {{ vulDetail.patch }}
          </a>
        </div>
      </template>
    </template>
  </article>
</template>
