<script setup lang="ts">
import { ChevronLeftIcon } from 'lucide-vue-next'

import { TooltipShowDelay } from '~/enums/common'

const router = useRouter()

const canGoBack = computed(() => window.history.length > 1)
</script>

<template>
  <Button
    v-if="canGoBack"
    v-tooltip.top="{ value: '返回上一页', showDelay: TooltipShowDelay.Fast }"
    :pt="{ root: { class: '!p-1' } }"
    severity="secondary"
    size="small"
    @click="router.back()"
  >
    <ChevronLeftIcon :size="18" />
  </Button>
</template>
