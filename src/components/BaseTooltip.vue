<script setup lang="ts">
import { autoUpdate, flip, offset, shift, useFloating } from '@floating-ui/vue'

// 定义 Props 接口
interface Props {
  /** tooltip 内容 */
  content?: string
  /** 显示位置 */
  placement?: 'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'
  /** 触发方式 */
  trigger?: 'hover' | 'click' | 'focus' | 'manual'
  /** 显示延迟(ms) */
  showDelay?: number
  /** 隐藏延迟(ms) */
  hideDelay?: number
  /** 是否显示箭头 */
  showArrow?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 最大宽度 */
  maxWidth?: string
  /** 自定义样式类 */
  tooltipClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  placement: 'top',
  trigger: 'hover',
  showDelay: 100,
  hideDelay: 100,
  showArrow: true,
  disabled: false,
  maxWidth: '200px',
  tooltipClass: '',
})

const emit = defineEmits<{
  show: []
  hide: []
}>()

// 响应式引用
const isOpen = ref(false)
const reference = ref<HTMLElement>()
const floating = ref<HTMLElement>()

// 使用 Floating UI
const { floatingStyles } = useFloating(
  reference,
  floating,
  {
    placement: toRef(props, 'placement'),
    strategy: ref('absolute'),
    whileElementsMounted: autoUpdate,
    middleware: [
      offset(8),
      flip(),
      shift({ padding: 5 }),
    ],
  },
)

// 定时器管理
let showTimer: NodeJS.Timeout | null = null
let hideTimer: NodeJS.Timeout | null = null

// 清除定时器
const clearTimers = () => {
  if (showTimer) {
    clearTimeout(showTimer)
    showTimer = null
  }

  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
}

// 显示 tooltip
const showTooltip = () => {
  if (props.disabled) {
    return
  }

  clearTimers()

  if (props.showDelay > 0) {
    showTimer = setTimeout(() => {
      isOpen.value = true
      emit('show')
    }, props.showDelay)
  }
  else {
    isOpen.value = true
    emit('show')
  }
}

// 隐藏 tooltip
const hideTooltip = () => {
  clearTimers()

  if (props.hideDelay > 0) {
    hideTimer = setTimeout(() => {
      isOpen.value = false
      emit('hide')
    }, props.hideDelay)
  }
  else {
    isOpen.value = false
    emit('hide')
  }
}

// 切换显示状态
const toggleTooltip = () => {
  if (isOpen.value) {
    hideTooltip()
  }
  else {
    showTooltip()
  }
}

// 手动控制显示/隐藏的方法
const show = () => {
  if (!props.disabled) {
    clearTimers()
    isOpen.value = true
    emit('show')
  }
}

const hide = () => {
  clearTimers()
  isOpen.value = false
  emit('hide')
}

// 暴露方法给父组件
defineExpose({
  show,
  hide,
  toggle: toggleTooltip,
  isOpen: readonly(isOpen),
})

// 事件处理器
const handleMouseEnter = () => {
  if (props.trigger === 'hover') {
    showTooltip()
  }
}

const handleMouseLeave = () => {
  if (props.trigger === 'hover') {
    hideTooltip()
  }
}

const handleClick = () => {
  if (props.trigger === 'click') {
    toggleTooltip()
  }
}

const handleFocus = () => {
  if (props.trigger === 'focus') {
    showTooltip()
  }
}

const handleBlur = () => {
  if (props.trigger === 'focus') {
    hideTooltip()
  }
}

// 点击外部关闭
const handleClickOutside = (event: Event) => {
  if (
    props.trigger === 'click'
    && isOpen.value
    && reference.value
    && floating.value
    && !reference.value.contains(event.target as Node)
    && !floating.value.contains(event.target as Node)
  ) {
    hideTooltip()
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isOpen.value) {
    hideTooltip()
  }
}

// 生命周期管理
onMounted(() => {
  if (props.trigger === 'click') {
    document.addEventListener('click', handleClickOutside)
  }

  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  clearTimers()
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeydown)
})

// 插槽类型定义
defineSlots<{
  default: () => VNode
  content?: () => VNode
}>()
</script>

<template>
  <div class="inline-flex-center">
    <!-- 触发器 -->
    <div
      ref="reference"
      class="inline-flex-center"
      @blur="handleBlur"
      @click="handleClick"
      @focus="handleFocus"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    >
      <slot />
    </div>

    <!-- Tooltip 内容 -->
    <Teleport to="body">
      <div
        v-if="isOpen"
        ref="floating"
        class="z-50 rounded-md bg-[var(--p-tooltip-background)] px-3 py-2 text-sm text-[var(--p-tooltip-color)] shadow-lg shadow-[var(--p-tooltip-shadow)]"
        :class="tooltipClass"
        :style="{
          ...floatingStyles,
          maxWidth: maxWidth,
        }"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
      >
        <!-- 箭头 -->
        <div
          v-if="showArrow"
          class="absolute size-2 rotate-45 bg-[var(--p-tooltip-background)]"
          :class="{
            'bottom-[-4px] left-1/2 -translate-x-1/2': placement.startsWith('top'),
            'left-1/2 top-[-4px] -translate-x-1/2': placement.startsWith('bottom'),
            'right-[-4px] top-1/2 -translate-y-1/2': placement.startsWith('left'),
            'left-[-4px] top-1/2 -translate-y-1/2': placement.startsWith('right'),
          }"
        />

        <!-- 内容 -->
        <div class="prose-sm relative z-10">
          <slot name="content">
            {{ content }}
          </slot>
        </div>
      </div>
    </Teleport>
  </div>
</template>
