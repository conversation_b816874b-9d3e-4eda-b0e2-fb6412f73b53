<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { ProValueType } from '~/enums/pro'
import { queryKeys } from '~/enums/query-key'
import { UserService } from '~/services-tw/org'
import type { TW_User } from '~/types-tw/user'

const props = defineProps<{
  userId?: TW_User['id']
}>()

const { data: userDetail } = useQuery({
  queryKey: queryKeys.user.detail(props.userId),
  queryFn: () => {
    if (props.userId) {
      return UserService.getUser(props.userId)
    }

    return null
  },
  enabled: !!props.userId,
})

const detailData = computed<ProFormLabelValueItem[]>(() => {
  return [
    {
      label: '姓名',
      value: userDetail.value?.name,
    },
    {
      label: '职位',
      value: userDetail.value?.appointment,
    },
    {
      label: '邮箱',
      value: userDetail.value?.email,
    },
    {
      label: '手机号',
      value: userDetail.value?.phone,
    },
    {
      label: '角色',
      value: userDetail.value?.roles?.map((r) => r.name).join(','),
    },
    {
      label: '入职时间',
      value: userDetail.value?.created_at,
      valueType: ProValueType.Date,
    },
  ]
})
</script>

<template>
  <div v-if="userDetail">
    <ProFormLabelValue
      :data="detailData"
      layout="vertical"
      readOnly
    />
  </div>
</template>
