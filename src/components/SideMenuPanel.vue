<script setup lang="ts">
import { ChevronDownIcon, ChevronRightIcon } from 'lucide-vue-next'
import type { MenuItem as PrimeMenuItem } from 'primevue/menuitem'

interface SideMenuPanelProps {
  /** 菜单项数据 */
  menuItems: PrimeMenuItem[]

  /** 展开的菜单键值 */
  expandedKeys?: Record<string, boolean>

  /** 当前选中的菜单项 */
  selectedKey?: string

  /** 是否收起 */
  collapsed?: boolean
}

const props = withDefaults(defineProps<SideMenuPanelProps>(), {
  collapsed: false,
})

const emit = defineEmits<{
  /** 展开键值变化 */
  'update:expandedKeys': [keys: Record<string, boolean>]
  /** 选择菜单项 */
  select: [item: PrimeMenuItem]
}>()

const internalExpandedKeys = ref(props.expandedKeys || {})

watch(() => props.expandedKeys, (newVal) => {
  if (newVal) {
    internalExpandedKeys.value = newVal
  }
}, { deep: true })

const handleExpandedKeysUpdate = (keys: Record<string, boolean>) => {
  internalExpandedKeys.value = keys
  emit('update:expandedKeys', keys)
}

const handleItemSelect = (item: PrimeMenuItem) => {
  emit('select', item)
}
</script>

<template>
  <div class="h-full">
    <PanelMenu
      :expandedKeys="internalExpandedKeys"
      :model="menuItems"
      :pt="{
        root: '!gap-1 !text-[13px]',
        panel: '!p-0 !border-none !bg-transparent',
        rootList: '!space-y-1',
      }"
      @update:expandedKeys="handleExpandedKeysUpdate"
    >
      <template #item="{ item }: { item: PrimeMenuItem }">
        <div
          class="flex cursor-pointer items-center rounded-md px-2 py-1.5"
          :class="{
            'bg-emphasis font-medium': selectedKey === item.key && !item.items,
          }"
          @click="handleItemSelect(item)"
        >
          <Component
            :is="item.data?.icon"
            v-if="item.data?.icon"
            class="mr-2"
            :size="16"
          />

          <template v-if="!collapsed">
            <span class="flex-1">{{ item.label }}</span>

            <span
              v-if="item.items && item.items.length"
              class="ml-auto"
            >
              <Component
                :is="internalExpandedKeys[item.key as string] ? ChevronDownIcon : ChevronRightIcon"
                :size="14"
              />
            </span>
          </template>
        </div>
      </template>
    </PanelMenu>
  </div>
</template>
