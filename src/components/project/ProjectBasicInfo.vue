<script setup lang="ts">
import { Button, Tag } from 'primevue'

import TextLink from '~/components/TextLink.vue'
import { ProjectStatusConfig, ProjectTypeConfig } from '~/constants-tw/project'
import { ProValueType } from '~/enums/pro'
import type { TW_Project } from '~/types-tw/project'

const props = defineProps<{
  projectId?: TW_Project['id']
}>()

const { data: projectDetail, refetch: refetchProjectDetail } = useQueryProjectDetail(props.projectId)

const { $openFlowDetailDialog, $openFlowSelectionDialog } = useNuxtApp()

const ContractLink = () => {
  const contract = projectDetail.value?.contract

  if (contract) {
    return h(TextLink, { class: 'line-clamp-3' }, () => contract.name)
  }

  return '-'
}

const ProjectStatusTag = () => {
  const status = projectDetail.value?.status
  const label = status !== undefined ? ProjectStatusConfig[status].label : undefined
  const severity = status !== undefined ? ProjectStatusConfig[status].severity : 'info'

  return h(Tag, {
    value: label,
    severity,
  })
}

const AutoFlowLink = () => {
  const projectId = projectDetail.value?.id
  const flowDetail = projectDetail.value?.flow_detail
  const flowName = flowDetail?.name

  if (flowName) {
    return h(TextLink, {
      onClick: () => {
        if (flowDetail) {
          $openFlowDetailDialog(flowDetail)
        }
      },
    }, () => flowName)
  }

  return h(Button, {
    label: '选择工作流',
    variant: 'outlined',
    size: 'small',
    onClick: () => {
      if (projectId) {
        $openFlowSelectionDialog(
          { id: projectId },
          {
            onDone: () => {
              refetchProjectDetail()
            },
          },
        )
      }
    },
  })
}

const basicInfoData = computed<ProFormLabelValueItem[]>(() => {
  const progress = projectDetail.value?.statistics?.task?.count
    ? `${Math.round(((projectDetail.value?.statistics?.task?.close ?? 0) / projectDetail.value?.statistics?.task?.count) * 100)}%`
    : '-'

  return [
    { label: '项目编号', value: projectDetail.value?.number },
    { label: '项目名称', value: projectDetail.value?.name, valueType: ProValueType.Text },
    { label: '关联合同', value: ContractLink },
    { label: '项目进度', value: progress },
    { label: '项目类型', value: projectDetail.value?.type?.map((type) => ProjectTypeConfig[type].label).join(',') },
    { label: '项目状态', value: ProjectStatusTag },
    { label: '预计周期', value: projectDetail.value?.start_date.join(' - ') },
    { label: '关联工作流', value: AutoFlowLink },
  ]
})
</script>

<template>
  <div>
    <ProFormLabelValue
      :data="basicInfoData"
      layout="horizontal"
      readOnly
    />
  </div>
</template>
