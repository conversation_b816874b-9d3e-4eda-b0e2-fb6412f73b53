<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/constants-tw/query-key'
import { UserService } from '~/services-tw/org'
import type { TW_Project } from '~/types-tw/project'

const props = defineProps<{
  projectId?: TW_Project['id']
}>()

const { data: projectDetail } = useQueryProjectDetail(props.projectId)

const clientId = computed(() => projectDetail.value?.customer_id)

const { data: clientInfo } = useQuery({
  queryKey: queryKeys.client.detail(clientId),
  queryFn: () => {
    if (clientId.value) {
      return UserService.getClientInfo(clientId.value)
    }

    return null
  },
})

const data = computed(() => [
  { label: '客户名', value: clientInfo.value?.name },
  { label: '客户经理', value: clientInfo.value?.user?.name },
  { label: '联系方式', value: clientInfo.value?.work_phone },
])
</script>

<template>
  <div v-if="clientInfo">
    <ProFormLabelValue
      :data="data"
      layout="vertical"
    />
  </div>

  <div v-else>
    暂无客户信息
  </div>
</template>
