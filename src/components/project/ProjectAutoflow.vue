<script setup lang="ts">
defineProps<{
  completedTasks: number
  totalTasks: number
  currentTask: string
  delayedTasks: number
}>()
</script>

<template>
  <div>
    <div class="mb-6">
      <div class="mb-2 flex items-center justify-between">
        <span class="text-gray-700">完成任务数：</span>
        <span class="font-medium">{{ completedTasks }}/{{ totalTasks }}</span>
      </div>
    </div>

    <div class="mb-6">
      <div class="mb-2 flex items-center justify-between">
        <span class="text-gray-700">当前任务：</span>
      </div>
      <div class="rounded-lg bg-blue-50 p-3">
        <span class="text-blue-700">{{ currentTask }}</span>
      </div>
    </div>

    <div class="mb-4">
      <div class="mb-2 flex items-center justify-between">
        <span class="text-gray-700">延期任务：</span>
        <span class="rounded-full bg-red-100 px-2 py-1 text-sm text-red-700">{{ delayedTasks }}</span>
      </div>
    </div>

    <div class="flex justify-end">
      <Button
        label="查看详情"
        size="small"
        variant="outlined"
      />
    </div>
  </div>
</template>
