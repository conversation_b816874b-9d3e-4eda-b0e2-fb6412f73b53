<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { number } from 'valibot'

import { queryKeys } from '~/constants-tw/query-key'
import { ProjectService } from '~/services-tw/project'

const projectId = useRouteParam({
  name: 'projectId',
  schema: number(),
})

const { data: teamMembers } = useQuery({
  queryKey: queryKeys.project.team(projectId),
  queryFn: () => {
    if (projectId.value) {
      return ProjectService.getProjectTeam(projectId.value)
    }

    return []
  },
  select: (data) => data.map((item) => item.user),
})
</script>

<template>
  <div class="p-card-container">
    <div class="space-y-0.5">
      <div
        v-for="member of teamMembers"
        :key="member.id"
        class="gap-5 rounded-lg p-2 flex-center hover:bg-emphasis"
      >
        <UserAvatar
          :avatar="member.avatar"
          class="shrink-0"
          size="large"
          :username="member.name"
        />

        <div class="flex-1">
          <div class="font-medium">
            {{ member.name }}
          </div>

          <div
            v-if="member.appointment"
            class="text-sm text-surface-500"
          >
            {{ member.appointment }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
