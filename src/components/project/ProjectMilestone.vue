<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/constants-tw/query-key'
import { ProjectService } from '~/services-tw/project'
import type { TW_Project, TW_ProjectMilestone } from '~/types-tw/project'

const props = defineProps<{
  projectId?: TW_Project['id']
}>()

const { data: milestones, isLoading } = useQuery({
  queryKey: queryKeys.project.milestone(props.projectId),
  queryFn: () => {
    if (props.projectId) {
      return ProjectService.getProjectMilestone(props.projectId)
    }

    return null
  },
  enabled: computed(() => !!props.projectId),
})
</script>

<template>
  <div
    v-if="!isLoading && milestones"
    class="w-full"
  >
    <Timeline
      class="w-full"
      :value="milestones"
    >
      <template #opposite="{ item }: { item: TW_ProjectMilestone }">
        <div class="text-surface-600">
          <TimeDisplay
            :time="item.created_at"
          />
        </div>
      </template>

      <template #content="{ item }: { item: TW_ProjectMilestone }">
        <div class="flex flex-col gap-1">
          <div>
            <Button
              class="!p-0"
              :label="item.name"
              severity="info"
              variant="link"
            />
          </div>

          <div>
            <UserSelectorChip
              :value="{
                id: item.created_users.id,
                username: item.created_users.name,
                avatar: item.created_users.avatar,
              }"
            />
          </div>

          <div class="gap-2 flex-center">
            <span>进度：</span>
            <div class="flex-1">
              <ProgressBar
                style="height: 0.5rem"
                :value="Math.round((item.statistics.sum_day_consumesume / item.statistics.task.count) * 100)"
              />
            </div>
          </div>
        </div>
      </template>
    </Timeline>

    <div
      v-if="milestones.length === 0"
      class="flex items-center justify-center p-4"
    >
      暂无项目阶段数据
    </div>
  </div>
  <div
    v-else
    class="flex w-full items-center justify-center p-4"
  >
    <i class="pi pi-spin pi-spinner mr-2" />
    <span>加载中...</span>
  </div>
</template>
