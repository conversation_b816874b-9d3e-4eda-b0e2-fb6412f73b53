<script setup lang="ts">
import { number } from 'valibot'

const projectId = useRouteParam({ name: 'projectId', schema: number() })

const mockData = {
  projectStatus: {
    totalProgress: 68,
    currentStage: '需求分析',
    stageProgress: 48,
  },
  workflowStatus: {
    completedTasks: 8,
    totalTasks: 15,
    currentTask: '需求分析',
    delayedTasks: 1,
  },
  projectTeam: {
    teamMembers: [
      {
        id: '1',
        name: '李四',
        avatar: '',
        role: '项目经理',
      },
      {
        id: '2',
        name: '李四',
        avatar: '',
        role: '项目经理',
      },
      {
        id: '3',
        name: '李四',
        avatar: '',
        role: '项目经理',
      },
    ],
  },
  customerInfo: {
    customerName: 'XX医疗集团',
    industry: '医疗健康',
    contactPerson: '张三',
    position: 'IT总监',
    contactMethod: '138-00113-8000',
    email: 'z<PERSON><PERSON>@qq.com',
  },
}
</script>

<template>
  <div
    class="grid grid-cols-12 gap-admin-layout"
  >
    <CardContainer
      class="col-span-12 p-card-container"
      title="项目信息"
    >
      <ProjectBasicInfo :projectId="projectId" />
    </CardContainer>

    <CardContainer
      class="col-span-12 p-card-container md:col-span-6"
      title="项目阶段"
    >
      <ProjectMilestone :projectId="projectId" />
    </CardContainer>

    <CardContainer
      class="col-span-12 p-card-container md:col-span-6"
      title="工作流状态"
    >
      <ProjectAutoflow v-bind="mockData.workflowStatus" />
    </CardContainer>

    <CardContainer class="col-span-12 md:col-span-6">
      <div class="flex flex-col">
        <CardContainerTitle class="p-card-container !pb-0">
          团队成员
        </CardContainerTitle>

        <div class="flex-1 overflow-y-auto">
          <ProjectTeam />
        </div>
      </div>
    </CardContainer>

    <CardContainer
      class="col-span-12 p-card-container md:col-span-6"
      title="客户信息"
    >
      <ProjectClientInfo :projectId="projectId" />
    </CardContainer>
  </div>
</template>
