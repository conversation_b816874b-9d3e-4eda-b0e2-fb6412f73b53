<script setup lang="ts">
const appStore = useAppStore()
const { isAppearanceDialogVisible } = storeToRefs(appStore)

const handleClose = () => {
  appStore.closeAppearanceDialog()
}
</script>

<template>
  <Dialog
    v-model:visible="isAppearanceDialogVisible"
    dismissableMask
    :draggable="false"
    header="个性化设置"
    modal
    @afterHide="handleClose()"
  >
    <AppearanceConfig />
  </Dialog>
</template>
