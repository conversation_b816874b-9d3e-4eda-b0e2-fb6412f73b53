<script setup lang="ts">
import { SearchIcon } from 'lucide-vue-next'

import { TooltipShowDelay } from '~/enums/common'

const appStore = useAppStore()

defineProps({
  variant: {
    type: String,
    default: 'outlined',
    validator: (value: string) => ['outlined', 'text'].includes(value),
  },
})
</script>

<template>
  <Button
    v-tooltip.top="{ value: `搜索 ${getKbd('K')}`, showDelay: TooltipShowDelay.Fast }"
    severity="secondary"
    size="small"
    :variant="variant"
    @click="appStore.toggleSearchPanel()"
  >
    <template #icon>
      <SearchIcon :size="14" />
    </template>
  </Button>
</template>
