<script setup lang="ts">
import { PaletteIcon } from 'lucide-vue-next'

import { TooltipShowDelay } from '~/enums/common'

const appStore = useAppStore()

interface Props {
  variant?: 'outlined' | 'text'
}

defineProps<Props>()
</script>

<template>
  <Button
    v-tooltip.top="{ value: '个性设置', showDelay: TooltipShowDelay.Fast }"
    severity="secondary"
    size="small"
    :variant="variant"
    @click="appStore.openAppearanceDialog()"
  >
    <template #icon>
      <PaletteIcon :size="14" />
    </template>
  </Button>
</template>
