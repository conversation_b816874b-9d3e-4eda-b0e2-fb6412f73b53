<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { useQuery } from '@tanstack/vue-query'
import type { ApexOptions } from 'apexcharts'

import { queryKeys } from '~/enums/query-key'

const { data: ticketLogStats } = useQuery({
  queryKey: queryKeys.stats.ticket.log,
  queryFn: () => {
    return StatsService.getTicketLog()
  },
})

const { isDarkMode } = useTheme()

const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  series: [{
    name: '新增工单数',
    data: ticketLogStats.value?.map((it) => it.count) || [],
  }],
  chart: {
    type: 'area',
    height: 300,
    toolbar: {
      show: false,
    },
    zoom: {
      enabled: true,
      type: 'x',
      allowMouseWheelZoom: true,
      autoScaleYaxis: true,
      zoomedArea: {
        fill: {
          color: $dt('blue.300').value,
          opacity: 0.5,
        },
        stroke: {
          color: $dt('blue.400').value,
          opacity: 0.5,
          width: 1,
        },
      },
    },
  },
  plotOptions: {
    bar: {
      horizontal: false,
      columnWidth: '55%',
    },
  },
  dataLabels: {
    enabled: false,
  },
  xaxis: {
    labels: {
      rotate: -10,
      rotateAlways: true,
      hideOverlappingLabels: false,
    },
    categories: ticketLogStats.value?.map((it) => it.date) || [],
  },
  colors: [$dt('blue.500').value],
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div>
    <div class="pb-2">
      <StatsCardHeader title="工单新增记录" />
    </div>

    <div ref="chartContainer" />
  </div>
</template>
