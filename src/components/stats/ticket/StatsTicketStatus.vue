<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import type { ApexOptions } from 'apexcharts'

import { queryKeys } from '~/enums/query-key'
import { ticketStatusConfig } from '~/enums/ticket'

const { data: ticketStatusStats } = useQuery({
  queryKey: queryKeys.stats.ticket.status,
  queryFn: () => {
    return StatsService.getTicketStatusStats()
  },
})

const { isDarkMode } = useTheme()

const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  series: ticketStatusStats.value?.map((it) => it.count) || [],
  labels: ticketStatusStats.value?.map((it) => ticketStatusConfig[it.status].label) || [],
  colors: ticketStatusStats.value?.map((it) => ticketStatusConfig[it.status].bgColor) || [],
  chart: {
    type: 'donut',
    height: 200,
  },
  dataLabels: {
    enabled: true,
  },
  plotOptions: {
    pie: {
      donut: {
        size: '70%',
        labels: {
          show: true,
          total: {
            label: '总数',
            show: true,
            showAlways: true,
          },
        },
      },
    },
  },
  legend: {
    position: 'bottom',
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div>
    <div class="pb-4">
      <StatsCardHeader title="工单状态分布" />
    </div>

    <div ref="chartContainer" />
  </div>
</template>
