<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { useQuery } from '@tanstack/vue-query'
import { BookTextIcon, ServerIcon, ShieldAlertIcon } from 'lucide-vue-next'

import { queryKeys } from '~/enums/query-key'
import { RouteKey } from '~/enums/route'
import { TicketType } from '~/enums/ticket'

const { data: vulStatusStats } = useQuery({
  queryKey: queryKeys.stats.ticket.count,
  queryFn: () => StatsService.getTicketCountStats(),
})

const stats = computed(() => [
  {
    label: '工单总数',
    value: `${vulStatusStats.value?.work_order ?? 0} 项`,
    colorFront: $dt('slate.500').value,
    colorBg: $dt('slate.100').value,
    icon: BookTextIcon,
    command: async () => {
      await navigateTo(getRoutePath(RouteKey.工单列表))
    },
  },
  {
    label: '基线检查相关',
    value: `${vulStatusStats.value?.baseline ?? 0} 项`,
    colorFront: $dt('blue.500').value,
    colorBg: $dt('blue.100').value,
    icon: ServerIcon,
    command: async () => {
      const path = getRoutePath(RouteKey.工单列表)
      await navigateTo(`${path}?type=${TicketType.Compliance}`)
    },
  },
  {
    label: '漏洞相关',
    value: `${vulStatusStats.value?.vul ?? 0} 项`,
    colorFront: $dt('red.500').value,
    colorBg: $dt('red.100').value,
    icon: ShieldAlertIcon,
    command: async () => {
      const path = getRoutePath(RouteKey.工单列表)
      await navigateTo(`${path}?type=${TicketType.Vul}`)
    },
  },
])
</script>

<template>
  <div class="grid grid-cols-3 gap-4">
    <Card
      v-for="item of stats"
      :key="item.label"
      class="flex-1 border border-divider"
    >
      <template #content>
        <div
          class="flex justify-between gap-8"
          :class="{
            'relative z-10 cursor-pointer after:absolute after:-inset-3 after:-z-10 after:rounded-lg hover:after:bg-emphasis': typeof item.command === 'function',
          }"
          @click="item.command?.()"
        >
          <div class="flex flex-col gap-2">
            <span class="text-secondary">
              {{ item.label }}
            </span>
            <span class="text-lg">
              <span class="font-semibold">
                <NumericDisplay>
                  {{ item.value }}
                </NumericDisplay>
              </span>
            </span>
          </div>

          <span
            v-if="item.icon"
            class="size-10 shrink-0 justify-center rounded-full p-1 text-center inline-flex-center"
            :style="{
              backgroundColor: item.colorBg,
              color: item.colorFront,
            }"
          >
            <Component
              :is="item.icon"
              :size="20"
            />
          </span>
        </div>
      </template>
    </Card>
  </div>
</template>
