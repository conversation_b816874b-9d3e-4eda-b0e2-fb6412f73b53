<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import type { ApexOptions } from 'apexcharts'

import { queryKeys } from '~/enums/query-key'
import { ticketGradeConfig } from '~/enums/ticket'

const { data: ticketLevelStats } = useQuery({
  queryKey: queryKeys.stats.ticket.grade,
  queryFn: () => {
    return StatsService.getTicketGradeStats()
  },
})

const { isDarkMode } = useTheme()

const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  series: ticketLevelStats.value?.map((it) => it.count) || [],
  labels: ticketLevelStats.value?.map((it) => ticketGradeConfig[it.grade].label) || [],
  colors: ticketLevelStats.value?.map((it) => ticketGradeConfig[it.grade].bgColor) || [],
  chart: {
    type: 'donut',
    height: 200,
  },
  dataLabels: {
    enabled: true,
  },
  plotOptions: {
    pie: {
      donut: {
        size: '70%',
        labels: {
          show: true,
          total: {
            label: '总数',
            show: true,
            showAlways: true,
          },
        },
      },
    },
  },
  legend: {
    position: 'bottom',
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div>
    <div class="pb-4">
      <StatsCardHeader title="工单重要等级分布" />
    </div>

    <div ref="chartContainer" />
  </div>
</template>
