<script setup lang="ts">
import { ShieldQuestionIcon } from 'lucide-vue-next'

import type { AssetType, TopologyNodeType } from '~/enums/asset'
import { assetDetailConfig } from '~/enums/asset'

interface Props {
  value: AssetType | TopologyNodeType | string
  title: string
  count: number
}

defineProps<Props>()

const getIcon = (type: Props['value']) => {
  if (Object.hasOwn(assetDetailConfig, type)) {
    return assetDetailConfig[type as AssetType].icon
  }

  return ShieldQuestionIcon
}
</script>

<template>
  <CardContainer class="p-card-container">
    <div class="flex flex-col gap-3 rounded-lg">
      <div class="justify-start gap-4 flex-center">
        <span class="justify-center rounded-lg bg-emphasis p-2 flex-center">
          <Component
            :is="getIcon(value)"
            :size="18"
          />
        </span>

        <span class="text-xl font-medium">{{ title }}</span>
      </div>

      <div class="px-2 text-3xl font-bold">
        <NumericDisplay>
          {{ count }}
        </NumericDisplay>
      </div>
    </div>
  </CardContainer>
</template>
