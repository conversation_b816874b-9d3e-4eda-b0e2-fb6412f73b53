<script setup lang="ts">
defineProps<{
  title?: string
}>()

defineSlots<{
  content?: VNode[]
  title?: VNode[]
  right?: VNode[]
}>()
</script>

<template>
  <div class="gap-2 flex-center">
    <slot name="content">
      <h3
        v-if="title || $slots.title"
        class="text-lg font-medium"
      >
        <slot name="title">
          {{ title }}
        </slot>
      </h3>

      <div class="ml-auto">
        <slot name="right" />
      </div>
    </slot>
  </div>
</template>
