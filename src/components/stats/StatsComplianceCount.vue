<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { useQuery } from '@tanstack/vue-query'
import { ListCheckIcon, PercentIcon, ServerIcon, ShieldAlertIcon, ShieldCheckIcon } from 'lucide-vue-next'

import { queryKeys } from '~/enums/query-key'

const { data: complianceCheckCountStats } = useQuery({
  queryKey: queryKeys.stats.compliance.count,
  queryFn: () => StatsService.getComplianceCheckCountStats(),
})

const stats = computed(() => [
  {
    label: '合规检测项次数',
    value: `${complianceCheckCountStats.value?.baseline ?? 0} 次`,
    colorFront: $dt('emerald.500').variable,
    colorBg: $dt('emerald.100').variable,
    icon: ListCheckIcon,
  },
  {
    label: '检测的服务器',
    value: `${complianceCheckCountStats.value?.server ?? 0} 台`,
    colorFront: $dt('blue.500').variable,
    colorBg: $dt('blue.100').variable,
    icon: ServerIcon,
  },
  {
    label: '通过检查',
    value: `${complianceCheckCountStats.value?.pass_count ?? 0} 项`,
    colorFront: $dt('emerald.500').variable,
    colorBg: $dt('emerald.100').variable,
    icon: ShieldCheckIcon,
  },
  {
    label: '未通过检查',
    value: `${complianceCheckCountStats.value?.not_pass_count ?? 0} 项`,
    colorFront: $dt('red.500').variable,
    colorBg: $dt('red.100').variable,
    icon: ShieldAlertIcon,
  },
  {
    label: '基线通过率',
    value: `${complianceCheckCountStats.value?.pass_rate ?? 0}%`,
    colorFront: $dt('blue.500').variable,
    colorBg: $dt('blue.100').variable,
    icon: PercentIcon,
  },
])
</script>

<template>
  <div class="@container">
    <div class="grid grid-cols-3 gap-admin-layout @lg:grid-cols-3 @2xl:grid-cols-4 @3xl:grid-cols-5">
      <Card
        v-for="item of stats"
        :key="item.label"
        class="flex-1 overflow-hidden border border-divider"
      >
        <template #content>
          <div class="flex justify-between gap-5">
            <div class="flex flex-col gap-2">
              <span class="text-secondary">
                {{ item.label }}
              </span>
              <span class="text-lg">
                <span class="font-semibold">
                  <NumericDisplay>
                    {{ item.value }}
                  </NumericDisplay>
                </span>
              </span>
            </div>

            <span
              v-if="item.icon"
              class="size-10 shrink-0 justify-center rounded-full p-1 text-center inline-flex-center"
              :style="{
                backgroundColor: item.colorBg,
                color: item.colorFront,
              }"
            >
              <Component
                :is="item.icon"
                :size="20"
              />
            </span>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>
