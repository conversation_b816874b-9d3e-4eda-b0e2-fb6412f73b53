<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import type { ApexOptions } from 'apexcharts'

import { queryKeys } from '~/enums/query-key'
import { getThreatLevelLabel, threatLevelConfig } from '~/enums/security'

const { data: vulLevelStats } = useQuery({
  queryKey: queryKeys.stats.vul.level,
  queryFn: () => {
    return StatsService.getVulLevelStats()
  },
})

const { themeMode, textColor } = useTheme()

const chartOptions = computed<ApexOptions>(() => {
  type Data = { series: number[], labels: string[], colors: string[] }

  const defaultData: Data = { series: [], labels: [], colors: [] }

  const { series, labels, colors } = vulLevelStats.value?.list.reduce<Data>((acc, it) => {
    acc.series.push(it.count)
    acc.labels.push(getThreatLevelLabel(it.level))
    acc.colors.push(it.level ? getTagColor(threatLevelConfig[it.level].severity, themeMode.value) : 'transparent')

    return acc
  }, defaultData) || defaultData

  return {
    theme: {
      mode: themeMode.value,
    },
    series,
    labels,
    colors,
    chart: {
      type: 'donut',
      height: 200,
      background: 'transparent',
    },
    dataLabels: {
      enabled: true,
    },
    tooltip: {
      enabled: false,
    },
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
          labels: {
            show: true,
            total: {
              label: '总数',
              show: true,
            },
          },
        },
      },
    },
    states: {
      hover: {
        filter: {
          type: 'none',
        },
      },
    },
    legend: {
      position: 'bottom',
      labels: {
        colors: textColor.value,
      },
      markers: {
        strokeWidth: 1,
      },
    },
  }
})

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div>
    <div class="pb-8">
      <StatsCardHeader title="本系统漏洞等级分布" />
    </div>

    <div ref="chartContainer" />
  </div>
</template>
