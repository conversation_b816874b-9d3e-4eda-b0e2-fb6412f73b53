<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { useQuery } from '@tanstack/vue-query'
import { BugIcon, ClockIcon, ServerIcon, ShieldAlertIcon, ShieldCheckIcon } from 'lucide-vue-next'

import { queryKeys } from '~/enums/query-key'
import { RouteKey } from '~/enums/route'
import { getThreatLevelLabel, getThreatLevelSeverity } from '~/enums/security'

const props = withDefaults(defineProps<{
  clickable?: boolean
}>(), {
  clickable: true,
})

const { data: vulStatusStats } = useQuery({
  queryKey: queryKeys.stats.vul.status,
  queryFn: () => StatsService.getVulStatusStats(),
})

const { data: vulLevelStats } = useQuery({
  queryKey: queryKeys.stats.vul.level,
  queryFn: () => {
    return StatsService.getVulLevelStats()
  },
})

const stats = computed(() => [
  {
    label: '漏洞总数',
    value: `${vulStatusStats.value?.vul ?? 0} 项`,
    colorFront: $dt('red.500').variable,
    colorBg: $dt('red.100').variable,
    icon: BugIcon,
    command: async () => {
      if (props.clickable) {
        await navigateTo(getRoutePath(RouteKey.漏洞管理))
      }
    },
  },
  {
    label: '漏洞关联服务器',
    value: `${vulStatusStats.value?.server ?? 0} 台`,
    colorFront: $dt('blue.500').variable,
    colorBg: $dt('blue.100').variable,
    icon: ServerIcon,
  },
])

const counts = computed(() => [
  {
    label: '已修复',
    value: vulStatusStats.value?.recovered ?? 0,
    colorFront: $dt('emerald.500').variable,
    colorBg: $dt('emerald.100').variable,
    icon: ShieldCheckIcon,
  },
  {
    label: '修复中',
    value: vulStatusStats.value?.processing ?? 0,
    colorFront: $dt('amber.500').variable,
    colorBg: $dt('amber.100').variable,
    icon: ClockIcon,
  },
  {
    label: '待修复',
    value: vulStatusStats.value?.wontfix ?? 0,
    colorFront: $dt('red.500').variable,
    colorBg: $dt('red.100').variable,
    icon: ShieldAlertIcon,
  },
])

const isVulTotal = (item: typeof stats.value[number]) => {
  return item.label === '漏洞总数'
}
</script>

<template>
  <div class="@container">
    <div class="grid grid-cols-12 gap-admin-layout">
      <Card
        v-for="item of stats"
        :key="item.label"
        class="overflow-hidden border border-divider"
        :class="{
          'col-span-12 @3xl:col-span-5': isVulTotal(item),
          'col-span-6 @3xl:col-span-3': !isVulTotal(item),
        }"
      >
        <template #content>
          <div
            :class="{
              'relative z-10 cursor-pointer after:absolute after:-inset-3 after:-z-10 after:rounded-lg hover:after:bg-emphasis': typeof item.command === 'function' && clickable,
            }"
          >
            <div
              class="flex justify-between gap-8"
              @click="item.command?.()"
            >
              <div class="flex flex-col gap-2">
                <div class="text-secondary">
                  {{ item.label }}
                </div>

                <div class="text-lg">
                  <span class="font-semibold">
                    <NumericDisplay>
                      {{ item.value }}
                    </NumericDisplay>
                  </span>
                </div>
              </div>

              <span
                v-if="item.icon"
                class="size-10 shrink-0 justify-center rounded-full p-1 text-center inline-flex-center"
                :style="{
                  backgroundColor: item.colorBg,
                  color: item.colorFront,
                }"
              >
                <Component
                  :is="item.icon"
                  :size="20"
                />
              </span>
            </div>

            <div
              v-if="isVulTotal(item)"
              class="mt-1 flex-wrap gap-2 flex-center"
            >
              <template
                v-for="level of vulLevelStats?.list"
                :key="level.level"
              >
                <Tag
                  v-if="level && level.count > 0"
                  :key="level.level"
                  class="text-sm"
                  :severity="getThreatLevelSeverity(level.level)"
                  :value="`${getThreatLevelLabel(level.level)}: ${level.count}`"
                />
              </template>
            </div>
          </div>
        </template>
      </Card>

      <Card class="col-span-6 overflow-hidden border border-divider @container/card @3xl:col-span-4">
        <template #content>
          <div
            class="flex justify-between gap-8"
          >
            <div class="flex flex-col gap-2">
              <div class="text-secondary">
                漏洞状态
              </div>

              <div class="flex flex-col gap-1 pt-2 @lg:flex-row @lg:flex-wrap @lg:gap-8">
                <div
                  v-for="item of counts"
                  :key="item.label"
                >
                  <span class="gap-2 text-[13px] flex-center text-secondary @lg:gap-1">
                    <Component
                      :is="item.icon"
                      :size="15"
                      :style="{
                        color: item.colorFront,
                      }"
                    />

                    <span>{{ item.label }}</span>

                    <NumericDisplay class="font-medium">
                      {{ item.value }}
                    </NumericDisplay>

                    项
                  </span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>
