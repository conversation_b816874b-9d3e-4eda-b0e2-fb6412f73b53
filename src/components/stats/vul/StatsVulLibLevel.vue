<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/enums/query-key'
import { getThreatLevelLabel, getThreatLevelSeverity, threatLevelConfig } from '~/enums/security'

const props = defineProps<{
  vendorId?: SecurityAlert['vendor_id']
  vulTypeId?: SecurityAlert['vul_type_id']
  title?: string
}>()

const { themeMode } = useTheme()

const { vendorId, vulTypeId } = toRefs(props)

const { data: vulLevelStats, isLoading: isVulLevelStatsLoading } = useQuery({
  queryKey: queryKeys.stats.vul.lib.level(vendorId, vulTypeId),
  queryFn: () => {
    return StatsService.getVulLibLevelStats({
      vendor_id: vendorId.value,
      vul_type_id: vulTypeId.value,
    })
  },
})
</script>

<template>
  <Card class="border border-divider">
    <template #title>
      <div class="justify-between gap-5 text-xl font-bold flex-center">
        <span>{{ title || '威胁等级统计' }}</span>
        <NumericDisplay>
          {{ vulLevelStats?.total }}
        </NumericDisplay>
      </div>
    </template>

    <template #content>
      <div class="flex flex-col gap-4 pt-4">
        <template v-if="vulLevelStats && !isVulLevelStatsLoading">
          <div
            v-for="(item) of vulLevelStats?.list"
            :key="item.level"
            class="flex flex-col text-sm"
          >
            <div class="gap-2 flex-center">
              <Tag
                class="whitespace-nowrap text-xs"
                :severity="getThreatLevelSeverity(item.level)"
                :value="getThreatLevelLabel(item.level)"
              />

              <div class="h-2 w-full overflow-hidden rounded-full bg-surface-100">
                <div
                  class="h-full transition-all duration-300"
                  :style="{
                    backgroundColor: item.level ? getTagColor(threatLevelConfig[item.level].severity, themeMode) : 'transparent',
                    width: item.count > 0 ? `${(item.count / vulLevelStats.total * 100)}%` : '0%',
                  }"
                />
              </div>
            </div>

            <div class="flex">
              <div class="ml-auto justify-between gap-1 tabular-nums flex-center text-secondary">
                <div class="gap-4 flex-center">
                  <span>{{ item.count }}</span>
                  <span class="w-12 text-right">{{ item.count > 0 ? ((item.count / vulLevelStats.total) * 100).toFixed(1) : 0 }}%</span>
                </div>
              </div>
            </div>
          </div>
        </template>

        <div
          v-if="isVulLevelStatsLoading"
          class="space-y-5"
        >
          <div
            v-for="i of 5"
            :key="i"
            class="gap-2 flex-center"
          >
            <Skeleton
              height="1.4rem"
              width="2.2rem"
            />

            <Skeleton
              class="opacity-80"
              height="1rem"
            />
          </div>
        </div>
      </div>
    </template>
  </Card>
</template>
