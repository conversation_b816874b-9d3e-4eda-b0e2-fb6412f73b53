<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { useQuery } from '@tanstack/vue-query'
import type { ApexOptions } from 'apexcharts'

import { queryKeys } from '~/enums/query-key'

const { data: vulTypeList } = useQuery({
  queryKey: queryKeys.stats.vul.type,
  queryFn: () => StatsService.getVulTypeStats(),
})

const { isDarkMode } = useTheme()

const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  series: [{
    name: '漏洞类型',
    data: vulTypeList.value?.map((item) => item.count) || [],
  }],
  chart: {
    height: 350,
    type: 'bar',
    toolbar: {
      show: false,
    },
  },
  plotOptions: {
    bar: {
      borderRadius: 3,
      columnWidth: '50%',
    },
  },
  tooltip: {
    enabled: true,
    showForZeroSeries: true,
    intersect: false,
  },
  dataLabels: {
    enabled: false,
  },
  stroke: {
    width: 0,
  },
  states: {
    hover: {
      filter: {
        type: 'none',
      },
    },
  },
  xaxis: {
    labels: {
      rotate: -20,
      rotateAlways: true,
      maxHeight: 120,
      style: {
        fontSize: '10px',
      },
      hideOverlappingLabels: false,
    },
    categories: vulTypeList.value?.map((item) => item.name) || [],
    tickPlacement: 'on',
  },
  colors: [isDarkMode.value ? $dt('red.700').value : $dt('red.500').value],
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div>
    <div class="pb-2">
      <StatsCardHeader title="漏洞库类型统计" />
    </div>

    <div ref="chartContainer" />
  </div>
</template>
