<script setup lang="ts">
import { Tag } from 'primevue'

import { TaskStatus, taskStatusConfig, TaskType } from '~/enums/asset'

const props = withDefaults(
  defineProps<{
    taskType?: TaskType
  }>(),
  {
    taskType: TaskType.AssetScan,
  },
)

const name = computed(() => {
  return props.taskType === TaskType.AssetScan ? '资产' : props.taskType === TaskType.BaselineScan ? '基线' : props.taskType === TaskType.VulScan ? '漏洞' : ''
})

const columns: ProTableColumn<TaskExecutionLog>[] = [
  {
    header: '任务名称',
    render: (data) => data.task.name,
  },
  {
    field: 'run_type_name',
    header: '执行类型',
    render: (data) => h('span', { class: 'text-sm' }, data.run_type_name),
  },
  {
    header: '开始时间',
    class: 'text-sm',
    render: (data) => data.create_time || '-',
  },
  {
    header: '状态',
    render: (data) => h(Tag, {
      severity: data.status === TaskStatus.Running
        ? 'warn'
        : data.status === TaskStatus.Success
          ? 'success'
          : data.status === TaskStatus.Failed
            ? 'danger'
            : 'secondary',
      value: taskStatusConfig[data.status].label,
    }),
  },
]
</script>

<template>
  <div class="flex flex-col gap-4">
    <h3 class="text-lg font-semibold">
      {{ name }}扫描任务
    </h3>

    <ProTable
      :columns="columns"
      :pagination="{
        show: false,
      }"
      :queryParams="{ task_type: taskType }"
      :requestFn="(queryParams) => {
        if (queryParams.task_type) {
          return TaskService.getTaskExecutionList(queryParams)
        }

        return null
      }"
      scrollable
      scrollHeight="400px"
      stripedRows
      :toolbar="false"
    />
  </div>
</template>
