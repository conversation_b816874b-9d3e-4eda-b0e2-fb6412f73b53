<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import type { ApexOptions } from 'apexcharts'

import { StatsService } from '@/services/stats'
import type { AssetType } from '~/enums/asset'
import { assetConfig, assetCriticalityConfig } from '~/enums/asset'
import { queryKeys } from '~/enums/query-key'

const selectedAssetType = ref<AssetType>()

const { isDarkMode } = useTheme()

const { data: grades } = useQuery({
  queryKey: queryKeys.stats.asset.importance(selectedAssetType),
  queryFn: () => StatsService.getAssetImportanceStats({ asset_type: selectedAssetType.value }),
})

const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  series: grades.value?.map((it) => it.count) || [],
  chart: {
    type: 'pie',
    height: 300,
  },
  labels: grades.value?.map((it) => `${it.name} (${it.count})`) || [],
  colors: grades.value?.map((it) => assetCriticalityConfig[it.grade].bgColor) || [],
  legend: {
    position: 'right',
    offsetX: 20,
    offsetY: 70,
  },
  responsive: [{
    breakpoint: 480,
    options: {
      chart: {
        height: 250,
      },
      legend: {
        position: 'bottom',
        offsetY: 0,
        offsetX: 0,
      },
    },
  }],
}))

const { chartContainer } = useApexChart(chartOptions)

const assetTypeOptions = computed(() => {
  return Object.values(assetConfig).map((it) => ({
    label: it.label,
    value: it.value,
  }))
})
</script>

<template>
  <div>
    <div class="pb-8">
      <StatsCardHeader>
        <template #title>
          IT 资产重要程度
        </template>

        <template #right>
          <ProSelect
            v-model="selectedAssetType"
            :options="assetTypeOptions"
            placeholder="资产类型"
            showClear
            size="small"
          />
        </template>
      </StatsCardHeader>
    </div>

    <div ref="chartContainer" />
  </div>
</template>
