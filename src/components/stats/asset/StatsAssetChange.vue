<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { useQuery } from '@tanstack/vue-query'
import type { ApexOptions } from 'apexcharts'

import { queryKeys } from '~/enums/query-key'

const { isDarkMode } = useTheme()

const { data: ipAssetChangeList } = useQuery({
  queryKey: queryKeys.stats.asset.ip.change,
  queryFn: () => StatsService.getIpAssetChangeStats(),
})

const assetData = computed(() => {
  return ipAssetChangeList.value?.map((item) => ({
    department: item.name,
    values: item.data.map((d) => d.count),
  })) || []
})

const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  chart: {
    type: 'bar',
    height: 350,
    toolbar: {
      show: false,
    },
  },
  plotOptions: {
    bar: {
      horizontal: false,
      columnWidth: '55%',
    },
  },
  dataLabels: {
    enabled: false,
  },
  stroke: {
    show: true,
    width: 2,
    colors: ['transparent'],
  },
  xaxis: {
    categories: assetData.value.map((item) => item.department),
  },
  colors: [$dt('blue.500').value, $dt('blue.600').value, $dt('green.500').value, $dt('slate.500').value, $dt('slate.400').value],
  series: assetData.value.at(0)?.values.map((_, index) => ({
    name: ipAssetChangeList.value?.at(0)?.data[index]?.name || `系列${index + 1}`,
    data: assetData.value.map((item) => item.values[index]),
  })) || [],
  legend: {
    position: 'top',
  },
  tooltip: {
    shared: true,
    intersect: false,
    y: {
      formatter: (val: number) => `${val}`,
    },
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div>
    <StatsCardHeader title="IT 资产变化" />

    <div ref="chartContainer" />
  </div>
</template>
