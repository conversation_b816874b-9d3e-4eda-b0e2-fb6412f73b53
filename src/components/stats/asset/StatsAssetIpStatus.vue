<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { useQuery } from '@tanstack/vue-query'
import type { ApexOptions } from 'apexcharts'

import { StatsService } from '@/services/stats'
import { queryKeys } from '~/enums/query-key'

const props = defineProps<{
  selectedNetwork?: Network['id']
  selectedNetworkDeviceId?: NetworkDevice['id']
}>()

const { isDarkMode } = useTheme()

const { selectedNetwork, selectedNetworkDeviceId } = toRefs(props)

const { data: ipStatusList } = useQuery({
  queryKey: queryKeys.stats.asset.ip.online(selectedNetwork, selectedNetworkDeviceId),
  queryFn: () => {
    if ((selectedNetwork.value && selectedNetworkDeviceId.value) || (!selectedNetwork.value && !selectedNetworkDeviceId.value)) {
      return StatsService.getIpOnlineStats({
        network_id: selectedNetwork.value,
        network_device_id: selectedNetworkDeviceId.value,
      })
    }

    return null
  },
})

interface IpStatusData {
  ip: string
  online: number
  offline: number
  unknown: number
}

const data = computed<IpStatusData[]>(() => {
  if (!ipStatusList.value) {
    return []
  }

  return ipStatusList.value.map((item) => {
    const statusCounts = {
      online: 0,
      offline: 0,
      unknown: 0,
    }

    item.data.forEach((status) => {
      switch (status.name) {
        case '在线':
          statusCounts.online = status.count
          break

        case '离线':
          statusCounts.offline = status.count
          break

        case '未知':
          statusCounts.unknown = status.count
          break
      }
    })

    return {
      ip: item.name,
      ...statusCounts,
    }
  })
})

const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  chart: {
    type: 'bar',
    height: 300,
    toolbar: {
      show: false,
    },
  },
  plotOptions: {
    bar: {
      horizontal: false,
      columnWidth: '55%',
    },
  },
  colors: [$dt('emerald.500').value, $dt('red.500').value, $dt('blue.500').value],
  dataLabels: {
    enabled: false,
  },
  series: [
    {
      name: '在线',
      data: data.value.map((item) => item.online),
    },
    {
      name: '离线',
      data: data.value.map((item) => item.offline),
    },
    {
      name: '未知',
      data: data.value.map((item) => item.unknown),
    },
  ],
  xaxis: {
    categories: data.value.map((item) => item.ip),
  },
  yaxis: {
    min: 0,
    max: 180,
    tickAmount: 6,
  },
  legend: {
    position: 'top',
    horizontalAlign: 'left',
  },
  grid: {
    borderColor: '#f1f1f1',
  },
  tooltip: {
    shared: true,
    intersect: false,
    y: {
      formatter: (val: number) => `${val}`,
    },
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div ref="chartContainer" />
</template>
