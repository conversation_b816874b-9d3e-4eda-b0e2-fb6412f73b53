<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { useQuery } from '@tanstack/vue-query'
import type { ApexOptions } from 'apexcharts'

import { queryKeys } from '~/enums/query-key'

const { data: ipLogs } = useQuery({
  queryKey: queryKeys.stats.asset.ip.log,
  queryFn: () => StatsService.getAssetIpLogStats(),
})

const { isDarkMode } = useTheme()

const chartOptions = computed<ApexOptions>(() => {
  const documentStyle = getComputedStyle(document.documentElement)

  const textColor = documentStyle.getPropertyValue($dt('text.color').name)
  const fillColor = documentStyle.getPropertyValue($dt('blue.500').name)
  const strokeColor = documentStyle.getPropertyValue($dt('blue.600').name)

  return {
    series: [{
      name: '变更 IP 数',
      data: ipLogs.value?.map((it) => it.count) || [],
    }],
    theme: {
      mode: isDarkMode.value ? 'dark' : 'light',
    },
    stroke: {
      curve: 'smooth',
    },
    chart: {
      type: 'area',
      height: 300,
      toolbar: {
        show: false,
      },
      zoom: {
        enabled: true,
        type: 'x',
        allowMouseWheelZoom: true,
        autoScaleYaxis: true,
        zoomedArea: {
          fill: {
            color: fillColor,
            opacity: 0.5,
          },
          stroke: {
            color: strokeColor,
            opacity: 0.5,
            width: 1,
          },
        },
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '55%',
      },
    },
    dataLabels: {
      enabled: false,
    },
    xaxis: {
      labels: {
        rotate: -10,
        rotateAlways: true,
        hideOverlappingLabels: false,
        style: {
          colors: textColor,
        },
      },
      categories: ipLogs.value?.map((it) => it.date) || [],
    },
    colors: [fillColor],
  }
})

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div>
    <StatsCardHeader title="IP 变更记录" />

    <div ref="chartContainer" />
  </div>
</template>
