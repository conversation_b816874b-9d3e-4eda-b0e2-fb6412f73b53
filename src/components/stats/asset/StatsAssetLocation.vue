<script setup lang="ts">
import { $dt } from '@primevue/themes'
import { useQuery } from '@tanstack/vue-query'
import type { ApexOptions } from 'apexcharts'

import { queryKeys } from '~/enums/query-key'

const props = defineProps<{
  selectedNetwork?: Network['id']
  selectedNetworkDeviceId?: NetworkDevice['id']
}>()

const { selectedNetwork, selectedNetworkDeviceId } = toRefs(props)

const { isDarkMode } = useTheme()

const { data: ipLocationList } = useQuery({
  queryKey: queryKeys.stats.asset.ip.location(selectedNetwork, selectedNetworkDeviceId),
  queryFn: () => {
    if ((selectedNetwork.value && selectedNetworkDeviceId.value) || (!selectedNetwork.value && !selectedNetworkDeviceId.value)) {
      return StatsService.getIpLocationStats({
        network_id: selectedNetwork.value,
        network_device_id: selectedNetworkDeviceId.value,
      })
    }

    return null
  },
})

const chartOptions = computed<ApexOptions>(() => ({
  theme: {
    mode: isDarkMode.value ? 'dark' : 'light',
  },
  series: ipLocationList.value?.map((item) => ({
    name: item.name,
    data: [item.data.count],
  })) || [],
  chart: {
    type: 'bar',
    height: 300,
    toolbar: {
      show: false,
    },
  },
  xaxis: {
    categories: ipLocationList.value?.map((item) => item.name) || [],
  },
  colors: [$dt('blue.500').value],
  plotOptions: {
    bar: {
      borderRadius: 4,
      horizontal: false,
      dataLabels: {
        position: 'top',
      },
    },
  },
  dataLabels: {
    enabled: true,
    formatter: (val: number) => `${val} 台`,
  },
  tooltip: {
    y: {
      formatter: (value: number) => `${value} 台设备`,
    },
  },
}))

const { chartContainer } = useApexChart(chartOptions)
</script>

<template>
  <div ref="chartContainer" />
</template>
