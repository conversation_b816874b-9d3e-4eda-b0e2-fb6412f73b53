<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'

import { StatsService } from '@/services/stats'
import { assetConfig, AssetType, TopologyNodeType } from '~/enums/asset'
import { queryKeys } from '~/enums/query-key'
import { RouteKey } from '~/enums/route'

const { data: counts } = useQuery({
  queryKey: queryKeys.stats.asset.count,
  queryFn: () => StatsService.getAssetCountStats(),
})

const statsGroups: { items: { value: string | number, valueKey: string, label: string, command?: () => void }[] }[] = [{
  items: [
    {
      value: TopologyNodeType.Network,
      valueKey: 'network',
      label: '网络',
      command: async () => {
        await navigateTo(getRoutePath(RouteKey.资产详情))
      },
    },
    {
      value: AssetType.NetworkDevice,
      valueKey: 'network_equipment',
      label: '网络设备',
      command: async () => {
        await navigateTo(getRoutePath(RouteKey.资产详情))
      },
    },
    { value: 'TopologyNodeType.IpSubnet', valueKey: 'ip_subnet', label: '子网' },
    { value: 'TopologyNodeType.Ip', valueKey: 'ip', label: 'IP' },
  ],
},
{
  items: [
    { value: AssetType.Server, valueKey: 'server', label: assetConfig[AssetType.Server].label },
    { value: AssetType.BusinessSystem, valueKey: 'business_system', label: assetConfig[AssetType.BusinessSystem].label },
    { value: AssetType.Database, valueKey: 'database', label: assetConfig[AssetType.Database].label },
    { value: AssetType.Other, valueKey: 'other', label: assetConfig[AssetType.Other].label },
  ],
}]

// const statsGroups = [
//   { value: AssetType.NetworkDevice, valueKey: 'network_equipment', label: '网络设备' },
//   { value: AssetType.Server, valueKey: 'server', label: '服务器' },
//   { value: AssetType.BusinessSystem, valueKey: 'web', label: '业务系统' },
//   { value: AssetType.Database, valueKey: 'database', label: '数据库' },
//   { value: AssetType.Other, valueKey: 'other', label: '其他资产' },
//   { value: TopologyNodeType.Network, valueKey: 'network', label: '网络' },
//   { value: 'TopologyNodeType.IpSubnet', valueKey: 'ip_subnet', label: '子网' },
//   { value: 'TopologyNodeType.Ip', valueKey: 'ip', label: 'IP' },
// ] satisfies { value: string | number, valueKey: string, label: string }[]
</script>

<template>
  <div class="gap-4 flex-center">
    <template
      v-for="(group, groupIndex) of statsGroups"
      :key="groupIndex"
    >
      <CardContainer class="basis-1/2 p-card-container">
        <div class=" justify-around overflow-hidden flex-center">
          <template
            v-for="(item, itemIdx) of group.items"
            :key="item.valueKey"
          >
            <div
              class="flex flex-1 flex-col items-center justify-around gap-2 rounded-lg px-4 py-1"
              :class="{
                'cursor-pointer hover:bg-emphasis': typeof item.command === 'function',
              }"
              @click="item.command?.()"
            >
              <div class="text-xl font-bold">
                {{ item.label }}
              </div>

              <div class="text-3xl font-bold">
                <NumericDisplay>
                  {{ counts ? counts[item.valueKey as keyof AssetStats['count']] || 0 : '-' }}
                </NumericDisplay>
              </div>
            </div>

            <Divider
              v-if="itemIdx !== group.items.length - 1"
              layout="vertical"
            />
          </template>
        </div>
      </CardContainer>
    </template>

    <!-- <div class="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
    <StatsMetricsCard
      v-for="group of statsGroups"
      :key="group.value"
      :count="counts ? counts[group.valueKey as keyof AssetStats['count']] || 0 : 0"
      :title="group.label"
      :value="group.value"
    />
  </div> -->
  </div>
</template>
