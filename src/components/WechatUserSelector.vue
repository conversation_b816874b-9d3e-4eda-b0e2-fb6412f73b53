<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { MinusIcon, UserRoundPlusIcon } from 'lucide-vue-next'
import type { MultiSelectProps } from 'primevue'

import { queryKeys } from '~/constants-tw/query-key'
import { UserService } from '~/services-tw/org'
import type { WechatUser, WechatUserListItem } from '~/types-tw/user'

type UserSelectorProps = MultiSelectProps

const props = defineProps<UserSelectorProps>()

const users = defineModel<WechatUser[]>()

const { data: userList, isLoading } = useQuery({
  queryKey: queryKeys.wechatUser.list(),
  queryFn: () => UserService.getWechatUsers({
    page: 1,
    page_size: 50,
  }),
})
</script>

<template>
  <MultiSelect
    v-model="users"
    dataKey="id"
    display="chip"
    filter
    fluid
    :inputId="props.inputId"
    :loading="isLoading"
    :maxSelectedLabels="4"
    optionLabel="username"
    :options="userList?.list"
    placeholder="选择用户"
    :pt="{
      label: '!p-1 !min-h-[33.5px]',
    }"
  >
    <template #option="{ option }: { option: WechatUserListItem }">
      <div class="gap-2 flex-center">
        <UserAvatar
          :avatar="option.avatar"
          :username="option.nickname"
        />

        <div class="flex flex-col">
          <span>{{ option.nickname }}</span>
        </div>
      </div>
    </template>

    <template #chip="{ value }: { value: WechatUser }">
      <UserSelectorChip
        showRemove
        :value="{ id: value.id, username: value.nickname, avatar: value.avatar }"
        @remove="users = users?.filter(it => it.id !== value.id)"
      />
    </template>

    <template #dropdownicon>
      <UserRoundPlusIcon :size="16" />
    </template>

    <template #header>
      <div class="gap-1 px-4 pb-0.5 pt-2 flex-center">
        <div class="font-medium">
          选择用户
        </div>

        <div class="ml-auto">
          <Button
            v-if="Array.isArray(users) && users.length > 0"
            class="ml-auto"
            label="移除所有"
            severity="danger"
            size="small"
            text
            @click.stop="users = undefined"
          >
            <template #icon>
              <MinusIcon :size="14" />
            </template>
          </Button>
        </div>
      </div>
    </template>
  </MultiSelect>
</template>
