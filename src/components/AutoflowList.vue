<script setup lang="ts">
import { But<PERSON>, Checkbox, DataView, Tag } from 'primevue'

import { InjectKey } from '~/constants-tw/common'
import { ProMenuActionType } from '~/enums/pro'
import { AUTH } from '~/enums/user'
import { ProjectService } from '~/services-tw/project'
import type { TW_AutoFlow, TW_AutoFlowListItem, TW_AutoFlowQuery } from '~/types-tw/project'

const props = defineProps<{
  selectionMode?: 'single' | 'multiple'
  showCreateButton?: boolean
}>()

const userStore = useUserStore()

const selectedFlowIds = defineModel<SelectedFlowIds>('selection')

type SelectedFlowIds = TW_AutoFlow['id'][]

const { $dialog, $confirm, $toast, $openFlowDetailDialog } = useNuxtApp()

const runtimeConfig = useRuntimeConfig()
const { n8nHost } = runtimeConfig.public

interface DialogRef {
  close: (selectedFlowIds?: SelectedFlowIds) => void
}

const dialogRef = inject<Ref<DialogRef | undefined>>(InjectKey.PrimeVueDialog, ref())

const isSelectMode = computed(() => props.selectionMode !== undefined)

const query = ref<TW_AutoFlowQuery>({
  limit: 20,
})

const loading = ref(false)

const autoflows = ref<TW_AutoFlowListItem[]>([])

async function loadData(options: { isLoadMore?: boolean, reset?: boolean } = {}) {
  const { isLoadMore = false, reset = false } = options

  if (loading.value) {
    return
  }

  loading.value = true

  try {
    if (reset) {
      query.value.cursor = undefined
    }

    const res = await ProjectService.getAutoFlows(query.value)

    if (isLoadMore) {
      autoflows.value.push(...(res.data ?? []))
    }
    else {
      autoflows.value = res.data ?? []
    }

    query.value.cursor = res.nextCursor
  }
  finally {
    loading.value = false
  }
}

const handleSearch = (value: string) => {
  query.value.name = value
  loadData({ reset: true })
}

onMounted(() => {
  loadData()
})

const handleViewFlow = (flowData: TW_AutoFlow) => {
  $openFlowDetailDialog(flowData)
}

const handleItemClick = (it: TW_AutoFlowListItem, event: MouseEvent) => {
  if ((event.target as HTMLElement).closest('button')) {
    return
  }

  if (isSelectMode.value && it.active) {
    if (props.selectionMode === 'single') {
      selectedFlowIds.value = selectedFlowIds.value?.some((i) => i === it.id) ? [] : [it.id]
    }
    else if (props.selectionMode === 'multiple') {
      selectedFlowIds.value = selectedFlowIds.value?.some((i) => i === it.id)
        ? selectedFlowIds.value?.filter((i) => i !== it.id)
        : [...(selectedFlowIds.value ?? []), it.id]
    }
  }
}

const handleCheckboxChange = (it: TW_AutoFlowListItem, value: boolean) => {
  if (isSelectMode.value) {
    selectedFlowIds.value = value
      ? [...(selectedFlowIds.value ?? []), it.id]
      : selectedFlowIds.value?.filter((i) => i !== it.id)
  }
}

const isItemSelected = (item: TW_AutoFlowListItem) => {
  return selectedFlowIds.value?.includes(item.id) ?? false
}

const handleCancel = () => {
  dialogRef.value?.close()
}

const handleConfirm = () => {
  dialogRef.value?.close(selectedFlowIds.value)
}

const handleCreateFlow = () => {
  $dialog.open(h('iframe', {
    src: `${n8nHost}/workflow/new`,
    class: 'size-full',
  }),
  {
    props: {
      header: '创建流程',
      pt: {
        root: 'p-dialog-maximized overflow-hidden',
        content: '!p-0',
      },
    },
    onClose: () => {
      loadData({ reset: true })
    },
  },
  )
}
</script>

<template>
  <div class="space-y-4">
    <div class="flex items-center gap-4">
      <SearchInput
        v-model="query.name"
        class="w-64"
        :loading="loading"
        placeholder="搜索流程名称"
        @update:debounceChange="handleSearch"
      />

      <div class="ml-auto">
        <Button
          v-if="showCreateButton"
          v-permission="AUTH.WORKFLOW_CREATE"
          label="创建流程"
          severity="primary"
          @click="handleCreateFlow()"
        />
      </div>
    </div>

    <DataView
      dataKey="id"
      layout="list"
      :value="autoflows"
    >
      <template #list="{ items }: { items: TW_AutoFlowListItem[] }">
        <div
          v-for="it of items"
          :key="it.id"
        >
          <div
            class="gap-5 border-b border-surface-200 px-2 py-4 flex-center"
            :class="[
              isItemSelected(it) ? 'bg-emphasis' : '',
              isSelectMode && it.active ? 'cursor-pointer hover:bg-surface-50' : '',
            ]"
            @click="(e: MouseEvent) => handleItemClick(it, e)"
          >
            <div
              v-if="isSelectMode"
              class="shrink-0"
              :class="{
                'pointer-events-none invisible': !it.active,
              }"
            >
              <Checkbox
                binary
                :modelValue="isItemSelected(it)"
                @click.stop
                @update:modelValue="handleCheckboxChange(it, $event)"
              />
            </div>

            <div class="flex-1 flex-col gap-4">
              <TextLink
                @click.stop="handleViewFlow(it)"
              >
                {{ it.name }}
              </TextLink>

              <div class="flex-wrap gap-3 pt-3 text-sm text-surface-500 flex-center">
                <Tag
                  :severity="it.active ? 'success' : 'secondary'"
                  :value="it.active ? '启用中' : '未启用'"
                />
                <span v-if="it.updatedAt">更新于 <TimeDisplay :time="it.updatedAt" /></span>
                <span v-if="it.createdAt">创建于 <TimeDisplay :time="it.createdAt" /></span>
              </div>
            </div>

            <div class="ml-auto gap-2 flex-center">
              <Button
                v-permission="AUTH.WORKFLOW_READ"
                label="查看"
                severity="secondary"
                size="small"
                @click.stop="handleViewFlow(it)"
              />

              <PopupMenu
                v-if="showCreateButton"
                :options="[
                  ...(userStore.hasPermission(AUTH.WORKFLOW_DELETE) ? [{
                    label: '删除',
                    actionType: ProMenuActionType.Delete,
                    command: () => {
                      $confirm.dialog({
                        message: `确定要删除工作流「${it.name}」吗？`,
                        accept: async () => {
                          await ProjectService.deleteAutoFlow(it.id)
                          $toast.contrast({
                            summary: '工作流已删除',
                          })
                          loadData({ reset: true })
                        },
                      })
                    },
                  }] : []),
                ]"
              >
                <ProBtnMore onlyIcon />
              </PopupMenu>
            </div>
          </div>
        </div>
      </template>

      <template #empty>
        <ProTableEmpty />
      </template>
    </DataView>

    <div
      v-if="query.cursor"
      class="mt-4 flex justify-center"
    >
      <Button
        label="加载更多"
        :loading="loading"
        severity="secondary"
        @click="loadData({ isLoadMore: true })"
      />
    </div>

    <div
      v-if="isSelectMode"
      class="justify-end gap-4 flex-center"
    >
      <Button
        label="取消"
        severity="secondary"
        @click="handleCancel()"
      />

      <Button
        :disabled="selectedFlowIds?.length === 0"
        label="确定选择"
        severity="primary"
        @click="handleConfirm()"
      />
    </div>
  </div>
</template>
