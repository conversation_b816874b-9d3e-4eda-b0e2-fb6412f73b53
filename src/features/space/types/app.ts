import type { SpaceAppResource, SpaceData } from './space'

export type WorkflowListQuery = {
  projectId: SpaceData['id']
  name?: SpaceAppResource['name']
  active?: boolean
}

export interface WorkflowApp {
  id: string
  name: string
}

export type WorkflowAppSetting = Omit<WorkflowApp, 'id'>

export type AppCreate = Omit<WorkflowApp, 'id'> & {
  projectId: SpaceData['id']
}

export type AppUpdate = Partial<AppCreate> & {
  id: WorkflowApp['id']
}

// 社区节点相关类型
export interface CommunityNode {
  id: string
  name: string
  packageName: string
  version: string
  latestVersion?: string
  description?: string
  author?: string
  verified: boolean
  installDate: string
  status: 'installed' | 'installing' | 'failed' | 'updating'
  hasUpdate?: boolean
}

export interface CommunityNodeInstallPayload {
  packageName: string
  version?: string
}

export interface CommunityNodeSearchResult {
  name: string
  version: string
  description: string
  author: string
  verified: boolean
  downloadCount: number
  lastModified: string
  keywords: string[]
}

export interface CommunityNodeListQuery {
  search?: string
  verified?: boolean
  page?: number
  pageSize?: number
}
