import type { ICredentialType, ProjectSharingData } from 'n8n-workflow'

import type { UniqueId } from '~/types/common'

import type { WorkflowApp } from './app'

export interface SpaceData {
  id: string
  name: string
  type?: string
  icon?: {
    type: string
    value: string
  }
  createdAt: string
  updatedAt?: string
}

export type SpaceSettingForm = Pick<SpaceData, 'name' | 'icon'>

export interface SpaceAppResource {
  id: string
  name: WorkflowApp['name']
  value?: string
  key?: string
  updatedAt?: string
  createdAt?: string
  homeProject?: ProjectSharingData
  type?: string
  sharedWithProjects?: ProjectSharingData[]
}

export interface SpaceMember {
  id: UniqueId
  userId: number
  name: string
  appointment?: string
  avatar?: string
  phone: string
  wecom_biz_mail?: string
}

export type SpaceMemberListItem = SpaceMember

export interface SpaceCredential {
  id: string
  name: string
  username: string
  password: string
  createdAt: string
  updatedAt?: string
}

export interface CreateSpaceCredentialDto {
  name: string
  type: ICredentialType['name']
  data?: Record<string, unknown>
}

export type UpdateSpaceCredentialDto = CreateSpaceCredentialDto
