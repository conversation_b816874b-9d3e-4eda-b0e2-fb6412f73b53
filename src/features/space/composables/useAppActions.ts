import { CopyPlusIcon, FileUpIcon, FolderOutputIcon } from 'lucide-vue-next'
import { safeParse, string } from 'valibot'

import { useWorkflowHelpers } from '#wf/composables/useWorkflowHelpers'
import { useRootStore } from '#wf/stores/root.store'
import type { IWorkflowToShare } from '#wf/types/interface'

import { GlobalEvent } from '~/enums/event'
import { ProMenuActionType } from '~/enums/pro'
import SpaceSelectForm from '~/features/space/components/space/SpaceSelectForm.vue'
import { AppService } from '~/features/space/services/app'
import { useSpaceStore } from '~/features/space/stores/space'
import type { SpaceAppResource } from '~/features/space/types/space'
import { saveAs } from '~/utils/file'

export function useAppActions() {
  const spaceStore = useSpaceStore()
  const { activeSpaceId } = storeToRefs(spaceStore)

  const rootStore = useRootStore()

  const workflowHelpers = useWorkflowHelpers()

  const emitter = useEmitter()
  const { $confirm, $toast, $dialog } = useNuxtApp()

  const handleExportWorkflow = async (itemData: SpaceAppResource) => {
    const workflow = await AppService.getWorkflow(itemData.id)
    const workflowData = await workflowHelpers.getWorkflowDataToSave(workflow)

    const { tags, ...data } = workflowData
    const exportData: IWorkflowToShare = {
      ...data,
      meta: {
        ...workflowData.meta,
        instanceId: rootStore.instanceId,
      },
    }

    saveAs(exportData, `${itemData.name || 'workflow'}.json`)

    $toast.success({
      summary: '工作流已导出',
    })
  }

  const getActionOptions = (
    src: SpaceAppResource,
    placement: 'canvas' | 'list' = 'list',
  ): ProMenuItem[] => {
    const actionEdit: ProMenuItem = {
      label: '编辑信息',
      actionType: ProMenuActionType.Edit,
      command: () => {
        emitter.emit(GlobalEvent.AppUpdate, src)
      },
    }

    const actionExport: ProMenuItem = {
      label: '导出工作流文件',
      actionType: ProMenuActionType.Export,
      itemIcon: FileUpIcon,
      command: async () => {
        handleExportWorkflow(src)
      },
    }

    const allOptions: ProMenuItem[] = [
      actionEdit,
      {
        label: '复制出新工作流',
        actionType: ProMenuActionType.Copy,
        itemIcon: CopyPlusIcon,
        command: async () => {
          await AppService.duplicateWorkflow(src.id)
          emitter.emit(GlobalEvent.AppListRefresh)
        },
      },
      {
        label: '移动到其他空间',
        actionType: ProMenuActionType.Move,
        itemIcon: FolderOutputIcon,
        command: async () => {
          $dialog.open(h(SpaceSelectForm), {
            props: {
              header: '移动到其他空间',
              pt: {
                root: 'dialog-form-wide',
              },
            },
            onClose: async (opt) => {
              const res = safeParse(string(), opt?.data)

              if (res.success) {
                const spaceId = res.output

                if (spaceId && spaceId !== activeSpaceId.value) {
                  await AppService.moveWorkflow(src.id, spaceId)

                  $toast.success({
                    summary: '工作流已移动',
                  })

                  emitter.emit(GlobalEvent.AppListRefresh)
                }
              }
            },
          })
        },
      },
      actionExport,
      {
        actionType: ProMenuActionType.Separator,
      },
      {
        label: '删除',
        actionType: ProMenuActionType.Delete,
        command: () => {
          $confirm.dialog({
            header: '确定删除工作流？',
            message: `该操作将删除工作流「${src.name}」，删除后不能恢复，请谨慎操作。`,
            accept: async () => {
              await AppService.deleteWorkflow(src.id)

              $toast.contrast({
                summary: '工作流已删除',
              })

              emitter.emit(GlobalEvent.AppListRefresh)
            },
          })
        },
      },
    ]

    if (placement === 'canvas') {
      return [actionExport]
    }

    return allOptions
  }

  return {
    getActionOptions,
    handleExportWorkflow,
  }
}
