import type { CreateSpaceCredentialDto, SpaceCredential, SpaceData, SpaceMember, SpaceMemberListItem, SpaceSettingForm } from '~/features/space/types/space'

export const SpaceService = {
  getSpaces(query?: WithPageQuery<Partial<SpaceData>>) {
    return useRequest<PageResult<SpaceData>>('/workflows/project', {
      query,
    })
  },

  getSpace(id: string) {
    return useRequest<SpaceData>(`/workflows/project/${id}`)
  },

  createSpace(payload: SpaceSettingForm) {
    return useRequest<SpaceData>('/workflows/project', {
      method: 'POST',
      body: payload,
    })
  },

  updateSpace(id: string, payload: SpaceSettingForm) {
    return useRequest<SpaceData>(`/workflows/project/${id}`, {
      method: 'PUT',
      body: payload,
    })
  },

  deleteSpace(id: string) {
    return useRequest(`/workflows/project/${id}`, {
      method: 'DELETE',
    })
  },

  getSpaceMembers(spaceId: SpaceData['id'], query?: WithPageQuery<Partial<SpaceMember>>) {
    return useRequest<PageResult<SpaceMemberListItem>>(`/workflows/project/user/${spaceId}`, {
      query,
    })
  },

  addSpaceMember({ spaceId, userIds }: { spaceId: SpaceData['id'], userIds: SpaceMember['userId'][] }) {
    return useRequest('/workflows/project/user', {
      method: 'POST',
      body: {
        projectId: spaceId,
        userIds,
      },
    })
  },

  removeSpaceMember({ spaceId, userId }: { spaceId: SpaceData['id'], userId: SpaceMember['userId'] }) {
    return useRequest(`/workflows/project/user/${userId}`, {
      method: 'DELETE',
      body: {
        projectId: spaceId,
      },
    })
  },

  getSpaceCredentials(query?: WithPageQuery<Partial<{ projectId: SpaceData['id'], name?: string }>>) {
    return useRequest<PageResult<SpaceCredential>>(`/workflows/credential`, {
      query,
    })
  },

  createSpaceCredential(payload: CreateSpaceCredentialDto & { projectId: SpaceData['id'] }) {
    return useRequest('/workflows/credential', {
      method: 'POST',
      body: payload,
    })
  },
}
