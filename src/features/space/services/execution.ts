import type { ExecutionSummary } from 'n8n-workflow'

import type { ExecutionSummaryWithScopes, IExecutionFlattedResponse, IExecutionsStopData } from '#wf/types/interface'

import type { PageResult, UnsafeAny } from '~/types/common'

export const ExecutionService = {
  getExecutions(query: WithPageQuery<{ workflowId: string }>) {
    const { workflowId, ...rest } = query ?? {}

    return useRequest<PageResult<ExecutionSummaryWithScopes>>(`/workflows/executor/${workflowId}`, {
      query: rest,
    })
  },

  getExecution(id: ExecutionSummary['id']) {
    return useRequest<IExecutionFlattedResponse | undefined>(`/workflows/executor/data/${id}`)
  },

  updateExecution(id: string, data: UnsafeAny) {
    return useRequest<ExecutionSummaryWithScopes>(`/executions/${id}`, {
      method: 'PATCH',
      body: data,
    })
  },

  deleteExecution(data: UnsafeAny) {
    return useRequest<boolean>('/executions/delete', {
      method: 'POST',
      body: data,
    })
  },

  stopExecution(id: string) {
    return useRequest<IExecutionsStopData>(`/executions/${id}/stop`, {
      method: 'POST',
    })
  },

  retryExecution(data: UnsafeAny) {
    return useRequest<boolean>('/executions/retry', {
      method: 'POST',
      body: data,
    })
  },

  stopWaitingWebhook(targetWorkflowId: string) {
    return useRequest<boolean>(`/workflows/executor/webhook/stop/${targetWorkflowId}`, {
      method: 'DELETE',
    })
  },
}
