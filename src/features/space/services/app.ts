import type { FrontendSettings } from '@n8n/api-types'

import { mockWorkflow } from '#wf/constants/mock-workflow'
import { useRootStore } from '#wf/stores/root.store'
import type { IExecutionPushResponse, IExecutionResponse, IStartRunData, IWorkflowDb } from '#wf/types/interface'

import { ApiStatus } from '~/enums/common'
import type { CommunityNode, CommunityNodeInstallPayload, CommunityNodeListQuery, CommunityNodeSearchResult, WorkflowAppSetting, WorkflowListQuery } from '~/features/space/types/app'
import type { SpaceAppResource, SpaceData } from '~/features/space/types/space'
import type { WorkflowCreate } from '~/features/space/types/workflow'

export const AppService = {
  getWorkflows(query?: WithPageQuery<WorkflowListQuery>) {
    return useRequest<PageResult<SpaceAppResource>>('/workflows', {
      query,
    })
  },

  getWorkflow(id: SpaceAppResource['id']) {
    return useRequest<IWorkflowDb>(`/workflows/${id}`)

    return Promise.resolve(mockWorkflow)
  },

  createWorkflow(payload: WorkflowCreate & { projectId: SpaceData['id'] }) {
    return useRequest('/workflows', {
      method: 'POST',
      body: payload,
    })
  },

  updateWorkflow(id: SpaceAppResource['id'], payload: WorkflowAppSetting) {
    return useRequest(`/workflows/${id}`, {
      method: 'PUT',
      body: payload,
    })
  },

  deleteWorkflow(id: SpaceAppResource['id']) {
    return useRequest(`/workflows/${id}`, {
      method: 'DELETE',
    })
  },

  duplicateWorkflow(id: SpaceAppResource['id']) {
    return useRequest('/workflows/copy', {
      method: 'POST',
      body: {
        id,
      },
    })
  },

  moveWorkflow(id: SpaceAppResource['id'], toSpaceId: SpaceData['id']) {
    return useRequest(`/workflows/move/${id}`, {
      method: 'PUT',
      body: {
        id: toSpaceId,
      },
    })
  },

  /**
   * 运行工作流
   * @param id 工作流ID
   * @param payload 运行数据
   * @returns 执行推送响应
   */
  runWorkflow(id: SpaceAppResource['id'], payload: IStartRunData) {
    return useRequest<{ data: IExecutionPushResponse }>(`/w/rest/workflows/${id}/run`, {
      method: 'POST',
      body: payload,
      headers: {
        'push-ref': useRootStore().pushRef,
      },
      responseAdapter: (data) => {
        return {
          status: ApiStatus.Success,
          result: (data as N8nResponse<IStartRunData>),
        }
      },
    })
    // return useRequest<{ data: IExecutionPushResponse }>(`/workflows/executor/${id}`, {
    //   method: 'POST',
    //   body: payload,
    //   headers: {
    //     'push-ref': useRootStore().pushRef,
    //   },
    // })
  },

  getWorkflowSettings() {
    return useRequest<{ data: FrontendSettings }>('/workflows/settings')
  },

  getExecutionData(executionId: string) {
    return useRequest<IExecutionResponse | null>(`/workflows/executions/${executionId}`)
  },

  // 社区节点相关 API
  getCommunityNodes(query?: CommunityNodeListQuery) {
    return useRequest<PageResult<CommunityNode>>('/community-nodes', {
      query,
    })
  },

  searchCommunityNodes(query: string) {
    return useRequest<CommunityNodeSearchResult[]>('/community-nodes/search', {
      query: { q: query },
    })
  },

  installCommunityNode(payload: CommunityNodeInstallPayload) {
    return useRequest<{ nodeId: string }>('/community-nodes/install', {
      method: 'POST',
      body: payload,
    })
  },

  uninstallCommunityNode(nodeId: string) {
    return useRequest(`/community-nodes/${nodeId}`, {
      method: 'DELETE',
    })
  },

  updateCommunityNode(nodeId: string, version?: string) {
    return useRequest(`/community-nodes/${nodeId}/update`, {
      method: 'PUT',
      body: { version },
    })
  },

  getCommunityNodeDetails(nodeId: string) {
    return useRequest<CommunityNode>(`/community-nodes/${nodeId}`)
  },
}
