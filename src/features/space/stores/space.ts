import { useQuery } from '@tanstack/vue-query'

import { StoreKey } from '~/constants-tw/common'
import { queryKeys } from '~/constants-tw/query-key'
import { SpaceService } from '~/features/space/services/space'
import type { SpaceData } from '~/features/space/types/space'

export const useSpaceStore = defineStore(StoreKey.Space, () => {
  const spaces = ref<SpaceData[]>([])

  const setSpaces = (spacesData: SpaceData[]) => {
    spaces.value = spacesData
  }

  const { isLoading: isLoadingSpaces, refetch } = useQuery({
    queryKey: queryKeys.space.all(),
    queryFn: () => SpaceService.getSpaces({
      page: 1,
      page_size: 100,
    }),
    enabled: false,
  })

  const fetchSpaces = async () => {
    const res = await refetch()

    setSpaces(res.data?.list ?? [])
  }

  const activeSpaceId = ref<string | null>(null)

  const setActiveSpaceId = (spaceId: string) => {
    activeSpaceId.value = spaceId
  }

  const activeSpace = computed<SpaceData | null>(() => {
    return spaces.value.find((space) => space.id === activeSpaceId.value) ?? null
  })

  const isEmptySpaces = computed(() => {
    return spaces.value.length <= 0
  })

  return {
    spaces,
    fetchSpaces,
    isLoadingSpaces,
    isEmptySpaces,

    activeSpace,

    activeSpaceId,
    setActiveSpaceId,
  }
})
