<script setup lang="ts">
import NumericDisplay from '~/components/NumericDisplay.vue'
import PopupMenu from '~/components/PopupMenu.vue'
import ProBtnMore from '~/components/pro/btn/ProBtnMore.vue'
import type { OrgItem } from '~/components/user/selector/UserSelectorPanelOrgList.vue'
import UserAvatar from '~/components/user/UserAvatar.vue'
import { queryKeys } from '~/constants-tw/query-key'
import { ProMenuActionType, ProValueType } from '~/enums/pro'
import { SpaceService } from '~/features/space/services/space'
import { useSpaceStore } from '~/features/space/stores/space'
import type { SpaceMemberListItem } from '~/features/space/types/space'

const { tableRef, tableAction } = useTableAction()
const { $confirm, $toast } = useNuxtApp()

const store = useSpaceStore()
const { activeSpace, activeSpaceId } = storeToRefs(store)

const activeUserId = ref<SpaceMemberListItem['userId']>()

const columns = computed<ProTableColumn<SpaceMemberListItem>[]>(() => [
  {
    field: 'username',
    header: '成员',
    valueType: ProValueType.Text,
    render: (data) => h('span', { class: 'gap-2 whitespace-nowrap flex-center' }, [
      h(UserAvatar, {
        avatar: data.avatar,
        size: 'large',
        username: data.name,
        class: 'overflow-hidden',
      }),
      h('span', { class: 'flex flex-col' }, [
        h('span', { class: 'font-medium' }, data.name),
        h('span', { class: 'text-secondary text-sm' }, data.appointment),
      ]),
    ]),
  },
  {
    field: 'email',
    header: '邮箱',
    valueType: ProValueType.Text,
  },
  {
    field: 'mobile',
    header: '手机号',
    valueType: ProValueType.Text,
    render: (data) => h(NumericDisplay, {
      class: 'text-secondary text-sm',
      value: data.phone ? maskMobile(data.phone) : '-',
    }),
  },
  {
    field: 'created_at',
    header: '加入时间',
    valueType: ProValueType.Date,
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', { class: 'table-action-group' }, [
      h(PopupMenu, {
        options: [
          {
            label: '详情',
            actionType: ProMenuActionType.Detail,
            command: () => {
              activeUserId.value = data.userId
            },
          },
          {
            label: '移除',
            actionType: ProMenuActionType.Delete,
            command: async () => {
              const spaceId = activeSpaceId.value

              if (spaceId) {
                $confirm.dialog({
                  message: '确定要移除该成员吗？',
                  header: '移除确认',
                  accept: async () => {
                    await SpaceService.removeSpaceMember({
                      spaceId,
                      userId: data.id,
                    })

                    $toast.success({
                      summary: '成员已移除',
                    })

                    tableAction.value?.reload()
                  },
                })
              }
            },
          },
        ],
      }, {
        default: () => h(ProBtnMore, {
          onlyIcon: true,
        }),
      }),
    ]),
  },
])

const userSelectorDialogVisible = ref(false)

const handleUserSelectorDone = async (users: OrgItem[]) => {
  if (activeSpaceId.value) {
    await SpaceService.addSpaceMember({
      spaceId: activeSpaceId.value,
      userIds: users.map((u) => u.id),
    })

    $toast.success({
      summary: '添加成员成功',
    })

    tableAction.value?.reload()

    userSelectorDialogVisible.value = false
  }
}
</script>

<template>
  <div
    v-if="activeSpace"
    class="h-full"
  >
    <ProTable
      :ref="tableRef"
      :columns="columns"
      :queryKey="queryKeys.space.members(activeSpace.id)"
      :requestFn="async (queryParams) => {
        return SpaceService.getSpaceMembers(activeSpace!.id, queryParams)
      }"
      :toolbarConfig="{
        search: {
          key: 'username',
          placeholder: '搜索成员',
        },
      }"
    >
      <template #toolbarRight>
        <div class="gap-2 py-1 flex-center">
          <div class="ml-auto gap-4 flex-center">
            <Button
              label="新增成员"
              @click="userSelectorDialogVisible = true"
            />
          </div>
        </div>
      </template>
    </ProTable>

    <UserSelectorDialog
      :userSelectorPanelProps="{
        onlyUser: true,
      }"
      :visible="userSelectorDialogVisible"
      @close="userSelectorDialogVisible = false"
      @done="handleUserSelectorDone"
    />

    <Dialog
      class="dialog-half"
      dismissableMask
      :draggable="false"
      header="用户信息"
      modal
      :visible="!!activeUserId"
      @update:visible="activeUserId = undefined"
    >
      <OrgUserDetail :userId="activeUserId" />
    </Dialog>
  </div>
</template>
