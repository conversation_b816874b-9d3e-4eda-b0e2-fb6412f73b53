<script setup lang="ts">
import { RouteKey } from '~/enums/route'
import { SpaceService } from '~/features/space/services/space'
import { useSpaceStore } from '~/features/space/stores/space'

const spaceStore = useSpaceStore()
const { activeSpace, activeSpaceId } = storeToRefs(spaceStore)

const { $confirm, $toast } = useNuxtApp()

const handleDeleteSpace = () => {
  if (activeSpaceId.value) {
    $confirm.dialog({
      header: '谨慎操作',
      message: `确定要删除空间「${activeSpace.value?.name || ''}」吗？删除后所有资产将无法恢复。`,
      acceptLabel: '确认删除',
      accept: async () => {
        await SpaceService.deleteSpace(activeSpaceId.value!)

        $toast.contrast({
          summary: '删除成功',
          detail: `空间「${activeSpace.value?.name || ''}」已删除`,
        })

        window.location.href = getRoutePath(RouteKey.TW_工作空间)
      },
    })
  }
}
</script>

<template>
  <div
    v-if="activeSpace"
    class="h-full"
  >
    <div class="flex flex-col">
      <div class="text-lg font-semibold">
        删除空间
      </div>
      <div class="pt-1 text-sm text-secondary">
        空间删除后所有资产将无法恢复，请谨慎操作
      </div>
      <div class="pt-2">
        <Button
          label="删除空间"
          variant="outlined"
          @click="handleDeleteSpace()"
        />
      </div>
    </div>
  </div>
</template>
