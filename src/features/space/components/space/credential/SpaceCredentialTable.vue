<script setup lang="ts">
import type { ICredentialType } from 'n8n-workflow'

import { useCredentialsStore } from '#wf/stores/credentials.store'

import PopupMenu from '~/components/PopupMenu.vue'
import ProBtnMore from '~/components/pro/btn/ProBtnMore.vue'
import { queryKeys } from '~/constants-tw/query-key'
import { ProMenuActionType, ProValueType } from '~/enums/pro'
import { SpaceService } from '~/features/space/services/space'
import { useSpaceStore } from '~/features/space/stores/space'
import type { SpaceCredential } from '~/features/space/types/space'

import SpaceCredentialConfigModal from './SpaceCredentialConfigModal.vue'
import SpaceCredentialModal from './SpaceCredentialModal.vue'

const { tableRef, tableAction } = useTableAction()
const { $confirm, $toast } = useNuxtApp()

const spaceStore = useSpaceStore()
const { activeSpace, activeSpaceId } = storeToRefs(spaceStore)

const credentialStore = useCredentialsStore()

const modalVisible = ref(false)
const configModalVisible = ref(false)

const selectedCredentialType = ref<ICredentialType['displayName']>()

const editState = ref<FormEditState>()

const columns = computed<ProTableColumn<SpaceCredential>[]>(() => [
  {
    field: 'name',
    header: '凭证名称',
    valueType: ProValueType.Text,
    render: (data) => h('span', { class: 'gap-2 whitespace-nowrap flex-center' }, [
      h('span', { class: 'font-medium' }, data.name),
    ]),
  },
  {
    field: 'type',
    header: 'type',
    valueType: ProValueType.Text,
  },
  {
    field: 'updatedAt',
    header: '最近更新',
    valueType: ProValueType.Date,
  },
  {
    class: 'min-w-table-actions',
    valueType: ProValueType.ActionGroup,
    render: (data) => h('div', { class: 'table-action-group' }, [
      h(PopupMenu, {
        options: [
          {
            label: '详情',
            actionType: ProMenuActionType.Detail,
            command: () => {
              selectedCredentialType.value = data.id
              editState.value = 'update'
              configModalVisible.value = true
            },
          },
          {
            label: '移除',
            actionType: ProMenuActionType.Delete,
            command: async () => {
              const spaceId = activeSpaceId.value

              if (spaceId) {
                $confirm.dialog({
                  message: '确定要移除该凭证吗？',
                  header: '移除确认',
                  accept: async () => {
                    await credentialStore.deleteCredential({ id: data.id })

                    $toast.success({
                      summary: '凭证已移除',
                    })

                    tableAction.value?.reload()
                  },
                })
              }
            },
          },
        ],
      }, {
        default: () => h(ProBtnMore, {
          onlyIcon: true,
        }),
      }),
    ]),
  },
])

const resetState = () => {
  editState.value = undefined
  selectedCredentialType.value = undefined
}

const handleCloseDialog = () => {
  resetState()
  modalVisible.value = false
}

const handleCloseConfigDialog = () => {
  resetState()
  configModalVisible.value = false
}

const handleNext = (credentialType: ICredentialType['displayName']) => {
  handleCloseDialog()

  editState.value = 'create'
  selectedCredentialType.value = credentialType
  configModalVisible.value = true
}

const handleSaveSuccess = async () => {
  tableAction.value?.reload()
  handleCloseConfigDialog()
}
</script>

<template>
  <div
    v-if="activeSpace"
    class="h-full"
  >
    <ProTable
      :ref="tableRef"
      :columns="columns"
      :queryKey="queryKeys.space.credentials({ projectId: activeSpace!.id })"
      :requestFn="async (queryParams) => {
        return SpaceService.getSpaceCredentials({ ...queryParams, projectId: activeSpace!.id })
      }"
      :toolbarConfig="{
        search: {
          key: 'name',
          placeholder: '搜索凭证',
        },
      }"
    >
      <template #toolbarRight>
        <div class="gap-2 py-1 flex-center">
          <div class="ml-auto gap-4 flex-center">
            <Button
              label="新增凭证"
              @click="modalVisible = true"
            />
          </div>
        </div>
      </template>
    </ProTable>

    <SpaceCredentialModal
      :visible="modalVisible"
      @close="handleCloseDialog()"
      @next="handleNext"
    />

    <SpaceCredentialConfigModal
      :activeCredentialId="selectedCredentialType"
      :editState="editState"
      :visible="configModalVisible"
      @close="handleCloseConfigDialog"
      @saveSuccess="handleSaveSuccess"
    />
  </div>
</template>
