<script setup lang="ts">
import type { ICredentialType } from 'n8n-workflow'

import { useCredentialsStore } from '#wf/stores/credentials.store'

defineProps<{
  visible?: boolean
}>()

const emit = defineEmits<{
  close: []
  next: [ICredentialType['displayName']]
}>()

const loading = ref(true)

const credentialsStore = useCredentialsStore()

onMounted(async () => {
  try {
    await credentialsStore.fetchCredentialTypes(false)
  }
  finally {
    loading.value = false
  }
})

const selectedCredentialType = ref<ICredentialType['displayName']>()
const searchValue = ref<string>()

const resetState = () => {
  selectedCredentialType.value = undefined
}

const handleClose = () => {
  resetState()
  emit('close')
}

const handelVisibleChange = (visible: boolean) => {
  if (!visible) {
    handleClose()
  }
}

const options = computed(() => {
  const all = credentialsStore.allCredentialTypes.map((type) => ({
    label: type.displayName,
    value: type.name,
  }))

  if (searchValue.value) {
    return all.filter((type) => type.label.toLowerCase().includes(searchValue.value!.toLowerCase()))
  }

  return all
})

const handleConfirm = () => {
  if (selectedCredentialType.value) {
    emit('next', selectedCredentialType.value)
    resetState()
  }
}
</script>

<template>
  <Dialog
    class="dialog-form"
    header="新增凭证"
    modal
    :visible="visible"
    @update:visible="handelVisibleChange"
  >
    <div class="flex flex-col gap-form-field">
      <FormItem label="选择要连接的应用或服务">
        <ProSelect
          v-model="selectedCredentialType"
          v-model:searchValue="searchValue"
          :loading="loading"
          :options="options"
          placeholder="选择或搜索..."
          :search="{
            enable: true,
          }"
          showClear
        />
      </FormItem>

      <FormActionGroup
        :confirmButtonProps="{
          label: '下一步',
          disabled: !selectedCredentialType,
        }"
        @cancel="handleClose()"
        @confirm="handleConfirm"
      />
    </div>
  </Dialog>
</template>
