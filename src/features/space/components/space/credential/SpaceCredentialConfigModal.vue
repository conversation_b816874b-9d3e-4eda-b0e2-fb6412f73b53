<script setup lang="ts">
import type { CredentialInformation, ICredentialDataDecryptedObject, ICredentialsDecrypted, ICredentialType, INodeParameters, INodeProperties } from 'n8n-workflow'
import * as NodeHelpers from 'n8n-workflow/dist/NodeHelpers.js'

import CredentialConfig from '#wf/components/credential/CredentialConfig.vue'
import CredentialIcon from '#wf/components/credential/CredentialIcon.vue'
import { useNodeHelpers } from '#wf/composables/useNodeHelpers'
import { credentialsService } from '#wf/services/credentials'
import { useCredentialsStore } from '#wf/stores/credentials.store'
import { useNDVStore } from '#wf/stores/ndv.store'
import { useNodeTypesStore } from '#wf/stores/nodeTypes.store'
import { useSettingsStore } from '#wf/stores/setting.store'
import type { ICredentialsDecryptedResponse, ICredentialsResponse, IUpdateInformation } from '#wf/types/interface'
import { isExpression, isTestableExpression } from '#wf/utils/expressions'
import { getNodeAuthOptions, getNodeCredentialForSelectedAuthType } from '#wf/utils/nodeTypesUtils'

import { SpaceService } from '~/features/space/services/space'
import { useSpaceStore } from '~/features/space/stores/space'
import type { CreateSpaceCredentialDto, UpdateSpaceCredentialDto } from '~/features/space/types/space'
import type { FormEditState } from '~/types/form'

const props = defineProps<{
  visible?: boolean
  activeCredentialId?: ICredentialType['name']
  editState?: FormEditState
}>()

const credentialId = computed(() => props.activeCredentialId ?? '')

const DEFAULT_CREDENTIAL_NAME = '未命名凭证'
const DEFAULT_CREDENTIAL_POSTFIX = '账号'

const emit = defineEmits<{
  close: []
  saveSuccess: []
}>()

const authError = ref('')

const handleDialogClose = () => {
  emit('close')
  authError.value = ''
}

const credentialsStore = useCredentialsStore()
const ndvStore = useNDVStore()
const nodeTypesStore = useNodeTypesStore()
const settingsStore = useSettingsStore()

const spaceStore = useSpaceStore()
const { activeSpaceId } = storeToRefs(spaceStore)

const nodeHelpers = useNodeHelpers()

const { $toast } = useNuxtApp()

const selectedCredential = ref('')
const credentialName = ref('')
const currentCredential = ref<ICredentialsResponse | ICredentialsDecryptedResponse | null>(null)
const credentialData = ref<ICredentialDataDecryptedObject>({})

const activeNodeType = computed(() => {
  const activeNode = ndvStore.activeNode

  if (activeNode) {
    return nodeTypesStore.getNodeType(activeNode.type, activeNode.typeVersion)
  }

  return null
})

// 凭证对节点来说是否是必填的
const requiredCredentials = ref(false)

const selectedCredentialType = computed(() => {
  if (props.editState !== 'create') {
    return null
  }

  if (selectedCredential.value !== '') {
    return credentialsStore.getCredentialTypeByName(selectedCredential.value) ?? null
  }
  else if (requiredCredentials.value) {
    // Otherwise, use credential type that corresponds to the first auth option in the node definition
    const nodeAuthOptions = getNodeAuthOptions(activeNodeType.value)

    // But only if there is zero or one auth options available
    if (nodeAuthOptions.length > 0 && activeNodeType.value?.credentials) {
      return getNodeCredentialForSelectedAuthType(activeNodeType.value, nodeAuthOptions[0].value)
    }
    else {
      return activeNodeType.value?.credentials ? activeNodeType.value.credentials[0] : null
    }
  }

  return null
})

const credentialTypeName = computed(() => {
  if (props.editState === 'update') {
    if (currentCredential.value) {
      return currentCredential.value.type
    }

    return null
  }

  if (selectedCredentialType.value) {
    return selectedCredentialType.value.name
  }

  return `${props.activeCredentialId}`
})

const getCredentialProperties = (name: string): INodeProperties[] => {
  const credentialTypeData = credentialsStore.getCredentialTypeByName(name)

  if (!credentialTypeData) {
    return []
  }

  if (credentialTypeData.extends === undefined) {
    return credentialTypeData.properties
  }

  const combineProperties = [] as INodeProperties[]

  for (const credentialsTypeName of credentialTypeData.extends) {
    const mergeCredentialProperties = getCredentialProperties(credentialsTypeName)
    NodeHelpers.mergeNodeProperties(combineProperties, mergeCredentialProperties)
  }

  // The properties defined on the parent credentials take precedence
  NodeHelpers.mergeNodeProperties(combineProperties, credentialTypeData.properties)

  return combineProperties
}

const credentialType = computed(() => {
  if (!credentialTypeName.value) {
    return null
  }

  const type = credentialsStore.getCredentialTypeByName(credentialTypeName.value)

  if (!type) {
    return null
  }

  return {
    ...type,
    properties: getCredentialProperties(credentialTypeName.value),
  }
})

const loadCurrentCredential = async () => {
  try {
    const { data: currentCredentials } = await credentialsService.getCredentialData(credentialId.value)

    if (!currentCredentials) {
      throw new Error(
        `凭证不存在: ${credentialId.value}`,
      )
    }

    currentCredential.value = currentCredentials

    credentialData.value = currentCredentials.data || {}

    if (currentCredentials.sharedWithProjects) {
      credentialData.value = {
        ...credentialData.value,
        sharedWithProjects: currentCredentials.sharedWithProjects,
      }
    }

    if (currentCredentials.homeProject) {
      credentialData.value = {
        ...credentialData.value,
        homeProject: currentCredentials.homeProject,
      }
    }

    credentialName.value = currentCredentials.name
  }
  catch (err) {
    $toast.error({
      summary: '加载凭证失败',
      detail: err instanceof Error ? err.message : String(err),
    })

    handleDialogClose()

    return
  }
}

const defaultCredentialTypeName = computed(() => {
  let defaultName = credentialTypeName.value

  if (!defaultName || defaultName === 'null') {
    if (activeNodeType.value?.credentials && activeNodeType.value.credentials.length > 0) {
      defaultName = activeNodeType.value.credentials[0].name
    }
  }

  return defaultName ?? ''
})

const initData = async () => {
  const credentialTypeData = credentialsStore.getCredentialTypeByName(defaultCredentialTypeName.value)

  if (props.editState === 'create') {
    credentialName.value = credentialTypeData?.displayName
      ? `${credentialTypeData.displayName} ${DEFAULT_CREDENTIAL_POSTFIX}`
      : DEFAULT_CREDENTIAL_NAME
  }
  else {
    await loadCurrentCredential()
  }
}

const displayCredentialParameter = (parameter: INodeProperties): boolean => {
  if (parameter.type === 'hidden') {
    return false
  }

  if (parameter.displayOptions?.hideOnCloud && settingsStore.isCloudDeployment) {
    return false
  }

  if (parameter.displayOptions === undefined) {
    // If it is not defined no need to do a proper check
    return true
  }

  return nodeHelpers.displayParameter(credentialData.value as INodeParameters, parameter, '', null)
}

const credentialProperties = computed(() => {
  const type = credentialType.value

  if (!type) {
    return []
  }

  const properties = type.properties.filter((propertyData: INodeProperties) => {
    if (!displayCredentialParameter(propertyData)) {
      return false
    }

    return (
      !type.__overwrittenProperties || !type.__overwrittenProperties.includes(propertyData.name)
    )
  })

  /**
   * 如果在应用所有凭证覆盖后只剩下 "notice" 属性，
   * 则不返回它们。这将避免显示引用已被覆盖属性的通知。
   */
  if (properties.every((p) => p.type === 'notice')) {
    return []
  }

  return properties
})

const isEditingManagedCredential = computed(() => {
  if (credentialId.value) {
    return credentialsStore.getCredentialById(credentialId.value)?.isManaged ?? false
  }

  return false
})

const requiredPropertiesFilled = computed(() => {
  for (const property of credentialProperties.value) {
    if (property.required !== true) {
      continue
    }

    const credentialProperty = credentialData.value[property.name]

    if (property.type === 'string' && !credentialProperty) {
      return false
    }

    if (property.type === 'number') {
      const containsExpression = typeof credentialProperty === 'string' && credentialProperty.startsWith('=')

      if (typeof credentialProperty !== 'number' && !containsExpression) {
        return false
      }
    }
  }

  return true
})

const getParentTypes = (name: string): string[] => {
  const type = credentialsStore.getCredentialTypeByName(name)

  if (type?.extends === undefined) {
    return []
  }

  const types: string[] = []

  for (const typeName of type.extends) {
    types.push(typeName)
    types.push.apply(types, getParentTypes(typeName)) // eslint-disable-line prefer-spread
  }

  return types
}

const parentTypes = computed(() => {
  if (credentialTypeName.value) {
    return getParentTypes(credentialTypeName.value)
  }

  return []
})

const isOAuthType = computed(() => {
  return (
    !!credentialTypeName.value
    && (((credentialTypeName.value === 'oAuth2Api' || parentTypes.value.includes('oAuth2Api'))
      && (credentialData.value.grantType === 'authorizationCode'
        || credentialData.value.grantType === 'pkce'))
      || credentialTypeName.value === 'oAuth1Api'
      || parentTypes.value.includes('oAuth1Api'))
  )
})

const nodesWithAccess = computed(() => {
  if (credentialTypeName.value) {
    return credentialsStore.getNodesWithAccess(credentialTypeName.value)
  }

  return []
})

const isCredentialTestable = computed(() => {
  if (isOAuthType.value || !requiredPropertiesFilled.value) {
    return false
  }

  const hasUntestableExpressions = Object.values(credentialData.value).reduce(
    (accu: boolean, value: CredentialInformation) =>
      accu || (typeof value === 'string' && isExpression(value) && !isTestableExpression(value)),
    false,
  )

  if (hasUntestableExpressions) {
    return false
  }

  const nodesThatCanTest = nodesWithAccess.value.filter((node) => {
    if (node.credentials) {
      // 返回可以测试此凭证的节点列表
      const eligibleTesters = node.credentials.filter((credential) => {
        return credential.name === credentialTypeName.value && credential.testedBy
      })

      // 如果有任何节点可以测试，则返回 true
      return !!eligibleTesters.length
    }

    return false
  })

  return !!nodesThatCanTest.length || (!!credentialType.value && !!credentialType.value.test)
})

const testCredential = async (credentialDetails: ICredentialsDecrypted) => {
  const result = await credentialsStore.testCredential(credentialDetails)

  if (result.status === 'Error') {
    authError.value = result.message

    throw new Error(result.message)
  }
  else {
    authError.value = ''
  }
}

const isTesting = ref(false)

const {
  loading,
  isFormCreate,
  isFormUpdate,
  openCreateModal,
  openUpdateModal,
  modalVisible,
  handleClose,
  handleSubmit,
  confirmBtnLabel,
} = useFormControl<CreateSpaceCredentialDto, UpdateSpaceCredentialDto>({
  btnLabel: {
    create: '确认创建',
    update: '确认保存',
  },
  onSubmit: async ({ valid }) => {
    if (valid) {
      const payload = {
        name: credentialName.value,
        type: credentialTypeName.value ?? '',
        data: credentialData.value,
      } satisfies CreateSpaceCredentialDto

      authError.value = ''

      if (isFormCreate.value) {
        if (activeSpaceId.value) {
          await SpaceService.createSpaceCredential({
            ...payload,
            projectId: activeSpaceId.value,
          })

          $toast.success({
            summary: '添加凭证成功',
          })

          emit('saveSuccess')
        }
      }
      else if (isFormUpdate.value) {
        await credentialsStore.updateCredential({
          id: credentialId.value,
          data: { ...payload, id: credentialId.value } as ICredentialsDecrypted,
        })

        $toast.success({
          summary: '更新凭证成功',
        })

        emit('saveSuccess')
      }
    }
  },
})

// 添加单独的测试连接功能
const handleTestConnection = async () => {
  if (!isCredentialTestable.value) {
    $toast.warn({
      summary: '无法测试',
      detail: '当前凭证类型不支持测试或缺少必填字段',
    })

    return
  }

  const payload = {
    name: credentialName.value,
    type: credentialTypeName.value ?? '',
    data: credentialData.value,
  } satisfies CreateSpaceCredentialDto

  isTesting.value = true

  try {
    await testCredential({ id: credentialId.value, ...payload })

    $toast.success({
      summary: '测试连接成功',
      detail: '凭证配置正确，连接测试通过',
    })
  }
  catch (error) {
    $toast.error({
      summary: '测试连接失败',
      detail: error instanceof Error ? error.message : String(error),
    })
  }
  finally {
    isTesting.value = false
  }
}

const handelVisibleChange = (visible: boolean) => {
  if (!visible) {
    handleDialogClose()
  }
}

watch(() => props.visible, async (isVisible) => {
  if (isVisible) {
    if (props.editState === 'create') {
      openCreateModal()
    }
    else if (props.editState === 'update') {
      openUpdateModal({
        name: credentialName.value,
        type: credentialTypeName.value ?? '',
        data: credentialData.value,
      })
    }

    await initData()
  }
  else {
    handleClose()
  }
})

const handleValueChange = ({ name, value }: IUpdateInformation) => {
  // 如果新值与当前值相同，则跳过更新
  if (credentialData.value[name] === value) {
    return
  }

  const { oauthTokenData, ...credData } = credentialData.value

  credentialData.value = {
    ...credData,
    [name]: value as CredentialInformation,
  }
}
</script>

<template>
  <ProDialogForm
    class="dialog-form-wider"
    :draggable="false"
    :formActionGroupProps="{
      cancelButtonProps: false,
      confirmButtonProps: {
        label: confirmBtnLabel,
      },
    }"
    :loading="loading"
    :visible="modalVisible"
    @submit="handleSubmit"
    @update:visible="handelVisibleChange"
  >
    <template #header>
      <div class="gap-3 flex-center">
        <CredentialIcon :credentialTypeName="credentialTypeName" />

        <div>
          <EditableTitle v-model="credentialName" />

          <div
            v-if="credentialType"
            class="text-sm text-secondary"
          >
            {{ credentialType.displayName }}
          </div>
        </div>
      </div>
    </template>

    <template #extraButtons>
      <Button
        v-if="isCredentialTestable"
        label="测试连接"
        :loading="isTesting"
        severity="secondary"
        @click="handleTestConnection"
      />
    </template>

    <CredentialConfig
      :authError="authError"
      :credentialData="credentialData"
      :credentialProperties="credentialProperties"
      :credentialType="credentialType"
      :isManage="isEditingManagedCredential"
      @valueChange="handleValueChange"
    />
  </ProDialogForm>
</template>
