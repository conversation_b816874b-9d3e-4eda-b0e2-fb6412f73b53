<script setup lang="ts">
import { InjectKey } from '~/constants-tw/common'
import { useSpaceStore } from '~/features/space/stores/space'
import type { SpaceAppResource } from '~/features/space/types/space'

interface DialogRef {
  close: (selectedSpaceId?: SpaceAppResource['id']) => void
}

const dialogRef = inject<Ref<DialogRef | undefined>>(InjectKey.PrimeVueDialog, ref())

const spaceStore = useSpaceStore()
const { spaces, activeSpaceId } = storeToRefs(spaceStore)

const selectedSpaceId = ref<SpaceAppResource['id']>()

watch(activeSpaceId, (val) => {
  selectedSpaceId.value = val ?? undefined
}, { immediate: true })

const handleSelectSpace = (val?: SpaceAppResource['id']) => {
  selectedSpaceId.value = val
}

const handleCancel = () => {
  dialogRef.value?.close()
}

const handleConfirm = () => {
  dialogRef.value?.close(selectedSpaceId.value)
}
</script>

<template>
  <div class="space-y-form-field">
    <FormItem label="选择空间">
      <ProSelect
        :modelValue="selectedSpaceId"
        :options="spaces.map(it => ({
          label: it.name,
          value: it.id,
        }))"
        @update:modelValue="handleSelectSpace"
      />
    </FormItem>

    <FormItem>
      <FormActionGroup
        :confirmButtonProps="{
          label: '确定',
        }"
        @cancel="handleCancel()"
        @confirm="handleConfirm()"
      />
    </FormItem>
  </div>
</template>
