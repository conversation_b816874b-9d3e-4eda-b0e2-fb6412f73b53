<script setup lang="ts">
import { ChevronDownIcon } from 'lucide-vue-next'
import type { MenuItem as PrimeMenuItem } from 'primevue/menuitem'

import type ProPrimePopover from '~/components/pro/ProPrimePopover.vue'
import { GlobalEvent } from '~/enums/event'
import { ProMenuActionType } from '~/enums/pro'
import { routeConfig, RouteKey } from '~/enums/route'
import { AUTH } from '~/enums/user'
import SpaceIcon from '~/features/space/components/space/SpaceIcon.vue'
import { useSpaceStore } from '~/features/space/stores/space'

const { refKey: popoverRefKey, ref: popoverRef } = useRef<InstanceType<typeof ProPrimePopover>>()

const userStore = useUserStore()

const spaceStore = useSpaceStore()
const { spaces, activeSpace, activeSpaceId } = storeToRefs(spaceStore)

const handleClosePopover = () => {
  popoverRef.value?.hide()
}

const spaceOptions = computed(() => {
  const opts = spaces.value.map<ProMenuItem>((it) => ({
    label: it.name,
    command: () => {
      handleClosePopover()
      navigateTo(getRoutePath(RouteKey.TW_工作空间_ID, { spaceId: it.id }))
    },
    active: it.id === activeSpace.value?.id,
  }))

  if (opts.length > 0) {
    opts.push({
      actionType: ProMenuActionType.Separator,
    })
  }

  opts.push({
    actionType: ProMenuActionType.Add,
    label: '新建工作空间',
    command: () => {
      handleClosePopover()
      emitter.emit(GlobalEvent.SpaceCreate)
    },
  })

  return opts
})

const menuItems: PrimeMenuItem[] = [
  {
    key: RouteKey.TW_工作空间_应用,
    label: '工作流应用',
    data: { icon: getRouteIcon(RouteKey.TW_工作空间_应用) },
    visible: userStore.hasPermission([AUTH.WORKFLOW_READ]),
  },
  {
    key: RouteKey.TW_工作空间_知识库,
    label: '知识库',
    data: { icon: getRouteIcon(RouteKey.TW_工作空间_知识库) },
    visible: userStore.hasPermission([AUTH.KNOWLEDGE_BASE_MANAGE, AUTH.KNOWLEDGE_BASE_READ]),
  },
  {
    key: RouteKey.TW_工作空间_设置,
    label: routeConfig[RouteKey.TW_工作空间_设置].title,
    data: { icon: getRouteIcon(RouteKey.TW_工作空间_设置) },
  },
  {
    key: RouteKey.TW_工作空间_凭证,
    label: routeConfig[RouteKey.TW_工作空间_凭证].title,
    data: { icon: getRouteIcon(RouteKey.TW_工作空间_凭证) },
  },
]

const { activeRouteKey: selectedMenuKey } = useNavMenu()

const handleMenuSelect = (menuItem: PrimeMenuItem) => {
  if (activeSpaceId.value) {
    if (menuItem.key) {
      navigateTo(getRoutePath(menuItem.key as RouteKey, { spaceId: activeSpaceId.value }))
    }
  }
}
</script>

<template>
  <div
    v-if="activeSpace"
    class="flex h-full w-64 flex-col"
  >
    <div class="px-card-container py-admin-layout-content">
      <ProPrimePopover
        :ref="popoverRefKey"
        :popoverProps="{
          pt: {
            content: '!p-1.5',
          },
        }"
        rootClass="w-full"
        triggerClass="w-full"
      >
        <template #default="{ isPopoverShow }: { isPopoverShow: boolean }">
          <Button
            class="!p-1 !font-medium"
            :class="{ '!bg-emphasis': isPopoverShow }"
            fluid
            severity="contrast"
            size="large"
            variant="text"
          >
            <span class="w-full gap-2 flex-center">
              <SpaceIcon />

              <span class="flex-1 truncate text-left text-[13px]">
                {{ activeSpace.name }}
              </span>

              <ChevronDownIcon
                :size="14"
                :strokeWidth="2.5"
              />
            </span>
          </Button>
        </template>

        <template #content>
          <div class="max-w-[320px]">
            <div class="p-1 text-sm font-medium text-secondary">
              工作空间
            </div>

            <ProMenu
              isEmbed
              :model="spaceOptions"
            />
          </div>
        </template>
      </ProPrimePopover>
    </div>

    <div class="flex-1 px-card-container">
      <SideMenuPanel
        :menuItems="menuItems"
        :selectedKey="selectedMenuKey"
        @select="handleMenuSelect"
      />
    </div>
  </div>
</template>
