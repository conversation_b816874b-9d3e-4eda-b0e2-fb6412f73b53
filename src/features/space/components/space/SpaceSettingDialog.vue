<script setup lang="ts">
import type { InputText } from 'primevue'
import { object } from 'valibot'

import { GlobalEvent } from '~/enums/event'
import { SpaceService } from '~/features/space/services/space'
import { useSpaceStore } from '~/features/space/stores/space'
import type { SpaceAppResource, SpaceSettingForm } from '~/features/space/types/space'

const spaceStore = useSpaceStore()
const { isEmptySpaces } = storeToRefs(spaceStore)

const { $toast } = useNuxtApp()

const { refKey, ref: inputRef } = useRef<
  InstanceType<typeof InputText> & { $el: HTMLInputElement }
>()

const {
  loading,
  isFormCreate,
  isFormUpdate,
  formTitleText,
  formValues,
  updatingItem,
  modalVisible,
  formResolver,
  openCreateModal,
  openUpdateModal,
  handleClose,
  handleSubmit,
  confirmBtnLabel,
  handleModalVisibleChange,
} = useFormControl<SpaceSettingForm, SpaceAppResource>({
  resolver: object({
    name: schemaNotEmptyString('请输入工作空间名称'),
  }),
  btnLabel: {
    create: '确认新建',
    update: '确认保存',
  },
  formTitle: {
    create: '新建工作空间',
    update: '编辑工作空间',
  },
  fetchDetail: (it) => toRaw(it),
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const payload: SpaceSettingForm = {
        name: states.name.value,
      }

      if (isFormCreate.value) {
        await SpaceService.createSpace(payload)

        $toast.success({
          summary: '创建成功',
          detail: `工作空间「${payload.name}」已创建`,
        })

        if (isEmptySpaces.value) {
          window.location.reload()
        }
      }
      else {
        if (isFormUpdate.value) {
          const id = updatingItem.value?.id

          if (id) {
            await SpaceService.updateSpace(id, payload)
          }
        }
      }
    }
  },
  onSubmitSuccess: () => {
    spaceStore.fetchSpaces()
  },
  onModalVisibleChange: (isVisible) => {
    if (isVisible) {
      nextTick(() => {
        // HACK: 等待表单渲染完成后，再聚焦到输入框
        setTimeout(() => {
          inputRef.value?.$el?.focus()
        }, 200)
      })
    }
  },
})

const { on } = useEmitter()

onMounted(() => {
  on(GlobalEvent.SpaceCreate, () => {
    openCreateModal()
  })

  on(GlobalEvent.SpaceUpdate, (defaultValues) => {
    if (defaultValues) {
      openUpdateModal(defaultValues)
    }
  })
})
</script>

<template>
  <ProDialogForm
    :dialogProps="{
      class: 'dialog-form-wide',
    }"
    :formActionGroupProps="{
      confirmButtonProps: {
        label: confirmBtnLabel,
      },
    }"
    :formProps="{ resolver: formResolver }"
    :initialValues="formValues"
    :loading="loading"
    :title="formTitleText"
    :visible="modalVisible"
    @cancel="handleClose"
    @submit="handleSubmit"
    @update:visible="handleModalVisibleChange"
  >
    <div class="flex flex-col gap-form-field">
      <div
        v-if="isFormCreate"
        class="pt-1"
      >
        <Message
          severity="secondary"
          size="small"
        >
          通过创建工作空间，将支持工作流在工作空间内进行协作和共享。
        </Message>
      </div>

      <FormItem
        label="工作空间名称"
        name="name"
        required
      >
        <InputText
          :ref="refKey"
          fluid
        />
      </FormItem>

      <!-- <FormItem label="工作空间描述">
        <Textarea fluid />
      </FormItem> -->
    </div>
  </ProDialogForm>
</template>
