<template>
  <div class="pointer-events-none absolute inset-0">
    <div class="workflow-card-background absolute inset-0" />
  </div>
</template>

<style scoped>
.workflow-card-background {
  @apply [--color-card-background:theme('colors.node-accent.100')] dark:[--color-card-background:theme('colors.node-accent.950')];

  background-image: radial-gradient(circle at 70% -60%, var(--color-card-background) 0%, transparent 65%);
}
</style>
