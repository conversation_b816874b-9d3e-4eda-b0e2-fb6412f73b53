<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { FilePlus2Icon, FileSymlinkIcon, FolderInputIcon, PackageIcon } from 'lucide-vue-next'

import { queryKeys } from '~/constants-tw/query-key'
import { DateFormat } from '~/enums/common'
import { GlobalEvent } from '~/enums/event'
import AppListItem from '~/features/space/components/app/list/AppListItem.vue'
import AppListItemBox from '~/features/space/components/app/list/AppListItemBox.vue'
import SimpleCommunityNodeManager from '~/features/space/components/app/SimpleCommunityNodeManager.vue'
import { AppService } from '~/features/space/services/app'
import { useSpaceStore } from '~/features/space/stores/space'
import { selectFile } from '~/utils/file'

const spaceStore = useSpaceStore()
const { activeSpaceId } = storeToRefs(spaceStore)

const { $toast } = useNuxtApp()

// 社区节点管理对话框状态
const showCommunityNodeManager = ref(false)

const { data, refetch } = useQuery({
  queryKey: queryKeys.space.list(activeSpaceId),
  queryFn: () => {
    if (activeSpaceId.value) {
      return AppService.getWorkflows({
        page: 1,
        page_size: 100,
        projectId: activeSpaceId.value,
      })
    }

    return null
  },
})

const { on } = useEmitter()

onMounted(() => {
  on(GlobalEvent.AppListRefresh, () => {
    refetch()
  })
})

// 处理导入工作流文件
const handleImportWorkflow = () => {
  selectFile({
    accept: '.json',
    onRead: async (content) => {
      try {
        const workflowData = JSON.parse(content as string)

        // 确保导入的工作流有名称
        const workflowName = workflowData.name || `导入的工作流-${formatDate(new Date(), DateFormat.YYYY_MM_DD_HH_MM)}`

        if (activeSpaceId.value) {
          await AppService.createWorkflow({
            ...workflowData,
            name: workflowName,
            projectId: activeSpaceId.value,
          })

          $toast.success({
            summary: '工作流已导入',
          })

          emitter.emit(GlobalEvent.AppListRefresh)
        }
      }
      catch {
        $toast.error({
          summary: '导入工作流失败',
          detail: '文件格式不正确或内容已损坏，请检查文件内容',
        })
      }
    },
    onError: () => {
      $toast.error({
        summary: '读取文件失败',
      })
    },
  })
}

const creatorOptions: ProMenuItem[] = [
  {
    label: '创建空白工作流',
    itemIcon: FilePlus2Icon,
    command: () => {
      emitter.emit(GlobalEvent.AppCreate)
    },
  },
  {
    label: '导入工作流文件',
    itemIcon: FolderInputIcon,
    command: () => {
      handleImportWorkflow()
    },
  },
  {
    label: '从模板创建',
    disabled: true,
    itemIcon: FileSymlinkIcon,
  },
  {
    label: '管理社区节点',
    itemIcon: PackageIcon,
    command: () => {
      showCommunityNodeManager.value = true
    },
  },
]
</script>

<template>
  <div class="@container">
    <div class="grid grid-cols-1 gap-4 @lg:grid-cols-2 @3xl:grid-cols-3 @4xl:grid-cols-5">
      <AppListItemBox>
        <div class="p-2">
          <div class="py-2 pl-3 font-semibold">
            创建工作流
          </div>

          <div class="pt-2">
            <ProMenu
              isEmbed
              :model="creatorOptions"
              :pt="{
                root: '!min-w-0',
              }"
            />
          </div>
        </div>
      </AppListItemBox>

      <AppListItem
        v-for="it of data?.list"
        :key="it.id"
        :itemData="it"
      />
    </div>

    <!-- 社区节点管理对话框 -->
    <SimpleCommunityNodeManager
      v-model:visible="showCommunityNodeManager"
    />
  </div>
</template>
