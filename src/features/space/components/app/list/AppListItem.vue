<script setup lang="ts">
import { DateFormat } from '~/enums/common'
import { RouteKey } from '~/enums/route'
import AppActionMenu from '~/features/space/components/app/AppActionMenu.vue'
import AppIcon from '~/features/space/components/app/AppIcon.vue'
import AppListItemBackground from '~/features/space/components/app/list/AppListItemBackground.vue'
import AppListItemBox from '~/features/space/components/app/list/AppListItemBox.vue'
import { useSpaceStore } from '~/features/space/stores/space'
import type { SpaceAppResource } from '~/features/space/types/space'

interface Props {
  itemData: SpaceAppResource
}

const props = defineProps<Props>()

const spaceStore = useSpaceStore()
const { activeSpaceId } = storeToRefs(spaceStore)

const handleClick = (useAuxClick?: boolean) => {
  if (activeSpaceId.value) {
    const targetPath = getRoutePath(RouteKey.TW_应用_工作流, {
      spaceId: activeSpaceId.value,
      workflowId: props.itemData.id,
    })

    if (useAuxClick) {
      window.open(targetPath, '_blank')
    }
    else {
      navigateTo(targetPath)
    }
  }
}

const isMenuOpen = ref(false)
</script>

<template>
  <AppListItemBox
    class="group/app-item cursor-pointer shadow-surface-200 dark:!shadow-none"
    :class="[
      isMenuOpen ? 'shadow' : 'hover:shadow',
    ]"
    @auxclick="handleClick(true)"
    @click="handleClick()"
  >
    <div class="h-full p-2">
      <div class="relative h-full overflow-hidden rounded-xl">
        <div class="relative z-10 h-full p-2">
          <div class="py-5">
            <AppIcon />
          </div>

          <div class="font-medium">
            {{ itemData.name }}
          </div>

          <div
            class="pt-1 text-xs text-secondary"
            :title="formatDate(itemData.updatedAt, DateFormat.YYYY_MM_DD_HH_MM_SS)"
          >
            最近更新：{{ formatDate(itemData.updatedAt, DateFormat.YYYY_MM_DD) }}
          </div>

          <div
            class="invisible absolute bottom-0 right-0 z-10 inline-flex items-center bg-content group-hover/app-item:visible"
            :class="{
              '!visible': isMenuOpen,
            }"
            @click.stop
          >
            <AppActionMenu
              :itemData="itemData"
              @hide="isMenuOpen = false"
              @open="isMenuOpen = true"
            />
          </div>
        </div>

        <AppListItemBackground />
      </div>
    </div>
  </AppListItemBox>
</template>
