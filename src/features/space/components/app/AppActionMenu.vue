<script setup lang="ts">
import { useAppActions } from '~/features/space/composables/useAppActions'
import type { SpaceAppResource } from '~/features/space/types/space'

interface Props {
  itemData: SpaceAppResource
  placement?: 'canvas' | 'list'
}

const props = withDefaults(defineProps<Props>(), {
  placement: 'list',
})

const { getActionOptions } = useAppActions()

const options = computed(() => getActionOptions(props.itemData, props.placement))

const isMenuOpen = ref(false)
</script>

<template>
  <PopupMenu
    :options="options"
    @hide="isMenuOpen = false"
    @open="isMenuOpen = true"
  >
    <slot>
      <ProBtnMore
        :highlight="isMenuOpen"
        onlyIcon
        size="small"
        :tooltip="false"
      />
    </slot>
  </PopupMenu>
</template>
