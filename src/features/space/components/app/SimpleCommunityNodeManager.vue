<script setup lang="ts">
import { PackageIcon, ShieldCheckIcon, XCircleIcon } from 'lucide-vue-next'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { $toast } = useNuxtApp()

// 响应式状态
const selectedTab = ref<'installed' | 'browse'>('installed')
const installPackageName = ref('')
const installVersion = ref('')
const showInstallDialog = ref(false)
const riskAccepted = ref(false)

// 模拟数据
const installedNodes = ref([
  {
    id: '1',
    name: 'Discord 节点',
    packageName: 'n8n-nodes-discord',
    version: '1.2.3',
    description: '与 Discord 集成的工作流节点',
    verified: true,
    hasUpdate: false,
  },
  {
    id: '2',
    name: 'AWS 节点',
    packageName: 'n8n-nodes-aws-extra',
    version: '2.1.0',
    description: '扩展的 AWS 服务集成',
    verified: false,
    hasUpdate: true,
  },
])

// 移除不再需要的搜索结果数据

// 重置安装表单
const resetInstallForm = () => {
  installPackageName.value = ''
  installVersion.value = ''
  riskAccepted.value = false
}

// 处理安装
const handleInstall = async () => {
  if (!installPackageName.value.trim()) {
    $toast.error({
      summary: '请输入包名',
      detail: '请输入要安装的社区节点包名',
    })

    return
  }

  if (!riskAccepted.value) {
    $toast.error({
      summary: '请确认风险',
      detail: '请确认您理解安装未验证代码的风险',
    })

    return
  }

  try {
    // 模拟安装过程
    await new Promise((resolve) => setTimeout(resolve, 2000))

    $toast.success({
      summary: '安装成功',
      detail: '社区节点已成功安装',
    })

    showInstallDialog.value = false
    resetInstallForm()
  }
  catch (error) {
    $toast.error({
      summary: '安装失败',
      detail: '安装社区节点时发生错误',
    })
  }
}

// 处理卸载
const handleUninstall = async (nodeId: string) => {
  try {
    // 模拟卸载过程
    await new Promise((resolve) => setTimeout(resolve, 1000))

    $toast.success({
      summary: '卸载成功',
      detail: '社区节点已成功卸载',
    })
  }
  catch (error) {
    $toast.error({
      summary: '卸载失败',
      detail: '卸载社区节点时发生错误',
    })
  }
}

// 处理更新
const handleUpdate = async (nodeId: string) => {
  try {
    // 模拟更新过程
    await new Promise((resolve) => setTimeout(resolve, 1500))

    $toast.success({
      summary: '更新成功',
      detail: '社区节点已成功更新',
    })
  }
  catch (error) {
    $toast.error({
      summary: '更新失败',
      detail: '更新社区节点时发生错误',
    })
  }
}

// 移除不再需要的搜索结果安装函数

// 打开 npm 页面浏览社区节点
const openNpmPage = () => {
  const url = 'https://www.npmjs.com/search?q=keywords%3An8n-community-node-package'
  window.open(url, '_blank')
}

// 关闭对话框时重置
watch(() => props.visible, (visible) => {
  if (!visible) {
    resetInstallForm()
    selectedTab.value = 'installed'
  }
})
</script>

<template>
  <Dialog
    class="w-full max-w-4xl"
    header="社区节点管理"
    modal
    :visible="visible"
    @update:visible="emit('update:visible', $event)"
  >
    <div class="space-y-6">
      <!-- 标签页导航 -->
      <div class="flex border-b border-gray-200">
        <button
          class="border-b-2 px-4 py-2 text-sm font-medium transition-colors"
          :class="[
            selectedTab === 'installed'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700',
          ]"
          @click="selectedTab = 'installed'"
        >
          已安装节点
        </button>
        <button
          class="border-b-2 px-4 py-2 text-sm font-medium transition-colors"
          :class="[
            selectedTab === 'browse'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700',
          ]"
          @click="selectedTab = 'browse'"
        >
          浏览节点
        </button>
      </div>

      <!-- 已安装节点 -->
      <div
        v-if="selectedTab === 'installed'"
        class="space-y-4"
      >
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">
            已安装的社区节点
          </h3>
          <Button
            icon="pi pi-plus"
            label="安装新节点"
            @click="showInstallDialog = true"
          />
        </div>

        <div
          v-if="!installedNodes.length"
          class="py-8 text-center"
        >
          <PackageIcon class="mx-auto mb-4 size-12 text-gray-400" />
          <p class="mb-4 text-gray-500">
            暂未安装任何社区节点
          </p>
          <Button
            label="安装第一个节点"
            @click="showInstallDialog = true"
          />
        </div>

        <div
          v-else
          class="space-y-3"
        >
          <Card
            v-for="node of installedNodes"
            :key="node.id"
            class="p-4"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="shrink-0">
                  <ShieldCheckIcon
                    v-if="node.verified"
                    class="size-5 text-green-500"
                  />
                  <PackageIcon
                    v-else
                    class="size-5 text-gray-400"
                  />
                </div>

                <div class="grow">
                  <div class="flex items-center space-x-2">
                    <h4 class="font-semibold">
                      {{ node.name }}
                    </h4>
                    <Badge
                      v-if="node.verified"
                      severity="success"
                      size="small"
                      value="已验证"
                    />
                  </div>
                  <p class="text-sm text-gray-600">
                    {{ node.packageName }} v{{ node.version }}
                  </p>
                  <p
                    v-if="node.description"
                    class="mt-1 text-sm text-gray-500"
                  >
                    {{ node.description }}
                  </p>
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <Badge
                  v-if="node.hasUpdate"
                  severity="info"
                  size="small"
                  value="有更新"
                />

                <div class="flex space-x-1">
                  <Button
                    v-if="node.hasUpdate"
                    v-tooltip="'更新到最新版本'"
                    icon="pi pi-refresh"
                    severity="info"
                    size="small"
                    @click="handleUpdate(node.id)"
                  />

                  <Button
                    v-tooltip="'卸载节点'"
                    icon="pi pi-trash"
                    severity="danger"
                    size="small"
                    @click="handleUninstall(node.id)"
                  />
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      <!-- 浏览节点 -->
      <div
        v-if="selectedTab === 'browse'"
        class="space-y-6"
      >
        <div class="text-center">
          <h3 class="mb-4 text-lg font-semibold">
            浏览社区节点
          </h3>
          <p class="mb-6 text-gray-600">
            在 npm 上浏览和发现更多社区开发的工作流节点
          </p>

          <div class="flex flex-col items-center space-y-4">
            <div class="flex items-center justify-center rounded-full bg-blue-50 p-6">
              <PackageIcon class="size-12 text-blue-500" />
            </div>

            <div class="space-y-2 text-center">
              <h4 class="font-medium">
                发现更多节点
              </h4>
              <p class="text-sm text-gray-500">
                访问 npm 查看所有可用的社区节点，包括详细信息、文档和用户评价
              </p>
            </div>

            <div class="flex space-x-3">
              <Button
                icon="pi pi-external-link"
                label="在 npm 上浏览"
                @click="openNpmPage"
              />
              <Button
                icon="pi pi-plus"
                label="安装节点"
                severity="secondary"
                @click="showInstallDialog = true"
              />
            </div>
          </div>
        </div>

        <!-- 提示信息 -->
        <div class="rounded-lg bg-blue-50 p-4">
          <div class="flex items-start space-x-3">
            <div class="shrink-0">
              <div class="flex size-6 items-center justify-center rounded-full bg-blue-100">
                <i class="pi pi-info-circle text-blue-600" />
              </div>
            </div>
            <div class="text-sm">
              <h5 class="font-medium text-blue-900">
                如何安装社区节点？
              </h5>
              <div class="mt-2 space-y-1 text-blue-700">
                <p>1. 在 npm 上找到你需要的节点包</p>
                <p>2. 复制包名（如：n8n-nodes-discord）</p>
                <p>3. 点击"安装节点"按钮，粘贴包名</p>
                <p>4. 确认风险提示并完成安装</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 安装对话框 -->
    <Dialog
      class="w-full max-w-md"
      header="安装社区节点"
      modal
      :visible="showInstallDialog"
      @update:visible="showInstallDialog = $event"
    >
      <div class="space-y-4">
        <div>
          <label class="mb-2 block text-sm font-medium text-gray-700">
            npm 包名 *
          </label>
          <InputText
            v-model="installPackageName"
            class="w-full"
            placeholder="例如: n8n-nodes-discord"
          />
          <p class="mt-1 text-xs text-gray-500">
            输入要安装的 npm 包名
          </p>
        </div>

        <div>
          <label class="mb-2 block text-sm font-medium text-gray-700">
            版本 (可选)
          </label>
          <InputText
            v-model="installVersion"
            class="w-full"
            placeholder="例如: 2.3.0 或留空安装最新版本"
          />
          <p class="mt-1 text-xs text-gray-500">
            留空将安装最新版本
          </p>
        </div>

        <div class="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
          <div class="flex">
            <XCircleIcon class="mt-0.5 size-5 shrink-0 text-yellow-400" />
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">
                安全警告
              </h3>
              <p class="mt-1 text-sm text-yellow-700">
                社区节点来自第三方开发者，可能包含未验证的代码。安装前请确保您信任该节点的来源。
              </p>
            </div>
          </div>
        </div>

        <div class="flex items-center">
          <Checkbox
            binary
            inputId="risk-accept"
            :modelValue="riskAccepted"
            @update:modelValue="riskAccepted = $event"
          />
          <label
            class="ml-2 text-sm text-gray-700"
            for="risk-accept"
          >
            我理解安装未验证代码的风险
          </label>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-2">
          <Button
            label="取消"
            severity="secondary"
            @click="showInstallDialog = false"
          />
          <Button
            :disabled="!installPackageName.trim() || !riskAccepted"
            label="安装"
            @click="handleInstall"
          />
        </div>
      </template>
    </Dialog>
  </Dialog>
</template>
