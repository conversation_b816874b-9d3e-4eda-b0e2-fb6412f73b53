<script setup lang="ts">
import { GitForkIcon } from 'lucide-vue-next'

interface Props {
  size?: number
}

const props = withDefaults(defineProps<Props>(), {
  size: 16,
})

const outerPadding = computed(() => {
  return `${Math.max(2, props.size * (2 / 16))}px`
})

const innerPadding = computed(() => {
  return `${Math.max(6, props.size * (6 / 16))}px`
})
</script>

<template>
  <div
    class="rounded-xl border border-surface-100 inline-flex-center dark:border-surface-600"
    :style="{ padding: outerPadding }"
  >
    <div
      class="justify-center rounded-lg border border-surface-200 bg-content dark:border-surface-500"
      :style="{ padding: innerPadding }"
    >
      <GitForkIcon :size="size" />
    </div>
  </div>
</template>
