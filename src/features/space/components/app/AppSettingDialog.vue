<script setup lang="ts">
import { InputText } from 'primevue'
import { object } from 'valibot'

import { GlobalEvent } from '~/enums/event'
import { AppService } from '~/features/space/services/app'
import { useSpaceStore } from '~/features/space/stores/space'
import type { AppCreate, AppUpdate } from '~/features/space/types/app'
import type { WorkflowCreate } from '~/features/space/types/workflow'

const spaceStore = useSpaceStore()
const { activeSpace } = storeToRefs(spaceStore)

const { refKey, ref: inputRef } = useRef<
  InstanceType<typeof InputText> & { $el: HTMLInputElement }
>()

const {
  loading,
  isFormCreate,
  isFormUpdate,
  formTitleText,
  formValues,
  updatingItem,
  modalVisible,
  formResolver,
  openCreateModal,
  openUpdateModal,
  handleClose,
  handleSubmit,
  confirmBtnLabel,
  handleModalVisibleChange,
} = useFormControl<AppCreate, AppUpdate>({
  resolver: object({
    name: schemaNotEmptyString('请输入工作流名称'),
  }),
  btnLabel: {
    create: '确认创建',
    update: '确认保存',
  },
  formTitle: {
    create: '创建工作流',
    update: '编辑工作流',
  },
  fetchDetail: (it) => AppService.getWorkflow(it.id) as unknown as Promise<AppCreate>,
  onSubmit: async ({ valid, states }) => {
    if (valid) {
      const payload: WorkflowCreate = {
        name: states.name.value,
        nodes: [],
        pinData: {},
        connections: {},
        active: false,
        settings: {
          executionOrder: 'v1',
        },
        tags: [],
        versionId: '',
      }

      if (isFormCreate.value) {
        if (activeSpace.value) {
          await AppService.createWorkflow({
            ...payload,
            projectId: activeSpace.value.id,
          })
        }
      }
      else {
        if (isFormUpdate.value) {
          const id = updatingItem.value?.id

          if (id) {
            await AppService.updateWorkflow(id, payload)
          }
        }
      }
    }
  },
  onSubmitSuccess: () => {
    emitter.emit(GlobalEvent.AppListRefresh)
  },
  onModalVisibleChange: (isVisible) => {
    if (isVisible) {
      nextTick(() => {
        // HACK: 等待表单渲染完成后，再聚焦到输入框
        setTimeout(() => {
          inputRef.value?.$el?.focus()
        }, 200)
      })
    }
  },
})

const { on } = useEmitter()

onMounted(() => {
  on(GlobalEvent.AppCreate, () => {
    openCreateModal()
  })

  on(GlobalEvent.AppUpdate, (defaultValues) => {
    if (defaultValues) {
      openUpdateModal(defaultValues)
    }
  })
})
</script>

<template>
  <ProDialogForm
    :dialogProps="{
      class: 'dialog-form-wide',
    }"
    :formActionGroupProps="{
      confirmButtonProps: {
        label: confirmBtnLabel,
      },
    }"
    :formProps="{ resolver: formResolver }"
    :initialValues="formValues"
    :loading="loading"
    :title="formTitleText"
    :visible="modalVisible"
    @cancel="handleClose"
    @submit="handleSubmit"
    @update:visible="handleModalVisibleChange"
  >
    <div class="flex flex-col gap-form-field">
      <FormItem
        label="工作流名称"
        name="name"
        required
      >
        <InputText
          :ref="refKey"
          fluid
        />
      </FormItem>
    </div>
  </ProDialogForm>
</template>
