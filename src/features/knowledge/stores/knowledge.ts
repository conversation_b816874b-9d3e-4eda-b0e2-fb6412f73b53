import { KnowledgeBaseService } from '~/features/knowledge/services/knowledgeBase'
import { KnowledgeFileService } from '~/features/knowledge/services/knowledgeFile'
import type { KnowledgeBase, KnowledgeFile } from '~/features/knowledge/types/knowledge'

export const useKnowledgeStore = defineStore('knowledge', () => {
  // 状态
  const knowledgeBases = ref<KnowledgeBase[]>([])
  const activeKnowledgeBase = ref<KnowledgeBase | null>(null)
  const files = ref<KnowledgeFile[]>([])
  const isLoadingBases = ref(false)
  const isLoadingFiles = ref(false)

  // 计算属性
  const activeKnowledgeBaseId = computed(() => activeKnowledgeBase.value?.id)
  const isEmptyBases = computed(() => knowledgeBases.value.length === 0)
  const isEmptyFiles = computed(() => files.value.length === 0)

  // 方法
  const fetchKnowledgeBases = async (spaceId: string) => {
    if (!spaceId) {
      return
    }

    isLoadingBases.value = true

    try {
      const result = await KnowledgeBaseService.getKnowledgeBases(spaceId)
      knowledgeBases.value = result.list

      return result
    }
    finally {
      isLoadingBases.value = false
    }
  }

  const fetchFiles = async (knowledgeBaseId: string) => {
    if (!knowledgeBaseId) {
      return
    }

    isLoadingFiles.value = true

    try {
      const result = await KnowledgeFileService.getFiles(knowledgeBaseId)
      files.value = result.list

      return result
    }
    finally {
      isLoadingFiles.value = false
    }
  }

  const setActiveKnowledgeBase = (base: KnowledgeBase | null) => {
    activeKnowledgeBase.value = base

    if (!base) {
      files.value = []
    }
  }

  const addKnowledgeBase = (base: KnowledgeBase) => {
    knowledgeBases.value.push(base)
  }

  const updateKnowledgeBase = (id: string, updates: Partial<KnowledgeBase>) => {
    const index = knowledgeBases.value.findIndex((b) => b.id === id)

    if (index !== -1) {
      knowledgeBases.value[index] = { ...knowledgeBases.value[index], ...updates }
    }

    if (activeKnowledgeBase.value?.id === id) {
      activeKnowledgeBase.value = { ...activeKnowledgeBase.value, ...updates }
    }
  }

  const removeKnowledgeBase = (id: string) => {
    const index = knowledgeBases.value.findIndex((b) => b.id === id)

    if (index !== -1) {
      knowledgeBases.value.splice(index, 1)
    }

    if (activeKnowledgeBase.value?.id === id) {
      activeKnowledgeBase.value = null
      files.value = []
    }
  }

  const addFiles = (newFiles: KnowledgeFile[]) => {
    files.value.push(...newFiles)

    // 更新对应知识库的文件数量
    if (activeKnowledgeBase.value) {
      updateKnowledgeBase(activeKnowledgeBase.value.id, {
        fileCount: activeKnowledgeBase.value.fileCount + newFiles.length,
      })
    }
  }

  const removeFile = (fileId: string) => {
    const index = files.value.findIndex((f) => f.id === fileId)

    if (index !== -1) {
      files.value.splice(index, 1)

      // 更新对应知识库的文件数量
      if (activeKnowledgeBase.value) {
        updateKnowledgeBase(activeKnowledgeBase.value.id, {
          fileCount: Math.max(0, activeKnowledgeBase.value.fileCount - 1),
        })
      }
    }
  }

  const removeFiles = (fileIds: string[]) => {
    fileIds.forEach((fileId) => {
      const index = files.value.findIndex((f) => f.id === fileId)

      if (index !== -1) {
        files.value.splice(index, 1)
      }
    })

    // 更新对应知识库的文件数量
    if (activeKnowledgeBase.value) {
      updateKnowledgeBase(activeKnowledgeBase.value.id, {
        fileCount: Math.max(0, activeKnowledgeBase.value.fileCount - fileIds.length),
      })
    }
  }

  const clearFiles = () => {
    files.value = []
  }

  const reset = () => {
    knowledgeBases.value = []
    activeKnowledgeBase.value = null
    files.value = []
    isLoadingBases.value = false
    isLoadingFiles.value = false
  }

  return {
    // 状态
    knowledgeBases: readonly(knowledgeBases),
    activeKnowledgeBase: readonly(activeKnowledgeBase),
    files: readonly(files),
    isLoadingBases: readonly(isLoadingBases),
    isLoadingFiles: readonly(isLoadingFiles),

    // 计算属性
    activeKnowledgeBaseId,
    isEmptyBases,
    isEmptyFiles,

    // 方法
    fetchKnowledgeBases,
    fetchFiles,
    setActiveKnowledgeBase,
    addKnowledgeBase,
    updateKnowledgeBase,
    removeKnowledgeBase,
    addFiles,
    removeFile,
    removeFiles,
    clearFiles,
    reset,
  }
})
