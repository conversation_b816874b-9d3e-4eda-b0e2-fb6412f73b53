<script setup lang="ts">
import { BookOpenIcon, MoreHorizontalIcon } from 'lucide-vue-next'

import { DateFormat } from '~/enums/common'
import { useKnowledgeActions } from '~/features/knowledge/composables/useKnowledgeActions'
import type { KnowledgeBase } from '~/features/knowledge/types/knowledge'

interface Props {
  knowledgeBase: KnowledgeBase
}

const props = defineProps<Props>()

const { getKnowledgeBaseActionOptions } = useKnowledgeActions()

const handleCardClick = () => {
  // 导航到知识库详情页
  navigateTo(`/space/${props.knowledgeBase.spaceId}/knowledge/${props.knowledgeBase.id}`)
}

const actionOptions = computed(() => getKnowledgeBaseActionOptions(props.knowledgeBase))
</script>

<template>
  <Card
    class="group relative h-48 cursor-pointer transition-all duration-200 hover:shadow-lg hover:shadow-primary-100 dark:hover:shadow-primary-900/20"
    @click="handleCardClick"
  >
    <template #content>
      <div class="flex h-full flex-col">
        <!-- 头部 -->
        <div class="mb-4 flex items-start justify-between">
          <div class="flex size-12 items-center justify-center rounded-lg bg-primary-50 dark:bg-primary-900/20">
            <BookOpenIcon
              class="text-primary-600 dark:text-primary-400"
              :size="24"
            />
          </div>

          <ProMenu
            :model="actionOptions"
            :pt="{
              root: 'opacity-0 group-hover:opacity-100 transition-opacity duration-200',
            }"
          >
            <Button
              class="!p-1.5"
              severity="contrast"
              size="small"
              variant="text"
              @click.stop
            >
              <MoreHorizontalIcon :size="16" />
            </Button>
          </ProMenu>
        </div>

        <!-- 内容 -->
        <div class="flex-1">
          <h3 class="mb-2 text-lg font-semibold text-surface-900 dark:text-surface-50">
            {{ knowledgeBase.name }}
          </h3>

          <p
            v-if="knowledgeBase.description"
            class="mb-4 line-clamp-2 text-sm text-surface-600 dark:text-surface-400"
          >
            {{ knowledgeBase.description }}
          </p>
        </div>

        <!-- 底部信息 -->
        <div class="flex items-center justify-between text-xs text-surface-500 dark:text-surface-400">
          <span>{{ knowledgeBase.fileCount }} 个文件</span>
          <span>{{ formatDate(knowledgeBase.updatedAt, DateFormat.MM_DD) }}</span>
        </div>
      </div>
    </template>
  </Card>
</template>

<style scoped>
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>
