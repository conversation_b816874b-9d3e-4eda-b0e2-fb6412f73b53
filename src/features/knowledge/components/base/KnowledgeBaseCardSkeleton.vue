<script setup lang="ts">
// 知识库卡片骨架屏组件
</script>

<template>
  <div class="rounded-lg border border-divider p-3.5">
    <div class="flex h-full flex-col opacity-70">
      <!-- 头部骨架 -->
      <div class="mb-4 flex items-start justify-between">
        <Skeleton
          class="rounded-lg"
          height="3rem"
          width="3rem"
        />
        <Skeleton
          class="rounded"
          height="1.5rem"
          width="1.5rem"
        />
      </div>

      <!-- 内容骨架 -->
      <div class="mb-4 flex-1">
        <!-- 标题 -->
        <Skeleton
          class="mb-2"
          height="1.5rem"
          width="75%"
        />

        <!-- 描述 -->
        <div class="space-y-2">
          <Skeleton
            height="1rem"
            width="100%"
          />
        </div>
      </div>

      <!-- 底部信息骨架 -->
      <div class="flex items-center justify-between">
        <Skeleton
          height="0.75rem"
          width="4rem"
        />
        <Skeleton
          height="0.75rem"
          width="3rem"
        />
      </div>
    </div>
  </div>
</template>
