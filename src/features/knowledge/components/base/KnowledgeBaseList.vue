<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { PlusIcon } from 'lucide-vue-next'

import { GlobalEvent } from '~/enums/event'
import KnowledgeBaseCard from '~/features/knowledge/components/base/KnowledgeBaseCard.vue'
import KnowledgeBaseCardSkeleton from '~/features/knowledge/components/base/KnowledgeBaseCardSkeleton.vue'
import { useKnowledgeStore } from '~/features/knowledge/stores/knowledge'
import { useSpaceStore } from '~/features/space/stores/space'

const knowledgeStore = useKnowledgeStore()
const { knowledgeBases, isLoadingBases, isEmptyBases } = storeToRefs(knowledgeStore)

const spaceStore = useSpaceStore()
const { activeSpaceId } = storeToRefs(spaceStore)

const { emit } = useEmitter()

// 获取知识库列表
const { isFetching, refetch } = useQuery({
  queryKey: ['knowledge-bases', activeSpaceId],
  queryFn: () => {
    if (activeSpaceId.value) {
      return knowledgeStore.fetchKnowledgeBases(activeSpaceId.value)
    }

    return null
  },
  enabled: computed(() => !!activeSpaceId.value),
  refetchOnWindowFocus: false,
})

const handleCreateKnowledgeBase = () => {
  emit(GlobalEvent.KnowledgeBaseCreate)
}

const handleRefresh = () => {
  refetch()
}
</script>

<template>
  <div class="h-full">
    <!-- 头部工具栏 -->
    <div class="mb-6 flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-surface-900 dark:text-surface-50">
          知识库
        </h1>
        <p class="mt-1 text-sm text-surface-600 dark:text-surface-400">
          管理和组织您的知识文档
        </p>
      </div>

      <Button
        label="新建知识库"
        :loading="isFetching"
        @click="handleCreateKnowledgeBase()"
      >
        <template #icon>
          <PlusIcon :size="16" />
        </template>
      </Button>
    </div>

    <!-- 内容区域 -->
    <div
      v-if="isLoadingBases"
      class="@container"
    >
      <div class="grid grid-cols-1 gap-6 @lg:grid-cols-2 @3xl:grid-cols-3 @5xl:grid-cols-4">
        <!-- 骨架屏卡片 -->
        <KnowledgeBaseCardSkeleton
          v-for="i of 6"
          :key="i"
        />
      </div>
    </div>

    <div
      v-else-if="isEmptyBases"
      class="flex h-64 flex-col items-center justify-center"
    >
      <div class="mb-4 flex size-16 items-center justify-center rounded-full bg-surface-100 dark:bg-surface-800">
        <PlusIcon
          class="text-surface-400 dark:text-surface-500"
          :size="32"
        />
      </div>
      <h3 class="mb-2 text-lg font-medium text-surface-900 dark:text-surface-50">
        暂无知识库
      </h3>
      <p class="mb-4 text-center text-sm text-surface-600 dark:text-surface-400">
        创建您的第一个知识库，开始整理和管理文档资料
      </p>
      <Button
        label="创建知识库"
        @click="handleCreateKnowledgeBase"
      >
        <template #icon>
          <PlusIcon :size="16" />
        </template>
      </Button>
    </div>

    <div
      v-else
      class="@container"
    >
      <div class="grid grid-cols-1 gap-6 @lg:grid-cols-2 @3xl:grid-cols-3 @5xl:grid-cols-4">
        <!-- 创建新知识库卡片 -->
        <Card
          class="group h-48 cursor-pointer border-2 border-dashed border-surface-300 bg-transparent transition-all duration-200 hover:border-primary-400 hover:bg-primary-50/50 dark:border-surface-600 dark:hover:border-primary-600 dark:hover:bg-primary-900/10"
          @click="handleCreateKnowledgeBase()"
        >
          <template #content>
            <div class="flex h-full flex-col items-center justify-center">
              <div class="mb-3 flex size-12 items-center justify-center rounded-lg bg-surface-100 group-hover:bg-primary-100 dark:bg-surface-700 dark:group-hover:bg-primary-900/20">
                <PlusIcon
                  class="text-surface-600 group-hover:text-primary-600 dark:text-surface-400 dark:group-hover:text-primary-400"
                  :size="24"
                />
              </div>
              <span class="text-sm font-medium text-surface-700 group-hover:text-primary-700 dark:text-surface-300 dark:group-hover:text-primary-300">
                新建知识库
              </span>
            </div>
          </template>
        </Card>

        <!-- 知识库卡片列表 -->
        <KnowledgeBaseCard
          v-for="base of knowledgeBases"
          :key="base.id"
          :knowledgeBase="base"
        />
      </div>
    </div>
  </div>
</template>
