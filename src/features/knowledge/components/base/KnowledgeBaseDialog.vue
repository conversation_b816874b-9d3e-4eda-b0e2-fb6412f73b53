<script setup lang="ts">
import { maxLength, minLength, object, optional, pipe, string } from 'valibot'

import { KnowledgeBaseService } from '~/features/knowledge/services/knowledgeBase'
import { useKnowledgeStore } from '~/features/knowledge/stores/knowledge'
import type { KnowledgeBase, KnowledgeBaseCreate } from '~/features/knowledge/types/knowledge'
import { useSpaceStore } from '~/features/space/stores/space'

interface KnowledgeBaseForm {
  name: string
  description?: string
}

const knowledgeStore = useKnowledgeStore()
const spaceStore = useSpaceStore()
const { activeSpaceId } = storeToRefs(spaceStore)

const { $toast } = useNuxtApp()

const {
  modalVisible,
  loading,
  formValues,
  formResolver,
  confirmBtnLabel,
  formTitleText,
  isFormCreate,
  isFormUpdate,
  updatingItem,
  openCreateModal,
  openUpdateModal,
  handleClose,
  handleSubmit,
  handleModalVisibleChange,
} = useFormControl<KnowledgeBaseForm, KnowledgeBase>({
  resolver: object({
    name: pipe(
      string(),
      minLength(1, '请输入知识库名称'),
      maxLength(50, '知识库名称不能超过50个字符'),
    ),
    description: optional(
      pipe(
        string(),
        maxLength(200, '描述不能超过200个字符'),
      ),
    ),
  }),
  btnLabel: {
    create: '确认创建',
    update: '确认保存',
  },
  formTitle: {
    create: '创建知识库',
    update: '编辑知识库',
  },
  fetchDetail: (item) => Promise.resolve({
    name: item.name,
    description: item.description,
  }),
  onSubmit: async ({ valid, states }) => {
    if (valid && activeSpaceId.value) {
      const payload: KnowledgeBaseCreate = {
        name: states.name.value,
        description: states.description?.value,
        spaceId: activeSpaceId.value,
      }

      if (isFormCreate.value) {
        const newBase = await KnowledgeBaseService.createKnowledgeBase(payload)
        knowledgeStore.addKnowledgeBase(newBase)

        $toast.success({
          summary: '创建成功',
          detail: `知识库「${payload.name}」已创建`,
        })
      }
      else if (isFormUpdate.value && updatingItem.value) {
        const updatedBase = await KnowledgeBaseService.updateKnowledgeBase(
          updatingItem.value.id,
          {
            name: payload.name,
            description: payload.description,
          },
        )
        knowledgeStore.updateKnowledgeBase(updatingItem.value.id, updatedBase)

        $toast.success({
          summary: '保存成功',
          detail: `知识库「${payload.name}」已保存`,
        })
      }
    }
  },
})

// 暴露方法给外部调用
defineExpose({
  openCreateModal,
  openUpdateModal,
})
</script>

<template>
  <ProDialogForm
    :dialogProps="{
      class: 'dialog-form-wide',
    }"
    :formActionGroupProps="{
      confirmButtonProps: {
        label: confirmBtnLabel,
      },
    }"
    :formProps="{ resolver: formResolver }"
    :initialValues="formValues"
    :loading="loading"
    :title="formTitleText"
    :visible="modalVisible"
    @cancel="handleClose"
    @submit="handleSubmit"
    @update:visible="handleModalVisibleChange"
  >
    <div class="flex flex-col gap-form-field">
      <FormItem
        label="知识库名称"
        name="name"
        required
      >
        <InputText
          fluid
          placeholder="请输入知识库名称"
        />
      </FormItem>

      <FormItem
        label="描述"
        name="description"
      >
        <Textarea
          fluid
          placeholder="请输入知识库描述（可选）"
          :rows="3"
        />
      </FormItem>
    </div>
  </ProDialogForm>
</template>
