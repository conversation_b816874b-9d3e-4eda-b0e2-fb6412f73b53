<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { MoreHorizontalIcon } from 'lucide-vue-next'

import { DateFormat } from '~/enums/common'
import { useKnowledgeActions } from '~/features/knowledge/composables/useKnowledgeActions'
import { useKnowledgeStore } from '~/features/knowledge/stores/knowledge'
import type { KnowledgeFile } from '~/features/knowledge/types/knowledge'

interface Props {
  knowledgeBaseId: string
}

const props = defineProps<Props>()

const knowledgeStore = useKnowledgeStore()
const { files, isLoadingFiles, isEmptyFiles } = storeToRefs(knowledgeStore)

const { getFileActionOptions, formatFileSizeDisplay, getFileTypeIcon } = useKnowledgeActions()

// 获取文件列表
const { isFetching, refetch } = useQuery({
  queryKey: ['knowledge-files', props.knowledgeBaseId],
  queryFn: () => knowledgeStore.fetchFiles(props.knowledgeBaseId),
  enabled: computed(() => !!props.knowledgeBaseId),
})

const selectedFiles = ref<KnowledgeFile[]>([])

const handleFileSelect = (file: KnowledgeFile, selected: boolean) => {
  if (selected) {
    selectedFiles.value.push(file)
  }
  else {
    const index = selectedFiles.value.findIndex((f) => f.id === file.id)

    if (index !== -1) {
      selectedFiles.value.splice(index, 1)
    }
  }
}

const handleSelectAll = (selected: boolean) => {
  if (selected) {
    selectedFiles.value = [...files.value]
  }
  else {
    selectedFiles.value = []
  }
}

const isFileSelected = (file: KnowledgeFile) => {
  return selectedFiles.value.some((f) => f.id === file.id)
}

const isAllSelected = computed(() => {
  return files.value.length > 0 && selectedFiles.value.length === files.value.length
})

const isPartiallySelected = computed(() => {
  return selectedFiles.value.length > 0 && selectedFiles.value.length < files.value.length
})

const handleBatchDelete = () => {
  if (selectedFiles.value.length > 0) {
    const { handleDeleteFiles } = useKnowledgeActions()
    handleDeleteFiles(selectedFiles.value)
    selectedFiles.value = []
  }
}

const handleRefresh = () => {
  refetch()
  selectedFiles.value = []
}
</script>

<template>
  <div class="space-y-4">
    <!-- 工具栏 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <h2 class="text-lg font-semibold text-surface-900 dark:text-surface-50">
          文件列表
        </h2>
        <span
          v-if="!isLoadingFiles"
          class="text-sm text-surface-600 dark:text-surface-400"
        >
          共 {{ files.length }} 个文件
        </span>
      </div>

      <div class="flex items-center space-x-2">
        <Button
          v-if="selectedFiles.length > 0"
          label="批量删除"
          severity="danger"
          size="small"
          variant="outlined"
          @click="handleBatchDelete"
        />
        <Button
          label="刷新"
          :loading="isFetching"
          severity="contrast"
          size="small"
          variant="outlined"
          @click="handleRefresh"
        />
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div
      v-if="files.length > 0"
      class="flex items-center justify-between rounded-lg border border-surface-200 bg-surface-50 px-4 py-2 dark:border-surface-700 dark:bg-surface-800"
    >
      <div class="flex items-center space-x-2">
        <Checkbox
          :indeterminate="isPartiallySelected"
          :modelValue="isAllSelected"
          @update:modelValue="handleSelectAll"
        />
        <span class="text-sm text-surface-700 dark:text-surface-300">
          {{ selectedFiles.length > 0 ? `已选择 ${selectedFiles.length} 个文件` : '全选' }}
        </span>
      </div>

      <div
        v-if="selectedFiles.length > 0"
        class="text-sm text-surface-600 dark:text-surface-400"
      >
        <Button
          label="取消选择"
          size="small"
          variant="text"
          @click="selectedFiles = []"
        />
      </div>
    </div>

    <!-- 文件列表 -->
    <div
      v-if="isLoadingFiles"
      class="flex h-32 items-center justify-center"
    >
      <ProgressSpinner />
    </div>

    <div
      v-else-if="isEmptyFiles"
      class="flex h-32 flex-col items-center justify-center"
    >
      <p class="text-surface-600 dark:text-surface-400">
        暂无文件
      </p>
    </div>

    <div
      v-else
      class="space-y-2"
    >
      <div
        v-for="file of files"
        :key="file.id"
        class="flex items-center justify-between rounded-lg border border-surface-200 bg-white p-4 transition-all hover:shadow-sm dark:border-surface-700 dark:bg-surface-900"
      >
        <div class="flex items-center space-x-4">
          <Checkbox
            :modelValue="isFileSelected(file)"
            @update:modelValue="(selected: boolean) => handleFileSelect(file, selected)"
          />

          <div class="flex items-center space-x-3">
            <span class="text-2xl">
              {{ getFileTypeIcon(file) }}
            </span>

            <div class="min-w-0 flex-1">
              <h3 class="truncate text-sm font-medium text-surface-900 dark:text-surface-50">
                {{ file.name }}
              </h3>
              <div class="flex items-center space-x-4 text-xs text-surface-500 dark:text-surface-500">
                <span>{{ formatFileSizeDisplay(file.size) }}</span>
                <span>{{ formatDate(file.createdAt, DateFormat.MM_DD_HH_MM) }}</span>
                <span>{{ file.uploadedBy }}</span>
              </div>
            </div>
          </div>
        </div>

        <ProMenu :model="getFileActionOptions(file)">
          <Button
            class="!p-2"
            severity="contrast"
            size="small"
            variant="text"
          >
            <MoreHorizontalIcon :size="16" />
          </Button>
        </ProMenu>
      </div>
    </div>
  </div>
</template>
