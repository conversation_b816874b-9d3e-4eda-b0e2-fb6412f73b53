<script setup lang="ts">
import { CloudUploadIcon, FileIcon, XIcon } from 'lucide-vue-next'

import { KnowledgeFileService } from '~/features/knowledge/services/knowledgeFile'
import { useKnowledgeStore } from '~/features/knowledge/stores/knowledge'

interface Props {
  knowledgeBaseId: string
  disabled?: boolean
}

const props = defineProps<Props>()

const knowledgeStore = useKnowledgeStore()
const { $toast } = useNuxtApp()

const isUploading = ref(false)
const dragActive = ref(false)
const selectedFiles = ref<File[]>([])

const fileInputRef = ref<HTMLInputElement>()

// 支持的文件类型
const acceptedTypes = [
  '.pdf', '.doc', '.docx', '.txt', '.md', '.rtf',
  '.xls', '.xlsx', '.ppt', '.pptx',
  '.jpg', '.jpeg', '.png', '.gif', '.webp',
  '.zip', '.rar', '.7z',
]

const handleFileSelect = (files: FileList | null) => {
  if (!files) { return }

  const newFiles = Array.from(files).filter((file) => {
    const extension = '.' + file.name.split('.').pop()?.toLowerCase()

    return acceptedTypes.includes(extension)
  })

  if (newFiles.length !== files.length) {
    $toast.warn({
      summary: '部分文件被忽略',
      detail: '只支持文档、图片和压缩包格式的文件',
    })
  }

  selectedFiles.value = [...selectedFiles.value, ...newFiles]
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  dragActive.value = false

  if (props.disabled || isUploading.value) { return }

  const files = event.dataTransfer?.files || null
  handleFileSelect(files)
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()

  if (!props.disabled && !isUploading.value) {
    dragActive.value = true
  }
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  dragActive.value = false
}

const handleInputChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  handleFileSelect(target.files)
}

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
}

const clearFiles = () => {
  selectedFiles.value = []
}

const formatFileSize = (size: number) => {
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }

  return `${fileSize.toFixed(index === 0 ? 0 : 1)} ${units[index]}`
}

const handleUpload = async () => {
  if (selectedFiles.value.length === 0) { return }

  isUploading.value = true

  try {
    const uploadedFiles = await KnowledgeFileService.uploadFiles(
      props.knowledgeBaseId,
      selectedFiles.value,
    )

    knowledgeStore.addFiles(uploadedFiles)

    $toast.success({
      summary: '上传成功',
      detail: `已上传 ${uploadedFiles.length} 个文件`,
    })

    clearFiles()

    // 重置文件输入框
    if (fileInputRef.value) {
      fileInputRef.value.value = ''
    }
  }
  catch (error) {
    $toast.error({
      summary: '上传失败',
      detail: error instanceof Error ? error.message : '未知错误',
    })
  }
  finally {
    isUploading.value = false
  }
}

const openFileDialog = () => {
  if (!props.disabled && !isUploading.value) {
    fileInputRef.value?.click()
  }
}
</script>

<template>
  <div class="space-y-4">
    <!-- 拖拽上传区域 -->
    <div
      class="relative rounded-lg border-2 border-dashed p-8 text-center transition-all duration-200"
      :class="{
        'border-primary-400 bg-primary-50/50 dark:border-primary-600 dark:bg-primary-900/10': dragActive,
        'border-surface-300 dark:border-surface-600': !dragActive,
        'cursor-pointer hover:border-primary-400 hover:bg-primary-50/30 dark:hover:border-primary-600 dark:hover:bg-primary-900/5': !disabled && !isUploading,
        'cursor-not-allowed opacity-50': disabled || isUploading,
      }"
      @click="openFileDialog"
      @dragleave="handleDragLeave"
      @dragover="handleDragOver"
      @drop="handleDrop"
    >
      <input
        ref="fileInputRef"
        :accept="acceptedTypes.join(',')"
        class="hidden"
        :disabled="disabled || isUploading"
        multiple
        type="file"
        @change="handleInputChange"
      >

      <div class="flex flex-col items-center">
        <div class="mb-4 flex size-16 items-center justify-center rounded-full bg-surface-100 dark:bg-surface-800">
          <CloudUploadIcon
            class="text-surface-600 dark:text-surface-400"
            :size="32"
          />
        </div>

        <h3 class="mb-2 text-lg font-medium text-surface-900 dark:text-surface-50">
          拖拽文件到这里或点击上传
        </h3>

        <p class="text-sm text-surface-600 dark:text-surface-400">
          支持 PDF、Word、Excel、PowerPoint、图片、压缩包等格式
        </p>

        <p class="mt-1 text-xs text-surface-500 dark:text-surface-500">
          单个文件最大 100MB
        </p>
      </div>
    </div>

    <!-- 已选择的文件列表 -->
    <div
      v-if="selectedFiles.length > 0"
      class="space-y-2"
    >
      <div class="flex items-center justify-between">
        <span class="text-sm font-medium text-surface-900 dark:text-surface-50">
          已选择 {{ selectedFiles.length }} 个文件
        </span>
        <Button
          label="清空"
          size="small"
          variant="text"
          @click="clearFiles"
        />
      </div>

      <div class="max-h-48 space-y-2 overflow-y-auto">
        <div
          v-for="(file, index) of selectedFiles"
          :key="index"
          class="flex items-center justify-between rounded-lg border border-surface-200 bg-surface-50 p-3 dark:border-surface-700 dark:bg-surface-800"
        >
          <div class="flex items-center space-x-3">
            <FileIcon
              class="text-surface-600 dark:text-surface-400"
              :size="20"
            />
            <div class="min-w-0 flex-1">
              <p class="truncate text-sm font-medium text-surface-900 dark:text-surface-50">
                {{ file.name }}
              </p>
              <p class="text-xs text-surface-500 dark:text-surface-500">
                {{ formatFileSize(file.size) }}
              </p>
            </div>
          </div>

          <Button
            class="!p-1"
            severity="contrast"
            size="small"
            variant="text"
            @click="removeFile(index)"
          >
            <XIcon :size="16" />
          </Button>
        </div>
      </div>

      <div class="flex justify-end space-x-2">
        <Button
          label="取消"
          severity="contrast"
          variant="outlined"
          @click="clearFiles"
        />
        <Button
          label="上传文件"
          :loading="isUploading"
          @click="handleUpload"
        />
      </div>
    </div>
  </div>
</template>
