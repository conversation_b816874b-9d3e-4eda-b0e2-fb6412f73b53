import { DownloadIcon, FolderOutputIcon, TrashIcon } from 'lucide-vue-next'

import { ProMenuActionType } from '~/enums/pro'
import { KnowledgeBaseService } from '~/features/knowledge/services/knowledgeBase'
import { KnowledgeFileService } from '~/features/knowledge/services/knowledgeFile'
import { useKnowledgeStore } from '~/features/knowledge/stores/knowledge'
import type { KnowledgeBase, KnowledgeFile } from '~/features/knowledge/types/knowledge'
// import { formatFileSize } from '~/utils/file'

export function useKnowledgeActions() {
  const knowledgeStore = useKnowledgeStore()
  const { $confirm, $toast } = useNuxtApp()

  // 知识库操作
  const handleDeleteKnowledgeBase = async (base: KnowledgeBase) => {
    $confirm.dialog({
      header: '删除知识库',
      message: `确定要删除知识库「${base.name}」吗？删除后其中的所有文件将无法恢复。`,
      acceptLabel: '确认删除',
      accept: async () => {
        try {
          await KnowledgeBaseService.deleteKnowledgeBase(base.id)
          knowledgeStore.removeKnowledgeBase(base.id)

          $toast.success({
            summary: '删除成功',
            detail: `知识库「${base.name}」已删除`,
          })
        }
        catch (error) {
          $toast.error({
            summary: '删除失败',
            detail: error instanceof Error ? error.message : '未知错误',
          })
        }
      },
    })
  }

  // 文件操作
  const handleDeleteFile = async (file: KnowledgeFile) => {
    $confirm.dialog({
      header: '删除文件',
      message: `确定要删除文件「${file.name}」吗？`,
      acceptLabel: '确认删除',
      accept: async () => {
        try {
          await KnowledgeFileService.deleteFile(file.id)
          knowledgeStore.removeFile(file.id)

          $toast.success({
            summary: '删除成功',
            detail: `文件「${file.name}」已删除`,
          })
        }
        catch (error) {
          $toast.error({
            summary: '删除失败',
            detail: error instanceof Error ? error.message : '未知错误',
          })
        }
      },
    })
  }

  const handleDeleteFiles = async (files: KnowledgeFile[]) => {
    const fileNames = files.map((f) => f.name).join('、')
    const displayNames = files.length > 3
      ? `${files.slice(0, 3).map((f) => f.name).join('、')} 等 ${files.length} 个文件`
      : fileNames

    $confirm.dialog({
      header: '批量删除文件',
      message: `确定要删除「${displayNames}」吗？`,
      acceptLabel: '确认删除',
      accept: async () => {
        try {
          const fileIds = files.map((f) => f.id)
          await KnowledgeFileService.deleteFiles(fileIds)
          knowledgeStore.removeFiles(fileIds)

          $toast.success({
            summary: '删除成功',
            detail: `已删除 ${files.length} 个文件`,
          })
        }
        catch (error) {
          $toast.error({
            summary: '删除失败',
            detail: error instanceof Error ? error.message : '未知错误',
          })
        }
      },
    })
  }

  const handleDownloadFile = async (file: KnowledgeFile) => {
    try {
      const blob = await KnowledgeFileService.downloadFile(file.id)
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = file.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      $toast.success({
        summary: '下载成功',
        detail: `文件「${file.name}」已开始下载`,
      })
    }
    catch (error) {
      $toast.error({
        summary: '下载失败',
        detail: error instanceof Error ? error.message : '未知错误',
      })
    }
  }

  const handleMoveFileToSpace = async (file: KnowledgeFile, targetSpaceId: string) => {
    try {
      await KnowledgeFileService.moveFileToSpace({
        fileId: file.id,
        targetSpaceId,
      })

      knowledgeStore.removeFile(file.id)

      $toast.success({
        summary: '移动成功',
        detail: `文件「${file.name}」已移动到目标工作空间`,
      })
    }
    catch (error) {
      $toast.error({
        summary: '移动失败',
        detail: error instanceof Error ? error.message : '未知错误',
      })
    }
  }

  // 获取文件操作菜单
  const getFileActionOptions = (file: KnowledgeFile): ProMenuItem[] => [
    {
      label: '下载',
      actionType: ProMenuActionType.Export,
      itemIcon: DownloadIcon,
      command: () => handleDownloadFile(file),
    },
    {
      label: '移动到其他空间',
      actionType: ProMenuActionType.Move,
      itemIcon: FolderOutputIcon,
      command: () => {
        // 这里需要打开空间选择对话框
        // 暂时使用简单的 prompt 演示
        const targetSpaceId = prompt('请输入目标空间ID:')

        if (targetSpaceId) {
          handleMoveFileToSpace(file, targetSpaceId)
        }
      },
    },
    {
      actionType: ProMenuActionType.Separator,
    },
    {
      label: '删除',
      actionType: ProMenuActionType.Delete,
      itemIcon: TrashIcon,
      command: () => handleDeleteFile(file),
    },
  ]

  // 获取知识库操作菜单
  const getKnowledgeBaseActionOptions = (base: KnowledgeBase): ProMenuItem[] => [
    {
      label: '编辑信息',
      actionType: ProMenuActionType.Edit,
      command: () => {
        // 触发编辑对话框
        // emitter.emit('knowledge-base-edit', base)
      },
    },
    {
      actionType: ProMenuActionType.Separator,
    },
    {
      label: '删除知识库',
      actionType: ProMenuActionType.Delete,
      itemIcon: TrashIcon,
      command: () => handleDeleteKnowledgeBase(base),
    },
  ]

  // 文件大小格式化
  const formatFileSizeDisplay = (size: number) => {
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let index = 0
    let fileSize = size

    while (fileSize >= 1024 && index < units.length - 1) {
      fileSize /= 1024
      index++
    }

    return `${fileSize.toFixed(index === 0 ? 0 : 1)} ${units[index]}`
  }

  // 文件类型图标
  const getFileTypeIcon = (file: KnowledgeFile) => {
    const type = file.type.toLowerCase()

    if (['pdf'].includes(type)) {
      return '📄'
    }

    if (['doc', 'docx'].includes(type)) {
      return '📝'
    }

    if (['xls', 'xlsx'].includes(type)) {
      return '📊'
    }

    if (['ppt', 'pptx'].includes(type)) {
      return '📋'
    }

    if (['txt', 'md'].includes(type)) {
      return '📄'
    }

    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(type)) {
      return '🖼️'
    }

    if (['mp4', 'avi', 'mov'].includes(type)) {
      return '🎥'
    }

    if (['mp3', 'wav', 'flac'].includes(type)) {
      return '🎵'
    }

    if (['zip', 'rar', '7z'].includes(type)) {
      return '📦'
    }

    return '📁'
  }

  return {
    handleDeleteKnowledgeBase,
    handleDeleteFile,
    handleDeleteFiles,
    handleDownloadFile,
    handleMoveFileToSpace,
    getFileActionOptions,
    getKnowledgeBaseActionOptions,
    formatFileSizeDisplay,
    getFileTypeIcon,
  }
}
