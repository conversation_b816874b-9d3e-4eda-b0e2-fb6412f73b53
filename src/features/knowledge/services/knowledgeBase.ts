import type { KnowledgeBase, KnowledgeBaseCreate, KnowledgeBaseUpdate } from '~/features/knowledge/types/knowledge'

// 模拟数据
const mockKnowledgeBases: KnowledgeBase[] = [
  {
    id: '1',
    name: '技术文档库',
    description: '存储各种技术文档和开发规范',
    spaceId: 'space-1',
    fileCount: 15,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T15:30:00Z',
  },
  {
    id: '2',
    name: '产品需求库',
    description: '产品需求文档和原型设计文件',
    spaceId: 'space-1',
    fileCount: 8,
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-18T11:20:00Z',
  },
  {
    id: '3',
    name: '培训资料库',
    description: '员工培训和学习资料',
    spaceId: 'space-1',
    fileCount: 23,
    createdAt: '2024-01-05T14:00:00Z',
    updatedAt: '2024-01-22T16:45:00Z',
  },
]

export const KnowledgeBaseService = {
  getKnowledgeBases(spaceId: string, query?: WithPageQuery) {
    return new Promise<PageResult<KnowledgeBase>>((resolve) => {
      setTimeout(() => {
        const filteredBases = mockKnowledgeBases.filter((base) => base.spaceId === spaceId)
        resolve({
          list: filteredBases,
          total: filteredBases.length,
          page: query?.page || 1,
          page_sum: Math.ceil(filteredBases.length / (query?.page_size || 20)),
        })
      }, 300)
    })
  },

  getKnowledgeBase(id: string) {
    return new Promise<KnowledgeBase | null>((resolve) => {
      setTimeout(() => {
        const base = mockKnowledgeBases.find((b) => b.id === id)
        resolve(base || null)
      }, 200)
    })
  },

  createKnowledgeBase(payload: KnowledgeBaseCreate) {
    return new Promise<KnowledgeBase>((resolve) => {
      setTimeout(() => {
        const newBase: KnowledgeBase = {
          id: `kb-${Date.now()}`,
          name: payload.name,
          description: payload.description,
          spaceId: payload.spaceId,
          fileCount: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
        mockKnowledgeBases.push(newBase)
        resolve(newBase)
      }, 500)
    })
  },

  updateKnowledgeBase(id: string, payload: KnowledgeBaseUpdate) {
    return new Promise<KnowledgeBase>((resolve, reject) => {
      setTimeout(() => {
        const baseIndex = mockKnowledgeBases.findIndex((b) => b.id === id)

        if (baseIndex === -1) {
          reject(new Error('知识库不存在'))

          return
        }

        const updatedBase = {
          ...mockKnowledgeBases[baseIndex],
          ...payload,
          updatedAt: new Date().toISOString(),
        }
        mockKnowledgeBases[baseIndex] = updatedBase
        resolve(updatedBase)
      }, 400)
    })
  },

  deleteKnowledgeBase(id: string) {
    return new Promise<void>((resolve, reject) => {
      setTimeout(() => {
        const baseIndex = mockKnowledgeBases.findIndex((b) => b.id === id)

        if (baseIndex === -1) {
          reject(new Error('知识库不存在'))

          return
        }

        mockKnowledgeBases.splice(baseIndex, 1)
        resolve()
      }, 300)
    })
  },
}
