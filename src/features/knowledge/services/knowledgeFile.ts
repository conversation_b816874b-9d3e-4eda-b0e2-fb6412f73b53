import type { KnowledgeFile, KnowledgeFileMovePayload } from '~/features/knowledge/types/knowledge'

// 模拟文件数据
const mockFiles: KnowledgeFile[] = [
  {
    id: 'file-1',
    name: 'Vue 3 开发指南.pdf',
    originalName: 'Vue 3 开发指南.pdf',
    size: 2048576, // 2MB
    type: 'pdf',
    mimeType: 'application/pdf',
    knowledgeBaseId: '1',
    spaceId: 'space-1',
    uploadedBy: 'user-1',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 'file-2',
    name: 'TypeScript 最佳实践.docx',
    originalName: 'TypeScript 最佳实践.docx',
    size: 1536000, // 1.5MB
    type: 'docx',
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    knowledgeBaseId: '1',
    spaceId: 'space-1',
    uploadedBy: 'user-2',
    createdAt: '2024-01-16T14:20:00Z',
    updatedAt: '2024-01-16T14:20:00Z',
  },
  {
    id: 'file-3',
    name: '代码规范文档.md',
    originalName: '代码规范文档.md',
    size: 51200, // 50KB
    type: 'md',
    mimeType: 'text/markdown',
    knowledgeBaseId: '1',
    spaceId: 'space-1',
    uploadedBy: 'user-1',
    createdAt: '2024-01-17T09:15:00Z',
    updatedAt: '2024-01-17T09:15:00Z',
  },
  {
    id: 'file-4',
    name: '产品需求文档 v2.0.pdf',
    originalName: '产品需求文档 v2.0.pdf',
    size: 3145728, // 3MB
    type: 'pdf',
    mimeType: 'application/pdf',
    knowledgeBaseId: '2',
    spaceId: 'space-1',
    uploadedBy: 'user-3',
    createdAt: '2024-01-12T16:45:00Z',
    updatedAt: '2024-01-12T16:45:00Z',
  },
]

export const KnowledgeFileService = {
  getFiles(knowledgeBaseId: string, query?: WithPageQuery) {
    return new Promise<PageResult<KnowledgeFile>>((resolve) => {
      setTimeout(() => {
        const filteredFiles = mockFiles.filter((file) => file.knowledgeBaseId === knowledgeBaseId)
        resolve({
          list: filteredFiles,
          total: filteredFiles.length,
          page: query?.page || 1,
          page_sum: Math.ceil(filteredFiles.length / (query?.page_size || 20)),
        })
      }, 300)
    })
  },

  uploadFiles(knowledgeBaseId: string, files: File[]) {
    return new Promise<KnowledgeFile[]>((resolve) => {
      setTimeout(() => {
        const uploadedFiles: KnowledgeFile[] = files.map((file, index) => {
          const fileExtension = file.name.split('.').pop() || ''
          const newFile: KnowledgeFile = {
            id: `file-${Date.now()}-${index}`,
            name: file.name,
            originalName: file.name,
            size: file.size,
            type: fileExtension,
            mimeType: file.type,
            knowledgeBaseId,
            spaceId: 'space-1', // 这里应该从当前工作空间获取
            uploadedBy: 'current-user',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
          mockFiles.push(newFile)

          return newFile
        })
        resolve(uploadedFiles)
      }, 1000) // 模拟上传时间
    })
  },

  deleteFile(fileId: string) {
    return new Promise<void>((resolve, reject) => {
      setTimeout(() => {
        const fileIndex = mockFiles.findIndex((f) => f.id === fileId)

        if (fileIndex === -1) {
          reject(new Error('文件不存在'))

          return
        }

        mockFiles.splice(fileIndex, 1)
        resolve()
      }, 300)
    })
  },

  deleteFiles(fileIds: string[]) {
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        fileIds.forEach((fileId) => {
          const fileIndex = mockFiles.findIndex((f) => f.id === fileId)

          if (fileIndex !== -1) {
            mockFiles.splice(fileIndex, 1)
          }
        })
        resolve()
      }, 500)
    })
  },

  moveFileToSpace(payload: KnowledgeFileMovePayload) {
    return new Promise<void>((resolve, reject) => {
      setTimeout(() => {
        const fileIndex = mockFiles.findIndex((f) => f.id === payload.fileId)

        if (fileIndex === -1) {
          reject(new Error('文件不存在'))

          return
        }

        const updatedFile = {
          ...mockFiles[fileIndex],
          spaceId: payload.targetSpaceId,
          knowledgeBaseId: payload.targetKnowledgeBaseId || mockFiles[fileIndex].knowledgeBaseId,
          updatedAt: new Date().toISOString(),
        }
        mockFiles[fileIndex] = updatedFile
        resolve()
      }, 600)
    })
  },

  downloadFile(fileId: string) {
    return new Promise<Blob>((resolve, reject) => {
      setTimeout(() => {
        const file = mockFiles.find((f) => f.id === fileId)

        if (!file) {
          reject(new Error('文件不存在'))

          return
        }

        // 模拟文件下载 - 创建一个空的 Blob
        const blob = new Blob(['模拟文件内容'], { type: file.mimeType })
        resolve(blob)
      }, 500)
    })
  },
}
