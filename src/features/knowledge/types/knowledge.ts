export interface KnowledgeBase {
  id: string
  name: string
  description?: string
  spaceId: string
  fileCount: number
  createdAt: string
  updatedAt: string
}

export interface KnowledgeFile {
  id: string
  name: string
  originalName: string
  size: number
  type: string
  mimeType: string
  knowledgeBaseId: string
  spaceId: string
  uploadedBy: string
  createdAt: string
  updatedAt: string
  url?: string
}

export interface KnowledgeBaseCreate {
  name: string
  description?: string
  spaceId: string
}

export interface KnowledgeBaseUpdate {
  name?: string
  description?: string
}

export interface KnowledgeFileUpload {
  knowledgeBaseId: string
  files: File[]
}

export interface KnowledgeFileMovePayload {
  fileId: string
  targetSpaceId: string
  targetKnowledgeBaseId?: string
}
