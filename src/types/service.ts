import type { UseFetchOptions } from '#app'

import type { Role, User } from './user'

/** 自定义的 fetch 配置 */
export type CustomFetchOptions<ResultValue> = UseFetchOptions<ResultValue> & {
  queryKey?: string | string[]
  manual?: boolean
}

export interface Captcha {
  /** 图形验证码的唯一标识 */
  uuid: string
  /** Base64 编码的图形验证码 */
  image: string
}

export type RoleListItem = Pick<Role, 'id' | 'name' | 'create_time' | 'update_time' | 'is_update'>

export type UserListItem = User
