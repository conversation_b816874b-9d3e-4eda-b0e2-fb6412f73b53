import type { TicketCommentActionStatus, TicketGrade, TicketStatus, TicketType } from '~/enums/ticket'

/** 工单 */
export interface Ticket {
  id: UniqueId
  /** 工单名称 */
  name: string
  /** 工单描述 */
  desc: string

  /** 工单创建时间 */
  create_time: string
  /** 工单提交时间 */
  submit_time?: string
  /** 计划处理时间 */
  schedule_time?: string

  /** 工单对象 ID */
  object_id: number
  /** 工单类型 */
  object_type: TicketType
  /** 工单类型名称 */
  object_type_name: string

  /** 重要程度 */
  grade: TicketGrade

  /** 工单状态 */
  status: TicketStatus

  /** 工单的创建人 */
  create_user: User
  /** 工单的提交人 */
  submit_user?: User
  /** 工单的处理人列表 */
  user_list?: User[]
}

export type TicketListItem = Omit<Ticket, 'result'>

export type TicketFormValues = Pick<Ticket, 'name' | 'desc' | 'grade' | 'object_type' | 'object_id' | 'schedule_time' | 'user_list'> & Partial<Pick<Ticket, 'status'>>

export type TicketFormUpdateValues = Omit<TicketFormValues, 'user_list'> & {
  user_list?: User['id'][]
}

/** 工单评论 */
export interface TicketComment {
  id: UniqueId
  /** 审核填写提交内容 */
  content?: string
  /** 评论内容 */
  comment?: string
  /** 评论创建人 */
  user: User
  /** 评论创建时间 */
  create_time: string
  /** 工单 ID */
  work_order_id: Ticket['id']
  /** 评论状态 */
  status: TicketCommentActionStatus
}

export type TicketCommentFormValues = Pick<TicketComment, 'content' | 'comment'> & {
  work_order_id: Ticket['id']
  status?: TicketCommentActionStatus
}
