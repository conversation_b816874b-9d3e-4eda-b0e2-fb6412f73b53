import type { AssetType } from '~/enums/asset'
import type { ComplianceCheckResultStatus, VulThreatLevel } from '~/enums/security'

/** 合规检查结果信息 */
export interface ComplianceResult {
  id: UniqueId
  baseline_id: BaselineTpl['id']
  status: ComplianceCheckResultStatus
  system_option: BaselinePolicyOption
  create_time: string
}

export interface ComplianceOptionResult {
  id: UniqueId
  name: string
  level: VulThreatLevel
  system_id: BaselineSystem['id']
  desc: string
  suggest: string
  command: string
}

/** 合规检查详情 */
export interface ComplianceCheck {
  id: UniqueId
  create_time: string
  server?: {
    id: Server['id']
    name: NonNullable<Server['asset']>['name']
    template_id: AssetTemplate['id']
    asset_type: AssetType
  }
  server_id?: Server['id']
  template_baseline: {
    name: BaselineTpl['name']
    system_name: BaselineSystem['name']
  }
  template_baseline_id: BaselineTpl['id']
  work_order: {
    /** 工单总数 */
    total: number
  }
}

/** 合规检查列表项 */
export type ComplianceListItem = ComplianceCheck

export interface VendorListItem {
  id: UniqueId
  /** 厂商名 */
  name: string
  /** 厂商英文名 */
  name_en: string
  /** 产品总数 */
  product_total: number
}

/** 漏洞 */
export type Vul = {
  id: UniqueId
  server_id: Server['id']
  desc: string
  /** 漏洞编号 */
  code: string
  /** 漏洞名称 */
  name: string
  /** 漏洞等级 */
  level?: VulThreatLevel
  /** 漏洞类型 */
  type: string
  /** 发现时间 */
  create_time: string
  /** 服务器 */
  server?: ServerInfo
  /** 参考链接 */
  refer_url?: string
  /** 影响 */
  impact?: string
  /** 修复建议 */
  remediation?: string
  tags?: string[]
  ip?: string
  port?: number
  /** 漏洞利用命令 */
  curl_command?: string
  /** 漏洞利用请求体 */
  request_body?: string
  /** 漏洞利用响应体 */
  response_body?: string
  work_order: {
    /** 工单总数 */
    total: number
  }
}

/** 漏洞类型 */
export type VulTypeListItem = {
  id: UniqueId
  cnnvd_id: SecurityAlert['cnnvd_id']
  pid: VulTypeListItem['cnnvd_id']
  name: string
}

/** 漏洞筛选参数 */
export interface VulFilterParams extends Partial<Pick<SecurityAlert, 'name' | 'vendor_id' | 'vul_type_id'>> {
  level?: VulThreatLevel
  publish_time?: [Date, Date] | []
  update_time?: [Date, Date] | []
}

/** 漏洞列表查询参数 */
export interface VulLibListQuery extends WithPageQuery<Pick<VulFilterParams, 'name' | 'vendor_id' | 'vul_type_id'>> {
  level?: VulThreatLevel
  publish_time_start?: string
  publish_time_end?: string
  update_time_start?: string
  update_time_end?: string
}

/** 安全通告 */
export interface SecurityAlert {
  cnnvd_id: string
  /** 漏洞编号 */
  cnnvd_code?: string
  /** 漏洞名称 */
  name: string
  /** 漏洞描述 */
  desc?: string
  /** CVE 编号 */
  cve_code?: string
  /** 漏洞厂商 */
  vendor_id?: VendorListItem['id']
  vendor_name?: string
  /** 风险等级 */
  level?: VulThreatLevel
  /** 漏洞类型 */
  vul_type_id?: string
  vul_type_name?: string
  /** 披露时间 */
  publish_time: string
  /** 更新时间 */
  update_time?: string
  /** 参考链接 */
  refer_url?: string
  /** 补丁 */
  patch?: string
}

export type CveKey = NonNullable<SecurityAlert['cnnvd_id'] | SecurityAlert['cve_code'] | SecurityAlert['cnnvd_code']>
