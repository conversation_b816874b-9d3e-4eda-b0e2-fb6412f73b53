import type { Connection } from '@vue-flow/core'
import { type InferInput, literal, object, string, union } from 'valibot'

import type { ContextMenuTarget } from '#wf/composables/useContextMenu'
import type { CanvasNode } from '#wf/types/canvas'
import type { AddedNodesAndConnections, INodeUi, IUpdateInformation, ToggleNodeCreatorOptions } from '#wf/types/interface'

import type { ApiStatus } from '~/enums/common'
import type { GlobalEvent } from '~/enums/event'
import type { RouteKey } from '~/enums/route'
import type { KnowledgeBase } from '~/features/knowledge/types/knowledge'
import type { AppCreate, AppUpdate } from '~/features/space/types/app'
import type { SpaceData, SpaceSettingForm } from '~/features/space/types/space'

/**
 * 通用的类型别名，用于替代 any 类型
 *
 * 主要目的：
 * - 通过显式声明来规避 TypeScript 的 strict 模式检查
 * - 在代码中清晰标识"有意使用 any"的场景
 *
 * 使用场景：
 * 1. 处理未知的 API 响应数据
 * 2. 处理动态或运行时确定的数据结构
 * 3. 与未提供类型定义的第三方库交互
 * 4. 在严格模式下确实需要使用 any 的情况
 * 5. 处理复杂的泛型或类型推导场景
 *
 * 注意事项：
 * - 这不是一个用于临时绕过类型检查的工具
 * - 使用此类型表明这里确实需要 any 的灵活性
 * - 优先考虑使用具体类型或 unknown
 * - 使用时添加注释说明使用原因是一个好的做法
 * - 应该作为经过充分考虑后的类型选择
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type AnyType = any

/**
 * 空对象类型，用于表示一个空的对象，没有任何属性
 */
export type EmptyObject = Record<string, never>

/**
 * @deprecated 这是一个临时的类型定义，用于暂时绕过 TypeScript 类型检查
 * TODO: 需要替换为具体的类型定义
 *
 * 警告：
 * 1. 这是一个不安全的类型定义，应该避免使用
 * 2. 使用此类型会失去 TypeScript 的类型安全保护
 * 3. 所有使用此类型的地方都需要重构，替换为明确的类型定义
 *
 * 查找使用位置：
 * 使用 IDE 的 "查找所有引用" 功能定位所有使用此类型的位置
 *
 * 重构建议：
 * 1. 对于 API 响应数据，定义具体的接口类型
 * 2. 对于配置对象，使用 Record<string, unknown> 或具体的配置接口
 * 3. 对于事件处理，使用 Event 或具体的事件类型
 * 4. 对于函数参数和返回值，明确指定类型或使用泛型
 * 5. 实在无法确定类型时，优先使用 unknown 而不是 any
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type UnsafeAny = any

/**
 * 通用的唯一标识符类型
 * 用于表示数据库主键等唯一标识
 */
export type UniqueId = number

/**
 * API 响应数据结构
 *
 * 统一的 API 响应格式，包含状态码、消息和数据
 * 用于规范化处理所有后端接口的响应数据
 */
export type ApiResponse<ResultData = unknown> = {
  status: ApiStatus
  /** 响应消息 */
  message?: string
  /** 响应数据主体 */
  result?: ResultData
}

export interface PageInfo {
  /** 总条数 */
  total: number
  /** 当前页码 */
  page: number
  /** 每页条数 */
  page_size: number
  /** 总页数 */
  page_sum: number
}

/**
 * 分页数据响应结构
 *
 * 用于包装后端返回的分页数据，包含数据列表和分页信息
 *
 * @template ListItem - 列表项的数据类型
 */
export interface PageResult<ListItem> extends Pick<PageInfo, 'page' | 'page_sum' | 'total'> {
  /** 列表数据 */
  list: ListItem[]
}

/**
 * 原始数据响应结构，用于包装后端返回的原始数据
 */
export type RawResult<ExtraData = unknown> = ExtraData & {
  /** 原始数据 */
  __raw: ApiResponse
}

/**
 * 分页查询参数类型
 *
 * 将分页参数（page、page_size）和其他自定义查询参数合并成一个类型
 *
 * @template ExtraParams - 额外的查询参数类型，默认为空对象
 *
 * @example
 * // 基础用法：只使用分页参数
 * type BasicQuery = WithPageQuery
 * // { page?: number; page_size?: number }
 *
 * // 带额外参数的用法
 * type UserQuery = WithPageQuery<{
 *   name?: string;
 *   status?: number;
 * }>
 * // { page?: number; page_size?: number; name?: string; status?: number }
 */
export type WithPageQuery<ExtraParams extends Record<string, unknown> = Record<string, never>> =
  Partial<Pick<PageInfo, 'page' | 'page_size'>> & ExtraParams

export interface RouteConfig {
  /** 路由标题，用于页面标题展示 */
  title: string

  /**
   * 路由路径
   * @example '/user/profile'
   * @example '/xxx/[routeParam]/xxx'
   */
  route: string

  /** 图标组件 */
  icon?: Component

  /** 权限列表，位于该列表的权限可以访问该路由 */
  permissions?: PermissionFlag[]
}

/**
 * 应用路由配置类型
 *
 * 用于定义应用中所有路由的元数据信息
 */
export type AppRouteConfig = Record<RouteKey, RouteConfig>

/**
 * 图标项类型
 *
 * 用于表示图标的基本信息
 */
export interface IconItem {
  /** 图标名称 */
  name: string
  /** 图标标签 */
  label: string
}

export interface IconCollection {
  /** 图标组名称 */
  groupName: '数据库' | '云服务' | '操作系统' | '编程语言' | '常用'
  /** 图标列表 */
  icons: IconItem[]
}

export const schemaTemplateIcon = object({
  /** 图标类型 */
  type: union([literal('internal'), literal('external')]),
  /** 图标名称 */
  name: string(),
  /** 图标标签 */
  label: string(),
})

/** 模板图标类型 */
export type TemplateIcon = InferInput<typeof schemaTemplateIcon>

/** 排序方向: 1(升序),-1(降序) */
export type SortOrder = 1 | -1 | null

/**
 * 基础枚举配置接口
 *
 * 用于表示枚举的基本属性，如标签、外观、颜色等
 */
export type BaseEnumConfig = {
  /** 枚举名称 */
  label: string
  /** 标签 severity */
  severity: TagSeverity
  /** 参考前景色，通常用于文本、图标等 */
  frontColor?: string
  /** 参考背景色，通常用于图表、背景等 */
  bgColor?: string
}

/**
 * 带值的枚举配置类型
 *
 * 用于表示枚举值及其对应的配置信息
 *
 * @template T - 枚举值的类型
 */
export interface EnumConfigWithValue<T extends string | number> extends BaseEnumConfig {
  /** 枚举值 */
  value: T
}

/**
 * 枚举配置映射类型
 *
 * 用于将枚举值映射到其对应的配置信息
 *
 * @template T - 枚举值的类型
 * @template TConfig - 枚举配置的类型，默认为 BaseEnumConfig
 */
export type EnumConfig<
  T extends string | number,
  TConfig = BaseEnumConfig,
> = {
  [K in T]: TConfig & { value: K }
}

/**
 * 复制接口类型的工具类型
 *
 * 创建一个与输入类型完全相同的新类型，但是是一个独立的类型定义
 */
export type CopyInterface<T> = {
  [K in keyof T]: T[K];
}

/**
 * 自定义错误数据
 */
export interface CustomErrorData {
  /** 错误状态码 */
  statusCode: ApiStatus
  /** 错误描述消息 */
  message?: string
  /** 自定义错误数据，用于传递额外信息 */
  data?: Record<string, unknown>
}

/**
 * 全局事件类型，用于定义所有可能的全局事件类型
 */
export type GlobalEvents = {
  [GlobalEvent.SpaceCreate]?: SpaceSettingForm
  [GlobalEvent.SpaceUpdate]?: SpaceData
  [GlobalEvent.AppCreate]?: AppCreate
  [GlobalEvent.AppUpdate]?: AppUpdate
  [GlobalEvent.AppListRefresh]?: null
  [GlobalEvent.KnowledgeBaseCreate]?: KnowledgeBase
  [GlobalEvent.KnowledgeBaseUpdate]?: KnowledgeBase
  [GlobalEvent.KnowledgeBaseListRefresh]?: null

  [GlobalEvent.AddStickyNote]?: null
  [GlobalEvent.CloseNodeDetailsView]?: null
  [GlobalEvent.ToggleNodeCreator]: ToggleNodeCreatorOptions
  [GlobalEvent.NodeTypeSelected]: string[]
  [GlobalEvent.StickyNoteEditModeOn]: INodeUi['id']
  [GlobalEvent.StickyNoteStyleEditorOn]: INodeUi['id']
  [GlobalEvent.OpenContextMenu]: {
    event: MouseEvent
  } & ContextMenuTarget

  [GlobalEvent.WorkflowSave]?: null

  [GlobalEvent.NodeActivate]: INodeUi['id']
  [GlobalEvent.NodeActivateByTrigger]: {
    sourceNodeId: string
    sourceHandleId: string
  }
  [GlobalEvent.NodeRun]: INodeUi['id']

  [GlobalEvent.NodeSelectAll]?: null
  [GlobalEvent.NodeDeselectAll]?: null

  [GlobalEvent.NodeAdd]: AddedNodesAndConnections
  [GlobalEvent.NodeCopy]: INodeUi['id'][]
  [GlobalEvent.NodePaste]: string
  [GlobalEvent.NodeDuplicate]: INodeUi['id'][]
  [GlobalEvent.NodeDelete]: INodeUi['id'][]
  [GlobalEvent.NodeMove]: {
    nodeId: INodeUi['id']
    position: CanvasNode['position']
  }
  [GlobalEvent.NodeRename]: IUpdateInformation
  [GlobalEvent.NodeUpdate]: {
    nodeId: INodeUi['id']
    parameters: INodeUi['parameters']
  }

  [GlobalEvent.EdgeCreate]: Connection
  [GlobalEvent.EdgeDelete]: Connection
}

/** 原生 N8n 响应数据结构 */
export interface N8nResponse<Data = unknown> {
  data: Data
}
