import type { FormFieldState } from '@primevue/forms'
import type { ButtonProps } from 'primevue'

type ErrorMessage = {
  /** 字段校验错误信息 */
  message: string
}

export type FormContext<FormValues extends Record<string, unknown>> = {
  [K in keyof FormValues]: Omit<FormFieldState, 'error' | 'errors'> & {
    error: ErrorMessage
    errors: ErrorMessage[]
  }
}

export type FormFieldContext = Omit<FormFieldState, 'error' | 'errors'> & {
  error: ErrorMessage
  errors: ErrorMessage[]
  onChange: ({ target: { value } }: { target: { value: unknown } }) => void
}

export type FormErrors<TValues extends Record<string, unknown>> = Partial<Record<keyof TValues, ErrorMessage[]>>

export type FormStates<T> = {
  [K in keyof T]: Ref<T[K]>
}

export type FormSubmitOptions<FormValues = Record<string, unknown>> = {
  valid: boolean
  states: FormStates<FormValues>
}

export type FormEditState = 'create' | 'update'

export interface FormActionGroupProps {
  /** 是否处于加载状态 */
  loading?: boolean
  justify?: 'start' | 'center' | 'end'
  template?: string
  cancelButtonProps?: ButtonProps | false
  confirmButtonProps?: ButtonProps | false
  onCancel?: () => void
  onConfirm?: () => void
}
