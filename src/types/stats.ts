import type { AssetCriticality, AssetType, TaskScheduleType, TaskStatus } from '~/enums/asset'
import type { TicketGrade, TicketStatus } from '~/enums/ticket'

export interface StatsOverviewQuery {
  network_id?: Network['id']
  network_equipment_id?: NetworkDevice['id']
  asset_type?: AssetType
}

export interface AssetScanTask {
  id: UniqueId
  task: TaskDetail
  task_id: TaskDetail['id']
  run_type: TaskScheduleType
  run_type_name: string
  status: TaskStatus
  create_time: string
  result: string
}

export interface AssetStats {
  /** 资产数量统计 */
  count: {
    /** 网络数量 */
    network: number
    /** 网络设备数量 */
    network_equipment: number
    /** 子网数量 */
    ip_subnet: number
    /** IP 数量 */
    ip: number
    /** 服务器数量 */
    server: number
    /** 数据库数量 */
    database: number
    /** Web 数量 */
    web: number
    /** 其他数量 */
    other: number
  }

  /** 资产等级统计 */
  grade: { name: string, count: number, grade: AssetCriticality }[]

  /** 资产扫描任务列表 */
  asset_scan_task_list: AssetScanTask[]

  ip_log_list: {
    date: string
    count: number
  }[]

  ip_status_list: {
    name: string
    data: {
      name: '在线' | '离线' | '未知'
      count: number
    }[]
  }[]

  ip_list: {
    name: string
    data: {
      name: string
      count: number
    }
  }[]

  asset_list: {
    name: string
    data: {
      name: string
      count: number
    }[]
  }[]
}

/** 资产数量统计 */
export interface AssetCountStats {
  /** 网络数量 */
  network: number
  /** 网络设备数量 */
  network_equipment: number
  /** 子网数量 */
  ip_subnet: number
  /** IP 数量 */
  ip: number
  /** 服务器数量 */
  server: number
  /** 数据库数量 */
  database: number
  /** Web 数量 */
  web: number
  /** 其他数量 */
  other: number
}

/** 资产重要性统计 */
export interface AssetImportanceStats {
  name: string
  count: number
  grade: AssetCriticality
}

/** 资产 IP 变更统计 */
export interface AssetIpLogStats {
  date: string
  count: number
}

/** IP 在线统计 */
export interface IpOnlineStats {
  name: string
  data: {
    name: '在线' | '离线' | '未知'
    count: number
  }[]
}

/** IP 位置统计 */
export interface IpLocationStats {
  name: string
  data: {
    name: string
    count: number
  }
}

/** IP 资产变化统计 */
export interface IpAssetChangeStats {
  name: string
  data: {
    name: string
    count: number
  }[]
}

/** 漏洞等级统计查询参数 */
export type VulLevelQuery = Omit<VulLibListQuery, 'level'>

export interface VulLevelStats {
  level: Vul['level']
  /** 该等级对应的漏洞数量 */
  count: number
}

/** 漏洞类型统计 */
export interface VulTypeStats {
  cnnvd_id: SecurityAlert['cnnvd_id']
  name: SecurityAlert['name']
  /** 该类型对应的漏洞数量 */
  count: number
}

/** 漏洞状态统计 */
export interface VulCountStats {
  vul: number
  server: number
  /** 已修复 */
  recovered: number
  /** 处理中 */
  processing: number
  /** 未修复 */
  wontfix: number
}

/** 合规检查项数值统计 */
export interface ComplianceCheckCountStats {
  baseline: number
  server: number
  /** 通过数量 */
  pass_count: number
  /** 不通过数量 */
  not_pass_count: number
  /** 通过率 */
  pass_rate: number
}

/** 厂商公布的漏洞统计 */
export interface VendorVulStats {
  id: VendorListItem['id']
  name: VendorListItem['name']
  count: number
}

/** 工单数量统计 */
export interface TicketCountStats {
  work_order: number
  baseline: number
  vul: number
}

/** 工单等级统计 */
export interface TicketGradeLevelStats {
  name: string
  grade: TicketGrade
  count: number
}

/** 工单类型统计 */
export interface TicketStatusStats {
  name: string
  status: TicketStatus
  count: number
}

export interface TicketLog {
  date: string
  count: number
}
