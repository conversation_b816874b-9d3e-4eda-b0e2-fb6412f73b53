import { array, email, maxLength, minLength, nullable, number, optional, pipe, regex, string, trim, undefinedable } from 'valibot'

export const schemaNotEmptyString = (message: string) => pipe(
  string(message),
  trim(),
  minLength(1, message),
)

export const schemaPassword = pipe(
  string('请输入密码'),
  trim(),
  minLength(8, '密码长度不能少于 8 位'),
  maxLength(20, '密码长度不能超过 20 位'),
  regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,20}$/,
    '密码必须包含大小写字母和数字',
  ),
)

export const schemaEmail = pipe(
  string('请输入邮箱'),
  trim(),
  email('请输入正确的邮箱格式'),
)

export const schemaMobile = pipe(
  string('请输入手机号'),
  trim(),
  minLength(1, '请输入手机号'),
  minLength(11, '手机号长度不正确'),
  regex(/^1[3-9]\d{9}$/, '请输入正确的手机号格式'),
)

export const schemaOptionalString = () => optional(nullable(undefinedable(string())))

export const schemaNotEmptyArray = (message: string) => pipe(
  array(number(), message),
  minLength(1, message),
)

/**
 * 变量名验证模式
 *
 * 规则：
 * 1. 必须以字母或下划线开头
 * 2. 只能包含字母、数字和下划线
 * 3. 长度在 1-20 之间
 */
export const schemaVar = () => pipe(
  string('请输入字段名'),
  trim(),
  minLength(1, '字段名不能为空'),
  regex(
    /^[a-zA-Z_][a-zA-Z0-9_]*$/,
    '只能包含字母、数字和下划线，且必须以字母或下划线开头',
  ),
  maxLength(20, '字段名长度不能超过 50 个字符'),
)
