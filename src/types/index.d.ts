declare module '#app' {
  interface PageMeta {
    /** 页面标题，用于网页标题、页面头部标题等 */
    title?: string

    /** 页面描述，用于 SEO 优化、页面头部展示等 */
    description?: string

    /** 页面布局配置 */
    layoutConfig?: {
      /** 是否使用自定义布局容器 */
      customContent?: boolean
      /** 是否不使用布局容器 padding */
      noContainerPadding?: boolean
      /** 是否不使用内容 padding */
      noContentPadding?: boolean
      /**
       * 是否显示布局头部
       *
       * 默认情况下，页面会使用自定义布局容器，并显示布局头部。
       * 如果你想自定义 header 内容，可以设置 `showHeader` 为 `false` 隐藏默认 header
       */
      showHeader?: boolean
      /** 是否显示布局头部返回按钮 */
      showHeaderBack?: boolean
    }

    /** 页面权限，优先级高于路由配置中定义的权限 */
    permissions?: string[]
  }
}

declare module 'ofetch' {
  interface FetchOptions {
    /**
     * 是否直接返回原始数据
     *
     * 默认情况下，API 响应会包含 `result` 字段，并包装在 `__raw` 字段中
     * 设置 `raw: true` 时，直接返回原始数据
     */
    raw?: boolean
  }
}

export {}
