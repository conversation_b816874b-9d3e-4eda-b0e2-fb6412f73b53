import type { UserStatus } from '~/enums/user'

export interface Role {
  id: UniqueId
  /** 角色名 */
  name: string
  /** 拥有权限的路由菜单列表 */
  menu_list?: NavMenuItemWithId['id'][]
  create_time: string
  update_time?: string
  /** 是否可修改 */
  is_update?: boolean
}

/** 用户数据 */
export interface User {
  id: UniqueId
  username: string
  email: string
  mobile?: string
  role_id: Role['id']
  role_name: Role['name']
  status: UserStatus
  organize_id: number
  organize_name: string
  avatar?: string
  job_number?: string
  position?: string
  create_time: string
  update_time?: string
  last_login_time?: string
  last_login_ip?: string
  /** 是否可修改 */
  is_update?: boolean
}

/** 当前登录用户数据 */
export interface CurrentUser extends User {
  /** 可访问的路由菜单（RouteKey[]） */
  menu_list?: string[]
}

/** 登录成功时获取到的用户数据 */
export interface AuthResponse {
  /** 授权令牌 */
  token?: string
  /** 用户信息 */
  user?: CurrentUser
}

/** 登录表单需要发送的数据 */
export interface LoginFormValues {
  username: string
  password: string
  uuid?: Captcha['uuid']
  captcha?: string
}

/** 登录表单需要校验的数据 */
export type LoginFormValidValues = Omit<LoginFormValues, 'uuid'>

export type ResetPasswordFormValues = {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

export type RoleFormValues = Pick<Role, 'name' | 'menu_list'>

export type UserFormValues = Pick<User, 'username' | 'role_id' | 'mobile' | 'email'> & {
  password?: LoginFormValues['password']
}

export interface UserGroup {
  id: UniqueId
  /** 组织名称 */
  name: string
  /** 父级组织 ID */
  parent_id?: UserGroup['id']
  /** 组织负责人 */
  user_id?: User['id']
  create_time: string
  update_time: string
}

export type UserGroupTreeNode = TreeNodeWrap<UserGroup>

export type UserGroupFormValues = Pick<UserGroup, 'name' | 'parent_id' | 'user_id'>

export interface License {
  /** 公钥 */
  public_key: string
  /** 授权对象 */
  name: string
  /** 版本 */
  version: string
  /** 过期时间 */
  expiration_time?: string
  /** 颁发时间 */
  create_time: string
  /** 是否有效 */
  status: boolean
  /** 本机识别码 */
  machine_id: string
}

export interface UserAuthResourceOperation {
  /** 操作标识，比如：create、update、export */
  code: string
  /** 操作名称，用于文案展示 */
  name?: string
}

export interface UserAuthResource {
  id: UniqueId
  /** 父级资源 ID */
  parent_id?: UserAuthResource['id']
  /** 资源名称，用于文案展示 */
  name: string
  /** 资源标识，按系统功能模块划分，比如该资源是用户管理模块，则标识为：user */
  code: string
  /** 操作权限 */
  operations: UserAuthResourceOperation[]
  /** 资源描述 */
  description?: string
}

export type UserAuthResourceFormValues = Pick<UserAuthResource, 'parent_id' | 'name' | 'code' | 'operations' | 'description'>

/**
 * 权限标识
 * 格式为 `resource.operation`，比如：file.edit、user.create
 */
export type PermissionFlag = string
