import type { ColumnProps, TagProps } from 'primevue'
import type { MenuItem } from 'primevue/menuitem'
import type { TreeNode } from 'primevue/treenode'

import type { ProMenuActionType, ProTableSortOrder, ProValueType } from '~/enums/pro'

export interface PrimeVuePageInfo {
  /** 当前页码 */
  page: PageInfo['page']
  /** 每页条数 */
  rows: PageInfo['page_size']
  /** 总页数 */
  pageCount: PageInfo['page_sum']
}

/** PrimeVue Tree 组件的选中状态 */
export type PrimeVueTreeSelectionKeys = Record<NavMenuItem['key'], { checked: boolean, partialChecked?: boolean }>

export interface PrimeVueMenuItem extends MenuItem {
  /** 自定义选项图标 */
  itemIcon?: Component | null
}

// MARK: PrimeVue 类型补充

/** PrimeVue PanelMenu 组件的展开状态 */
export type ExpandedKeys = Record<string, boolean>

/**
 * PrimeVue 树节点的包装类型
 *
 * 扩展 PrimeVue 的 TreeNode 类型，使其能够包含自定义数据
 *
 * @template T - 需要添加到树节点的自定义数据类型
 *
 * @example
 * interface FileNode {
 *   name: string;
 *   size: number;
 * }
 *
 * // 文件树节点类型
 * type FileTreeNode = TreeNodeWrap<FileNode>
 */
export type TreeNodeWrap<T> = TreeNode & T & {
  /** 子节点列表 */
  children?: TreeNodeWrap<T>[]
}

/** PrimeVue Tag 组件的 severity 类型 */
export type TagSeverity = TagProps['severity']

/**
 * 表格操作类型，包含表格的重新加载、重置等操作
 */
export interface TableActionType {
  /** 重新加载数据 */
  reload: () => void
  /** 重新加载数据并重置分页 */
  reloadAndRest: () => void
  /** 重置分页、过滤、排序、表单等 */
  reset: () => void
}

/** 值枚举类型 */
export type ProValueEnumObj = Record<string, string>

/** 值枚举类型 */
export type ProValueEnumMap = Map<AnyType, {
  label: string
  value: AnyType
}>

/**
 * 表格列配置接口
 */
export interface ProTableColumn<RecordType = Record<string, unknown>> extends Omit<ColumnProps, 'hidden'> {
  /** 值类型，用于自动转换值、渲染值 */
  valueType?: ProValueType

  /** 值枚举，用于自动转换值、渲染值
   *
   * 支持两种类型：
   * 1. 对象类型，key 为枚举值，value 为枚举标签
   * 2. Map 类型，key 为枚举值，value 为枚举标签
   */
  valueEnum?: ProValueEnumObj | ProValueEnumMap

  /** 列宽度 */
  width?: string | number

  /** 自定义单元格渲染函数 */
  render?: (rowData: RecordType, rowIndex: number) => VNode | string | null | undefined

  /** 过滤字段名，不传则使用 field */
  filterField?: string

  /** 自定义过滤器渲染函数 */
  filterRender?: (filterModel: UnsafeAny) => VNode

  /** 自定义样式 */
  style?: string | object

  /** 自定义类名 */
  class?: string | object | string[]

  /** 是否固定列 */
  frozen?: boolean

  /** 固定列位置 */
  alignFrozen?: 'left' | 'right'

  /** 是否隐藏该表格列 */
  hideInTable?: boolean

  /** 是否隐藏过滤器 */
  hideInFilter?: boolean

  /** 导出时的列标题 */
  exportHeader?: string

  /** 是否在导出时包含该列 */
  exportable?: boolean

  /** 是否在分组时包含该列 */
  groupable?: boolean
}

/** 表格搜索值 */
export type ProTableSearchValue = string

/** 表格过滤条件 */
export type ProTableFilterValues = Record<string, UnsafeAny>

/** 表格分组配置 */
export interface ProTableGroupConfig {
  /** 分组字段 */
  by: string
}

/** 表格排序 */
export interface ProTableSort {
  field: string
  order: ProTableSortOrder
}

export interface ProSelectOption<V = string | number> {
  label: string
  value: V
  description?: string
  icon?: Component | string
  [key: string]: UnsafeAny
}

export interface ProMenuItem extends PrimeVueMenuItem {
  /** 菜单的操作类型 */
  actionType?: ProMenuActionType
  /** 菜单图标 */
  itemIcon?: Component | null
  /** 是否激活 */
  active?: boolean
  /** 子菜单 */
  items?: ProMenuItem[]
  /** 是否隐藏 */
  hide?: boolean
}

/** 表单标签值项 */
export interface ProFormLabelValueItem {
  label?: string
  value?: AnyType
  valueType?: ProValueType
  labelIcon?: Component
}
