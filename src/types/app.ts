import type { HintedString } from '@primevue/core'

export type ThemeMode = 'light' | 'dark'

export type ThemeName = ThemeMode | 'auto'

type SubLayoutName = 'dashboard' | 'settings' | 'space' | 'space-app-workflow'

export type LayoutName =
  'default'
  | 'admin-horizontal'
  | 'admin-horizontal-blank'
  | 'admin-vertical'
  | 'admin-vertical-blank'
  | 'client-view'
  | SubLayoutName

export type PrimaryColorName = HintedString<'emerald' | 'orange' | 'blue' | 'indigo' | 'rose' | 'noir'>

export type SurfaceColorName = 'zinc' | 'slate' | 'gray' | 'neutral' | 'stone' | 'viva' | 'ocean'

/** 页面切换过渡动画类型 */
export type PageTransition = 'none' | 'fade' | 'slide' | 'zoom'

export interface PrimaryColor {
  name: PrimaryColorName | 'noir'
  palette: Record<number, string>
}

export interface SurfaceColor {
  name: SurfaceColorName
  palette: Record<number, string>
}

/** 主题配置 */
export interface ThemeState {
  /** 主题模式: 亮色/暗色/自动 */
  themeMode: ThemeName
  /** 主色调 */
  primaryColor: PrimaryColorName
  /** 表面色 */
  surfaceColor: SurfaceColorName
  /** 页面切换过渡动画类型 */
  transition: PageTransition
  /** 是否展示面包屑导航 */
  showBreadcrumb: boolean
  /** 页面布局 */
  layout: LayoutName
}
