import type { VulThreatLevel } from '~/enums/security'

/** 合规检查模板 */
export interface BaselineTpl {
  id: UniqueId
  name: string
  /** 系统 ID */
  system_id: UniqueId
}

export type BaselineTplListItem = BaselineTpl

export type BaselineFormValues = Pick<BaselineTpl, 'name' | 'system_id'>

/** 检查的操作系统 */
export type BaselineSystem = {
  id: UniqueId
  /** 系统名称 */
  name: string
}

export type BaselineOption = {
  id: UniqueId
  system_option?: {
    id: UniqueId
    system_id: UniqueId
    name: string
  }
  template_baseline_id: UniqueId
  system_option_id: UniqueId
  kwargs: Record<string, unknown>
}

export type BaselineOptionFormValues = Pick<BaselineOption, 'system_option_id' | 'kwargs'>

/** 系统检查项 */
export interface BaselinePolicyOption {
  id: UniqueId
  /** 检查项名称 */
  name: string
  /** 检查项归属的系统 */
  system_id: BaselineSystem['id']
  /** 是否可更改 */
  is_update: boolean
  /** 检查项级别 */
  level: VulThreatLevel
  /** 检查项描述 */
  desc: string
  /** 检查项修复建议 */
  suggest: string
  /** 检查项命令 */
  command: string
}

/** 系统检查项 */
export type BaselinePolicyOptionListItem = Pick<BaselinePolicyOption, 'id' | 'name' | 'system_id' | 'is_update' | 'level'>

/** 基线策略表单值 */
export type BaselinePolicyFormValues = Omit<BaselinePolicyOption, 'id' | 'is_update'>

export type SystemOptionFormValues = {
  system_option_id: BaselinePolicyOption['id']
}
