import type { TaskScheduleType, TaskStatus } from '~/enums/asset'

/** 扫描任务详情 */
export interface Task {
  id: UniqueId
  interval_object: {
    id: UniqueId
    every: number
    period: string
  }
  crontab_object: {
    id: UniqueId
    minute: '0'
    hour: '*'
    day_of_month: '*'
    month_of_year: '*'
    day_of_week: '*'
    timezone: 'Asia/Shanghai'
  }
  clocked_object: {
    id: UniqueId
    clocked_time: string
  }
  /** 任务名称 */
  name: string
  /** 任务类型 */
  task: string
  /** 任务参数 */
  args: string
  /** 任务关键字参数 */
  kwargs: string
  /** 队列 */
  queue?: string
  /** 交换机 */
  exchange?: string
  /** 路由键 */
  routing_key?: string
  headers: string
  priority?: string
  expires?: string
  expire_seconds?: string
  start_time?: string
  /** 创建时间 */
  create_time?: string
  /** 最后一次执行时间 */
  last_run_at?: string
  enabled: boolean
  /** 总共执行次数 */
  total_run_count: number
  date_changed: string
  description: string
  interval: null
  crontab?: number
  solar?: null
  clocked?: null
}

export type TaskListItem = Task

export type TaskDetail = Task

interface TaskSchedule {
  /** 时间间隔 - 周期数 */
  every?: number
  /** 时间间隔 - 间隔周期 */
  period?: string

  /** 计划时间 - 分钟 */
  minute?: string
  /** 计划时间 - 小时 */
  hour?: string
  /** 计划时间 - 周 */
  day_of_week?: string
  /** 计划时间 - 日 */
  day_of_month?: string
  /** 计划时间 - 月 */
  month_of_year?: string

  /** 定时时间 - 时间 */
  clocked_time?: string
}

/** 扫描任务配置 */
export interface TaskScanConfig {
  id: UniqueId
  /** 任务名称 */
  name: string
  /** 任务描述 */
  description: string
  /** 任务过期时间，过期后不再执行 */
  expires?: string
  /** 任务首次开始执行时间 */
  start_time?: string

  /** 任务类型 */
  schedule_type: TaskScheduleType

  /** 时间间隔 Schedule */
  interval_object: TaskSchedule
  /** 时间间隔 Schedule ID */
  interval?: UniqueId

  /** 计划时间 Schedule */
  crontab_object: TaskSchedule
  /** 计划时间 Schedule ID */
  crontab?: UniqueId

  /** 定时时间 Schedule */
  clocked_object: TaskSchedule
  /** 定时时间 Schedule ID */
  clocked?: UniqueId

  /** 总执行次数 */
  total_run_count: number
  /** 最后一次执行时间 */
  last_run_at: string
}

export type TaskScanFormValues = Pick<TaskScanConfig, 'name' | 'description' | 'schedule_type' | 'expires' | 'start_time'> & {
  schedule?: TaskSchedule
}

export interface TaskExecutionLog {
  id: UniqueId
  task: Pick<Task, 'id' | 'name'>
  run_type_name: string
  status: TaskStatus
  create_time: string
  result: string
}
