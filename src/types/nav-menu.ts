import type { RouteKey } from '~/enums/route'

interface BaseMenuItem extends Omit<PrimeVueMenuItem, 'url'> {
  key: RouteKey
  items?: NavMenuItem[]
  /** 是否为菜单组，如果是，则在点击时忽略路由跳转 */
  group?: boolean
  permissions?: PermissionFlag[]
}

interface RouteMenuItem extends BaseMenuItem {
  route?: AppRouteConfig[RouteKey]['route']
  url?: never
  /** 自定义激活判断函数 */
  isActive?: (route: ReturnType<typeof useRoute>) => boolean
}

interface UrlMenuItem extends BaseMenuItem {
  url?: PrimeVueMenuItem['url']
  route?: never
}

export type NavMenuItem = RouteMenuItem | UrlMenuItem

/** 后端存储的权限菜单 */
export type NavMenuItemWithId = {
  id: RouteKey
  path: NavMenuItem['route']
}
