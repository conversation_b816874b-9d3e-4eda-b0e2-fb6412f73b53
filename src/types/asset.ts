import type { AssetCriticality, AssetDetailTab, AssetFieldType, AssetSearchType, AssetShareCollectionLinkStatus, AssetType, Protocol, ServerPortStatus, SubnetIpStatus, TopologyNodeType } from '~/enums/asset'
import type { VulThreatLevel } from '~/enums/security'

export type NetworkJob = {
  id: UniqueId
  ip_segment?: string
  network_equipment_id: NetworkDevice['id']
}

export type AssetDetailTabType = (typeof AssetDetailTab)[keyof typeof AssetDetailTab]

// MARK: >> 资产模板字段

/** 资产模板字段 */
export interface AssetField {
  id: UniqueId
  /** 所属资产模板 */
  template_id: AssetTemplate['id']
  /** 字段展示名称 */
  name: string
  /** 字段名称 */
  field_name: string
  field_type?: {
    id: AssetField['field_type_id']
    name: string
    form: string
    sql: string
  }
  /** 字段类型 ID */
  field_type_id: AssetFieldType
  /** 字段配置 */
  kwargs?: {
    /** 字段默认值 */
    defaultValue?: string
    /** 是否唯一标识 */
    unique?: boolean
    /** 是否为必要字段 */
    required?: boolean
    /** @deprecated 是否在列表中展示 */
    displayInList?: boolean

    /** 输入占位符 */
    placeholder?: string
    /** 选项 */
    options?: {
      label: string
      value: string
    }[]
  } & Record<string, unknown>
  /** 字段是否可更改 */
  is_update?: boolean
}

/** 资产模板字段表单需要填写的值 */
export type AssetFieldFormValues = Pick<AssetField, 'name' | 'field_name' | 'field_type_id' | 'kwargs'>

export type AssetTemplate = {
  id: UniqueId
  /** 资产模板名称 */
  name: string
  /** 所属的资产类型 */
  asset_type: AssetType
  /** 模板展示图标 */
  icon: string
  field_list?: AssetField[]
  /** 是否可被修改 */
  is_update?: boolean
}

// MARK: >> 资产模板

export type AssetTemplateListItem = AssetTemplate

export type AssetTemplateFormValues = Pick<AssetTemplate, 'name' | 'icon'>

// MARK: >> 网络

/** 网络 */
export type Network = {
  id: UniqueId
  name: string
  /** 父级网络 ID */
  parent_id?: Network['id']
  /** 创建时间 */
  create_time: string
  /** 各种数量统计 */
  count: {
    network_equipment: number
    node: number
  }
}

export type NetworkTreeNode = TreeNodeWrap<Network>

export type NetworkFormValues = Pick<Network, 'name' | 'parent_id'>

// MARK: >> 网络设备

/** 网络设备 */
export interface NetworkDevice extends BasicAssetDetail {
  id: UniqueId
  /** 父级网络设备 ID */
  parent_id?: NetworkDevice['id']
  /** 所属网络 */
  network: Network
  network_id: Network['id']
  template: AssetTemplate
  /** 该网络设备基本哪个模板创建的 */
  template_id: AssetTemplate['id']
  /** 资产模板中定义的动态字段值 */
  asset?: {
    /** 资产 ID */
    id: NetworkDevice['id']
    /** 资产名称 */
    name?: string
    /** 重要程度 */
    grade?: AssetCriticality
  } & Record<string, unknown>
  /** 节点 URL */
  node_url?: string
  /** 节点状态，true 表示正常，false 表示异常 */
  node_status?: boolean
  asset_scan_task_id?: UniqueId
  asset_scan_task?: TaskScanConfig
  baseline_scan_task_id?: UniqueId
  baseline_scan_task?: TaskScanConfig
  vul_scan_task_id?: UniqueId
  vul_scan_task?: TaskScanConfig
  /** 各种数量统计 */
  count: {
    ip_subnet: number
    ip: number
    server: number
    web: number
    database: number
    other: number
  }
}

export type NetworkDeviceFormValues = Pick<NetworkDevice, 'network_id' | 'template_id' | 'parent_id'> & {
  asset?: Omit<NetworkDevice['asset'], 'id'>
}

export type NetworkDeviceTreeNode = TreeNodeWrap<NetworkDevice>

// MARK: >> 子网

/** 子网 */
export interface Subnet {
  id: UniqueId
  /** 所属网络设备 */
  network_equipment: NetworkDevice
  /** 所属网络设备 */
  network_equipment_id: NetworkDevice['id']
  /** IP 段 */
  ip_segment: string
  /** 状态 */
  status: SubnetIpStatus
  /** 统计 */
  count: {
    online: number
    offline: number
    unknown: number
  }
}

export type SubnetFormValues = Pick<Subnet, 'network_equipment_id' | 'ip_segment'>

export type SubnetTreeNode = TreeNodeWrap<Subnet>

/** 资产所属服务器的详细信息 */
export type ServerDetail = Pick<Server, 'id' | 'asset' | 'template_id' | 'network_equipment_id' | 'ip_list' | 'asset_type' | 'port'>

/** 资产所属服务器的信息 */
export interface ServerInfo extends Pick<Server, 'id' | 'template_id'> {
  name: NonNullable<Server['asset']>['name']
  asset_type: AssetType
  ip_list: Omit<SubnetIp, 'server' | 'server_id'>[]
}

/** 子网 IP */
export interface SubnetIp extends Pick<NetworkDevice, 'asset'> {
  id: UniqueId
  /** 所属网络 */
  network_id: Network['id']
  /** 所属服务器 */
  server?: ServerInfo
  server_id?: Server['id']
  ipv4: string
  ipv6?: string
  /** MAC 地址 */
  mac?: string
  /** 状态 */
  status?: SubnetIpStatus
  create_time: string
  last_scan_time?: string
}

export type SubnetIpFormValues = Pick<SubnetIp, 'ipv4' | 'ipv6' | 'mac' | 'server_id'>

export type SubnetIpListQuery = WithPageQuery<{
  subnet_id?: Subnet['id']
  /** 名称搜索 */
  search?: string
}>

export interface IpLog {
  id: UniqueId
  ip: SubnetIp
  task_log: {
    id: UniqueId
    task_id: UniqueId
    create_time: string
  }
  old_ipv4: string
  new_ipv4: string
  create_time: string
}

/** 资产详情的通用数据结构 */
export interface BasicAssetDetail {
  id: UniqueId
  asset_type: AssetType
  /** 所属终端设备 */
  network_equipment?: NetworkDevice
  /** 所属终端设备 ID */
  network_equipment_id: NetworkDevice['id']
  /** 所属资产模板 */
  template?: AssetTemplate
  template_id?: AssetTemplate['id']
  /** 资产的动态字段值 */
  asset?: NetworkDevice['asset']
  /** 所属终端设备 */
  server?: ServerInfo
  server_id?: NonNullable<BasicAssetDetail['server']>['id']
  /** 所属终端设备的端口 */
  port?: ServerPort
  port_id?: ServerPort['id']
  /** 创建时间 */
  create_time?: string
}

export type BasicAssetDetailFormValues = Pick<BasicAssetDetail, 'template_id' | 'server_id' | 'port_id'> & {
  asset?: Omit<NonNullable<BasicAssetDetail['asset']>, 'id'>
}

// MARK: >> 服务器

/** 服务器 */
export interface Server extends Omit<BasicAssetDetail, 'server' | 'server_id'> {
  id: UniqueId
  /** 父级服务器 ID */
  parent_id?: Server['id']
  config?: {
    id: UniqueId
    password: string
    template_baseline_name: string | null
    protocol_name: string
    server_id: UniqueId
    template_baseline_id: null
    protocol: 1
    port: number
    username: string
    key: string
  }
  ip_list?: ServerInfo['ip_list']
  /** 漏洞等级统计 */
  level?: { level: VulThreatLevel | null, count: number }[]
}

export interface ServerListItem extends BasicAssetDetail, ServerDetail {
}

export type ViewedServer = Pick<Server, 'id'>

export type ServerTreeNode = TreeNodeWrap<Server>

export type ServerFormValues = BasicAssetDetailFormValues

export interface ServerConfig {
  id: UniqueId
  protocol_name?: string
  server_id?: Server['id']
  protocol?: Protocol
  port?: number
  username?: string
  password?: string

  /** 配置的基线模板 */
  template_baseline_id?: BaselineTpl['id']
}

export type ServerConfigFormValues = Pick<ServerConfig, 'protocol' | 'port' | 'username' | 'password' | 'template_baseline_id'>

export interface ServerPort {
  id: UniqueId
  /** 端口 */
  port: number
  /** 所属服务器 */
  server_id: Server['id']
  status: ServerPortStatus
  /** 指纹 */
  fingerprint?: string[]
  /** 最后一次扫描时间 */
  last_scan_time?: string
}

export type ServerPortFormValues = Pick<ServerPort, 'port'>

// MARK: >> 数据库

/** 数据库 */
export interface Database extends BasicAssetDetail {
  id: UniqueId
  /** 数据库表名称 */
  table_name?: string
}

export type DatabaseFormValues = BasicAssetDetailFormValues

// MARK: >> 业务系统

/** 业务系统 */
export interface BusinessSystem extends BasicAssetDetail {
  id: UniqueId
}

export type BusinessSystemFormValues = BasicAssetDetailFormValues

// MARK: >> 其他资产

/** 其他资产 */
export interface OtherAsset extends BasicAssetDetail {
  id: UniqueId
}

export type OtherAssetFormValues = BasicAssetDetailFormValues

// MARK: >> 网络拓扑

export interface SubNode {
  type: TopologyNodeType
  data: WithPageQuery<Partial<{
    network_id: Network['id']
    network_equipment_id: NetworkDevice['id']
    server_id: Server['id']
    database_id: Database['id']
    web_id: BusinessSystem['id']
    other_id: OtherAsset['id']
  }>>
}

export interface Topology {
  id: string
  parent_id?: Topology['id']
  /** 节点类型 */
  topology_type: TopologyNodeType
  /** 节点元数据 */
  node?: {
    count: number
    /** 是否还有更多子节点 */
    has_more: boolean
    /** 子节点 */
    sub_node?: SubNode
  }
}

export type TopologyNode = Topology & {
  key: Topology['id']
} & ({
  topology_type: TopologyNodeType.Network
  nodeData: {
    name: string
  }
} | {
  topology_type: TopologyNodeType.Root
  nodeData: {
    name: string
  }
} | {
  topology_type: TopologyNodeType.NetworkDevice
  nodeData: NetworkDevice & {
    name: string
  }
} | {
  topology_type: TopologyNodeType.Server
  nodeData: Server & {

  }
} | {
  topology_type: TopologyNodeType.Database
  nodeData: Database & {
    name: string
  }
} | {
  topology_type: TopologyNodeType.Web
  nodeData: BusinessSystem & {
    name: string
  }
} | {
  topology_type: TopologyNodeType.Other
  nodeData: OtherAsset & {
    name: string
  }
} | {
  topology_type: TopologyNodeType.LoadMore
  nodeData: {
    name: string
  }
} | {
  topology_type: TopologyNodeType.ServerTemplateType
  nodeData: {
    icon: AssetTemplate['icon']
    name: string
  }
} | {
  topology_type: TopologyNodeType.ServerAssetType
  nodeData: {
    icon: AssetTemplate['icon']
    asset_type: AssetType
    name: string
  }
})

export type TopologyTreeNode = TopologyNode & {
  children?: TopologyTreeNode[]
}

export interface AssetShareCollectionLinkInfo {
  /** 链接的唯一标识 */
  key: string
  /** 所属网络设备 ID */
  network_equipment_id: number
  /** 链接的过期时间 */
  expires: string | null
  /** 链接的密码 */
  password?: string
  /** 链接的状态 */
  status: AssetShareCollectionLinkStatus
}

export type AssetShareCollectionLinkInfoFormValues = Pick<AssetShareCollectionLinkInfo, 'expires' | 'status'>

export type AssetDetailResult = NetworkDevice | ServerDetail | Database | BusinessSystem | OtherAsset

// MARK: >> 全局资产搜索

export interface SearchAssetIp extends BasicAssetDetail {
  search_type: AssetSearchType.Ip
  ipv4: string
  ipv6?: string
  mac?: string
  status: SubnetIpStatus
  subnet: Subnet
}

export interface SearchAssetNetworkDevice extends BasicAssetDetail {
  search_type: AssetSearchType.NetworkDevice
  network: NetworkDevice['network']
  ip_list: ServerInfo['ip_list']
}

export interface SearchAssetServer extends BasicAssetDetail {
  search_type: AssetSearchType.Server
  ip_list: ServerInfo['ip_list']
}

export interface SearchAssetDatabase extends Database {
  search_type: AssetSearchType.Database
}

export interface SearchAssetWeb extends BusinessSystem {
  search_type: AssetSearchType.Web
}

export interface SearchAssetOther extends OtherAsset {
  search_type: AssetSearchType.Other
}

export type AssetSearchItem = {
  search_type: AssetSearchType
} & (SearchAssetIp | SearchAssetNetworkDevice | SearchAssetServer | SearchAssetDatabase | SearchAssetWeb | SearchAssetOther)

export interface AssetSearch extends PageResult<AssetSearchItem> {
  count: {
    count: number
    name: string
    type: AssetSearchType | null
  }[]
}

/** 资产导入结果消息节点 */
export interface AssetImportResultNode {
  /** 状态类型 */
  status: 'success' | 'error' | 'info'
  /** 消息内容 */
  message: string
  /** 子节点列表 */
  children?: AssetImportResultNode[]
}

/** 资产导入的结果 */
export type AssetImportResult = AssetImportResultNode[]
