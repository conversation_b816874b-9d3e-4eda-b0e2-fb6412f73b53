/**
 * Fetch API 相关类型定义
 *
 * 扩展 ofetch 库的 FetchOptions 接口，添加对响应适配器的支持
 */

import type { AnyType, ApiResponse } from '~/types/common'

// 扩展 FetchOptions 接口
declare module 'ofetch' {
  interface FetchOptions {
    /**
     * 响应适配器函数
     * 用于处理非标准 API 响应结构
     *
     * @param responseData API 原始响应数据
     * @param response 完整的响应对象
     */
    responseAdapter?: <ResponseData>(responseData: ResponseData, response: AnyType) => ApiResponse

    /**
     * 是否返回原始响应数据
     */
    raw?: boolean

    /**
     * 是否忽略响应错误
     */
    ignoreResponseError?: boolean
  }
}
