export interface ERReportFormValues {
  /** 报告标题 */
  title: string
  /** 公司名称 */
  companyName: string
  /** 客户名称 */
  clientName: string
  /** 报告日期 */
  date: string

  /** 应急人员 */
  responders: {
    name: string
    date: string
    location: string
  }[]

  /** 问题名称 */
  problemName: string
  /** 问题发生时间 */
  problemTime: string
  /** 问题描述 */
  problemDescription: string

  /** 紧急程度 */
  urgencyLevel: string
  /** 要求 */
  requirements: string
  /** 分析结果 */

  /** 攻击时间线 */
  attackTimeline: {
    time: string
    ip: string
    trace: string
  }[]

  /** 服务器分析内容 */
  serverAnalysis: {
    name: string
    description: string
    findings: string
  }[]
  /** 分析结果 */
  analysisResults: string
  /** 结论 */
  conclusions: string
  /** 建议 */
  recommendations: string
}

export interface InspirationFormValues {
  /** 灵感类型 */
  inspirationType: string
  /** 灵感内容 */
  inspirationContent: string
  /** 语言风格 */
  languageStyle: string
  /** 文章篇幅 */
  contentLength: string
}

export type AgentRoleTitle = '渗透测试工程师' | '应急响应工程师' | '网络安全工程师' | '文档工程师' | '数据安全工程师' | '安全运维工程师' | '代码免杀工程师' | '项目经理'

export interface AgentRole {
  avatar: string
  title: AgentRoleTitle
  description: string
  rolePrompt: string
}

export type AgentChatHistory = Record<AgentRoleTitle, ChatHistory[]>

/**
 * 聊天消息类型
 */
export interface ChatMessage {
  /** 消息唯一ID */
  id: string
  /** 消息内容 */
  content: string
  /** 消息角色：用户或助手 */
  role: 'user' | 'assistant'
  /** 消息时间戳 */
  timestamp: Date
}

/**
 * 聊天历史记录类型
 */
export interface ChatHistory {
  /** 历史记录唯一ID */
  id: string
  /** 聊天消息列表 */
  messages: ChatMessage[]
  /** 最后更新时间 */
  lastUpdated: Date
  /** 预览消息 */
  previewMessage: string
}

/** 文本改写历史记录 */
export interface RewriteHistoryItem {
  style: string
  temperature: string
  content: string
  originalText: string
  timestamp: number
}
