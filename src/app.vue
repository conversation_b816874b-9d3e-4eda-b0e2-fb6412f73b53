<script setup lang="ts">
import { consola } from 'consola'

import AppearanceDialog from '~/components/common/AppearanceDialog.vue'

// const LazyStagewiseToolbar = defineAsyncComponent(
//   async () => await import('@stagewise/toolbar-vue').then((m) => m.StagewiseToolbar),
// )

const runtimeConfig = useRuntimeConfig()

const appStore = useAppStore()
const userStore = useUserStore()

const route = useRoute()

const { isSearchPanelVisible } = storeToRefs(appStore)

/**
 * 处理搜索面板的快捷键，当用户按下快捷键时，会打开或关闭搜索面板
 */
const handleToggleSearchPanel = (ev: KeyboardEvent) => {
  if (isMac()
    ? ev.metaKey && ev.key.toLowerCase() === 'k'
    : ev.ctrlKey && ev.key.toLowerCase() === 'k') {
    ev.stopPropagation()
    appStore.toggleSearchPanel()
  }
}

onMounted(() => {
  consola.info('当前环境：', runtimeConfig.public.releaseEnv)

  appStore.setupTheme()
  setupAuthListener()

  window.addEventListener('keydown', handleToggleSearchPanel)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleToggleSearchPanel)
})

onMounted(async () => {
  if (userStore.isLoggedIn()) {
    /*
     * 应用初始化时获取最新用户信息
     *
     * 目的：
     * - 确保用户数据与服务器保持同步
     * - 更新本地存储的用户信息，包括：
     *    - 用户权限
     *    - 角色设置
     *    - 部门归属
     *    - 其他个人信息
     *
     * 这样可以避免使用过期的用户数据，保证应用功能的正常运行
     */
    if (!isFuYao()) {
      const currentUser = await UserService.getCurrentUser()
      userStore.updateUser(currentUser)
    }
  }
})

const layoutName = computed<LayoutName>(() => {
  const metaLayout = route.meta.layout

  if (metaLayout !== false) {
    if (metaLayout) {
      return metaLayout
    }

    if (route.meta.pageGroup === 'admin') {
      const customContentContainer = route.meta.layoutConfig?.customContent ?? false

      if (customContentContainer) {
        if (appStore.themeState.layout === 'admin-horizontal') {
          return 'admin-horizontal-blank'
        }

        return 'admin-vertical-blank'
      }

      return appStore.themeState.layout
    }
  }

  return 'default'
})
</script>

<template>
  <div
    id="app-root"
    class="size-full"
  >
    <NuxtLayout :name="layoutName">
      <NuxtPage
        :transition="{
          name: appStore.themeState.transition,
          mode: 'out-in',
        }"
      />
    </NuxtLayout>

    <!--
      全局消息提示组件 - 用于显示全局消息提示
      使用方法: useToast() 组合式函数来触发
      用途: 用于显示各种类型的消息提示，如成功、错误、警告等
    -->
    <Toast />

    <!--
      全局确认对话框组件 - 用于显示模态确认对话框
      使用方法: useConfirm() 组合式函数来触发
      用途: 用于需要用户确认的重要操作，如删除、提交等
    -->
    <ConfirmDialog />

    <!--
      上下文确认弹出框组件 - 用于显示轻量级确认弹出框
      使用方法: useConfirm() 组合式函数，并指定 group="delete-confirm-popup"，表示专门用于删除确认
      用途: 主要用于删除操作的快速确认，显示在触发元素附近
    -->
    <ConfirmPopup group="delete-confirm-popup" />

    <!--
      动态对话框组件 - 用于显示动态对话框
      使用方法: useDialog() 组合式函数，并指定 content 为组件实例
      用途: 用于显示动态对话框，允许用户调整应用的外观设置
    -->
    <DynamicDialog />

    <!--
      个性化配置弹出框组件 - 用于显示外观配置弹出框
      使用方法: 通过 ref 获取组件实例，并调用 show() 方法
      用途: 用于显示外观配置弹出框，允许用户调整应用的外观设置
    -->
    <AppearanceDialog />

    <!--
      搜索面板组件 - 用于显示搜索面板
      使用方法: 通过 v-model:visible 绑定搜索面板的可见状态
      用途: 用于显示搜索面板，允许用户输入搜索关键词
    -->
    <SearchPanel v-model:visible="isSearchPanelVisible" />

    <div
      v-if="isDev() || isTest()"
      class="pointer-events-none fixed bottom-0 right-0 text-xs opacity-15"
    >
      {{ isDev() ? '开发环境' : isTest() ? '测试环境' : null }}
    </div>

    <!-- <LazyStagewiseToolbar v-if="isDev()" /> -->
  </div>
</template>
