import { StoreKey } from '@/enums/common'
import { WorkspaceService } from '~/services-tw/workspace'
import type { TW_WorkspaceStats } from '~/types-tw/workspace'

export const useWorkspaceStatsStore = defineStore(
  StoreKey.WorkspaceStats,
  () => {
    /** 工作区统计数据 */
    const stats = ref<TW_WorkspaceStats>()

    /** 数据加载状态 */
    const isLoading = ref(false)

    /** 错误信息 */
    const error = ref<string>()

    /** 最后更新时间 */
    const lastUpdated = ref<Date>()

    /** 数据刷新间隔（毫秒），默认5分钟 */
    const refreshInterval = ref(5 * 60 * 1000)

    /** 自动刷新定时器 */
    let refreshTimer: NodeJS.Timeout | null = null

    /** 获取工作区统计数据 */
    const fetchStats = async () => {
      if (isLoading.value) {
        return
      }

      isLoading.value = true
      error.value = undefined

      try {
        const data = await WorkspaceService.getWorkspaceStats()
        stats.value = data
        lastUpdated.value = new Date()
      }
      catch (err) {
        error.value = err instanceof Error ? err.message : '获取工作区统计数据失败'
        console.error('获取工作区统计数据失败:', err)
      }
      finally {
        isLoading.value = false
      }
    }

    /**
     * 刷新数据
     */
    const refreshStats = async () => {
      await fetchStats()
    }

    /** 停止自动刷新 */
    const stopAutoRefresh = () => {
      if (refreshTimer) {
        clearInterval(refreshTimer)
        refreshTimer = null
      }
    }

    /** 启动自动刷新 */
    const startAutoRefresh = () => {
      stopAutoRefresh()
      refreshTimer = setInterval(fetchStats, refreshInterval.value)
    }

    /** 设置刷新间隔 */
    const setRefreshInterval = (interval: number) => {
      refreshInterval.value = interval

      if (refreshTimer) {
        startAutoRefresh()
      }
    }

    /** 清空数据 */
    const clearStats = () => {
      stats.value = undefined
      error.value = undefined
      lastUpdated.value = undefined

      stopAutoRefresh()
    }

    // 计算属性：基础统计信息
    const baseStats = computed(() => stats.value?.base_count)

    // 计算属性：工作流执行统计
    const workflowExecutionStats = computed(() => stats.value?.workflow_execution_counts || [])

    // 计算属性：执行状态分布
    const executionStatusStats = computed(() => stats.value?.execution_status_distribution || [])

    // 计算属性：工作流节点统计
    const workflowNodeStats = computed(() => stats.value?.workflow_node_counts || [])

    // 计算属性：最近执行统计
    const recentExecutionStats = computed(() => stats.value?.recent_execution_counts || [])

    // 计算属性：是否有数据
    const hasData = computed(() => !!stats.value)

    // 计算属性：数据是否过期（超过刷新间隔的2倍时间）
    const isDataStale = computed(() => {
      if (!lastUpdated.value) {
        return true
      }

      const now = new Date()
      const timeDiff = now.getTime() - lastUpdated.value.getTime()

      return timeDiff > refreshInterval.value * 2
    })

    // 计算属性：健康状态
    const healthStatus = computed(() => {
      if (!baseStats.value) {
        return 'unknown'
      }

      const healthIndex = baseStats.value.health_index

      if (healthIndex >= 80) {
        return 'excellent'
      }

      if (healthIndex >= 60) {
        return 'good'
      }

      if (healthIndex >= 40) {
        return 'warning'
      }

      return 'critical'
    })

    // 组件卸载时清理定时器
    onUnmounted(() => {
      stopAutoRefresh()
    })

    return {
      // 状态
      stats,
      isLoading,
      error,
      lastUpdated,
      refreshInterval,

      // 计算属性
      baseStats,
      workflowExecutionStats,
      executionStatusStats,
      workflowNodeStats,
      recentExecutionStats,
      hasData,
      isDataStale,
      healthStatus,

      // 方法
      fetchStats,
      refreshStats,
      startAutoRefresh,
      stopAutoRefresh,
      setRefreshInterval,
      clearStats,
    }
  },
)

/**
 * 使用示例：
 *
 * ```vue
 * <script setup>
 * const workspaceStats = useWorkspaceStatsStore()
 *
 * // 页面加载时获取数据
 * onMounted(async () => {
 *   await workspaceStats.fetchStats()
 *   // 启动自动刷新
 *   workspaceStats.startAutoRefresh()
 * })
 *
 * // 页面卸载时停止自动刷新
 * onUnmounted(() => {
 *   workspaceStats.stopAutoRefresh()
 * })
 * </script>
 *
 * <template>
 *   <div>
 *     <!-- 加载状态 -->
 *     <div v-if="workspaceStats.isLoading">加载中...</div>
 *
 *     <!-- 错误状态 -->
 *     <div v-else-if="workspaceStats.error" class="error">
 *       {{ workspaceStats.error }}
 *       <button @click="workspaceStats.refreshStats()">重试</button>
 *     </div>
 *
 *     <!-- 数据展示 -->
 *     <div v-else-if="workspaceStats.hasData">
 *       <!-- 基础统计 -->
 *       <div v-if="workspaceStats.baseStats">
 *         <h3>基础统计</h3>
 *         <p>总数量: {{ workspaceStats.baseStats.total_count }}</p>
 *         <p>活跃数量: {{ workspaceStats.baseStats.active_count }}</p>
 *         <p>健康指数: {{ workspaceStats.baseStats.health_index }}</p>
 *         <p>健康状态: {{ workspaceStats.healthStatus }}</p>
 *       </div>
 *
 *       <!-- 工作流执行统计 -->
 *       <div v-if="workspaceStats.workflowExecutionStats.length">
 *         <h3>工作流执行统计</h3>
 *         <ul>
 *           <li v-for="item in workspaceStats.workflowExecutionStats" :key="item.name">
 *             {{ item.name }}: {{ item.count }}
 *           </li>
 *         </ul>
 *       </div>
 *
 *       <!-- 执行状态分布 -->
 *       <div v-if="workspaceStats.executionStatusStats.length">
 *         <h3>执行状态分布</h3>
 *         <ul>
 *           <li v-for="item in workspaceStats.executionStatusStats" :key="item.status">
 *             {{ item.status }}: {{ item.count }}
 *           </li>
 *         </ul>
 *       </div>
 *
 *       <!-- 刷新按钮 -->
 *       <button @click="workspaceStats.refreshStats()">刷新数据</button>
 *
 *       <!-- 数据更新时间 -->
 *       <p v-if="workspaceStats.lastUpdated">
 *         最后更新: {{ workspaceStats.lastUpdated.toLocaleString() }}
 *         <span v-if="workspaceStats.isDataStale" class="warning">（数据已过期）</span>
 *       </p>
 *     </div>
 *   </div>
 * </template>
 * ```
 */
