import { merge } from 'lodash-es'

import { StoreKey } from '@/enums/common'
import { AUTH } from '~/enums/user'
import type { ClientAuthValues } from '~/types-tw/user'

export const useUserStore = defineStore(
  StoreKey.User,
  () => {
    const token = ref<string>()
    const user = ref<CurrentUser>()

    function updateUser(userData: Partial<CurrentUser>) {
      user.value = merge({}, user.value, userData)
    }

    const TW_clientInfo = ref<ClientAuthValues>()

    function TW_setClientInfo(info: ClientAuthValues) {
      TW_clientInfo.value = info
    }

    function TW_isClientAuthorized() {
      return !!TW_clientInfo.value?.openid
    }

    function login(userData: AuthResponse) {
      token.value = userData.token
      user.value = userData.user
    }

    /**
     * 请勿直接使用此方法清除用户信息
     *
     * 正确的登出流程请使用 /utils/auth.ts 中的 handleLogout 方法，它包含了完整的登出逻辑
     */
    function logout() {
      token.value = undefined
      user.value = undefined
      TW_clientInfo.value = undefined

      if (import.meta.client) {
        window.localStorage.removeItem(StoreKey.User)
      }
    }

    function isLoggedIn() {
      return !!token.value
    }

    const permissions = computed<PermissionFlag[]>(() => {
      return user.value?.menu_list ?? []
    })

    // 使用 Set 缓存权限列表，提高查询效率
    const permissionsSet = computed(() => new Set(permissions.value))

    /**
     * 检查用户是否具有指定的访问权限
     *
     * @param permKeys - 需要检查的权限，可以是单个权限，也可以是权限数组
     */
    const hasPermission = (permKeys: PermissionFlag | PermissionFlag[] | undefined | null) => {
      if (!permKeys) {
        return false
      }

      if (!isFuYao()) {
        const isSuperAdmin = user.value?.role_id === 1

        if (isSuperAdmin) {
          // HACK: 简单通过角色 ID 判断为超级管理员，拥有所有权限
          return true
        }
      }

      if (permissionsSet.value.has(AUTH.SKIP_FUYAO) || permissionsSet.value.has(AUTH.SKIP_SHIZHEN)) {
        return true
      }

      if (Array.isArray(permKeys)) {
        // HACK: 简单通过权限字符串判断为跳过权限检查
        if (permKeys.includes(AUTH.SKIP_FUYAO) || permKeys.includes(AUTH.SKIP_SHIZHEN)) {
          return true
        }

        return permKeys.some((p) => permissionsSet.value.has(p))
      }

      return permissionsSet.value.has(permKeys)
    }

    return {
      token,
      user,
      permissions,

      login,
      logout,
      isLoggedIn,
      updateUser,

      hasPermission,

      TW_clientInfo,
      TW_setClientInfo,
      TW_isClientAuthorized,
    }
  },
  {
    persist: {
      key: StoreKey.User,
      storage: piniaPluginPersistedstate.localStorage(),
    },
  },
)
