import { palette, updatePreset, updateSurfacePalette } from '@primevue/themes'
import { isEqual, merge } from 'lodash-es'
import { nanoid } from 'nanoid'

import { StoreKey } from '@/enums/common'
import { primaryColors, surfaceColors, themeNoir } from '@/enums/theme'

const SYSTEM_THEME_MODE = 'auto'

export const useAppStore = defineStore(
  StoreKey.App,
  () => {
    const runtimeConfig = useRuntimeConfig()

    const defaultTheme = (): ThemeState => ({
      themeMode: 'light',
      primaryColor: 'noir',
      surfaceColor: 'zinc',
      transition: 'none',
      showBreadcrumb: false,
      layout: isInWeCom() ? 'admin-vertical' : 'admin-horizontal',
    })

    const themeState = ref<ThemeState>(defaultTheme())

    const useCustomPrimaryColor = ref(false)
    const customPrimaryColor = ref<string>()

    const darkModeMediaQuery = import.meta.client
      ? window.matchMedia('(prefers-color-scheme: dark)')
      : null

    let removeSystemThemeListener: (() => void) | null = null

    function applyTheme(mode: ThemeName) {
      document.documentElement.classList.toggle(runtimeConfig.public.darkModeClass, mode === 'dark')
    }

    function setPrimaryColor(color: PrimaryColor) {
      themeState.value.primaryColor = color.name
    }

    function setSurfaceColor(color: SurfaceColor) {
      themeState.value.surfaceColor = color.name
    }

    const instanceId = nanoid(4)

    const themeChannel = import.meta.client
      ? new BroadcastChannel('theme-channel')
      : null

    const messageHandler = (event: MessageEvent) => {
      const { type, payload, source } = event.data

      if (source === instanceId) {
        return
      }

      if (type === 'themeState') {
        // 只有当主题状态发生变化时，才进行更新，否则会导致无限循环
        if (!isEqual(themeState.value, payload)) {
          themeState.value = merge({}, themeState.value, payload)
        }
      }
    }

    if (themeChannel) {
      themeChannel.addEventListener('message', messageHandler)
    }

    function syncThemeState() {
      themeChannel?.postMessage({
        type: 'themeState',
        payload: toRaw(themeState.value),
        source: instanceId,
      })
    }

    // 监听主题状态变化并同步
    watch(
      themeState,
      () => {
        syncThemeState()
      },
      { deep: true },
    )

    // 获取当前系统主题
    function getSystemTheme(): ThemeName {
      return darkModeMediaQuery?.matches ? 'dark' : 'light'
    }

    // 设置系统主题监听器
    function setupSystemThemeListener() {
      if (darkModeMediaQuery) {
        const handleSystemThemeChange = () => {
          if (themeState.value.themeMode === SYSTEM_THEME_MODE) {
            applyTheme(getSystemTheme())
          }
        }

        darkModeMediaQuery.addEventListener('change', handleSystemThemeChange)

        removeSystemThemeListener = () => {
          darkModeMediaQuery.removeEventListener('change', handleSystemThemeChange)
        }
      }
    }

    // 清理系统主题监听器
    function cleanupSystemThemeListener() {
      if (removeSystemThemeListener) {
        removeSystemThemeListener()
        removeSystemThemeListener = null
      }
    }

    // 处理主题变化
    function handleThemeChange() {
      // 清理之前的监听器
      cleanupSystemThemeListener()

      if (themeState.value.themeMode === SYSTEM_THEME_MODE) {
        // 仅在自动模式下设置系统主题监听器
        setupSystemThemeListener()
        applyTheme(getSystemTheme())
      }
      else {
        applyTheme(themeState.value.themeMode)
      }
    }

    function getPresetExt(color: PrimaryColor) {
      if (color.name === 'noir') {
        return themeNoir
      }
      else {
        return {
          semantic: {
            primary: color.palette,
            colorScheme: {
              light: {
                primary: {
                  color: '{primary.500}',
                  contrastColor: '#ffffff',
                  hoverColor: '{primary.600}',
                  activeColor: '{primary.700}',
                },
                highlight: {
                  background: '{primary.50}',
                  focusBackground: '{primary.100}',
                  color: '{primary.700}',
                  focusColor: '{primary.800}',
                },
              },
              dark: {
                primary: {
                  color: '{primary.400}',
                  contrastColor: '{surface.900}',
                  hoverColor: '{primary.300}',
                  activeColor: '{primary.200}',
                },
                highlight: {
                  background: 'color-mix(in srgb, {primary.400}, transparent 84%)',
                  focusBackground: 'color-mix(in srgb, {primary.400}, transparent 76%)',
                  color: 'rgba(255,255,255,.87)',
                  focusColor: 'rgba(255,255,255,.87)',
                },
              },
            },
          },
        }
      }
    }

    function applyPrimaryColor(colorName: PrimaryColorName) {
      const color = primaryColors.find((color) => color.name === colorName)

      updatePreset(getPresetExt(color ?? { name: colorName, palette: palette(colorName) }))
    }

    function applySurfaceColor(colorName: SurfaceColorName) {
      const color = surfaceColors.find((color) => color.name === colorName)

      if (color?.palette) {
        // HACK: 延迟更新，否则会导致字体颜色不生效
        setTimeout(() => {
          updateSurfacePalette(color.palette)
        }, 0)
      }
    }

    // 监听整个主题状态的变化
    watch(() => themeState.value.themeMode, () => {
      handleThemeChange()
    })

    watch(() => themeState.value.primaryColor, (newColor, oldColor) => {
      if (newColor !== oldColor) {
        applyPrimaryColor(newColor)
      }

      const isCustomPrimaryColor = !primaryColors.find((color) => color.name === newColor)

      if (isCustomPrimaryColor) {
        customPrimaryColor.value = newColor
        useCustomPrimaryColor.value = true
      }
      else {
        useCustomPrimaryColor.value = false
      }
    }, { immediate: true })

    watch(() => themeState.value.surfaceColor, (newColor, oldColor) => {
      if (newColor !== oldColor) {
        applySurfaceColor(newColor)
      }
    })

    function setupTheme() {
      // 仅在客户端环境下执行系统主题检测
      if (import.meta.client) {
        handleThemeChange()
      }

      applyPrimaryColor(themeState.value.primaryColor)
      applySurfaceColor(themeState.value.surfaceColor)
    }

    const isAppearanceDialogVisible = ref(false)

    function openAppearanceDialog() {
      isAppearanceDialogVisible.value = true
    }

    function closeAppearanceDialog() {
      isAppearanceDialogVisible.value = false
    }

    const isSearchPanelVisible = ref(false)

    function toggleSearchPanel() {
      isSearchPanelVisible.value = !isSearchPanelVisible.value
    }

    function resetToDefaultTheme() {
      themeState.value = defaultTheme()
    }

    return {
      themeState,
      setPrimaryColor,
      setSurfaceColor,
      setupTheme,
      resetToDefaultTheme,

      useCustomPrimaryColor,
      customPrimaryColor,

      isAppearanceDialogVisible,
      openAppearanceDialog,
      closeAppearanceDialog,

      isSearchPanelVisible,
      toggleSearchPanel,
    }
  },
  {
    persist: {
      key: StoreKey.App,
      storage: piniaPluginPersistedstate.localStorage(),
      omit: ['isSearchPanelVisible'],
    },
  },
)
