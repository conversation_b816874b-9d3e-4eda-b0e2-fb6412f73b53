import arrayToTree from 'array-to-tree'

import { StoreKey } from '~/enums/common'
import { UserService } from '~/services-tw/org'
import type { DepartmentTreeNode, TW_Department } from '~/types-tw/user'

type ActiveDepartment = Pick<TW_Department, 'id' | 'name'>

export const useAdminDepartmentStore = defineStore(
  StoreKey.AdminDepartment,
  () => {
    const activeDepartment = ref<ActiveDepartment>()
    const activeDepartmentId = computed(() => activeDepartment.value?.id)

    const flatDepartments = ref<TW_Department[]>([])

    const normalizedDepartmentTree = computed<DepartmentTreeNode[]>(() => {
      const normalized = flatDepartments.value?.map((it) => ({
        ...it,
        key: String(it.id),
        label: it.name,
      })) || []

      return normalized
    })

    const departmentTree = computed<DepartmentTreeNode[]>(() => {
      if (Array.isArray(normalizedDepartmentTree.value) && normalizedDepartmentTree.value.length > 0) {
        return arrayToTree<DepartmentTreeNode>(normalizedDepartmentTree.value, {
          customID: 'id',
          parentProperty: 'parent',
        })
      }

      return []
    })

    // 获取选中节点的所有父节点ID
    const getParentKeys = (tree: DepartmentTreeNode[], targetId: number, parentKeys: number[] = []): number[] => {
      for (const node of tree) {
        if (Number(node.key) === targetId) {
          return parentKeys
        }

        if (Array.isArray(node.children) && node.children.length) {
          const found = getParentKeys(node.children, targetId, [...parentKeys, Number(node.key)])

          if (found.length) {
            return found
          }
        }
      }

      return []
    }

    const expandedKeys = ref<ExpandedKeys>({})

    function setExpandedKeys(keys: ExpandedKeys) {
      expandedKeys.value = keys
    }

    async function fetchDepartments() {
      const departmentList = await UserService.getDepartments()

      if (departmentList) {
        flatDepartments.value = departmentList

        // 如果检测到没选中，则默认选中第一个组织
        if (!activeDepartmentId.value) {
          const firstDepartment = departmentList.at(0)

          if (firstDepartment) {
            activeDepartment.value = firstDepartment
          }
        }
      }

      return departmentList
    }

    watch(activeDepartmentId, () => {
      if (activeDepartmentId.value) {
        const parentKeys = getParentKeys(departmentTree.value, activeDepartmentId.value)

        const keys = parentKeys.reduce((acc, key) => {
          acc[key] = true

          return acc
        }, {} as ExpandedKeys)

        setExpandedKeys({ ...expandedKeys.value, ...keys })
      }
    })

    function setActiveDepartment(dept: ActiveDepartment) {
      activeDepartment.value = dept
    }

    return {
      activeDepartment,
      activeDepartmentId,
      /** 组织树 */
      departmentTree,
      /** 展开的节点 */
      expandedKeys,

      fetchDepartments,
      setExpandedKeys,
      setActiveDepartment,
    }
  },
)
