import arrayToTree from 'array-to-tree'

import { StoreKey } from '~/enums/common'

export const useAdminUserGroupStore = defineStore(
  StoreKey.AdminUserGroup,
  () => {
    const activeUserGroup = ref<Pick<UserGroup, 'id' | 'name'>>()
    const activeUserGroupId = computed(() => activeUserGroup.value?.id)

    const flatUserGroups = ref<UserGroup[]>([])

    const normalizedUserGroupTree = computed<UserGroupTreeNode[]>(() => {
      const normalized: UserGroupTreeNode[] = flatUserGroups.value?.map((it) => ({
        ...it,
        key: String(it.id),
        label: it.name,
      }))

      return normalized
    })

    const userGroupTree = computed<UserGroupTreeNode[]>(() => {
      if (Array.isArray(normalizedUserGroupTree.value) && normalizedUserGroupTree.value.length > 0) {
        return arrayToTree<UserGroupTreeNode>(normalizedUserGroupTree.value)
      }

      return []
    })

    // 获取选中节点的所有父节点ID
    const getParentKeys = (tree: UserGroupTreeNode[], targetId: number, parentKeys: number[] = []): number[] => {
      for (const node of tree) {
        if (Number(node.key) === targetId) {
          return parentKeys
        }

        if (Array.isArray(node.children) && node.children.length) {
          const found = getParentKeys(node.children, targetId, [...parentKeys, Number(node.key)])

          if (found.length) {
            return found
          }
        }
      }

      return []
    }

    // 计算展开的节点
    const expandedKeys = computed(() => {
      if (!activeUserGroupId.value) {
        return {}
      }

      const parentKeys = getParentKeys(userGroupTree.value, activeUserGroupId.value)

      return parentKeys.reduce((acc, key) => {
        acc[key] = true

        return acc
      }, {} as ExpandedKeys)
    })

    async function fetchUserGroups() {
      const userGroupList = await UserService.getUserGroups()

      if (userGroupList) {
        flatUserGroups.value = userGroupList

        // 如果检测到没选中，则默认选中第一个组织
        if (!activeUserGroupId.value) {
          const firstUserGroup = userGroupList.at(0)

          if (firstUserGroup) {
            activeUserGroup.value = {
              id: firstUserGroup.id,
              name: firstUserGroup.name,
            }
          }
        }
      }

      return userGroupList
    }

    function setActiveUserGroup(userGroup: Pick<UserGroup, 'id' | 'name'>) {
      activeUserGroup.value = userGroup
    }

    return {
      activeUserGroup,
      activeUserGroupId,
      /** 组织树 */
      userGroupTree,
      /** 展开的节点 */
      expandedKeys,

      fetchUserGroups,
      setActiveUserGroup,
    }
  },
)
