import { StoreKey } from '~/enums/common'
import { routeConfig, RouteKey } from '~/enums/route'

// 触发折叠的窗口宽度阈值
const COLLAPSE_WIDTH = 992

export const useAdminNavMenuStore = defineStore(
  StoreKey.AdminNavMenu,
  () => {
    const runtimeConfig = useRuntimeConfig()

    const userStore = useUserStore()

    const filterMenuItems = (items: NavMenuItem[]): NavMenuItem[] => {
      return items
        .map((item) => {
          // 如果有子菜单，递归过滤
          if (item.items) {
            const filteredItems = filterMenuItems(item.items)

            // 如果过滤后没有子菜单，则不显示父菜单
            if (filteredItems.length === 0) {
              return null
            }

            return {
              ...item,
              items: filteredItems,
            }
          }

          // 如果用户没有权限，则不显示该菜单
          if (userStore.hasPermission(item.permissions)) {
            return item
          }

          return null
        })
        .filter((item): item is NavMenuItem => item !== null)
    }

    const getRouteConfig = (key: RouteKey): Pick<NavMenuItem, 'label' | 'route' | 'permissions'> => {
      const config = routeConfig[key]

      return {
        label: config.title,
        route: config.route,
        permissions: 'permissions' in config ? config.permissions : undefined,
      }
    }

    const createMenuItem = (key: RouteKey) => ({
      key,
      ...getRouteConfig(key),
    })

    const navMenuItems = computed(() => {
      const baseMenuItems: NavMenuItem[] = [
        ...(isFuYao()
          ? [
              createMenuItem(RouteKey.TW_工作台),
              {
                ...createMenuItem(RouteKey.TW_项目管理),
                group: true,
                items: [
                  createMenuItem(RouteKey.TW_项目列表),
                ],
              },
              {
                ...createMenuItem(RouteKey.TW_工作空间),
                isActive: (theRoute: ReturnType<typeof useRoute>) => {
                  return theRoute.path.startsWith(getRoutePath(RouteKey.TW_工作空间))
                },
              },
              {
                ...createMenuItem(RouteKey.TW_AI工具),
                group: true,
                items: [
                  createMenuItem(RouteKey.TW_AI_智能助手),
                  createMenuItem(RouteKey.TW_应急响应报告生成器),
                  createMenuItem(RouteKey.TW_AI_灵感中心),
                  createMenuItem(RouteKey.TW_AI_报告润色),
                  createMenuItem(RouteKey.TW_AI_文档对比),
                  createMenuItem(RouteKey.TW_AI_文本改写),
                  createMenuItem(RouteKey.TW_AI_报告生成引擎),
                ],
              },
              {
                ...createMenuItem(RouteKey.TW_客户管理),
                group: true,
                items: [
                  createMenuItem(RouteKey.TW_客户列表),
                  createMenuItem(RouteKey.TW_微信端用户),
                ],
              },
              {
                ...createMenuItem(RouteKey.TW_组织架构),
                group: true,
                items: [
                  createMenuItem(RouteKey.TW_成员与部门),
                  createMenuItem(RouteKey.TW_角色管理),
                  createMenuItem(RouteKey.TW_权限资源),
                ],
              },
            ]
          : [
              {
                ...createMenuItem(RouteKey.仪表盘),
                group: true,
                items: [
                  createMenuItem(RouteKey.资产概览),
                  createMenuItem(RouteKey.安全概览),
                  createMenuItem(RouteKey.工单概览),
                  ...(runtimeConfig.public.releaseEnv !== 'prod-shizhen-sass'
                    ? [
                        createMenuItem(RouteKey.智能中心),
                        createMenuItem(RouteKey.智能分析),
                      ]
                    : []),
                ],
              },
              {
                ...createMenuItem(RouteKey.资产管理),
                group: true,
                items: [
                  createMenuItem(RouteKey.资产搜索),
                  createMenuItem(RouteKey.资产详情),
                  createMenuItem(RouteKey.资产拓扑图),
                ],
              },
              {
                ...createMenuItem(RouteKey.安全中心),
                group: true,
                items: [
                  createMenuItem(RouteKey.漏洞管理),
                  createMenuItem(RouteKey.基线管理),
                  createMenuItem(RouteKey.安全通告),
                ],
              },
              {
                ...createMenuItem(RouteKey.工单管理),
                group: true,
                items: [
                  createMenuItem(RouteKey.工单列表),
                  createMenuItem(RouteKey.待办工单),
                ],
              },
              {
                ...createMenuItem(RouteKey.配置中心),
                group: true,
                items: [
                  createMenuItem(RouteKey.资产模板),
                  createMenuItem(RouteKey.基线模板),
                ],
              },
              {
                ...createMenuItem(RouteKey.权限管理),
                group: true,
                items: [
                  createMenuItem(RouteKey.成员与部门),
                  createMenuItem(RouteKey.角色管理),
                ],
              },
            ]),
      ]

      return filterMenuItems(baseMenuItems)
    })

    const flatNavMenuItems = computed(() => {
      const result: Array<NavMenuItem & { parentKey?: NavMenuItem['key'] }> = []

      navMenuItems.value.forEach((menu) => {
        // 添加父级菜单
        result.push(menu)

        // 添加子菜单，并包含父级key
        if (menu.items) {
          menu.items.forEach((item) => {
            result.push({
              ...item,
              parentKey: menu.key,
            })
          })
        }
      })

      return result
    })

    const getDefaultExpandedKeys = () => {
      const keys = navMenuItems.value
        .filter(
          (item) => [RouteKey.资产管理, RouteKey.安全中心].includes(item.key as RouteKey),
        )
        .map((item) => item.key)

      return Object.fromEntries(keys.map((key) => [key, true]))
    }

    const expandedKeys = ref<ExpandedKeys>(getDefaultExpandedKeys())

    function setExpandedKeys(keys: ExpandedKeys) {
      expandedKeys.value = keys
    }

    const route = useRoute()

    // 添加路由监听器来更新侧边导航菜单的展开状态
    watch(
      () => route.path,
      (path) => {
        // 找到匹配当前路径的菜单项
        const findMatchingItem = (items: NavMenuItem[]): NavMenuItem | undefined => {
          for (const it of items) {
            if (isRoutePathEqual(it.route, path)) {
              return it
            }

            if (it.items) {
              const match = findMatchingItem(it.items)

              if (match) {
                return it
              }
            }
          }
        }

        const matchingItem = findMatchingItem(navMenuItems.value)

        if (matchingItem) {
          expandedKeys.value = { ...expandedKeys.value, [matchingItem.key]: true }
        }
      },
      { immediate: true },
    )

    const collapsed = ref(true)

    const toggleCollapsed = () => {
      collapsed.value = !collapsed.value
    }

    // 监听窗口大小变化
    onMounted(() => {
      let previousWidth = window.innerWidth

      const handleResize = () => {
        const currentWidth = window.innerWidth

        // 只在宽度发生变化时更新状态
        if (currentWidth !== previousWidth) {
          collapsed.value = currentWidth <= COLLAPSE_WIDTH
          previousWidth = currentWidth
        }
      }

      window.addEventListener('resize', handleResize)

      // 初始化时执行一次
      handleResize()

      onUnmounted(() => {
        window.removeEventListener('resize', handleResize)
      })
    })

    return {
      navMenuItems,
      flatNavMenuItems,
      expandedKeys,
      collapsed,
      toggleCollapsed,
      setExpandedKeys,
    }
  },
  {
    persist: {
      key: StoreKey.AdminNavMenu,
      storage: piniaPluginPersistedstate.localStorage(),
      pick: ['expandedKeys', 'collapsed'],
    },
  },
)
