import type { AssetType } from '~/enums/asset'
import { AssetDetailTab } from '~/enums/asset'
import { StoreKey } from '~/enums/common'

type ActiveTemplateMap = Record<AssetType, AssetTemplateListItem | undefined>

export const useAssetInfoStore = defineStore(
  StoreKey.AssetInfo,
  () => {
    const activeAssetTab = ref<AssetDetailTabType>(AssetDetailTab.Search)

    const activeNetworkId = ref<Network['id']>()
    const activeNetworkDevice = ref<NetworkDevice>()
    const activeNetworkDeviceId = computed(() => activeNetworkDevice.value?.id)

    const activeTemplateMap = ref<ActiveTemplateMap>({} as ActiveTemplateMap)
    const activeTemplate = computed(() => {
      if (Object.hasOwn(activeTemplateMap.value, activeAssetTab.value)) {
        return activeTemplateMap.value[activeAssetTab.value as AssetType]
      }

      return undefined
    })
    const activeTemplateId = computed(() => activeTemplate.value?.id)

    const searchIp = ref<string>()

    function setSearchIp(ip: string) {
      searchIp.value = ip
    }

    const viewedServer = ref<ViewedServer>()

    const assetCollectionKey = ref<AssetShareCollectionLinkInfo['key']>()
    const isAssetCollection = computed(() => !!assetCollectionKey.value)

    function setAssetCollectionKey(key: AssetShareCollectionLinkInfo['key']) {
      assetCollectionKey.value = key
    }

    const setViewedServer = (server: ViewedServer | undefined) => {
      viewedServer.value = server
    }

    return {
      /** 当前选中的网络 */
      activeNetworkId,

      /** 当前选中的网络设备 */
      activeNetworkDevice,
      activeNetworkDeviceId,

      /** 当前选中的资产类型 */
      activeAssetTab,

      /** 当前选中的模板 */
      activeTemplate,
      activeTemplateId,
      activeTemplateMap,

      /** 当前查看的服务器 */
      viewedServer,
      setViewedServer,

      /** 搜索的 IP */
      searchIp,
      setSearchIp,

      /** 当前选中的资产采集任务 */
      assetCollectionKey,
      /** 是否是资产采集任务 */
      isAssetCollection,
      setAssetCollectionKey,
    }
  },
  {
    persist: {
      key: StoreKey.AssetInfo,
      storage: piniaPluginPersistedstate.localStorage(),
      pick: ['activeAssetTab'],
    },
  },
)
