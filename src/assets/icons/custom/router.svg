<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.6 (67491) - http://www.bohemiancoding.com/sketch -->
    <title>动态加速网络-32px</title>
    <desc>Created with Sketch.</desc>
    <g id="动态加速网络-32px" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="Rectangle-Copy" fill="#0052D9" opacity="0" x="0" y="0" width="32" height="32"></rect>
        <g id="Group-2" transform="translate(2.000000, 0.000000)" fill="#0052D9" fill-rule="nonzero">
            <g id="_编组_">
                <polygon id="Shape" points="26 22.84 14 29.7 2 22.84 2 9.16 14 2.3 26 9.16 26 9.14 28 8 14 0 0 8 0 24 14 32 28 24 28 12 26 13.14"></polygon>
            </g>
            <g id="Group" transform="translate(7.000000, 7.000000)">
                <polygon id="Shape" points="4.99 15.85 7 17 14 13 14 10.7"></polygon>
                <g transform="translate(0.000000, 1.000000)" id="Shape">
                    <polygon points="2 7.47 0 8.61 0 12 2.97 13.7 4.99 12.55 2 10.84"></polygon>
                    <polygon points="2 5.16 2 5.16 7 2.3 7 2.3 9.02 1.15 7 0 0 4 0 6.31"></polygon>
                    <polygon points="9.02 3.46 12 5.16 12 8.54 14 7.4 14 4 11.03 2.3"></polygon>
                </g>
                <circle id="Oval" cx="7" cy="2" r="2"></circle>
                <circle id="Oval" cx="7" cy="16" r="2"></circle>
                <g transform="translate(0.000000, 3.000000)" id="Shape">
                    <polygon points="6 5.48 12.03 2.04 11.04 0.3 6 3.18"></polygon>
                    <polygon points="4 4.32 0 6.6 0 8.91 4 6.62"></polygon>
                </g>
                <g transform="translate(1.000000, 6.000000)" id="Shape">
                    <polygon points="7 3.53 0.98 6.96 1.97 8.7 7 5.83"></polygon>
                    <polygon points="9 2.39 9 4.69 13 2.41 13 0.1"></polygon>
                </g>
            </g>
        </g>
    </g>
</svg>