/* 导入重置样式，并将其放入 tailwind-base 层 */
@import url("./reset.css") layer(tailwind-base);

/* 导入 PrimeVue 重置样式 */
@import url("./primevue-override.css");

/* 导入页面过渡动画 */
@import url("./transition.css");

/* 导入 markdown 样式 */
@import url("./markdown.css");

/* ==== 工作流相关 ==== */

/* 导入 vue flow 必要的样式 */
@import url("@vue-flow/core/dist/style.css");

/* 导入 vue flow 默认主题样式 */
@import url("@vue-flow/core/dist/theme-default.css");

/* 导入 vue flow 节点 resizer 样式 */
@import url("@vue-flow/node-resizer/dist/style.css");

/* 导入 vue flow 节点 resizer 样式 */
@import url("./vueflow-override.css");

/* 导入工作流 canvas 样式 */
@import url("./workflow-canvas.css");

/* ==== 工作流相关 ==== */

/*
 * PrimeVue 和 Tailwind 结合使用时，需要正确地调整 layer 的顺序。
 * 参考：https://primevue.org/theming/styled/#libraries
 */
@layer tailwind-base, primevue, tailwind-utilities;

@layer tailwind-base {
  @tailwind base;
}

@layer tailwind-utilities {
  @tailwind components;
  @tailwind utilities;
}

/* ---- */

* {
  /* 现代滚动条样式 */
  scrollbar-width: thin;
}

html {
  --selection-background: var(--p-surface-200);
  --selection-text-color: var(--p-surface-950);

  @apply h-full
  [--layout-admin-content-padding:theme(spacing.5)]
  md:[--layout-admin-content-padding:theme(spacing.7)]
  [--layout-admin-content-rounded:theme(borderRadius.lg)]
  md:[--layout-admin-content-rounded:theme(borderRadius.xl)]
  [--layout-client-page-padding:theme(spacing.4)]
  md:[--layout-client-page-padding:theme(spacing.5)]
  [--layout-admin-container-padding:theme(spacing.3)]
  lg:[--layout-admin-container-padding:theme(spacing.4)]
  [--color-page-background:var(--p-surface-100)]
  dark:[--color-page-background:var(--p-surface-800)];

  font-size: 14px;
}

body {
  @apply h-full bg-page dark:bg-surface-800;
}

img {
  @apply text-sm text-secondary;
}

#__nuxt {
  @apply h-full;
}

::selection {
  color: var(--selection-text-color);
  background-color: var(--selection-background);
}

.iconify {
  @apply size-full;
}

.form-item-label-with-mark {
  @apply relative;
}

.form-item-label-with-mark::after {
  @apply text-danger-400 content-["*"] absolute left-0 top-0 -translate-x-[8px] translate-y-[2px];
}

/* 修正 nuxt-icon 的 margin， 否则会导致组件的宽度不协调 */
.nuxt-icon {
  @apply !m-0;
}

.dialog-form {
  @apply w-10/12 md:w-[26rem];
}

.dialog-form-wide {
  @apply w-11/12 md:w-[30rem];
}

.dialog-form-wider {
  @apply w-11/12 md:w-[clamp(350px,_35vw,_550px)];
}

.dialog-half {
  @apply w-[clamp(300px,_50vw,_800px)];
}

.dialog-full {
  @apply h-[clamp(300px,_90vh,_950px)] w-[clamp(300px,_90vw,_1000px)];
}

/* 登录页背景 */
.bg-grid-small-black {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='8' height='8' fill='none' stroke='rgb(0 0 0 / 0.1)'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

html.p-dark .shiki,
html.p-dark .shiki span {
  font-weight: var(--shiki-dark-font-weight) !important;

  /* Optional, if you also want font styles */
  font-style: var(--shiki-dark-font-style) !important;
  color: var(--shiki-dark) !important;
  text-decoration: var(--shiki-dark-text-decoration) !important;
  background-color: var(--shiki-dark-bg) !important;
}

.prose {
  @apply text-current prose-headings:text-current prose-strong:text-current prose-a:text-current prose-code:text-current
  prose-a:underline prose-a:decoration-dotted prose-a:underline-offset-[3px]
  prose-li:list-inside prose-li:list-disc;
}
