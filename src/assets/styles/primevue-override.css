:root body {
  --p-button-sm-icon-only-width: auto;
}

/**
 * 此样式文件用于覆盖 PrimeVue 的样式。
 *
 * 对于不能直接通过 design token 调整的样式，采用 class 覆盖的方式。
 * 但最佳实践是使用 design token 调整样式。
 */

/* HACK: 修正 PrimeVue Toast 标题的行高，否则会与图标对不齐 */
.p-toast-summary {
  line-height: 1.2;
}

/* 覆盖 DataTable 的遮罩层样式 */
.p-datatable-mask.p-overlay-mask {
  --p-mask-background: transparent;

  @apply bg-[var(--p-mask-background)] dark:bg-transparent;
}

input::placeholder,
.p-placeholder,
.p-textarea::placeholder {
  @apply text-[0.875em] leading-[1.5];
}

.p-avatar {
  & > img {
    @apply bg-[var(--p-avatar-background)];
  }
}

.p-message {
  @apply leading-[1.2];
}

.p-drawer-mask {
  @apply p-3;

  .p-drawer {
    @apply rounded-2xl;
  }
}

.p-dialog-mask {
  @apply p-admin-layout;

  .p-dialog-maximized {
    @apply rounded-lg md:rounded-xl !w-full !h-full;
  }
}

.p-dialog-header,
.p-drawer-header {
  .p-button.p-button-icon-only {
    @apply !rounded-lg !p-0 !size-8;
  }
}

.p-datatable-table {
  .p-datatable-column-title {
    @apply whitespace-nowrap;
  }

  .p-datatable-empty-message > td {
    @apply !p-0;
  }
}

.p-tooltip {
  @apply text-sm;
}

.p-tag {
  @apply whitespace-nowrap;

  .p-tag-label {
    @apply whitespace-nowrap;
  }
}

/* 修正 PrimeVue 的行高，否则会导致组件的高度不协调 */
button,
input,
optgroup,
select,
.p-button-label,
.p-select-label,
.p-select-option,
.p-togglebutton-label,
.p-tag-label,
.p-chip-label,
.p-inputtext,
.p-treeselect {
  @apply leading-none;
}

.p-menu-overlay {
  /* HACK: 修正 PrimeVue 的菜单层级，防止被其他组件遮挡 */
  z-index: 1200;
}
