:root {
  /* = */
  --color-canvas-accent: theme("colors.indigo.500");
  --color-canvas-background: var(--color-page-background);
  --color-canvas-node-background: var(--p-content-background);
  --color-canvas-label-background: var(--color-canvas-background);
  --color-canvas-node-border: var(--p-content-border-color);
  --color-canvas-node-border-selected: var(--p-surface-500);
  --color-canvas-handle: var(--p-surface-500);
  --size-canvas-handle: 10px;
  --size-width-canvas-handle-input-main: 2.5px;
  --size-height-canvas-handle-input-main: 10px;

  /* = */
  --color-canvas-node-success: theme("colors.emerald.500");
  --color-canvas-node-error: theme("colors.red.500");
  --color-canvas-node-warning: theme("colors.amber.500");

  /* = */
  --color-sticky-1: theme("colors.stone.500");
  --color-sticky-2: theme("colors.orange.500");
  --color-sticky-3: theme("colors.red.500");
  --color-sticky-4: theme("colors.emerald.500");
  --color-sticky-5: theme("colors.blue.500");
  --color-sticky-6: theme("colors.indigo.500");
  --color-sticky-7: currentcolor;

  /* = */
  --color-sticky-foreground-1: theme("colors.neutral.950");
  --color-sticky-foreground-2: theme("colors.orange.950");
  --color-sticky-foreground-3: theme("colors.red.950");
  --color-sticky-foreground-4: theme("colors.emerald.950");
  --color-sticky-foreground-5: theme("colors.blue.950");
  --color-sticky-foreground-6: theme("colors.indigo.950");
  --color-sticky-foreground-7: currentcolor;

  /* = */
  --color-sticky-background-1: theme("colors.neutral.200");
  --color-sticky-background-2: theme("colors.orange.50");
  --color-sticky-background-3: theme("colors.red.50");
  --color-sticky-background-4: theme("colors.emerald.50");
  --color-sticky-background-5: theme("colors.blue.50");
  --color-sticky-background-6: theme("colors.indigo.50");
  --color-sticky-background-7: transparent;

  /* = */
  --color-sticky-border-1: theme("colors.neutral.300");
  --color-sticky-border-2: theme("colors.orange.200");
  --color-sticky-border-3: theme("colors.red.200");
  --color-sticky-border-4: theme("colors.emerald.200");
  --color-sticky-border-5: theme("colors.blue.200");
  --color-sticky-border-6: theme("colors.indigo.200");
  --color-sticky-border-7: transparent;

  /* = */
  --color-edge-hovered: theme("colors.indigo.600");
  --color-edge-selected: var(--color-edge-hovered);
  --color-edge-success: theme("colors.emerald.500");
  --color-edge-error: theme("colors.red.500");
  --color-edge-pinned: theme("colors.stone.500");
  --color-edge-main: theme("colors.indigo.400");
  --color-edge-secondary: theme("colors.stone.500");

  /* = */
  --handle-indicator-width: 16px;
  --handle-indicator-height: 16px;

  &.p-dark {
    /* 主色调调整 */
    --color-canvas-accent: theme("colors.indigo.400");
    --color-canvas-handle: theme("colors.slate.400");
    --color-canvas-node-border-selected: theme("colors.slate.400");

    /* 状态颜色 */
    --color-canvas-node-success: theme("colors.emerald.400");
    --color-canvas-node-error: theme("colors.red.400");
    --color-canvas-node-warning: theme("colors.amber.400");

    /* 边缘颜色 */
    --color-edge-hovered: theme("colors.indigo.400");
    --color-edge-success: theme("colors.emerald.400");
    --color-edge-error: theme("colors.red.400");
    --color-edge-pinned: theme("colors.slate.400");
    --color-edge-main: theme("colors.indigo.300");
    --color-edge-secondary: theme("colors.slate.400");

    /* 便签主色调 */
    --color-sticky-1: theme("colors.slate.400");
    --color-sticky-2: theme("colors.orange.400");
    --color-sticky-3: theme("colors.red.400");
    --color-sticky-4: theme("colors.emerald.400");
    --color-sticky-5: theme("colors.blue.400");
    --color-sticky-6: theme("colors.indigo.400");
    --color-sticky-7: currentcolor;

    /* 便签前景色 */
    --color-sticky-foreground-1: theme("colors.neutral.50");
    --color-sticky-foreground-2: theme("colors.orange.50");
    --color-sticky-foreground-3: theme("colors.red.50");
    --color-sticky-foreground-4: theme("colors.emerald.50");
    --color-sticky-foreground-5: theme("colors.blue.50");
    --color-sticky-foreground-6: theme("colors.indigo.50");
    --color-sticky-foreground-7: currentcolor;

    /* 便签背景色 */
    --color-sticky-background-1: theme("colors.neutral.800");
    --color-sticky-background-2: theme("colors.orange.800");
    --color-sticky-background-3: theme("colors.red.800");
    --color-sticky-background-4: theme("colors.emerald.800");
    --color-sticky-background-5: theme("colors.blue.800");
    --color-sticky-background-6: theme("colors.indigo.800");
    --color-sticky-background-7: transparent;

    /* 便签边框色 */
    --color-sticky-border-1: theme("colors.neutral.600");
    --color-sticky-border-2: theme("colors.orange.600");
    --color-sticky-border-3: theme("colors.red.600");
    --color-sticky-border-4: theme("colors.emerald.600");
    --color-sticky-border-5: theme("colors.blue.600");
    --color-sticky-border-6: theme("colors.indigo.600");
    --color-sticky-border-7: transparent;
  }
}

.vue-flow__edges:has(.bring-to-front),
.vue-flow__edge-label.selected {
  @apply z-10;
}

.vue-flow__nodesselection-rect,
.vue-flow__selection {
  @apply border-transparent outline-node-accent-500 outline-2 outline-dashed outline-offset-4 bg-node-accent-500/5 rounded-md;
}

.vue-flow__handle.vf-handle-default,
.vue-flow__handle.vf-handle-output-main-connected,
.vue-flow__handle.vf-handle-output-non-main-connected {
  @apply size-[var(--size-canvas-handle)] border-2 border-[var(--color-canvas-node-background)] bg-[var(--color-canvas-handle)];
}

.vue-flow__handle.vf-handle-input-main,
.vue-flow__handle.vf-handle-input-main-connected,
.vue-flow__handle.vf-handle-input-non-main-connected {
  @apply w-[var(--size-width-canvas-handle-input-main)] h-[var(--size-height-canvas-handle-input-main)]
  border-none min-w-max min-h-max inline-block rounded-none
  bg-[var(--color-canvas-handle)];
}

.vue-flow__handle.vf-handle-input-main-connected,
.vue-flow__handle.vf-handle-input-non-main-connected {
  @apply bg-[var(--color-canvas-accent)];
}

.vue-flow__handle.vf-handle-output-main-connected,
.vue-flow__handle.vf-handle-output-non-main-connected {
  @apply bg-[var(--color-canvas-accent)];
}

.vue-flow__handle.vf-handle-input-main.top,
.vue-flow__handle.vf-handle-input-main.bottom,
.vue-flow__handle.vf-handle-input-non-main.top,
.vue-flow__handle.vf-handle-input-non-main.bottom,
.vue-flow__handle.vf-handle-input-non-main-connected.top,
.vue-flow__handle.vf-handle-input-non-main-connected.bottom {
  @apply !w-[var(--size-height-canvas-handle-input-main)] !h-[var(--size-width-canvas-handle-input-main)];
}
