.vue-flow__resize-control.line {
  --spacing-s: 0.5rem;

  z-index: 1;
  border-color: transparent;

  &.top {
    height: var(--spacing-s);
    border-top-width: var(--spacing-s);
  }

  &.right {
    width: var(--spacing-s);
    border-right-width: var(--spacing-s);
  }

  &.bottom {
    height: var(--spacing-s);
    border-bottom-width: var(--spacing-s);
  }

  &.left {
    width: var(--spacing-s);
    border-left-width: var(--spacing-s);
  }
}

.vue-flow__resize-control.handle {
  --spacing-s: 0.5rem;

  z-index: 1;
  width: var(--spacing-s);
  height: var(--spacing-s);
  border: 0;
  border-radius: 0;
  background-color: transparent;
}
