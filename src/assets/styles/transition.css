/**
 * 页面动画样式定义
 * 
 * 该文件负责定义系统中所有页面级别的过渡动画和预览动画效果，
 * 包括页面切换动画和内容预览动画等。
 */

/* 页面过渡动画 */
.slide-transition {
  position: relative;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.15s ease-out;
}

.slide-enter-from {
  transform: translateY(10px);
  opacity: 0;
}

.slide-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}

/* 淡入淡出过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

/* 缩放过渡动画 */
.zoom-enter-active,
.zoom-leave-active {
  transition: all 0.15s ease-in-out;
}

.zoom-enter-from,
.zoom-leave-to {
  transform: scale(0.9);
  opacity: 0;
}

.zoom-enter-to,
.zoom-leave-from {
  transform: scale(1);
  opacity: 1;
}

/* ==== 预览动画样式 ==== */

.none-preview {
  opacity: 1;
}

@keyframes fade-preview {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.fade-preview {
  transition: opacity 0.5s;
}

.fade-preview.is-previewing {
  animation: fade-preview 1s;
}

@keyframes slide-preview {
  0% {
    transform: translateY(0);
    opacity: 1;
  }

  50% {
    transform: translateY(10px);
    opacity: 0.5;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-preview {
  transition:
    transform 0.5s,
    opacity 0.5s;
}

.slide-preview.is-previewing {
  animation: slide-preview 1s;
}

@keyframes zoom-preview {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.zoom-preview {
  transition:
    transform 0.5s,
    opacity 0.5s;
}

.zoom-preview.is-previewing {
  animation: zoom-preview 1s;
}
