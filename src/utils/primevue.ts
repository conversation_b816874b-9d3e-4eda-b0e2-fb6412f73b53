import type { PassThrough } from '@primevue/core'

import type { AnyType } from '~/types/common'

/**
 * 合并 PrimeVue 的 PassThrough 选项，对字符串值进行合并而不是替换
 * 适用于合并 Tailwind CSS 类名等场景
 *
 * @param sources 要合并的源对象列表
 * @returns 合并后的对象
 */
export function mergePT<T>(...sources: (PassThrough<T> | undefined)[]): PassThrough<T> {
  // 过滤掉 undefined 值
  const validSources = sources.filter(Boolean) as PassThrough<T>[]

  if (validSources.length === 0) {
    return {} as PassThrough<T>
  }

  if (validSources.length === 1) {
    return validSources[0]
  }

  const result = {} as Record<string, AnyType>

  // 递归合并所有来源对象
  for (const source of validSources) {
    const typedSource = source as Record<string, AnyType>

    for (const key in typedSource) {
      const sourceValue = typedSource[key]

      if (key in result) {
        const resultValue = result[key]

        // 字符串值特殊处理：合并而不是替换
        if (typeof sourceValue === 'string' && typeof resultValue === 'string') {
          result[key] = `${resultValue} ${sourceValue}`
        }
        // 对象值递归合并
        else if (
          sourceValue
          && resultValue
          && typeof sourceValue === 'object'
          && typeof resultValue === 'object'
          && !Array.isArray(sourceValue)
          && !Array.isArray(resultValue)
        ) {
          result[key] = mergePT(resultValue, sourceValue)
        }
        // 其他情况直接替换
        else {
          result[key] = sourceValue
        }
      }
      else {
        // 结果对象中不存在该键，直接赋值
        result[key] = sourceValue
      }
    }
  }

  return result as PassThrough<T>
}
