/**
 * 复制文本到剪贴板
 *
 * @param text 要复制的文本
 * @returns 是否复制成功
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  // 优先使用 Clipboard API
  if (navigator.clipboard && window.isSecureContext) {
    try {
      await navigator.clipboard.writeText(text)

      return true
    }
    catch {
      return false
    }
  }

  // 降级方案：使用 execCommand
  try {
    const textArea = document.createElement('textarea')
    textArea.value = text

    // 防止滚动
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'

    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    const success = document.execCommand('copy')
    textArea.remove()

    return success
  }
  catch {
    return false
  }
}
