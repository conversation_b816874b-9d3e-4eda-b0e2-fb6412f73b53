import forge from 'node-forge'

interface CryptoOptions {
  padding?: forge.pss.PSS
  algorithm?: 'RSA-OAEP' | 'RSAES-PKCS1-V1_5'
  hash?: 'sha1' | 'sha256' | 'sha384' | 'sha512'
}

/**
 * 加载 RSA 公钥
 *
 * @param pemKey - PEM 格式的公钥字符串
 * @returns 返回已解析的公钥
 */
export async function loadPublicKey(pemKey: string): Promise<forge.pki.rsa.PublicKey> {
  try {
    return forge.pki.publicKeyFromPem(pemKey)
  }
  catch {
    throw new Error('无效的公钥格式')
  }
}

/**
 * 使用 RSA 公钥加密消息
 *
 * @param message - 要加密的原始消息
 * @param publicKey - forge 公钥对象
 * @param options - 加密选项
 * @returns 返回 Base64 编码的加密消息
 */
export async function encryptMessage(
  message: string,
  publicKey: forge.pki.rsa.PublicKey,
  options: CryptoOptions = {
    algorithm: 'RSA-OAEP',
    hash: 'sha256',
  },
): Promise<string> {
  try {
    const messageBuffer = forge.util.encodeUtf8(message)

    let encrypted: string

    if (options.algorithm === 'RSA-OAEP') {
      encrypted = publicKey.encrypt(messageBuffer, 'RSA-OAEP', {
        md: forge.md[options.hash || 'sha256'].create(),
        mgf1: {
          md: forge.md[options.hash || 'sha256'].create(),
        },
      })
    }
    else {
      encrypted = publicKey.encrypt(messageBuffer, 'RSAES-PKCS1-V1_5')
    }

    return forge.util.encode64(encrypted)
  }
  catch {
    throw new Error('加密失败')
  }
}
