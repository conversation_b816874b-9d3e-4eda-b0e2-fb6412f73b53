import { consola } from 'consola'

import { routeConfig, RouteKey } from '~/enums/route'

/**
 * 规范化路由路径，移除路径末尾的斜杠
 *
 * 例如：服务器端的路由路径可能会自动添加尾斜杠，所以需要移除，否则会导致路由匹配失败
 *
 * @param path - 路由路径
 * @returns 规范化后的路由路径
 */
export function normalizeRoutePath(path: string) {
  return path.replace(/\/$/, '')
}

/**
 * 检查路由是否为动态路由（支持 /:param 和 /[param] 两种格式）
 *
 * @param path - 要检查的路由路径
 * @returns 如果路径包含动态参数则返回 true，否则返回 false
 */
export function isDynamicRoute(path: string): boolean {
  // Vue Router 格式 /:param 的检测
  if (path.includes(':')) {
    return true
  }

  // Nuxt 3 格式 /[param] 的检测
  return path.includes('[') && path.includes(']')
}

/**
 * 比较动态路由与实际路由是否匹配
 *
 * @param path1 - 第一个路由路径
 * @param path2 - 第二个路由路径
 * @returns 如果路由匹配则返回 true，否则返回 false
 *
 * @example
 * ```ts
 * isDynamicRouteFullMatch('/xxx/:xxxId', '/xxx/123') // true
 * isDynamicRouteFullMatch('/xxx/[xxxId]', '/xxx/123') // true
 * isDynamicRouteFullMatch('/xxx/:xxxId', '/xxx/123/detail') // false
 * ```
 */
export function isDynamicRouteFullMatch(path1: string, path2: string): boolean {
  const isDynamicRoute1 = isDynamicRoute(path1)
  const isDynamicRoute2 = isDynamicRoute(path2)

  // 如果其中一个是动态路由，需要特殊处理
  if (isDynamicRoute1 || isDynamicRoute2) {
    // 确保动态路由模板在path1，实际路由在path2
    const [templatePath, actualPath] = isDynamicRoute1
      ? [path1, path2]
      : [path2, path1]

    // 将路径分割成段落
    const templateSegments = templatePath.split('/')
    const actualSegments = actualPath.split('/')

    // 如果段落数量不同，则不匹配
    if (templateSegments.length !== actualSegments.length) {
      return false
    }

    // 逐段比较
    for (let i = 0; i < templateSegments.length; i++) {
      const templateSeg = templateSegments[i]
      const actualSeg = actualSegments[i]

      // 如果当前段是动态参数（包含[]），则视为匹配
      if (templateSeg.includes('[') && templateSeg.includes(']')) {
        continue
      }

      // 否则必须完全相同
      if (templateSeg !== actualSeg) {
        return false
      }
    }

    return true
  }

  return false
}

/**
 *  判断两个路由路径是否相等
 *
 * @param path1 - 路由路径1
 * @param path2 - 路由路径2
 * @returns 如果两个路径相等则返回 true，否则返回 false
 */
export function isRoutePathEqual(path1: string | undefined, path2: string | undefined) {
  if (!path1 || !path2) {
    return false
  }

  const normalizedPath1 = normalizeRoutePath(path1)
  const normalizedPath2 = normalizeRoutePath(path2)

  if (normalizedPath1 === normalizedPath2) {
    return true
  }

  // 检测是否符合动态路由的格式
  return isDynamicRouteFullMatch(normalizedPath1, normalizedPath2)
}

/**
 * 根据 RouteKey 获取对应的完整路由路径
 *
 * @param routeKey - 路由配置中定义的路由键名
 * @param params - 动态路由参数对象，用于替换路径中的动态参数（如 [projectId]）
 * @returns 返回与 routeKey 对应的实际路由路径
 * @example
 * ```ts
 * // 基础用法
 * getRoutePath(RouteKey.首页) // 返回: '/'
 *
 * // 带动态参数
 * getRoutePath(RouteKey.TW_客户_项目详情, { projectId: '123' }) // 返回: '/client/project/123'
 * ```
 */
export function getRoutePath(routeKey: RouteKey, params?: Record<string, string | number>) {
  let path: string = routeConfig[routeKey].route

  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      const paramPattern = `[${key}]`

      if (!path.includes(paramPattern)) {
        consola.warn(
          `[路由参数警告] 路由 "${routeKey}" 的路径 "${path}" 中未找到参数 "${key}" 的占位符`,
          '\n提供的参数：', params,
        )

        return
      }

      path = path.replace(paramPattern, String(value))
    })
  }

  return path
}

/**
 * 根据路由路径获取对应的 RouteConfig
 * 返回匹配的 RouteConfig，如果未找到则返回 null
 */
export function getRouteByPath(path: string): RouteConfig | null {
  for (const k in routeConfig) {
    const routeItem = routeConfig[k as RouteKey]
    const routePath = routeItem.route

    if (isRoutePathEqual(routePath, path)) {
      return routeItem
    }
  }

  return null
}

/**
 * 根据路由路径获取对应的 RouteKey
 * 返回匹配的 RouteKey，如果未找到则返回 null
 */
export function getRouteKeyByPath(path: string): RouteKey | null {
  for (const k in routeConfig) {
    const routeItem = routeConfig[k as RouteKey]
    const routePath = routeItem.route

    if (isRoutePathEqual(routePath, path)) {
      return k as RouteKey
    }
  }

  return null
}

/**
 * 根据 RouteKey 获取对应的图标组件
 *
 * @param routeKey - 路由键值或菜单组键值
 * @returns 返回与 routeKey 对应的图标组件
 */
export function getRouteIcon(routeKey: RouteKey, fallback: Component | null = null): Component | null {
  if (Object.hasOwn(routeConfig, routeKey)) {
    const route = routeConfig[routeKey]
    const icon = 'icon' in route ? route.icon : null

    if (icon) {
      return icon
    }
  }

  return fallback
}

/**
 * 路由后退功能
 *
 * 该函数用于处理页面返回逻辑，会优先尝试使用浏览器的历史记录后退
 * 如果历史记录栈为空（如新页面直接打开），则跳转到指定的回退路径
 *
 * 注意：该函数只能在组合式 API 中使用，因为它依赖于 useRouter 函数
 * 如果在非组合式 API 或 setup 外部调用，可能会导致“composable can only be called inside setup”错误
 *
 * @param options - 配置选项
 * @param options.fallbackPath - 当无法后退时跳转的路径，默认为首页('/')
 * @param options.router - 可选的 router 实例，如果不提供则通过 useRouter 获取
 */
export function routeBack({
  fallbackPath,
  router,
}: {
  fallbackPath?: string | null
  router?: ReturnType<typeof useRouter>
} = {}) {
  const routerInstance = router || useRouter()

  if (window.history.state.back) {
    routerInstance.back()
  }
  else {
    routerInstance.push(fallbackPath ?? '/')
  }
}

/**
 * 判断是否为工作流应用页面
 *
 * @param path - 路由路径
 * @returns 如果路径为工作流应用页面则返回 true，否则返回 false
 */
export function isWorkflowApp(path: string) {
  return [
    RouteKey.TW_应用_工作流,
    RouteKey.TW_应用_工作流执行记录,
  ].some((routeKey) => isDynamicRouteFullMatch(path, getRoutePath(routeKey)))
}

/**
 * 判断是否为工作流执行记录画布页面
 */
export function isWorkflowDemo(path: string) {
  return isDynamicRouteFullMatch(path, getRoutePath(RouteKey.TW_应用_工作流执行记录画布))
}
