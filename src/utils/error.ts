/**
 * 抛出自定义错误并显示错误信息
 *
 * 使用 Nuxt 的 showError 方法展示自定义错误信息。
 * 这是 Nuxt 的内置错误处理机制的一部分，会触发错误页面的展示。
 *
 * 更多信息请参考： Nuxt Error Handling（https://nuxt.com/docs/getting-started/error-handling）
 *
 * 调用此方法的效果：
 * - 立即中断当前执行流程
 * - 自动跳转到 Nuxt 的错误页面（error.vue）
 * - 在错误页面显示传入的错误信息
 */
export function throwCustomError(errorData: CustomErrorData) {
  showError({
    statusCode: errorData.statusCode,
    message: errorData.message,
    data: errorData,
  })
}
