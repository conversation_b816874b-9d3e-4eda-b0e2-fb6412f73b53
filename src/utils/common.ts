import { sanitizeUrl } from '@braintree/sanitize-url'
import { GlobeIcon, ShieldQuestion } from 'lucide-vue-next'
import mitt, { type Emitter } from 'mitt'

import type { AssetType } from '~/enums/asset'
import { assetDetailConfig, TopologyNodeType } from '~/enums/asset'
import { UserStatus } from '~/enums/user'
import type { GlobalEvents } from '~/types/common'
import type { TW_User } from '~/types-tw/user'

/**
 * 将相对路径转换为完整的图片URL
 *
 * @example
 * appImage('avatar/1.jpg') // 返回: 'https://api.example.com/static/avatar/1.jpg'
 */
export function appImage(path: string) {
  const runtimeConfig = useRuntimeConfig()
  const { apiHost } = runtimeConfig.public

  const normalizedPath = path.replace(/^\/+/, '')

  return `${apiHost}/static/${normalizedPath}`
}

/**
 * 根据排序方向和字段名生成排序字符串
 *
 * @example
 * getOrderBy(1, 'createdAt')  // 返回: 'createdAt'
 * getOrderBy(-1, 'createdAt') // 返回: '-createdAt'
 * getOrderBy(0, 'createdAt')  // 返回: null
 */
export function getOrderBy(sortOrder: SortOrder, key?: string) {
  if (key) {
    return sortOrder === 1 ? key : sortOrder === -1 ? `-${key}` : null
  }

  return undefined
}

export function getTopologyNodeIcon(topologyType: TopologyNodeType): Component | null {
  if (
    topologyType === TopologyNodeType.Root
    || topologyType === TopologyNodeType.Network
    || topologyType === TopologyNodeType.NetworkDevice
  ) {
    return GlobeIcon
  }
  else if (Object.hasOwn(assetDetailConfig, topologyType)) {
    return assetDetailConfig[topologyType as unknown as AssetType].icon
  }

  return ShieldQuestion
}

interface ParsedItem {
  label: string
  /** 链接，已经过无害化处理 */
  value: string
  type: 'text' | 'link'
}

/**
 * 解析带有标签和值格式的文本内容
 *
 * @param text - 要解析的原始文本，每行格式为 "标签: 值"
 * @returns 解析后的对象数组，每个对象包含标签、值和类型信息
 *
 * @example
 * const text = `
 * 标题: 示例文档
 * 链接: https://example.com
 * 作者: 张三
 * `
 * parseKeyValueText(text)
 * // 返回:
 * // [
 * //   { label: '标题', value: '示例文档', type: 'text' },
 * //   { label: '链接', value: 'https://example.com', type: 'link' },
 * //   { label: '作者', value: '张三', type: 'text' }
 * // ]
 */
export function parseKeyValueText(text: string): ParsedItem[] {
  if (text.startsWith('http')) {
    return text
      .split(',')
      .map<ParsedItem>((item) => {
        return {
          label: '',
          value: item.trim(),
          type: 'link',
        }
      })
  }

  return text
    .split('\r\n')
    .filter((line) => line.includes(':'))
    .map<ParsedItem>((line) => {
      const [label, ...valueParts] = line.split(':')
      const value = sanitizeUrl(valueParts.join(':').trim())

      return {
        label: label.trim(),
        value,
        type: value.startsWith('http') ? 'link' : 'text',
      }
    })
}

/**
 * 手机号脱敏处理
 * @param mobile 手机号
 * @returns 脱敏后的手机号，格式：133****8888
 */
export function maskMobile(mobile: string): string {
  if (!mobile || mobile.length !== 11) {
    return mobile
  }

  return `${mobile.slice(0, 3)}****${mobile.slice(7)}`
}

/**
 * 将枚举配置转换为 valueEnum 格式
 *
 * @param enumConfig - 枚举配置
 * @param asMap - 是否返回 Map 类型，默认为 false
 * @returns 返回 valueEnum 格式（对象或 Map）
 */
export function toValueEnum<T extends string | number>(
  enumConfig: EnumConfig<T, { label: string }>,
  asMap = true,
): ProValueEnumObj | ProValueEnumMap {
  if (asMap) {
    return new Map(
      Object.entries(enumConfig).map(([,value]) => {
        const v = (value as { value: T }).value

        return [v, {
          label: (value as { label: string }).label,
          value: v,
        }]
      }),
    )
  }

  return Object.keys(enumConfig).reduce((acc, key) => {
    acc[key as T] = enumConfig[key as T].label

    return acc
  }, {} as Record<T, string>)
}

/**
 * 从 valueEnum 中获取选项列表
 *
 * @param valueEnum - 值枚举对象或 Map
 * @returns 返回选项列表 { label: string, value: string | number }[]
 */
export function toOptionsFromValueEnum(valueEnum?: ProValueEnumObj | ProValueEnumMap) {
  if (!valueEnum) {
    return undefined
  }

  // 处理 Map 类型的 valueEnum
  if (valueEnum instanceof Map) {
    return Array.from(valueEnum.entries()).map(([key, value]) => ({
      label: typeof value === 'object' ? value.label : value,
      value: key,
    }))
  }

  // 处理对象类型的 valueEnum
  return Object.entries(valueEnum).map(([key, value]) => ({
    label: typeof value === 'object' ? (value as { label: string }).label : value,
    value: key,
  }))
}

/**
 * 获取列的 field 值
 *
 * @param column - 列配置
 * @returns 返回列的 field 值
 */
export function getFieldName<T>(column: ProTableColumn<T>): string | undefined {
  const columnField = column.field

  if (typeof columnField === 'function') {
    return columnField(column)
  }

  return columnField
}

/**
 * 规范化用户数据，将十阵和扶摇的用户数据结构进行统一
 */
export function normalizedUser(user: TW_User): CurrentUser {
  return {
    id: user.id,
    username: user.name,
    avatar: user.avatar,
    email: user.email,
    mobile: user.phone,
    role_id: user.roles?.[0]?.id ?? 0,
    role_name: user.appointment ?? '',
    status: UserStatus.Employed,
    organize_id: 0,
    organize_name: '',
    create_time: user.created_at,
    update_time: user.updated_at,
    menu_list: user.permissions,
  }
}

/**
 * 睡眠函数
 *
 * @param ms - 睡眠时间（毫秒）
 * @returns 一个 Promise，在指定时间后 resolve
 */
export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * 全局事件发射器，用于在组件之间传递事件
 */
export const emitter: Emitter<GlobalEvents> = mitt<GlobalEvents>()
