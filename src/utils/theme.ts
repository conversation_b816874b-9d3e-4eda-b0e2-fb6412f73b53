import { $dt } from '@primevue/themes'

/**
 * 获取标签颜色值
 *
 * @param severity - 标签严重程度（如：info、warn、danger 等）
 * @param mode - 主题模式（如：light、dark）
 * @returns 返回对应的颜色值
 *
 * @description
 * - 根据传入的严重程度和主题模式，从 PrimeVue 主题配置中获取对应的标签颜色
 * - 如果获取失败（比如严重程度不存在），则返回次要（secondary）标签的颜色作为默认值
 * - 使用 $dt 函数从 PrimeVue 主题系统中读取颜色配置
 *
 * @example
 * getTagColor('warn', 'light') // 返回浅色主题下警告标签的颜色
 */
export function getTagColor(severity: TagSeverity, mode: ThemeMode): string {
  const defaultColor = $dt('tag.secondary.color').value[mode].value

  try {
    const tagColor = $dt(`tag.${severity}.color`)
    const color = tagColor.value[mode].value

    return color || defaultColor
  }
  catch {
    return defaultColor
  }
}
