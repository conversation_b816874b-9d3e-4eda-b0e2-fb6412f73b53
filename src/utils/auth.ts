import { WWLoginType } from '@wecom/jssdk'
import { consola } from 'consola'

import { RouteKey } from '~/enums/route'
import { UserService } from '~/services-tw/org'
import { normalizedUser } from '~/utils/common'

const enum AuthMessageType {
  Login = 'login',
  Logout = 'logout',
}

type AuthMessage =
  | {
    type: AuthMessageType.Login
    data: AuthResponse
  }
  | {
    type: AuthMessageType.Logout
  }

/**
 * 身份验证频道 - 用于跨标签页通信
 * 使用 BroadcastChannel API 在不同标签页之间同步用户的身份验证状态
 * 每个标签页都会创建这个频道的实例并监听消息
 */
const authChannel = new BroadcastChannel('auth-channel')

/**
 * 重定向到登录页
 *
 * 将当前路径作为重定向路径添加到登录页面的查询参数中
 */
export async function redirectToLogin() {
  const redirectPath = window.location.href
  const Url = new URL(redirectPath)

  await navigateTo({
    path: getRoutePath(RouteKey.登录),
    query: {
      // 转发当前页面的查询参数，防止一些配置参数丢失（如 ?debug=true）
      ...Object.fromEntries(Url.searchParams),
      redirectTo: window.encodeURIComponent(redirectPath),
    },
    /**
     * 使用 `replace: true` 是为了：
     * - 安全性：防止用户通过浏览器后退按钮访问需要认证的页面
     * - 用户体验：防止用户在登出后通过后退按钮陷入无效页面的循环
     */
    replace: true,
  })
}

/**
 * 处理登录成功后的操作
 *
 * 该函数负责在用户成功登录后执行一系列操作：
 * 1. 更新用户状态存储（userStore）
 * 2. 处理登录后的重定向逻辑
 * 3. 移除URL中的敏感参数（如授权码）
 *
 * @param loginUser - 包含用户身份信息的认证响应对象
 */
async function handleAfterLogin(loginUser: AuthResponse) {
  const userStore = useUserStore()
  userStore.login(loginUser)

  const route = useRoute()

  // 从 URL 查询参数中提取重定向路径，同时移除敏感参数（如授权码）
  // 保留其他查询参数以便在重定向后保持 URL 状态
  const { redirectTo: redirectUrl, code, ...query } = route.query

  let path: string

  if (typeof redirectUrl === 'string') {
    // 如果存在有效的重定向路径参数，则使用该路径
    // 解码 URL 编码的路径字符串
    path = window.decodeURIComponent(redirectUrl)
  }
  else {
    // 如果没有指定重定向路径，则默认导航到首页
    path = getRoutePath(RouteKey.首页)
  }

  // 执行导航操作
  // replace: true - 替换当前历史记录，防止用户通过浏览器返回按钮回到登录页
  // query - 保留其他非敏感的查询参数
  await navigateTo({
    path,
    query,
  }, {
    external: true,
    replace: true,
  })
}

/**
 * 处理登录成功的操作
 *
 * 在任何登录成功后的场景下（如第三方免登、账号密码登录），都应该调用此方法来同步登录状态
 *
 * 作用：
 * - 初始化用户信息
 * - 同步身份信息和登录状态至所有打开的标签页
 * - 重定向到指定路径
 */
export async function handleLoginSuccess(loginUser: AuthResponse) {
  authChannel.postMessage({
    type: AuthMessageType.Login,
    data: toRaw(loginUser),
  } satisfies AuthMessage)

  await handleAfterLogin(loginUser)
}

/**
 * 处理用户登出后的操作
 *
 * 该函数负责在用户登出后执行一系列操作：
 * 1. 清除用户状态存储（userStore）
 * 2. 处理登出后的重定向逻辑
 */
async function handleAfterLogout() {
  const userStore = useUserStore()
  userStore.logout()

  await redirectToLogin()
}

/**
 * 处理用户登出操作
 *
 * 该方法会:
 * 1. 通过 BroadcastChannel 向所有打开的标签页广播登出消息
 * 2. 清除当前页面的用户状态
 * 3. 重定向到登录页面，如果提供了有效的当前路径，会将其作为登录后的重定向地址
 *
 * 使用场景:
 * - 用户主动登出
 * - 会话过期自动登出
 * - Token 失效
 * - 权限变更需要重新登录
 *
 * 调用此方法将确保所有打开的标签页同步登出，维护一致的登录状态。
 */
export async function handleLogout() {
  authChannel.postMessage({
    type: AuthMessageType.Logout,
  } satisfies AuthMessage)

  await handleAfterLogout()
}

/**
 * 设置身份验证状态监听器
 * 监听其他标签页的身份验证状态变化，保持所有标签页状态同步
 *
 * 监听的事件类型：
 * - login: 其他标签页登录成功
 * - logout: 其他标签页登出
 */
export function setupAuthListener() {
  authChannel.addEventListener('message', (ev: MessageEvent<AuthMessage>) => {
    const eventData = ev.data
    const { type } = eventData

    switch (type) {
      case AuthMessageType.Login: {
        const { data } = eventData
        // 其他标签页登录成功
        // 1. 同步用户信息到当前页面
        // 2. 更新路由状态
        // 3. 重新初始化用户权限
        handleAfterLogin(data)
        break
      }

      case AuthMessageType.Logout: {
        // 其他标签页触发登出
        // 1. 清除本地存储的用户信息
        // 2. 重置应用状态
        // 3. 跳转到登录页
        handleAfterLogout()
        break
      }
    }
  })
}

/**
 * 处理第三方登录，如企微、钉钉、飞书等
 */
export async function handleThirdPartyLogin(code?: string) {
  let authCode = code

  if (!authCode) {
    const url = new URL(window.location.href)
    authCode = url.searchParams.get('code') ?? undefined
  }

  consola.log('---- 第三方登录 ----', { authCode })

  /**
   * 如果提供了 authCode，则使用第三方授权码登录
   * 否则，跳转至企微授权登录页
   */
  if (typeof authCode === 'string') {
    const res = await UserService.loginWecom(authCode)
    consola.success('企微登录成功', res)
    await handleLoginSuccess({ token: res.token, user: normalizedUser(res.user_info) })
  }
  else {
    const runtimeConfig = useRuntimeConfig()
    const { wecomAppId, wecomAgentId } = runtimeConfig.public

    /**
     * 配置企微授权登录回调地址，需要在企微后台进行以下配置：
     * 企微后台管理 → 应用管理 → 企业微信授权登录 → Web 网页授权回调域
     *
     * 回调域可设置为域名/IP，如果需要本地调试，则设置为本地 IP 地址
     */
    const authRedirectUri = window.location.origin

    if (isInWeCom()) {
      const url = new URL('https://open.weixin.qq.com/connect/oauth2/authorize')

      url.searchParams.set('appid', wecomAppId)
      url.searchParams.set('agentid', wecomAgentId)
      url.searchParams.set('response_type', 'code')
      url.searchParams.set('scope', 'snsapi_privateinfo')
      url.searchParams.set('redirect_uri', authRedirectUri)
      url.hash = 'wechat_redirect'

      window.location.replace(url.toString())
    }
    else {
      const url = new URL('https://login.work.weixin.qq.com/wwlogin/sso/login')

      url.searchParams.set('appid', wecomAppId)
      url.searchParams.set('agentid', wecomAgentId)
      url.searchParams.set('login_type', WWLoginType.corpApp)
      url.searchParams.set('lang', 'zh')
      url.searchParams.set('redirect_uri', authRedirectUri)

      window.location.replace(url.toString())
    }
  }
}
