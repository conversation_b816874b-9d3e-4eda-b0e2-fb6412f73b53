export function isMobile() {
  return navigator.userAgent.toLowerCase().includes('mobile') || false
}

export function isMac() {
  return navigator.userAgent.toLowerCase().includes('mac') || false
}

export function isWindows() {
  return navigator.userAgent.toLowerCase().includes('windows') || false
}

export function isDev() {
  return import.meta.dev || false
}

export function isTest() {
  const runtimeConfig = useRuntimeConfig()
  const { releaseEnv } = runtimeConfig.public

  return releaseEnv.startsWith('test') || releaseEnv.startsWith('mock') || false
}

/**
 * 检查当前浏览器是否为 Safari
 *
 * Safari 浏览器的 UA 中会包含 safari 但不包含 chrome 等其他浏览器标识
 */
export function isSafari() {
  const ua = navigator.userAgent.toLowerCase()

  return (ua.includes('safari') && !ua.includes('chrome') && !ua.includes('android')) || false
}

export function isFuYao() {
  const runtimeConfig = useRuntimeConfig()
  const { releaseEnv } = runtimeConfig.public

  return releaseEnv?.includes('fuyao') || false
}

/**
 * 检查是否在企业微信内部打开
 *
 * 企业微信浏览器的 UA 中会包含 wxwork 标识，可以据此判断是否在企业微信内部打开
 */
export function isInWeCom() {
  const ua = navigator.userAgent.toLowerCase()

  return ua.includes('wxwork') || false
}

/**
 * 根据操作系统获取快捷键
 */
export function getKbd(key: string) {
  return isMac() ? `⌘${key}` : `Ctrl + ${key}`
}
