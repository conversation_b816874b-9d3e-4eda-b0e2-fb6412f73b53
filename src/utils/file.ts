/**
 * 文件处理相关工具函数
 */

/**
 * 将数据转换为 Blob 对象
 *
 * @param data - 要转换的数据，可以是字符串或对象
 * @param options - 转换选项
 * @returns Blob 对象
 *
 * @example
 * // 转换 JSON 对象为 Blob
 * const jsonBlob = createBlob({ name: '测试', value: 123 }, {
 *   type: 'application/json',
 *   stringify: true,
 *   formatting: 2
 * })
 *
 * // 转换 CSV 数据为 Blob
 * const csvBlob = createBlob('id,name\n1,测试', { type: 'text/csv' })
 */
export function createBlob(
  data: unknown,
  options: {
    /** MIME 类型，默认为 'application/octet-stream' */
    type?: string
    /** 是否将数据转换为 JSON 字符串，对非字符串数据有效 */
    stringify?: boolean
    /** JSON 格式化缩进，仅当 stringify 为 true 时有效 */
    formatting?: number
    /** 编码方式，默认为 utf-8 */
    charset?: string
  } = {},
): Blob {
  const {
    type = 'application/octet-stream',
    stringify = false,
    formatting = 0,
    charset,
  } = options

  // 处理 MIME 类型，添加编码方式（如果提供）
  const mimeType = charset ? `${type};charset=${charset}` : type

  // 处理数据内容
  let content: string | Blob

  if (data instanceof Blob) {
    return data
  }
  else if (typeof data === 'string') {
    content = data
  }
  else if (stringify) {
    content = JSON.stringify(data, null, formatting)
  }
  else {
    // 如果不是字符串且不需要转换为 JSON，则尝试转为字符串
    content = String(data)
  }

  return new Blob([content], { type: mimeType })
}

/**
 * 触发文件下载
 *
 * @param blob - 要下载的 Blob 对象或数据
 * @param fileName - 下载文件名
 * @param options - 下载选项
 *
 * @example
 * // 下载 JSON 数据
 * downloadBlob({ name: '测试', value: 123 }, '测试数据.json', {
 *   type: 'application/json',
 *   stringify: true,
 *   formatting: 2
 * })
 *
 * // 下载 CSV 数据
 * downloadBlob('id,name\n1,测试', 'data.csv', { type: 'text/csv' })
 */
export function downloadBlob(
  blob: Blob | unknown,
  fileName: string,
  options: {
    /** MIME 类型，当 blob 不是 Blob 对象时使用 */
    type?: string
    /** 是否将数据转换为 JSON 字符串，对非 Blob、非字符串数据有效 */
    stringify?: boolean
    /** JSON 格式化缩进，仅当 stringify 为 true 时有效 */
    formatting?: number
    /** 编码方式，默认为 utf-8 */
    charset?: string
  } = {},
): void {
  // 如果不是 Blob 对象，转换为 Blob
  const blobData = blob instanceof Blob
    ? blob
    : createBlob(blob, options)

  const url = URL.createObjectURL(blobData)

  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  document.body.appendChild(link)

  link.click()

  URL.revokeObjectURL(url)
  link.remove()
}

/**
 * 简易的文件保存接口
 *
 * @param blob - 要保存的 Blob 对象或数据
 * @param fileName - 下载的文件名
 *
 * @example
 * // 保存 Blob 对象
 * const jsonBlob = new Blob(['{"name":"测试"}'], {type: 'application/json'})
 * saveAs(jsonBlob, '测试文件.json')
 *
 * // 保存 JSON 数据（自动识别文件类型）
 * saveAs({ name: '测试', value: 123 }, '测试数据.json')
 *
 * // 保存 CSV 数据
 * saveAs('id,name\n1,张三', '联系人.csv')
 *
 * // 保存纯文本
 * saveAs('Hello World!', 'hello.txt')
 */
export function saveAs(blob: Blob | unknown, fileName: string): void {
  // 确定数据类型并设置适当的选项
  let options = {}

  // 如果不是 Blob 对象，尝试根据文件扩展名推断类型
  if (!(blob instanceof Blob)) {
    const extension = fileName.split('.').pop()?.toLowerCase()

    // 根据扩展名设置不同的选项
    switch (extension) {
      case 'json':
        options = {
          type: 'application/json',
          stringify: true,
          formatting: 2,
          charset: 'utf-8',
        }
        break

      case 'csv':
        options = {
          type: 'text/csv',
          charset: 'utf-8',
        }
        break

      case 'txt':
        options = {
          type: 'text/plain',
          charset: 'utf-8',
        }
        break

      case 'html':
        // fall through

      case 'htm':
        options = {
          type: 'text/html',
          charset: 'utf-8',
        }
        break

      case 'xml':
        options = {
          type: 'application/xml',
          charset: 'utf-8',
        }
        break

      case 'js':
        options = {
          type: 'text/javascript',
          charset: 'utf-8',
        }
        break

      case 'css':
        options = {
          type: 'text/css',
          charset: 'utf-8',
        }
        break

      case 'svg':
        options = { type: 'image/svg+xml' }
        break

      case 'png':
        options = { type: 'image/png' }
        break

      case 'jpg':
        // fall through

      case 'jpeg':
        options = { type: 'image/jpeg' }
        break

      case 'gif':
        options = { type: 'image/gif' }
        break

      case 'pdf':
        options = { type: 'application/pdf' }
        break

      default:
        // 对于未知类型，使用通用的二进制类型
        options = { type: 'application/octet-stream' }
    }
  }

  downloadBlob(blob, fileName, options)
}

/**
 * 文件读取结果类型
 */
type FileReaderResultType = string | ArrayBuffer | null

/**
 * 文件选择配置选项
 */
interface FileSelectOptions {
  /** 允许的文件类型，默认为 '*' */
  accept?: string
  /** 是否允许多选，默认为 false */
  multiple?: boolean
  /** 文件选择后的回调函数 */
  onSelect?: (files: File[]) => void
  /** 文件读取后的回调函数 */
  onRead?: (result: FileReaderResultType, file: File) => void
  /** 错误处理回调函数 */
  onError?: () => void
  /** 文件读取方式，默认为 'text' */
  readAs?: 'text' | 'dataURL' | 'arrayBuffer' | 'binaryString'
}

/**
 * 打开文件选择对话框
 *
 * @param options - 文件选择配置
 *
 * @example
 * // 选择并读取 JSON 文件
 * selectFile({
 *   accept: '.json',
 *   onRead: (content, file) => {
 *     try {
 *       const data = JSON.parse(content as string)
 *     } catch (error) {
 *       console.error('解析 JSON 失败', error)
 *     }
 *   }
 * })
 *
 * // 选择多个图片文件
 * selectFile({
 *   accept: 'image/*',
 *   multiple: true,
 *   readAs: 'dataURL',
 *   onRead: (dataUrl, file) => {
 *     const img = new Image()
 *     img.src = dataUrl as string
 *     document.body.appendChild(img)
 *   }
 * })
 */
export function selectFile(options: FileSelectOptions = {}): void {
  const {
    accept = '*',
    multiple = false,
    onSelect,
    onRead,
    onError,
    readAs = 'text',
  } = options

  // 创建文件输入元素
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.accept = accept
  fileInput.multiple = multiple

  // 监听文件选择事件
  fileInput.addEventListener('change', (event) => {
    const files = (event.target as HTMLInputElement).files

    if (!files || files.length === 0) {
      return
    }

    const selectedFiles = Array.from(files)

    // 如果提供了选择回调，则执行
    if (onSelect) {
      onSelect(selectedFiles)
    }

    // 如果提供了读取回调，则读取文件
    if (onRead) {
      selectedFiles.forEach((file) => {
        const reader = new FileReader()

        reader.onload = (e) => {
          onRead(e.target?.result || null, file)
        }

        if (onError) {
          reader.onerror = () => {
            onError()
          }
        }

        // 根据指定的方式读取文件
        switch (readAs) {
          case 'text':
            reader.readAsText(file)
            break

          case 'dataURL':
            reader.readAsDataURL(file)
            break

          case 'arrayBuffer':
            reader.readAsArrayBuffer(file)
            break

          case 'binaryString':
            reader.readAsBinaryString(file)
            break
        }
      })
    }
  })

  // 触发文件选择
  fileInput.click()
}
