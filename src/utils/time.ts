import dayjs from 'dayjs'

import { DateFormat } from '~/enums/common'
import type { AnyType } from '~/types/common'

/**
 * 格式化日期为指定格式的字符串
 *
 * @param date - 支持 Date 对象、时间戳、ISO 字符串等 dayjs 可解析的日期格式
 * @param format - 日期格式模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串，如果输入为空则返回 undefined
 *
 * @example
 * formatDate(new Date())                    // '2024-03-20 15:30:00'
 * formatDate('2024-03-20', 'YYYY-MM-DD')   // '2024-03-20'
 * formatDate(1710921600000)                // '2024-03-20 15:30:00'
 * formatDate(null)                         // undefined
 */
export function formatDate(date: AnyType, format = DateFormat.YYYY_MM_DD_HH_MM_SS): string | undefined {
  if (date) {
    return dayjs(date).format(format)
  }

  return undefined
}

/**
 * 格式化日期范围数组为开始和结束时间对象
 *
 * @param dateRange - 包含开始和结束日期的数组 [startDate, endDate]
 * @param format - 日期格式模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 包含格式化后的开始时间和结束时间的对象
 *
 * @example
 * // 自定义格式
 * formatDateRange(['2024-03-20', '2024-03-21'], 'YYYY-MM-DD')
 * // 返回: {
 * //   start: '2024-03-20',
 * //   end: '2024-03-21'
 * // }
 *
 * formatDateRange(null)
 * // 返回: { start: undefined, end: undefined }
 */
export function formatDateRange(dateRange: AnyType, format = DateFormat.YYYY_MM_DD_HH_MM_SS): { start: string | undefined, end: string | undefined } {
  if (dateRange) {
    return {
      start: formatDate(dateRange.at(0), format),
      end: formatDate(dateRange.at(1), format),
    }
  }

  return { start: undefined, end: undefined }
}

/**
 * 将日期格式化为相对时间描述
 *
 * @param date - 支持 Date 对象、时间戳、ISO 字符串等 dayjs 可解析的日期格式
 * @returns 返回相对时间描述，如"5 分钟前"、"2 小时前"、"3 天前"等。
 *          如果超过 30 天则返回具体日期，如果输入为空则返回 undefined
 *
 * @example
 * formatRelativeTime(new Date())           // "刚刚"
 * formatRelativeTime('2024-03-20 12:00')   // "3 小时前"
 * formatRelativeTime('2024-02-20')         // "2024-02-20"
 * formatRelativeTime(null)                 // undefined
 */
export function formatRelativeTime(date: AnyType): string | undefined {
  if (date) {
    const now = dayjs()
    const target = dayjs(date)

    // 先计算分钟差
    const diffMinutes = now.diff(target, 'minute')

    if (diffMinutes < 60) {
      if (diffMinutes <= 1) {
        return '刚刚'
      }

      return `${diffMinutes} 分钟前`
    }

    // 超过60分钟，再计算小时差
    const diffHours = now.diff(target, 'hour')

    if (diffHours < 24) {
      return `${diffHours} 小时前`
    }

    // 超过24小时，再计算天数差
    const diffDays = now.diff(target, 'day')

    if (diffDays < 30) {
      return `${diffDays} 天前`
    }

    // 超过30天，返回具体日期
    return formatDate(date, DateFormat.YYYY_MM_DD)
  }

  return undefined
}

/**
 * 判断一个值是否可以被解析为有效的日期
 *
 * @param value - 需要判断的值
 * @returns 如果值可以被解析为有效的日期则返回 true，否则返回 false
 */
export function isParsableValidDate(value: AnyType): boolean {
  const date = dayjs(value)

  return date.isValid()
}
