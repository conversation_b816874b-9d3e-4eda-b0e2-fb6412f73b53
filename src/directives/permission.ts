import { useUserStore } from '~/stores/user'
import type { PermissionFlag } from '~/types/user'

export const vPermission = {
  mounted(el: HTMLElement, binding: DirectiveBinding<PermissionFlag | PermissionFlag[]>) {
    const userStore = useUserStore()
    const perm = binding.value

    if (!userStore.hasPermission(perm)) {
      // 如果没有权限，则从 DOM 中移除元素
      el.parentNode?.removeChild(el)
    }
  },
}
