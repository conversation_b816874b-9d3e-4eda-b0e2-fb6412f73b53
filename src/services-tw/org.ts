import type { AuthResponse, ClientAuthValues, ClientFormValues, ClientListItem, PermissionFormValues, PermissionItem, RoleFormValues, RoleListItem, TW_Client, TW_Department, TW_DepartmentListItem, TW_LoginFormValues, TW_Role, TW_User, TW_UserFormValues, TW_UserListItem, WechatUser, WechatUserListItem } from '~/types-tw/user'

export const UserService = {
  /** 使用企微授权码登录 */
  loginWecom(authCode: string) {
    return useRequest<AuthResponse>('/public/login', {
      method: 'POST',
      body: { code: authCode },
    })
  },

  /** 使用密码登录 */
  loginWithPassword(values: TW_LoginFormValues) {
    return useRequest<AuthResponse>('/public/ac-login', {
      method: 'POST',
      body: values,
    })
  },

  clientLogin(authCode: string) {
    return useRequest<ClientAuthValues>('/public/wechat/user_id', {
      query: {
        code: authCode,
      },
    })
  },

  getDepartments(query?: Partial<TW_DepartmentListItem>) {
    return useRequest<TW_DepartmentListItem[]>('/depts', {
      query,
    })
  },

  getUsers(query?: Partial<Pick<TW_User, 'name'>> & { dept_id?: TW_Department['id'] }) {
    return useRequest<TW_UserListItem[]>('/depts/users', {
      query,
    })
  },

  getUser(userId: TW_User['id']) {
    return useRequest<TW_User>(`/depts/users/${userId}`)
  },

  updateUser(userId: TW_User['id'], payload: Partial<TW_UserFormValues>) {
    return useRequest(`/depts/users/${userId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  /** 获取角色列表 */
  getRoles(query?: WithPageQuery<Partial<RoleListItem>>) {
    return useRequest<PageResult<RoleListItem>>('/roles', {
      query,
    })
  },

  /** 获取角色详情 */
  getRole(roleId: TW_Role['id']) {
    return useRequest<TW_Role>(`/roles/${roleId}`)
  },

  createRole(payload: RoleFormValues) {
    return useRequest('/roles', {
      method: 'POST',
      body: payload,
    })
  },

  updateRole(roleId: TW_Role['id'], payload: RoleFormValues) {
    return useRequest(`/roles/${roleId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  deleteRole(roleId: TW_Role['id']) {
    return useRequest(`/roles/${roleId}`, {
      method: 'DELETE',
    })
  },

  getPermissions(query?: Partial<PermissionItem>) {
    return useRequest<PermissionItem[]>('/menus', {
      query,
    })
  },

  createPermission(payload: PermissionFormValues) {
    return useRequest('/menus', {
      method: 'POST',
      body: payload,
    })
  },

  updatePermission(permissionId: PermissionItem['id'], payload: PermissionFormValues) {
    return useRequest(`/menus/${permissionId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  deletePermission(permissionId: PermissionItem['id']) {
    return useRequest(`/menus/${permissionId}`, {
      method: 'DELETE',
    })
  },

  getClients(query?: WithPageQuery<Partial<ClientListItem>>) {
    return useRequest<PageResult<ClientListItem>>('/customers', {
      query,
    })
  },

  getClient(clientId: TW_Client['id']) {
    return useRequest<TW_Client>(`/customers/${clientId}`)
  },

  getClientInfo(clientId: TW_Client['id']) {
    return useRequest<TW_Client>(`/customers/${clientId}`)
  },

  updateClient(clientId: TW_Client['id'], payload: ClientFormValues) {
    return useRequest(`/customers/${clientId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  updateClientUser(clientId: TW_Client['id'], userIds: TW_User['id'][]) {
    return useRequest(`/customers/${clientId}`, {
      method: 'PUT',
      body: {
        user_ids: userIds,
      },
    })
  },

  getWechatUsers(query?: WithPageQuery<Partial<WechatUserListItem>>) {
    return useRequest<PageResult<WechatUserListItem>>('/wechat/users', {
      query,
    })
  },

  getWechatUser(wechatUserId: WechatUser['id']) {
    return useRequest<WechatUser>(`/wechat/users/${wechatUserId}`)
  },

}
