import type { FeedbackSubmitValues } from '~/types-tw/client'
import type { ProjectListItem, ProjectPublicDetail, TW_AutoFlow, TW_AutoFlowListItem, TW_AutoFlowQuery, TW_Project, TW_ProjectDesc, TW_ProjectFeedback, TW_ProjectMilestone, TW_ProjectTeam } from '~/types-tw/project'

export const ProjectService = {
  getProjects(query?: WithPageQuery<Partial<ProjectListItem>>) {
    return useRequest<PageResult<ProjectListItem>>('/projects/list', {
      query,
    })
  },

  getProjectOverview(projectId: TW_Project['id']) {
    return useRequest<TW_Project>(`/projects/outline/${projectId}`)
  },

  /**
   * 获取公开可查看的项目详情
   */
  getPublicProjectDetail(projectId: ProjectPublicDetail['id']) {
    return useRequest<ProjectPublicDetail>(`/public/wechat/project/${projectId}`)
  },

  updateProject(projectId: ProjectListItem['id'], payload: Partial<ProjectListItem>) {
    return useRequest<ProjectListItem>(`/projects/${projectId}`, {
      method: 'PUT',
      body: payload,
    })
  },

  /** 关联工作流 */
  associateAutoFlow(projectId: TW_Project['id'], flowId: TW_AutoFlow['id']) {
    return useRequest(`/projects/flow/${projectId}`, {
      method: 'PUT',
      body: {
        flow_id: flowId,
      },
    })
  },

  getAutoFlows(query?: TW_AutoFlowQuery) {
    return useRequest<{ data?: TW_AutoFlowListItem[], nextCursor?: string }>('/workflow/list', {
      query,
    })
  },

  /** 删除工作流 */
  deleteAutoFlow(flowId: TW_AutoFlow['id']) {
    return useRequest(`/workflow/list/${flowId}`, {
      method: 'DELETE',
    })
  },

  getProjectTeam(projectId: TW_Project['id']) {
    return useRequest<TW_ProjectTeam[]>(`/projects/team/${projectId}`)
  },

  getProjectDesc(projectId: TW_Project['id']) {
    return useRequest<TW_ProjectDesc>(`/projects/desc/${projectId}`)
  },

  getProjectFeedback(projectId: TW_Project['id']) {
    return useRequest<TW_ProjectFeedback>(`/projects/feedback/${projectId}`)
  },

  getProjectMilestone(projectId: TW_Project['id']) {
    return useRequest<TW_ProjectMilestone[]>(`/projects/milestone/${projectId}`)
  },

  /** 客户提交项目评价 */
  submitFeedback(payload: FeedbackSubmitValues) {
    return useRequest('/public/wechat/project/evaluate', {
      method: 'POST',
      body: payload,
    })
  },
}
