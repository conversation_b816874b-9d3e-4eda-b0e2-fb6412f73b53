/** 登录表单需要发送的数据 */
export interface TW_LoginFormValues {
  phone: string
  password: string
}

export interface TW_Department {
  id: UniqueId
  parent?: UniqueId
  /** 部门名称 */
  name: string
}

export type TW_DepartmentListItem = TW_Department

export type TW_DepartmentFormValues = Pick<TW_Department, 'parent' | 'name'>

export interface TW_User {
  id: UniqueId
  user_id: string
  /** 用户姓名 */
  name: string
  email: string
  phone: string
  roles?: TW_Role[]
  department?: TW_Department
  /** 职位 */
  appointment?: string
  avatar?: string
  permissions?: PermissionItem['code'][]
  created_at: string
  updated_at?: string
}

export type TW_UserListItem = TW_User

export type TW_UserFormValues = {
  role_ids?: TW_Role['id'][]
}

export interface TW_Client {
  id: UniqueId
  address?: string
  /** 客户名称 */
  name: string
  /** 客户编号 */
  number: string
  /** 客户联系电话 */
  work_phone?: string
  country?: string
  /** 所在省 */
  country_province?: string
  /** 所在市 */
  country_province_city?: string
  /** 发票单位地址 */
  invoice_unit_address?: string
  /** 发票单位银行名称 */
  invoice_unit_bank_name?: string
  /** 发票单位银行账号 */
  invoice_unit_bank_number?: string
  /** 发票单位名称 */
  invoice_unit_name?: string
  /** 发票单位税号 */
  invoice_unit_number?: string
  /** 发票单位电话 */
  invoice_unit_phone?: string
  /** 客户类型 */
  type?: string
  type_id?: number
  /** 客户经理 */
  user?: {
    id: UniqueId
    avatar: string
    name: string
    phone?: string
  }
  wechat_users?: WechatUser[]
  created_at: string
  updated_at?: string
}

export type ClientListItem = TW_Client

export type ClientFormValues = Pick<TW_Client, 'work_phone'>

export interface ClientAuthValues {
  openid: string
}

export type WechatUser = {
  id: UniqueId
  openid: string
  nickname: string
  avatar: string
  created_at: string
  updated_at?: string
  is_follow: boolean
  username: string
  phone: string
  email: string
}

export type WechatUserListItem = WechatUser

export interface TW_Role {
  id: UniqueId
  /** 角色名 */
  name: string
  /** 角色描述 */
  description?: string
  /** 角色拥有的权限列表 */
  menu_ids?: PermissionItem['id'][]
  created_at: string
}

export type DepartmentTreeNode = TreeNodeWrap<TW_Department>

export type RoleListItem = TW_Role

export type RoleFormValues = Pick<TW_Role, 'name' | 'description' | 'menu_ids'>

/** 权限项 */
export interface PermissionItem {
  id: UniqueId
  parent_id?: PermissionItem['id']
  /** 权限展示名称 */
  name: string
  /** 权限标识 */
  code: string
  /** 权限描述 */
  description?: string
}

export type PermissionFormValues = Pick<PermissionItem, 'parent_id' | 'name' | 'code'>

export interface AuthResponse {
  token: string
  user_info: TW_User
}
