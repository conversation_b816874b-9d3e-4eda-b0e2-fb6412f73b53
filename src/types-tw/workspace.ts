/** 工作区基础统计信息 */
export interface TW_WorkspaceBaseCount {
  /** 总数量 */
  total_count: number
  /** 活跃数量 */
  active_count: number
  /** 执行数量 */
  execution_count: number
  /** 执行成功数量 */
  execution_success_count: number
  /** 执行失败数量 */
  execution_failed_count: number
  /** 正在执行数量 */
  execution_running_count: number
  /** 健康指数 */
  health_index: number
}

/** 工作流执行统计 */
export interface TW_WorkflowExecutionCount {
  /** 工作流名称 */
  name: string
  /** 执行次数 */
  count: number
}

/** 执行状态分布 */
export interface TW_ExecutionStatusDistribution {
  /** 状态：canceled(已取消) | waiting(等待中) | error(错误) | success(成功) */
  status: 'canceled' | 'waiting' | 'error' | 'success'
  /** 数量 */
  count: number
}

/** 工作流节点统计 */
export interface TW_WorkflowNodeCount {
  /** 工作流名称 */
  name: string
  /** 节点数量 */
  node_count: number
}

/** 最近执行统计 */
export interface TW_RecentExecutionCount {
  /** 日期 (YYYY-MM-DD 格式) */
  date: string
  /** 执行总数 */
  count: number
  /** 成功执行数 */
  success_count: number
  /** 失败执行数 */
  error_count: number
}

/** 工作区统计信息 */
export interface TW_WorkspaceStats {
  /** 基础统计信息 */
  base_count: TW_WorkspaceBaseCount
  /** 工作流执行统计列表 */
  workflow_execution_counts: TW_WorkflowExecutionCount[]
  /** 执行状态分布列表 */
  execution_status_distribution: TW_ExecutionStatusDistribution[]
  /** 工作流节点统计列表 */
  workflow_node_counts: TW_WorkflowNodeCount[]
  /** 最近执行统计列表 */
  recent_execution_counts: TW_RecentExecutionCount[]
}
