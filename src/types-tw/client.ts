import type { TW_Project } from '~/types-tw/project'
import type { WechatUser } from '~/types-tw/user'

export interface FeedbackFormValues {
  /** 响应速度 */
  response_score: number
  /** 专业能力 */
  professional_score: number
  /** 沟通效果 */
  communication_score: number
  /** 标签 */
  tags?: string[]
  /** 内容 */
  comment?: string
}

export interface FeedbackSubmitValues extends FeedbackFormValues {
  openid: WechatUser['openid']
  project_id: TW_Project['id']
}
