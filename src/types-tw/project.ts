import type { ProjectStatus, ProjectType } from '~/constants-tw/project'
import type { TW_Client as ProjectClient, TW_User } from '~/types-tw/user'

/** 项目合同 */
interface ProjectContract {
  id: number
  customer_id: ProjectClient['id']
  /** 合同名称 */
  name: string
  /** 合同编号 */
  number: string
}

export interface TW_Project {
  id: UniqueId
  /** 项目编号 */
  number: string
  /** 项目名称 */
  name: string
  /** 项目关联的自动化流程 ID */
  flow_id: string

  /** 项目负责人 */
  leader_object?: {
    user: TW_User
  }

  /** 项目销售人 */
  sale_object?: {
    user: TW_User
  }

  /** 项目类型 */
  type?: ProjectType[]

  /** 项目状态 */
  status: ProjectStatus

  /** 截止日期 */
  start_date: [string, string]

  /** 客户 ID */
  customer_id?: ProjectClient['id']

  /** 项目统计 */
  statistics?: {
    /** 团队人数 */
    team?: number
    /** 任务统计 */
    task?: {
      /** 已完成 */
      close?: number
      /** 总数 */
      count?: number
    }
  }

  contract?: ProjectContract

  /** 项目关联的工作流详情 */
  flow_detail?: TW_AutoFlow
}

export type ProjectPublicDetail = Omit<TW_Project, 'leader_object' | 'sale_object'>

export type ProjectListItem = TW_Project

export interface TW_AutoFlow {
  id: string
  /** 流程名称 */
  name: string
  description?: string
  /** 是否启用 */
  active: boolean
  createdAt: string
  updatedAt?: string
}

export type TW_AutoFlowListItem = TW_AutoFlow

export interface TW_AutoFlowQuery {
  limit: number
  cursor?: string
  name?: string
}

export interface TW_ProjectTeam {
  user: TW_User
}

export interface TW_ProjectDesc {
  contract_id: string
}

export interface TW_ProjectFeedback {
  /** 评价标签 */
  hot_tags: Record<string, number>
  /** 总体评分 */
  satisfaction: {
    overall?: {
      score?: string
      total?: string
    }
    communication?: {
      score?: string
    }
    professional?: {
      score?: string
    }
    response?: {
      score?: string
    }
  }
  /** 评价列表 */
  judge_list: Partial<{
    comment: string
    communication: number
    created_at: string
    id: number
    professional: number
    project_id: number
    response: number
    score: number
    tags: string[]
    type: string
    user_id: number
    user_name: string
  }>[]
}

export interface TW_ProjectMilestone {
  id: number
  created_at: string
  created_users: TW_User
  name: string
  projects_id: TW_Project['id']
  sort: number
  statistics: {
    sum_day_consumesume: number
    task: {
      count: number
      jxz: number
      wks: number
      ygb: number
      yqx: number
      ywc: number
      yyq: number
      yzt: number
    }
    task_day_consume: number
    wo: {
      count: number
      jxz: number
      wks: number
      ygb: number
      yqx: number
      ywc: number
      yzt: number
    }
    wo_day_consume: number
  }
}
