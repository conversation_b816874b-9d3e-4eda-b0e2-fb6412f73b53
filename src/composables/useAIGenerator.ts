import { createDeepSeek } from '@ai-sdk/deepseek'
import { streamText } from 'ai'

import { DEFAULT_MAX_TOKENS } from '~/constants-tw/aigc'

type StreamTextOptions = Parameters<typeof streamText>[0]

export interface AIGeneratorOptions extends Omit<StreamTextOptions, 'model' | 'onError'> {
  /** 最大历史消息数量限制 */
  maxHistoryMessages?: number
  /** 是否启用思考 */
  enableThinking?: boolean
  /** 错误处理回调函数 */
  onError?: (error: Error) => void
}

/**
 * 简化版的聊天消息，用于 AI 请求
 */
export interface AIChatMessage {
  /** 消息内容 */
  content: string
  /** 消息角色：用户或助手 */
  role: 'user' | 'assistant'
}

export interface GenerateOptions extends Pick<StreamTextOptions, 'system' | 'prompt' | 'temperature'> {
  /** 聊天历史记录 */
  messages?: AIChatMessage[]
  /** 模型名称 */
  modelName?: string
}

// 估算文本 token 数量（粗略估算）
function estimateTokens(text: string): number {
  // 分离中文和非中文字符
  const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || []
  const nonChineseChars = text.match(/[^\u4e00-\u9fa5]/g) || []

  // 估算token数
  const chineseTokens = chineseChars.length * 1.5
  const nonChineseTokens = nonChineseChars.length / 4

  return Math.ceil(chineseTokens + nonChineseTokens)
}

// 智能截断消息历史，确保不超过最大 token 限制
function truncateMessages(messages: AIChatMessage[], system: string | undefined, maxTokens = DEFAULT_MAX_TOKENS): AIChatMessage[] {
  // 保留的消息
  const result: AIChatMessage[] = []
  // 已使用的tokens
  let usedTokens = system ? estimateTokens(system) : 0

  // 从最新消息开始处理，确保最近的对话被保留
  const reversedMessages = [...messages].reverse()

  // 始终保留最后一条用户消息
  const lastUserMessage = reversedMessages.find((m) => m.role === 'user')

  if (lastUserMessage) {
    const lastUserTokens = estimateTokens(lastUserMessage.content)
    usedTokens += lastUserTokens
    result.unshift(lastUserMessage)
  }

  // 处理其他消息
  for (const message of reversedMessages) {
    // 跳过已添加的最后一条用户消息
    if (message === lastUserMessage) {
      continue
    }

    const messageTokens = estimateTokens(message.content)

    // 如果添加这条消息会超过限制，则停止
    if (usedTokens + messageTokens > maxTokens) {
      break
    }

    usedTokens += messageTokens
    result.unshift(message)
  }

  return result
}

function useDeepSeek() {
  const runtimeConfig = useRuntimeConfig()
  const { aiBaseUrl, aiApiKey } = runtimeConfig.public

  const deepseek = createDeepSeek({
    baseURL: aiBaseUrl,
    apiKey: aiApiKey,
  })

  return deepseek
}

/**
 * AI 文本生成的通用 Composable
 */
export function useAIGenerator(options: AIGeneratorOptions = {}) {
  const {
    onError,
    maxHistoryMessages = 100,
    maxTokens = DEFAULT_MAX_TOKENS,
    enableThinking = false,
  } = options

  const deepseek = useDeepSeek()

  const runtimeConfig = useRuntimeConfig()
  const { aiDefaultModel } = runtimeConfig.public

  const isGenerating = ref(false)
  const generatedContent = ref('')
  const abortController = ref<AbortController | null>(null)

  if (!aiDefaultModel) {
    throw new Error('')
  }

  const generate = async (options: GenerateOptions) => {
    const {
      prompt,
      messages,
      system,
      modelName = aiDefaultModel,
      temperature,
    } = options

    try {
      isGenerating.value = true
      generatedContent.value = ''

      abortController.value = new AbortController()

      let result

      // 统一设置流式文本生成选项，包括公共参数
      const streamOptions: StreamTextOptions = {
        model: deepseek(modelName),
        abortSignal: abortController.value.signal,
        maxTokens,
        temperature,
        system,
      }

      if (messages && messages.length > 0) {
        // 聊天模式：使用历史消息生成回复
        let processedMessages = messages

        // 1. 限制历史消息数量，避免超出模型上下文窗口
        if (processedMessages.length > maxHistoryMessages) {
          // 保留最新的 N 条消息，确保对话连贯性
          processedMessages = processedMessages.slice(-maxHistoryMessages)
        }

        // 2. 根据 token 限制智能截断历史，优先保留最近和重要的消息
        processedMessages = truncateMessages(processedMessages, system, maxTokens)

        if (!enableThinking) {
          // HACK: 通过添加 /no_think 标记，避免模型思考
          processedMessages.forEach((message, index) => {
            if (index === processedMessages.length - 1) {
              if (message.role === 'user' && message.content) {
                message.content = `${message.content}/no_think`
              }
            }
          })
        }

        // 使用聊天模式调用 AI 接口
        result = streamText({
          ...streamOptions,
          messages: processedMessages,
        })
      }
      else if (prompt) {
        // 提示词模式：使用单一提示词生成文本
        result = streamText({
          ...streamOptions,
          prompt: enableThinking ? prompt : `${prompt}/no_think`,
        })
      }
      else {
        throw new Error('必须提供 prompt 或 messages')
      }

      for await (const chunk of result.textStream) {
        // 迭代处理文本流中的每个片段，实现流式显示 AI 生成内容
        generatedContent.value += chunk
      }

      return true
    }
    catch (err) {
      if (err instanceof Error && err.name !== 'AbortError' && onError) {
        onError(err)
      }

      return false
    }
    finally {
      isGenerating.value = false
      abortController.value = null
    }
  }

  /**
   * 停止生成
   */
  const stopGeneration = () => {
    if (abortController.value) {
      abortController.value.abort()
      abortController.value = null
    }
  }

  return {
    generate,
    stopGeneration,
    generatedContent,
    isGenerating,
  }
}
