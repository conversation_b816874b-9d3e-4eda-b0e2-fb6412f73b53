import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/constants-tw/query-key'
import { ProjectService } from '~/services-tw/project'
import type { TW_Project } from '~/types-tw/project'

export function useQueryProjectDetail(projectId: MaybeRefOrGetter<TW_Project['id'] | undefined>) {
  return useQuery({
    queryKey: queryKeys.project.detail(projectId),
    queryFn: () => {
      const id = toValue(projectId)

      if (id) {
        return ProjectService.getProjectOverview(id)
      }

      return null
    },
  })
}
