import { useQuery } from '@tanstack/vue-query'

import { queryKeys } from '~/enums/query-key'

/**
 * 查询资产模板字段列表
 *
 * @param templateId - 模板 ID，可以是响应式引用或普通值，如果不传或无值，则不触发请求数据
 *
 * @example
 * // 1. 使用普通值
 * const { data: fieldList } = useQueryTemplateFields(1)
 *
 * // 2. 使用响应式引用
 * const { data: fieldList } = useQueryTemplateFields(formValues.value.template_id)
 *
 * // 3. 使用计算属性
 * const { data: fieldList } = useQueryTemplateFields(computed(() => formValues.value.template_id))
 */
export function useQueryTemplateFields(templateId: Ref<AssetTemplate['id'] | undefined>) {
  const hasTemplateId = computed(() => typeof templateId.value === 'number')

  return useQuery({
    queryKey: queryKeys.asset.template.field.all(templateId),
    queryFn: () => {
      if (typeof templateId.value === 'number') {
        return AssetService.getAssetTemplateFieldList({
          template_id: templateId.value,
        })
      }

      return null
    },
    enabled: hasTemplateId,
  })
}
