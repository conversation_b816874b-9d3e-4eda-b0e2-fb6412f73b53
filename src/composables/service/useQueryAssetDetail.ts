import type { UseQueryOptions } from '@tanstack/vue-query'
import { useQuery } from '@tanstack/vue-query'

import { AssetType } from '~/enums/asset'
import { queryKeys } from '~/enums/query-key'

interface QueryAssetDetailParams {
  assetType: MaybeRefOrGetter<AssetType | undefined>
  assetId: MaybeRefOrGetter<BasicAssetDetail['id'] | undefined>
  options?: Omit<UseQueryOptions<AssetDetailResult | null>, 'queryKey' | 'queryFn'>
}

export function useQueryAssetDetail({ assetId, assetType, options = {} }: QueryAssetDetailParams) {
  return useQuery<AssetDetailResult | null>({
    queryKey: queryKeys.asset.server.detail(assetType, assetId),
    queryFn: () => {
      const type = toValue(assetType)
      const id = toValue(assetId)

      if (type && id) {
        switch (type) {
          case AssetType.NetworkDevice:
            return AssetService.getNetDeviceDetail(id)

          case AssetType.Server:
            return AssetService.getServerDetail(id)

          case AssetType.Database:
            return AssetService.getDatabaseDetail(id)

          case AssetType.BusinessSystem:
            return AssetService.getBusinessSystemDetail(id)

          case AssetType.Other:
            return AssetService.getOtherAssetDetail(id)

          default:
            return null
        }
      }

      return null
    },

    ...options,
  })
}
