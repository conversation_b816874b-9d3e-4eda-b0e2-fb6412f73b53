import { consola } from 'consola'
import type { DataTableProps } from 'primevue'

import type { ProTableColumn, ProTableFilterValues, ProTableSort } from '~/types/pro'

type TablePersistFeature = 'search' | 'sorts' | 'filters' | 'hiddenColumns' | 'size'

export interface TablePersistConfig {
  enabled?: boolean
  keyPrefix?: string
  features?: TablePersistFeature[]
}

export interface TablePersistOptions<T> {
  search: Ref<ProTableSearchValue | undefined>
  sorts: Ref<ProTableSort[] | undefined>
  filters: Ref<ProTableFilterValues | undefined>
  hiddenColumnFields: Ref<ProTableColumn<T>['field'][] | undefined>
  size: Ref<DataTableProps['size'] | undefined>
  persistConfig: TablePersistConfig
}

const allFeatures: TablePersistFeature[] = ['search', 'sorts', 'filters', 'hiddenColumns', 'size']

/**
 * 表格持久化组合式函数
 * 用于将表格的搜索、排序、筛选、隐藏列等状态持久化到 localStorage
 *
 * - 支持持久化的功能：
 *   - 搜索（search）
 *   - 排序（sorts）
 *   - 筛选（filters）
 *   - 隐藏列（hiddenColumns）
 *   - 尺寸大小（size）
 *
 * - 当表格状态发生变化时，自动保存到 localStorage
 * - 组件初始化时，自动从 localStorage 恢复上次的状态
 *
 * @returns 返回清除存储的方法
 */
export function useTablePersist<T>(options: TablePersistOptions<T>) {
  const { search, sorts, filters, hiddenColumnFields, size, persistConfig } = options

  const config: TablePersistConfig = {
    enabled: false,
    keyPrefix: 'pro-table',
    features: allFeatures,
    ...persistConfig,
  }

  const getStorageKey = (feature: string) => {
    return `${config.keyPrefix}-${feature}`
  }

  const getStorageValue = <V>(key: string): V | null => {
    try {
      const value = localStorage.getItem(key)

      return value ? JSON.parse(value) : null
    }
    catch {
      return null
    }
  }

  const setStorageValue = (key: string, value: unknown) => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    }
    catch (error) {
      consola.error('无法保存到 localStorage', error)
    }
  }

  // 处理特定特性的函数
  const handleFeature = (feature: TablePersistFeature, action: 'initialize' | 'save' | 'clear', value?: unknown) => {
    if (!config.features?.includes(feature)) {
      return
    }

    const storageKey = getStorageKey(feature)

    if (action === 'initialize') {
      switch (feature) {
        case 'search':
          search.value = getStorageValue(storageKey) || undefined
          break

        case 'sorts':
          sorts.value = getStorageValue(storageKey) || undefined
          break

        case 'filters':
          filters.value = getStorageValue(storageKey) || undefined
          break

        case 'hiddenColumns':
          hiddenColumnFields.value = getStorageValue(storageKey) || undefined
          break

        case 'size':
          size.value = getStorageValue(storageKey) || undefined
          break
      }
    }
    else if (action === 'save' && value !== undefined) {
      setStorageValue(storageKey, value)
    }
    else if (action === 'clear') {
      localStorage.removeItem(storageKey)
    }
  }

  // 初始化数据
  const initializeFromStorage = () => {
    if (!config.enabled) {
      return
    }

    allFeatures.forEach((feature) => handleFeature(feature, 'initialize'))
  }

  // 监听变化并保存
  watch([search, sorts, filters, hiddenColumnFields, size], ([newSearch, newSorts, newFilters, newHiddenColumns, newSize]) => {
    if (!config.enabled) {
      return
    }

    // 直接处理每个特性
    handleFeature('search', 'save', newSearch)
    handleFeature('sorts', 'save', newSorts)
    handleFeature('filters', 'save', newFilters)
    handleFeature('hiddenColumns', 'save', newHiddenColumns)
    handleFeature('size', 'save', newSize)
  }, { deep: true })

  // 初始化
  initializeFromStorage()

  return {
    clearStorage: () => {
      if (!config.enabled) {
        return
      }

      allFeatures.forEach((feature) => handleFeature(feature, 'clear'))
    },
  }
}
