import type { GlobalEvent } from '~/enums/event'
import type { GlobalEvents } from '~/types/common'
import { emitter } from '~/utils/common'

/**
 * 事件发射器组合式函数，用于在组件中管理事件订阅，并在组件卸载时自动清理
 *
 * 使用此组合式函数可以避免在 onBeforeUnmount 中手动移除事件监听器，防止内存泄漏
 *
 * @example
 * ```ts
 * // 在组件中使用
 * const { on, emit } = useEmitter()
 *
 * // 注册事件（会在组件卸载时自动清理）
 * on(GlobalEvent.NodeActivate, handleNodeActivate)
 * on(GlobalEvent.NodeDelete, handleNodeDelete)
 *
 * // 发送事件
 * emit(GlobalEvent.NodeAdd, addedNodesAndConnections)
 * ```
 */
export function useEmitter() {
  // 存储组件中注册的所有事件及其处理函数
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  const handlers = new Map<GlobalEvent, Function[]>()

  /**
   * 注册事件监听器，并在组件卸载时自动移除
   *
   * @param event 事件名称
   * @param handler 事件处理函数
   */
  const on = <E extends GlobalEvent>(event: E, handler: (payload: GlobalEvents[E]) => void) => {
    emitter.on(event, handler)

    if (!handlers.has(event)) {
      handlers.set(event, [])
    }

    handlers.get(event)?.push(handler)
  }

  /**
   * 发送事件
   *
   * @param event 事件名称
   * @param payload 事件数据
   */
  const emit = <E extends GlobalEvent>(event: E, payload?: GlobalEvents[E]) => {
    // 由于 mitt 的类型定义，这里需要做一个类型断言
    // @ts-expect-error mitt 的类型定义与我们的类型定义不完全匹配
    emitter.emit(event, payload)
  }

  // 手动移除特定事件监听器
  const off = <E extends GlobalEvent>(event: E, handler: (payload: GlobalEvents[E]) => void) => {
    emitter.off(event, handler)

    const eventHandlers = handlers.get(event)

    if (eventHandlers) {
      const index = eventHandlers.indexOf(handler)

      if (index !== -1) {
        eventHandlers.splice(index, 1)
      }

      if (eventHandlers.length === 0) {
        handlers.delete(event)
      }
    }
  }

  // 在组件卸载前自动清理所有注册的事件监听器
  onBeforeUnmount(() => {
    handlers.forEach((eventHandlers, event) => {
      eventHandlers.forEach((handler) => {
        emitter.off(event, handler as UnsafeAny)
      })
    })

    handlers.clear()
  })

  return {
    on,
    off,
    emit,
  }
}
