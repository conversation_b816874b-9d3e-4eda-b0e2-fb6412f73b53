import { useEventListener } from '@vueuse/core'

/**
 * 处理输入法（IME）组合输入状态的组合式API
 *
 * 用于监听输入法组合事件，防止在 CJK 输入过程中触发提交操作
 *
 * @param element 需要监听的元素引用
 * @returns isComposing - 表示当前是否处于输入法组合输入状态的响应式引用
 */
export function useIMEInput(element: Ref<HTMLElement | null>) {
  const isComposing = ref(false)
  let cleanupFns: (() => void)[] = []

  const cleanup = () => {
    cleanupFns.forEach((fn) => fn())
    cleanupFns = []
  }

  // 监听元素引用的变化
  watchEffect((onCleanup) => {
    cleanup()

    const el = element.value

    if (!el) {
      return
    }

    const stopCompositionStart = useEventListener(el, 'compositionstart', () => {
      isComposing.value = true
    })

    const stopCompositionEnd = useEventListener(el, 'compositionend', () => {
      // 由于 compositionend 事件在键盘事件之前触发
      // 添加一个微小的延迟确保状态在键盘事件处理时已更新
      setTimeout(() => {
        isComposing.value = false
      }, 0)
    })

    cleanupFns = [stopCompositionStart, stopCompositionEnd]
    onCleanup(cleanup)
  })

  return {
    isComposing,
  }
}
