import { consola } from 'consola'
import type { BaseIssue, BaseSchema } from 'valibot'
import { safeParse } from 'valibot'

type Schema = BaseSchema<unknown, unknown, BaseIssue<unknown>>

interface UseRouteParamOptions<T extends Schema> {
  /** 参数名称 */
  name: string
  /** valibot schema */
  schema: T
}

/**
 * 根据 schema 类型进行值转换
 * @param value 原始字符串值
 * @param schema valibot schema
 */
function transformValueBySchema(value: string, schema: Schema) {
  // 如果是数字类型的 schema
  if (schema.type === 'number') {
    const num = Number(value)

    return Number.isNaN(num) ? value : num
  }

  // 如果是布尔类型的 schema
  if (schema.type === 'boolean') {
    if (value.toLowerCase() === 'true') {
      return true
    }

    if (value.toLowerCase() === 'false') {
      return false
    }

    return value
  }

  // 其他类型直接返回原值
  return value
}

/**
 * 用于安全地获取和验证路由参数的组合式函数
 * @example
 * ```ts
 * // 获取数字类型的 ID 参数
 * const id = useRouteParam({
 *   name: 'id',
 *   schema: number()
 * })
 *
 * // 获取字符串类型的参数
 * const key = useRouteParam({
 *   name: 'key',
 *   schema: string()
 * })
 * ```
 */
export function useRouteParam<T extends Schema>({
  name,
  schema,
}: UseRouteParamOptions<T>) {
  const route = useRoute()

  return computed(() => {
    const param = route.params[name]

    if (typeof param !== 'string') {
      return undefined
    }

    // 先进行类型转换
    const transformedValue = transformValueBySchema(param, schema)

    // 然后进行验证
    const result = safeParse(schema, transformedValue)

    if (result.success) {
      return result.output
    }
    else {
      consola.error(`路由参数 ${name} 验证失败`, result.issues)
    }

    return undefined
  })
}
