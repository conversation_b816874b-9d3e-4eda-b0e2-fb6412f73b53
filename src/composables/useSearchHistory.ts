import { useLocalStorage } from './useLocalStorage'

interface UseSearchHistoryOptions {
  /** 最大历史记录数，默认 10，如果超出，则移除最旧的记录 */
  maxHistory?: number
}

const DEFAULT_MAX_HISTORY = 10

export function useSearchHistory(key: string, options?: UseSearchHistoryOptions) {
  const maxHistory = options?.maxHistory ?? DEFAULT_MAX_HISTORY
  const searchHistory = useLocalStorage<string[]>(`search-history-${key}`, [])

  const addHistory = (keyword: string) => {
    if (!keyword) {
      return
    }

    const history = searchHistory.value
    // 移除重复项
    const index = history.indexOf(keyword)

    if (index > -1) {
      history.splice(index, 1)
    }

    // 添加到开头
    history.unshift(keyword)

    // 限制数量
    if (history.length > maxHistory) {
      history.pop()
    }

    searchHistory.value = history
  }

  const removeHistory = (keyword: string) => {
    const history = searchHistory.value
    const index = history.indexOf(keyword)

    if (index > -1) {
      history.splice(index, 1)
      searchHistory.value = history
    }
  }

  const clearHistory = () => {
    searchHistory.value = []
  }

  return {
    searchHistory,
    addHistory,
    removeHistory,
    clearHistory,
  }
}
