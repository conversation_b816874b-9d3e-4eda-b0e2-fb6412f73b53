/**
 * 导航菜单相关的组合式函数
 */
export function useNavMenu() {
  const route = useRoute()

  /**
   * 检查导航菜单项是否处于激活状态
   *
   * @param item 导航菜单项
   */
  const isNavMenuItemActive = (item: NavMenuItem): boolean => {
    if (item.route) {
      if (isRoutePathEqual(item.route, route.path)) {
        return true
      }
    }

    if (item.isActive) {
      return item.isActive(route)
    }

    return false
  }

  const activeRouteKey = ref('')

  watch(() => route.path, (newPath) => {
    const routeKey = getRouteKeyByPath(newPath)

    if (routeKey) {
      activeRouteKey.value = routeKey
    }
  }, { immediate: true })

  return {
    isNavMenuItemActive,
    activeRouteKey,
  }
}
