/**
 * 防抖状态 Composable
 *
 * 用于处理状态的瞬时变化，避免 UI 闪烁。
 * 特别适用于加载状态、错误状态等快速变化的状态。
 *
 * 特性：
 * - 状态变为 true 时立即生效
 * - 状态变为 false 时延迟生效
 * - 支持自定义延迟时间（设置为 0 时禁用防抖）
 * - 自动清理定时器，避免内存泄漏
 *
 * @example
 * ```ts
 * // 基本用法
 * const isLoading = ref(false)
 * const { debouncedState } = useDebounceState(isLoading)
 *
 * // 自定义延迟时间
 * const { debouncedState } = useDebounceState(isLoading, { delay: 500 })
 *
 * // 禁用防抖（状态变化立即生效）
 * const { debouncedState } = useDebounceState(isLoading, { delay: 0 })
 *
 * // 在模板中使用
 * <div v-if="debouncedState">加载中...</div>
 * ```
 */

export interface DebounceStateOptions {
  /**
   * 延迟时间（毫秒），默认为 200ms
   * 设置为 0 时禁用防抖，状态变化将立即生效
   */
  delay?: number
}

export function useDebounceState<T extends boolean | undefined>(
  state: Ref<T>,
  options: DebounceStateOptions = {},
) {
  const {
    delay = 100,
  } = options

  // 初始状态直接使用原始状态
  const debouncedState = ref(state.value) as Ref<T>
  const timer = ref<ReturnType<typeof setTimeout> | null>(null)

  // 监听状态变化
  watch(state, (newVal) => {
    if (delay === 0) {
      debouncedState.value = newVal
    }
    else {
      if (timer.value) {
        clearTimeout(timer.value)
      }

      timer.value = setTimeout(() => {
        debouncedState.value = newVal
      }, delay)
    }
  }, { immediate: true })

  // 组件卸载时清除定时器
  onBeforeUnmount(() => {
    if (timer.value) {
      clearTimeout(timer.value)
      timer.value = null
    }
  })

  return {
    /** 防抖后的状态 */
    debouncedState,
    /** 手动重置状态 */
    reset: () => {
      debouncedState.value = state.value
    },
    /** 手动清除定时器 */
    clearTimer: () => {
      if (timer.value) {
        clearTimeout(timer.value)
        timer.value = null
      }
    },
  }
}

export default useDebounceState
