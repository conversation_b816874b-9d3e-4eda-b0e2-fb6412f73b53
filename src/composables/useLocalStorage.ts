import { consola } from 'consola'

/**
 * 用于在浏览器的 localStorage 中存储和获取数据的组合式函数
 *
 * 该函数会自动处理 JSON 序列化/反序列化，并在值变化时更新到 localStorage
 *
 * @param key - localStorage 中使用的键名
 * @param initialValue - 当 localStorage 中不存在该键或读取出错时使用的初始值
 * @returns 返回一个响应式引用，可直接修改其 value 属性来更新存储
 *
 * @example
 * // 在组件中使用
 * const xxx = useLocalStorage('xxx', '')
 * // 读取值
 * log(xxx.value)
 * // 设置新值 (会自动保存到 localStorage)
 * xxx.value = '新用户名'
 */
export function useLocalStorage<T>(key: string, initialValue?: T) {
  const storedValue = ref<T>((() => {
    try {
      const item = window.localStorage.getItem(key)

      return item ? JSON.parse(item) : initialValue
    }
    catch (error) {
      consola.error('无法读取 localStorage:', error)

      return initialValue
    }
  })())

  // 监听值的变化并更新到 localStorage
  watch(
    storedValue,
    (newValue) => {
      try {
        window.localStorage.setItem(key, JSON.stringify(newValue))
      }
      catch (error) {
        consola.error('无法写入 localStorage:', error)
      }
    },
    { deep: true },
  )

  return storedValue
}
