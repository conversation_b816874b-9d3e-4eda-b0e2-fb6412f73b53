import { valibotResolver } from '@primevue/forms/resolvers/valibot'
import { consola } from 'consola'
import { cloneDeep, isEqual, isPlainObject, merge } from 'lodash-es'

type FormResolver = typeof valibotResolver
type FormResolverParams = Parameters<FormResolver>
type ResolverSchema = FormResolverParams[0]

interface UseFormControlOptions<FormValues = unknown, UpdatingItem = FormValues> {
  /** 表单初始值 */
  initialValues?: Partial<FormValues>
  /** 表单验证规则 */
  resolver?: ResolverSchema
  /** 关闭表单时是否重置表单，默认 true */
  resetOnClose?: boolean
  /** 按钮文本 */
  btnLabel?: Partial<Record<FormEditState, string>>
  /** 表单标题 */
  formTitle?: Partial<Record<FormEditState, string>>
  /** 是否开启表单脏检查 */
  enableDirtyCheck?: boolean
  /** 表单初始状态 */
  initialFormEditState?: FormEditState
  /** 如果传递，则会调用以获取详情数据来填充表单内容 */
  fetchDetail?: (updatingItem: UpdatingItem) => Promise<FormValues> | FormValues
  /** 表单值变化回调 */
  onFormValuesChange?: (newValues: Partial<FormValues>, oldValues: Partial<FormValues>) => void
  /**
   * 监听表单特定字段变化的回调
   *
   * @example
   * ```ts
   * onFieldChange: {
   *   asset: (newValue, oldValue) => {
   *     log(newValue, oldValue)
   *   },
   * }
   * ```
   */
  onFieldChange?: {
    [K in keyof FormValues]?: (newValue: FormValues[K], oldValue: FormValues[K]) => void
  }
  /** 关闭表单回调 */
  onClose?: () => void
  /** 表单提交，可以在这里调用接口上传表单数据 */
  onSubmit?: (params: FormSubmitOptions<FormValues>) => Promise<void>
  /** 表单提交成功回调 */
  onSubmitSuccess?: () => void

  onModalVisibleChange?: (visible: boolean) => void
}

export function useFormControl<FormValues = unknown, UpdatingItem = FormValues>(
  options: MaybeRefOrGetter<UseFormControlOptions<FormValues, UpdatingItem>>,
) {
  const {
    initialValues = {},
    resolver,
    resetOnClose = true,
    enableDirtyCheck = false,
    btnLabel = {
      create: '确认',
      update: '保存',
    },
    formTitle = {
      create: '添加',
      update: '编辑',
    },
    initialFormEditState,
    fetchDetail,
    onFormValuesChange,
    onFieldChange,
    onClose,
    onSubmit,
    onSubmitSuccess,
    onModalVisibleChange,
  } = toValue(options)

  const formEditState = ref<FormEditState | undefined>(initialFormEditState)

  const isFormCreate = computed(() => formEditState.value === 'create')
  const isFormUpdate = computed(() => formEditState.value === 'update')

  const formValues = ref<FormValues>(cloneDeep(initialValues as FormValues))

  const forceUpdateKey = ref(0)

  const forceUpdateForm = () => {
    consola.warn('触发表单强制重渲染')
    forceUpdateKey.value++
  }

  /**
   * 存储当前正在编辑的数据项
   * - 在表单更新模式下，保存原始数据或请求远程数据以获取详情
   * - 在创建模式下为 undefined
   */
  const updatingItem = ref<UpdatingItem>()

  const loading = ref(false)

  const modalVisible = computed(() => {
    return formEditState.value === 'create' || formEditState.value === 'update'
  })

  watch(
    modalVisible,
    (visible) => {
      onModalVisibleChange?.(visible)
    },
    { flush: 'sync' },
  )

  const confirmBtnLabel = computed(() => {
    return formEditState.value === 'create' ? btnLabel.create : formEditState.value === 'update' ? btnLabel.update : ''
  })

  const formTitleText = computed(() => {
    return formEditState.value === 'create' ? formTitle.create : formEditState.value === 'update' ? formTitle.update : ''
  })

  const formResolver = resolver ? valibotResolver(resolver) : undefined

  // 不传参数就会重置为初始值。
  const resetForm = (options?: {
    /** 表单状态 */
    state?: FormEditState
    /** 表单值 */
    values?: Partial<FormValues>
    /** 更新项 */
    updatingItem?: UpdatingItem
  }) => {
    const valuesToReset = options?.values
    const clonedInitialValues = cloneDeep(initialValues)

    if (isPlainObject(valuesToReset)) {
      formValues.value = cloneDeep(merge({}, clonedInitialValues, valuesToReset))
    }
    else {
      formValues.value = clonedInitialValues
    }

    if (options?.state === 'update') {
      updatingItem.value = options?.updatingItem
    }
    else {
      updatingItem.value = undefined
    }

    formEditState.value = options?.state
  }

  const handleClose = () => {
    if (resetOnClose) {
      resetForm()
    }

    onClose?.()
  }

  const openCreateModal = (initialStates?: Partial<FormValues>) => {
    if (initialStates instanceof Event) {
      consola.warn(`[useFormControl] ${openCreateModal.name}: 请勿将事件对象作为初始值传入`)

      return
    }

    resetForm({
      state: 'create',
      values: initialStates,
    })
  }

  const openUpdateModal = async (item: UpdatingItem) => {
    resetForm({
      state: 'update',
    })

    if (fetchDetail) {
      const detail = await fetchDetail(item)

      resetForm({
        state: 'update',
        values: detail,
        updatingItem: item,
      })
    }
    else {
      resetForm({
        state: 'update',
        updatingItem: item,
      })
    }

    forceUpdateForm()
  }

  // 监听特定字段的变化
  if (onFieldChange) {
    Object.entries(onFieldChange).forEach(([field, callback]) => {
      watch(
        () => {
          try {
            return (formValues.value)[field]
          }
          catch (err) {
            consola.error(err)

            return undefined
          }
        },
        (newValue, oldValue) => {
          if (newValue !== oldValue) {
            if (typeof callback === 'function') {
              callback(newValue, oldValue)
            }
          }
        },
        { flush: 'sync' },
      )
    })
  }

  const isFormDirty = ref<boolean>()

  // 监听整个表单值的变化
  watch(
    formValues,
    (newValues, oldValues) => {
      // 监听表单值变化，实现表单脏检查(form dirty checking)
      // 当表单的当前值与初始值不一致时，标记表单为"脏"状态
      if (enableDirtyCheck) {
        if (!isEqual(formValues.value, initialValues)) {
          isFormDirty.value = true
        }
        else {
          isFormDirty.value = false
        }
      }

      onFormValuesChange?.(newValues, oldValues)
    },
    { deep: true, flush: 'sync' },
  )

  const handleSubmit = async (params: FormSubmitOptions<FormValues>) => {
    if (typeof onSubmit === 'function') {
      const { valid, states } = params
      consola.withTag('form states').log(toRaw(states))

      if (valid) {
        try {
          loading.value = true

          await onSubmit(params)

          onSubmitSuccess?.()

          handleClose()
        }
        finally {
          loading.value = false
        }
      }
    }
  }

  const handleModalVisibleChange = (visible: boolean) => {
    if (!visible) {
      handleClose()
    }
  }

  return {
    /** 表单的编辑状态 */
    formEditState,
    /** 是否是创建状态 */
    isFormCreate,
    /** 是否是更新状态 */
    isFormUpdate,

    /** 表单是否脏 */
    isFormDirty,

    formValues,
    /** 强制更新表单 */
    forceUpdateKey,
    forceUpdateForm,

    updatingItem,
    modalVisible,
    /** 表单是否加载中 */
    loading,
    confirmBtnLabel,
    formTitleText,

    formResolver,
    resetForm,
    handleClose,
    openCreateModal,
    openUpdateModal,
    handleSubmit,
    handleModalVisibleChange,
  }
}
