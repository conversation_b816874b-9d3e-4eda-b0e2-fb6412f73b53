import { consola } from 'consola'

import { routeConfig, RouteKey } from '~/enums/route'
import type { RouteConfig } from '~/types/common'
import type { PermissionFlag } from '~/types/user'

export interface UseAccessibleRouteOptions {
  /**
   * 默认路由键，如果用户有权限访问此路由，则返回此路由
   */
  defaultRouteKey?: RouteKey
}

/**
 * 查找用户有权限访问的第一个路由
 *
 * 首先从路由配置中查找，如果未找到则从路由元数据中查找
 *
 * 路由查找逻辑：
 * 1. 首先尝试默认路由
 * 2. 如果用户无权访问默认路由，则遍历所有路由查找第一个有权限的路由
 * 3. 如果没有找到任何可访问的路由，则返回 null，由调用者决定如何处理
 */
export function useAccessibleRoute(options: UseAccessibleRouteOptions = {}) {
  const router = useRouter()
  const userStore = useUserStore()

  // 如果未提供默认路由，则使用配置中的默认路由
  const internalDefaultRouteKey = isFuYao()
    ? RouteKey.TW_项目管理
    : RouteKey.仪表盘
  const defaultRouteKey = options.defaultRouteKey || internalDefaultRouteKey

  /**
   * 查找用户有权限访问的第一个路由
   */
  function findFirstAccessibleRoute(): RouteKey | null {
    const allRoutes = router.getRoutes()

    // 尝试获取默认路由的权限要求
    const defaultRoute = routeConfig[defaultRouteKey] as RouteConfig
    const defaultRoutePermissions = defaultRoute?.permissions
      ?? allRoutes.find((r) => getRouteKeyByPath(r.path) === defaultRouteKey)?.meta.permissions as PermissionFlag[] | undefined

    // 检查用户是否有权限访问默认路由
    if (userStore.hasPermission(defaultRoutePermissions)) {
      return defaultRouteKey
    }
    else {
      consola.warn('用户无权访问默认页面', {
        path: defaultRoute.route,
        routePermissions: defaultRoutePermissions,
        userPermissions: userStore.permissions,
      })
    }

    // HACK: 权限校验逻辑不完善，临时注释
    // 遍历所有路由，查找用户有权限访问的第一个路由
    // for (const routeItem of allRoutes) {
    //   if (isDynamicRoute(routeItem.path)) {
    //     // 因为无法确定路由参数，所以动态路由不作为可访问路由
    //     continue
    //   }

    //   const permissions = getRouteByPath(routeItem.path)?.permissions ?? routeItem.meta.permissions as PermissionFlag[] | undefined

    //   if (userStore.hasPermission(permissions)) {
    //     const routeKey = getRouteKeyByPath(routeItem.path)

    //     if (routeKey) {
    //       return routeKey
    //     }
    //   }
    // }

    // consola.warn('用户无权访问任何需要鉴权的页面', {
    //   path: defaultRoute.route,
    //   routePermissions: defaultRoutePermissions,
    //   userPermissions: userStore.permissions,
    // })

    return null
  }

  const firstAccessibleRoute = computed(() => {
    const routeKey = findFirstAccessibleRoute()

    if (routeKey) {
      return routeConfig[routeKey]
    }
    else {
      return null
    }
  })

  return {
    firstAccessibleRoute,
  }
}
