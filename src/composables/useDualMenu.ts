import { nanoid } from 'nanoid'

import type DualMenu from '~/components/DualMenu.vue'

/**
 * 用于创建双模式菜单（上下文菜单 + 弹出菜单）的组合式 API
 *
 * @example
 * const { dualMenuRef, dualMenu } = useDualMenu()
 *
 * // 在模板中使用
 * <DualMenu :ref="dualMenuRef" />
 *
 * // 在方法中使用
 * dualMenu.value?.showMenu(event, 'popup')
 */
export function useDualMenu() {
  const refKey = `dual-menu-${nanoid(4)}`

  const dualMenu = useTemplateRef<InstanceType<typeof DualMenu>>(refKey)

  return {
    dualMenuRef: refKey,
    dualMenu,
  }
}
