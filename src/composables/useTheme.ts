import { $dt } from '@primevue/themes'
import { get } from 'lodash-es'

export function useTheme() {
  const appStore = useAppStore()

  const { themeState } = storeToRefs(appStore)

  const isDarkMode = computed(() => {
    if (themeState.value.themeMode === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }

    return themeState.value.themeMode === 'dark'
  })

  const themeMode = computed(() => {
    if (themeState.value.themeMode === 'auto') {
      if (isDarkMode.value) {
        return 'dark'
      }

      return 'light'
    }

    return themeState.value.themeMode
  })

  const textColor = computed(() => {
    const textColor = $dt('text.color').value

    return get(textColor, `${themeMode.value}.value`)
  })

  return {
    themeState,
    themeMode,
    isDarkMode,
    textColor,
  }
}
