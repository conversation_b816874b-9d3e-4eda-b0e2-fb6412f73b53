import type { ApexOptions } from 'apexcharts'
import ApexCharts from 'apexcharts'

export function useApexChart(chartOptions: MaybeRefOrGetter<ApexOptions>) {
  const chartContainer = ref<HTMLElement | null>(null)

  let chart: ApexCharts | null = null

  // 监听配置变化，自动更新图表
  watch(chartOptions, () => {
    if (chart && chartContainer.value) {
      chart.updateOptions(toValue(chartOptions))
    }
  }, { deep: true })

  onMounted(() => {
    // 创建图表实例
    chart = new ApexCharts(chartContainer.value, toValue(chartOptions))

    // 渲染图表
    chart.render()
  })

  onUnmounted(() => {
    // 销毁图表实例
    if (chart) {
      chart.destroy()
      chart = null
    }
  })

  return {
    chartContainer,
  }
}
