import { nanoid } from 'nanoid'
import type { Popover } from 'primevue'

import type ProPopover from '~/components/pro/ProPopover.vue'

/**
 * ProPopover 组件的组合式函数
 *
 * 用于创建和管理 ProPopover 组件的引用。
 * 主要功能：
 * 1. 生成唯一的 popover 引用 key，避免多个 popover 实例间的冲突
 * 2. 创建并返回 ProPopover 组件的模板引用
 *
 * @returns 返回包含以下属性的对象:
 * - popoverRef: string - popover 的唯一引用 key
 * - popover: Ref<InstanceType<typeof ProPopover>> - popover 组件的模板引用
 */
export function useProPopover() {
  // 为了防止多个 Popover 组件的 ref 冲突，使用 nanoid 生成一个唯一的 key
  const popoverRef = `popover-${nanoid(4)}`

  const popover = useTemplateRef<InstanceType<typeof ProPopover>>(popoverRef)

  return { popoverRef, popover }
}

export function usePopover() {
  // 为了防止多个 Popover 组件的 ref 冲突，使用 nanoid 生成一个唯一的 key
  const popoverRef = `popover-${nanoid(4)}`

  const popover = useTemplateRef<InstanceType<typeof Popover>>(popoverRef)

  return { popoverRef, popover }
}
