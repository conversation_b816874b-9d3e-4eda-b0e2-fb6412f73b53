import { type QueryKey, useQuery } from '@tanstack/vue-query'

/**
 * useLoadMore 配置选项接口
 *
 * @template T - 列表项数据类型
 * @template Q - 查询参数类型（可选）
 */
interface UseLoadMoreOptions<T, Q = unknown> {
  /**
   * Vue Query 的查询键，用于缓存和标识查询
   * @example ['users', 'list']
   */
  queryKey: QueryKey

  /**
   * 获取数据的异步函数
   * @param query - 包含分页参数和自定义查询参数的对象
   * @returns 返回带分页信息的数据结果
   */
  queryFn: (query: { page: number, pageSize: number } & Partial<Q>) => Promise<PageResult<T>> | null

  /** 每页加载数量，默认 10 */
  pageSize?: PageInfo['page_size']

  /**
   * 数据变化时的回调函数
   * @param data - 当前页的数据列表
   */
  onDataChange?: (data: T[]) => void
}

/**
 * 用于处理分页加载更多数据的组合式函数
 *
 * @description
 * 该组合式函数封装了常见的加载更多逻辑，支持：
 * - 分页加载
 * - 数据缓存
 * - 加载状态管理
 * - 刷新数据
 * - 自定义查询参数
 *
 * @example
 * ```ts
 * // 基础用法
 * const { data, isLoading, hasMore, loadMore, refresh } = useLoadMore({
 *   queryKey: ['users'],
 *   queryFn: ({ page, pageSize }) => fetchUsers({ page, pageSize }),
 *   pageSize: 20
 * })
 *
 * // 带查询参数的用法
 * const { data, refresh } = useLoadMore<User, { status: string }>({
 *   queryKey: ['users'],
 *   queryFn: ({ page, pageSize, status }) => fetchUsers({ page, pageSize, status }),
 *   onDataChange: (newData) => log('新数据:', newData)
 * })
 *
 * // 刷新带查询参数
 * refresh({ status: 'active' })
 * ```
 */
export function useLoadMore<T, Q = unknown>(options: UseLoadMoreOptions<T, Q>) {
  const { queryKey, queryFn, pageSize = 10, onDataChange } = options

  const allData = ref<T[]>([])

  const page = ref(1)
  const currentQuery = ref<Q | undefined>(undefined)
  const hasMore = ref(true)

  const { isLoading, refetch } = useQuery({
    queryKey: [...queryKey, page, currentQuery],
    queryFn: () => queryFn({
      page: page.value,
      pageSize,
      ...(currentQuery.value || {}),
    }),
    select: (response) => {
      if (!response) {
        return null
      }

      const newData = response.list

      onDataChange?.(newData)

      if (page.value === 1) {
        allData.value = newData
      }
      else {
        allData.value = [...(allData.value as T[]), ...newData]
      }

      hasMore.value = response.page < response.page_sum

      return response
    },
  })

  const loadMore = () => {
    if (!isLoading.value && hasMore.value) {
      page.value += 1
    }
  }

  const refresh = (query?: Q) => {
    page.value = 1
    currentQuery.value = query

    return refetch()
  }

  return {
    /** 累积加载的所有数据 */
    data: allData,
    /** 是否正在加载数据 */
    isLoading,
    /** 是否还有更多数据可以加载 */
    hasMore,
    /** 加载下一页数据的函数 */
    loadMore,
    /**
     * 刷新数据的函数
     * @param query - 可选的查询参数，会替换当前的查询参数
     */
    refresh,
  }
}
