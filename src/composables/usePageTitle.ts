/**
 * 获取当前页面标题的组合式函数
 *
 * 页面标题的优先级：
 * 1. 页面组件中通过 definePageMeta 定义的 title
 * 2. `~/enums/route/@routeConfig` 中配置的路由标题
 *
 * @returns 当前页面标题
 */
export function usePageInfo() {
  const route = useRoute()

  const getPageTitle = () => {
    // 获取页面组件中定义的 meta title
    const metaTitle = route.meta.title

    if (typeof metaTitle === 'string') {
      return metaTitle
    }

    const routeItem = getRouteByPath(route.path)

    if (routeItem) {
      return routeItem.title
    }

    return ''
  }

  // 创建响应式的页面标题
  const pageTitle = computed(() => getPageTitle())

  const pageDesc = computed(() => route.meta.description)

  return {
    pageTitle,
    pageDesc,
  }
}
