import { routeConfig, RouteKey } from '~/enums/route'
import { getRoutePath } from '~/utils/route'

/**
 * 定义重定向映射关系
 *
 * key - 需要重定向的路径
 * value - 重定向到的路由键
 */
const redirectMap: Record<string, RouteKey> = {
  [routeConfig[RouteKey.仪表盘].route]: RouteKey.资产概览,
  [routeConfig[RouteKey.系统设置].route]: RouteKey.个人中心,
  [routeConfig[RouteKey.TW_项目管理].route]: RouteKey.TW_项目列表,
  [routeConfig[RouteKey.TW_组织架构].route]: RouteKey.TW_成员与部门,
}

/**
 * 路由重定向中间件
 *
 * 集中管理所有需要重定向的路由规则
 * 当访问特定路径时，自动重定向到目标路由
 *
 * 优势：
 * - 集中管理所有重定向规则，便于维护
 * - 在服务器端处理重定向，减少客户端跳转
 * - 支持保留查询参数
 */
export default defineNuxtRouteMiddleware(async (to) => {
  // 如果不是客户端渲染，则跳过（可选，取决于是否需要服务端重定向）
  if (import.meta.server) {
    return
  }

  const path = normalizeRoutePath(to.path)

  // 处理根路径的特殊逻辑
  if (path === '' || path === '/') {
    const { firstAccessibleRoute } = useAccessibleRoute()

    if (firstAccessibleRoute.value) {
      return await navigateTo({
        path: firstAccessibleRoute.value.route,
        query: to.query,
        replace: true,
      })
    }
    else {
      await handleLogout()

      return await navigateTo({
        path: routeConfig[RouteKey.登录].route,
        query: to.query,
        replace: true,
      })
    }
  }

  // 检查是否需要重定向
  if (path in redirectMap) {
    const redirectPath = getRoutePath(redirectMap[path])

    if (path !== redirectPath) {
      return await navigateTo({
        path: redirectPath,
        query: to.query, // 保留查询参数
        replace: true, // 替换当前历史记录，避免重复路由记录
      })
    }
  }
})
