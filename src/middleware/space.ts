import { <PERSON><PERSON><PERSON> } from '~/enums/route'
import { useSpaceStore } from '~/features/space/stores/space'

export default defineNuxtRouteMiddleware(async (to) => {
  const spaceRootPath = getRoutePath(RouteKey.TW_工作空间)

  if (to.path.startsWith(spaceRootPath)) {
    const spaceStore = useSpaceStore()

    if (spaceStore.spaces.length === 0) {
      await spaceStore.fetchSpaces()
    }

    const urlSpaceId = to.params.spaceId

    // 使用 useLocalStorage 获取上次访问的空间 ID
    const lastVisitedSpaceId = useLocalStorage<string | null>('lastVisitedSpaceId', null)

    if (typeof urlSpaceId === 'string') {
      // 检查 spaceId 是否存在于已获取的 spaces 列表中
      const spaceExists = spaceStore.spaces.some((space) => space.id === urlSpaceId)

      if (spaceExists) {
        // 如果存在，设置为当前活动空间并保存到本地存储
        spaceStore.setActiveSpaceId(urlSpaceId)
        lastVisitedSpaceId.value = urlSpaceId
      }
      else {
        // 如果不存在，重定向到第一个可用的空间
        const firstSpace = spaceStore.spaces[0]

        if (firstSpace) {
          // 保存第一个空间 ID 到本地存储
          lastVisitedSpaceId.value = firstSpace.id

          return navigateTo({
            path: `/space/${firstSpace.id}`,
            replace: true,
          })
        }
      }
    }
    else if (lastVisitedSpaceId.value && spaceStore.spaces.some((space) => space.id === lastVisitedSpaceId.value)) {
      // 如果 URL 中没有 spaceId 但有上次访问记录，并且该空间仍然存在，重定向到上次访问的空间
      return navigateTo({
        path: `/space/${lastVisitedSpaceId.value}`,
        replace: true,
      })
    }
    else if (spaceStore.spaces.length > 0) {
      // 如果没有有效的上次访问记录但有可用空间，重定向到第一个空间
      const firstSpace = spaceStore.spaces[0]

      if (firstSpace) {
        // 保存第一个空间 ID 到本地存储
        lastVisitedSpaceId.value = firstSpace.id

        return navigateTo({
          path: `/space/${firstSpace.id}`,
          replace: true,
        })
      }
    }
  }
})
