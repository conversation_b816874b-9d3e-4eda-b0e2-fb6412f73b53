import { consola } from 'consola'

import { ApiStatus } from '~/enums/common'
import { RouteKey } from '~/enums/route'

/**
 * 路由鉴权中间件
 *
 * 功能：
 * - 保护需要登录才能访问的页面
 * - 已登录用户访问登录页时重定向到首页
 *
 * 使用场景：
 * - 公共页面：无需登录即可访问（如登录页）
 * - 私有页面：需要登录才能访问，未登录将重定向到登录页
 *
 * 配置方式：
 * 在 `~/app.config.ts` 中配置 `auth.publicPages` 数组来定义公共页面
 */
export default defineNuxtRouteMiddleware(async (to) => {
  // 仅在客户端执行鉴权逻辑
  if (import.meta.server) {
    return
  }

  const toPath = normalizeRoutePath(to.path)

  const appConfig = useAppConfig()

  const userStore = useUserStore()
  const isLoggedIn = userStore.isLoggedIn()

  // 判断目标路由是否为公共页面
  const isPublicPage = appConfig.auth.publicPages.includes(toPath)

  if (isPublicPage) {
    const isLoginPage = isRoutePathEqual(toPath, getRoutePath(RouteKey.登录))

    if (isLoggedIn) {
      /**
       * 场景：已登录用户试图访问登录页
       * 处理：重定向到首页
       */
      if (isLoginPage) {
        return await navigateTo({
          path: getRoutePath(RouteKey.首页),
          replace: true,
        })
      }
    }
    else {
      /**
       * 场景：未登录用户试图访问登录页
       * 处理：跳转至企微授权登录页
       */
      if (isLoginPage) {
        if (isFuYao()) {
          /**
           * 如果是在企微内部访问登录页，则直接走免登流程
           * 否则如果是在 Web 端访问登录页，则提供使用第三方登录流程
           */
          if (isInWeCom()) {
            return await handleThirdPartyLogin()
          }
        }
      }
    }
  }
  else {
    /**
     * 场景：未登录用户试图访问非公共页面
     * 处理：重定向到登录页
     */
    if (!isLoggedIn) {
      if (isFuYao()) {
        return await handleThirdPartyLogin()
      }

      return await redirectToLogin()
    }
    else {
      /**
       * 场景：已登录用户试图访问需要鉴权的页面，但用户没有权限
       * 处理：重定向到错误页，提示用户无权限
       */

      /**
       * 优先从页面元数据中获取页面的访问权限，如果获取不到，则从路由配置中获取
       */
      const routeItem = getRouteByPath(toPath)
      const routePermissions = to.meta.permissions ?? routeItem?.permissions

      const hasPermission = userStore.hasPermission(routePermissions)

      if (!hasPermission) {
        consola.warn('middleware/auth - 用户无权访问该页面', {
          path: toPath,
          routePermissions,
          userPermissions: userStore.permissions,
        })

        return abortNavigation({
          statusCode: ApiStatus.Unauthorized,
          message: '没有权限访问该页面',
        })
      }
    }
  }
})
